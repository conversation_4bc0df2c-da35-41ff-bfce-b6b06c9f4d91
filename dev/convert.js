require('dotenv').config()

const webp = require('webp-converter')
const fs = require('fs')
const path = require('path')
const process = require("process")

const root = path.resolve(__dirname, '../')
const resourcesPath = `${ root }/resources`

const pathsToConvert = [
    'blrp_inventory/images'
]

const handle = async () => {
    await new Promise(async resolve => {
        for (const resourcePath of pathsToConvert) {
            console.log(`[Image Converter] Converting png to webp in ${ resourcePath }`)

            fs.readdir(`${ resourcesPath }/${ resourcePath }`, async (err, files) => {
                if (err) return console.error(`[Image Converter] Error reading ${ resourcePath }, ${ err }`)

                let total = files.length
                let complete = 0

                files.forEach((file, index) => {
                    if (file && file.includes('.jpg') || file.includes('.png')) {
                        const filePath = `${ resourcesPath }/${ resourcePath }/${ file }`
                        const toFilePath = `${ resourcesPath }/${ resourcePath }/${ file }`
                            .replace('.png', '.webp')
                            .replace('.jpg', '.webp')

                        let logging = null
                        const result = webp.cwebp(filePath, toFilePath, '-q 80', logging = '-v')

                        result.then((response) => {
                            complete++
                            fs.rmSync(filePath)
                            console.log(`[Image Converter] Converted ${ file }`)

                            if (complete === total) {
                                resolve()
                            }
                        })
                    } else {
                        complete++

                        if (complete === total) {
                            resolve()
                        }
                    }
                })
            })
        }
    })

    console.log("%cIMAGES CONVERTED. DO NOT FORGET TO ADD FILES TO GIT", "background: red; color: yellow; font-size: x-large");
}

handle()


