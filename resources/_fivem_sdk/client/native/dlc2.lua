-- Example: DLC2::IS_DLC_PRESENT($\mpbusiness2\); ($ = gethashkey)  bruteforce these: 0xB119F6D 0x96F02EE6
-- @module native
-- @submodule dlc2
-- @see IS_DLC_PRESENT
-- @usage BOOL IS_DLC_PRESENT(Hash dlcHash);
-- @param dlcHash Hash
-- @return BOOL
function IsDlcPresent(dlcHash) end

-- @todo
-- @module native
-- @submodule dlc2
-- @see GET_IS_LOADING_SCREEN_ACTIVE
-- @usage BOOL GET_IS_LOADING_SCREEN_ACTIVE();
-- @return BOOL
function GetIsLoadingScreenActive() end