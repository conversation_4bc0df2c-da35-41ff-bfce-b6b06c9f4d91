// var users = [
//     {
//         name: 'ss',
//         points: 1,
//         bullseyes: 0,
//     },
//     {
//         name: 'ss',
//         points: 5,
//         bullseyes: 0,
//     },
//     {
//         name: 'ss',
//         points: 20,
//         bullseyes: 0,
//     },
//     {
//         name: 'ss',
//         points: 3,
//         bullseyes: 0,
//     },
// ]

// users.sort(function(a, b) {
//     return b.points-a.points
// })

// users.forEach(function (val, i) {
//     $('.scoreboard-list').append(`
//         <div class='scoreboard-player-card'>
//             <p class='scoreboard-player-name'>${val.name}</p>
//             <p class='scoreboard-player-bullseyes'>${val.bullseyes}</p>
//             <p class='scoreboard-player-points'>${val.points}</p>
//         </div>
//     `)
// });

window.addEventListener('message', function(event) {
    var data = event.data;

    if(data.type === 'update-scoreboard'){
        // var users = data.players

        // users.sort(function(a, b) {
        //     return b.points-a.points
        // })

        // $('.scoreboard-list').html('')

        // users.forEach(function (val, i) {
        //     $('.scoreboard-list').append(`
        //         <div class='scoreboard-player-card'>
        //             <p class='scoreboard-player-name'>${val.name}</p>
        //             <p class='scoreboard-player-bullseyes'>${val.bullseyes}</p>
        //             <p class='scoreboard-player-points'>${val.points}</p>
        //         </div>
        //     `)
        // });

        $('.scoreboard-list').html('') // reset
        $('.scoreboard-list').append(data.nui)
    }
});