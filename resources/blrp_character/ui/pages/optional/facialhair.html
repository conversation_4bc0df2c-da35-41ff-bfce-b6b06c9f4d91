<h2 class="header">Facial Hair</h2>
<div class="list">
    <h3 class="header">Style</h3>
    <div class="controls">
        <button class="arrow left"></button>
        <select class="headoverlay" id="beard_1" data-index="1">
            <option value="255" selected="selected">Clean Shaven</option>
            <option value="0">Light Stubble</option>
            <option value="1">Balbo</option>
            <option value="2">Circle Beard</option>
            <option value="3">Goatee</option>
            <option value="4">Chin</option>
            <option value="5">Chin Fuzz</option>
            <option value="6">Pencil Chin Strap</option>
            <option value="7">Scruffy</option>
            <option value="8">Musketeer</option>
            <option value="9">Mustache</option>
            <option value="10">Trimmed Beard</option>
            <option value="11">Stubble</option>
            <option value="12">Thin Circle Beard</option>
            <option value="13">Horseshoe</option>
            <option value="14">Pencil and <PERSON><PERSON></option>
            <option value="15"><PERSON> Beard</option>
            <option value="16">Balbo and Sideburns</option>
            <option value="17">Mutton Chops</option>
            <option value="18">Scruffy Beard</option>
            <option value="19">Curly</option>
            <option value="20">Curly & Deep Stranger</option>
            <option value="21">Handlebar</option>
            <option value="22">Faustic</option>
            <option value="23">Otto & Patch</option>
            <option value="24">Otto & Full Stranger</option>
            <option value="25">Light Franz</option>
            <option value="26">The Hampstead</option>
            <option value="27">The Ambrose</option>
            <option value="28">Lincoln Curtain</option>
        </select>
        <button class="arrow right"></button>
    </div>
</div>
<div class="slider opacity">
    <h3 class="header">Opacity</h3>
    <div class="valuelabel center">50%</div>
    <div class="controls">
        <button class="arrow left"></button>
        <input type="range" min="0" max="100" class="headoverlay" id="beard_2" data-index="1">
        <button class="arrow right"></button>
    </div>
</div>
<div class="colorselect">
    <h3 class="header">Color</h3>
    <div class="palette hair overlaycolor" id="beard_3" data-index="1" data-colortype="1">
    </div>
</div>