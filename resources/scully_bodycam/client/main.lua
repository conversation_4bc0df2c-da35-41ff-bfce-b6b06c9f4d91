-- Created by <PERSON><PERSON> | https://discord.gg/scully

Scully.Bodycam = {
    Display = false,
    Callsign = '',
    Name = '',
    Department = '',
}

RegisterNetEvent('core:client:registerSelectedPlayer', function()
    local me = exports.blrp_core:me()
    local character_name = (me.get('callsign') or '') .. ' ' .. string.sub(me.get('firstname'), 1, 1) .. '. ' .. me.get('lastname')
    local department

    if me.hasGroup('LSPD_Internal') then
        department = 'Los Santos Police Department'
    elseif me.hasGroup('Sheriff_Internal') then
        department = 'Blaine County Sheriffs Office'
    elseif me.hasGroup('SAHP_Internal') then
        department = 'San Andreas Highway Patrol'
    elseif me.hasGroup('LSFD') then
        department = 'Los Santos Fire Department'
    else
        department = 'State of San Andreas'
    end

    Scully.Bodycam.Name = character_name
    Scully.Bodycam.Department = department

    if Scully.Bodycam.Display then
        SendNUIMessage({
            type = 'updateName',
            playerName = Scully.Bodycam.Name
        })
        SendNUIMessage({
            type = 'updateDepartment',
            department = Scully.Bodycam.Department
        })
    end
end)

RegisterNetEvent("sullybodycam:toggle")
AddEventHandler("sullybodycam:toggle", function()

    Scully.Bodycam.Display = not Scully.Bodycam.Display

    if Scully.Bodycam.Display then
        TriggerEvent('core:client:registerSelectedPlayer') -- Force update when toggling on
    end

    if Scully.PlaySound and Scully.Bodycam.Display then
        SendNUIMessage({type = 'playBeep'})
    end

    SendNUIMessage({
        type = 'display',
        enable = Scully.Bodycam.Display,
        department = Scully.Bodycam.Department
    })

    SendNUIMessage({
        type = 'updateDepartment',
        department = Scully.Bodycam.Department
    })
end, false)

-- CreateThread(function()
--     while true do
--         Wait(Scully.SoundInterval * 60000)

--         if Scully.PlaySound then
--             if Scully.Bodycam.Display then
--                 SendNUIMessage({type = 'playBeep'})
--             end
--         end
--     end
-- end)
