document.addEventListener('DOMContentLoaded', function() {
    const timeElement = document.getElementById('time');
    const departmentElement = document.getElementById('department');
    const bodycamElement = document.getElementById('bodycam');
    const nameElement = document.getElementById('name');
    const audioElement = document.getElementById('bodycamSound');

    setTime();

    window.addEventListener('message', function(event) {
        const data = event.data;

        switch (data.type) {
            case 'display':
                departmentElement.textContent = data.department.toUpperCase();
                bodycamElement.style.display = data.enable ? 'block' : 'none';
                break;

            case 'updateName':
                nameElement.textContent = data.playerName.toUpperCase();
                break;

            case 'updateDepartment':
                departmentElement.textContent = data.department.toUpperCase();
                break;

            case 'playBeep':
                if (audioElement) {
                    audioElement.currentTime = 0;
                    audioElement.volume = 0.2;
                    audioElement.play();
                }
                break;

            default:
                break;
        }
    });

    function setTime() {
        const currentDate = new Date();
        const timeString = `${currentDate.toLocaleDateString('en', { month: 'short'}).toUpperCase()} ${newNumber(currentDate.getDate())} ${currentDate.getFullYear()}  ${newNumber(currentDate.getHours())}:${newNumber(currentDate.getMinutes())}:${newNumber(currentDate.getSeconds())} ${currentDate.toLocaleString('en', {timeZoneName: 'short'}).split(' ').pop()}`;
        timeElement.textContent = timeString;
        
        const nextSecond = 1000 - (currentDate.getMilliseconds() % 1000);

        setTimeout(setTime, nextSecond);
    }

    function newNumber(number) {
        return number < 10 ? '0' + number : number; 
    }
});