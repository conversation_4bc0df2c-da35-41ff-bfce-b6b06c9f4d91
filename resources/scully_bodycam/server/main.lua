--[[ Created by <PERSON><PERSON>#5775 | https://discord.gg/eNtGFS6
RegisterNetEvent('scully_bodycam:checkPermission', function()
    local src = source
    if IsPlayerAceAllowed(src, 'bodycam') then
        TriggerClientEvent('scully_bodycam:hasPermission', src)
    end
end)

RegisterNetEvent('scully_bodycam:getName', function()
    local src = source
    local playerName = exports['scully_radio2']:getPlayerName(src)
    TriggerClientEvent('scully_bodycam:updateName', src, playerName)
end)]]