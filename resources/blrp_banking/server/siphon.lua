local actions = {
  ['Fine'] = {
    [1000001] = 0.11, -- BCSO
    [1000002] = 0.11, -- LSPD
    [1000007] = 0.11, -- SAHP
  },

  ['Restitution'] = {
    [1000000] = 0.03, -- State of San Andreas
    [1000001] = 0.04, -- BCSO
    [1000002] = 0.04, -- LSPD
    [1000007] = 0.04, -- SAHP
    [1000003] = 0.03, -- Department of Corrections
    [1000004] = 0.02, -- Department of Justice
  },

  ['HospitalCheckIn'] = {
    [1000005] = 0.06, -- Los Santos Fire Department
  },

  ['LocalEMSPickup'] = {
    [1000005] = 0.10, -- Los Santos Fire Department
  },

  ['RedwoodCigarettePurchase'] = {
    [1000001] = 0.04, -- BCSO
    [3579698] = 0.10, -- Redwood Tobacco
  },

  ['CelltowaPhonePurchase'] = {
    [1000001] = 0.04, -- BCSO
  },

  ['BSTPurchase'] = {
    [1000002] = 0.04, -- LSPD
  },
}

local faction_account = {
  ['BCSO'] = 1000001,
  ['LSPD'] = 1000002,
  ['DOC']  = 1000003,
  ['EMS']  = 1000005,
  ['SAHP'] = 1000007,
}

AddEventHandler('blrp_banking:server:siphonAmount', function(action, full_amount)
  local account_numbers = actions[action]

  if not account_numbers then
    return
  end

  local transaction_uuid = randomUuid()

  for account_number, coefficient in pairs(account_numbers) do
    local siphon_amount = math.ceil(full_amount * coefficient)
    local percent = math.ceil(100 * coefficient)

    if siphon_amount > 0 then
      MySQL.Async.execute('UPDATE bank_accounts SET balance = balance + @amount WHERE account_number = @account_number', {
        amount = siphon_amount,
        account_number = account_number
      }, function(rows_affected)
        if rows_affected <= 0 then
          return
        end

        MySQL.Async.execute('INSERT INTO bank_account_transactions (account_number, transaction_uuid, transactor_name, transaction_type, note, amount, hidden, timestamp) VALUES (@account_number, @transaction_uuid, @transactor_name, @transaction_type, @note, @amount, @hidden, @timestamp)', {
          account_number = account_number,
          transaction_uuid = transaction_uuid,
          transactor_name = 'STATE OF SAN ANDREAS',
          transaction_type = 'GOVT DEPOSIT',
          note = percent .. '% payout of $' .. full_amount .. ' ' .. action,
          amount = siphon_amount,
          hidden = true,
          timestamp = os.date("%Y-%m-%d %H:%M:%S"),
        })
      end)
    end
  end
end)

AddEventHandler('blrp_banking:server:debitFaction', function(player, faction_id, amount, transaction_reason)
  local character = exports.blrp_core:character(player)

  local account_number = faction_account[faction_id]

  if not account_number then
    return
  end

  if amount < 0 then
    return
  end

  local rows_affected = MySQL.Sync.execute('UPDATE bank_accounts SET balance = balance - @amount WHERE account_number = @account_number', {
    amount = amount,
    account_number = account_number
  })

  if rows_affected <= 0 then
    return
  end

  MySQL.Async.execute('INSERT INTO bank_account_transactions (account_number, transaction_uuid, transactor_name, transaction_type, note, amount, hidden, timestamp) VALUES (@account_number, @transaction_uuid, @transactor_name, @transaction_type, @note, @amount, @hidden, @timestamp)', {
    account_number = account_number,
    transaction_uuid = randomUuid(),
    transactor_name = character.get('firstname') .. ' ' .. character.get('lastname'),
    transaction_type = 'Withdrawal',
    note = 'Automated transaction - ' .. transaction_reason,
    amount = amount,
    hidden = true,
    timestamp = os.date("%Y-%m-%d %H:%M:%S"),
  })
end)
