-- author https://forum.fivem.net/u/thespartapt
-- modified byt https://github.com/TheSpartaPT/FiveM-ES-CarWash and https://forum.fivem.net/t/release-carwash/9615

--DO-NOT-EDIT-BELLOW-THIS-LINE--

Key = 201 -- ENTER
vehicleWashStation = {
	{-93.967,6393.821,31.452}, -- Paleto Bay
	{1362.839,3591.670,34.923}, -- Sandy Shores West
	{2524.302,4194.887,39.956}, -- Sandy Shores East
	{-699.891,-932.739,19.014}, -- Little Seoul
	{24.750,-1391.926,29.334}, -- <PERSON><PERSON><PERSON>
	{168.033,-1715.346,29.292}, -- <PERSON>
	{993.901, -112.814, 73.469, hide_blip = true}, -- <PERSON> Autocare
	{-1416.9180908203,-446.81289672852,35.909706115723, hide_blip = true}, -- hay<PERSON>
	{489.92678833008,-1344.2626953125,29.234645843506, hide_blip = true}, -- <PERSON> Auto
	{851.30413818359,-2110.6442871094,30.206617355347, hide_blip = true}, -- LS Tuners
	{83.020,6515.687,31.354, hide_blip = true}, -- Vice Motors
}

Citizen.CreateThread(function ()
	Citizen.Wait(0)
	for i = 1, #vehicleWashStation do
		if not vehicleWashStation[i].hide_blip then
			garageCoords = vehicleWashStation[i]
			stationBlip = AddBlipForCoord(garageCoords[1], garageCoords[2], garageCoords[3])
			SetBlipSprite(stationBlip, 100)
			SetBlipScale(stationBlip, 0.7)
			SetBlipAsShortRange(stationBlip, true)
		end
	end
  return
end)

function DrawSpecialText(m_text, showtime)
	SetTextEntry_2("STRING")
	AddTextComponentString(m_text)
	DrawSubtitleTimed(showtime, 1)
end

Citizen.CreateThread(function ()
	while true do
		Citizen.Wait(0)
    local ped = PlayerPedId()
		if IsPedSittingInAnyVehicle(ped) then
			for i = 1, #vehicleWashStation do
				garageCoords2 = vehicleWashStation[i]
        if IsEntityAtCoord(ped, garageCoords2[1], garageCoords2[2], garageCoords2[3], 2.001, 2.001, 5.001, 0, 1, 0) then
          DrawMarker(23, garageCoords2[1], garageCoords2[2], garageCoords2[3]-1.0, 0, 0, 0, 0, 0, 0, 5.0, 5.0, 2.0, 0, 157, 0, 155, 0, 0, 2, 0, 0, 0, 0)
          if IsEntityAtCoord(ped, garageCoords2[1], garageCoords2[2], garageCoords2[3], 2.001, 2.001, 2.001, 0, 1, 0) then
  					DrawSpecialText("Press [~g~ENTER~s~] to clean your vehicle!")
  					if(IsControlJustPressed(1, Key)) then
  						SetVehicleDirtLevel(GetVehiclePedIsUsing(PlayerPedId()),0)
  						SetVehicleUndriveable(GetVehiclePedIsUsing(PlayerPedId()), false)
  						msg = "Vehicle ~y~Clean~s~!"
  						DrawSpecialText(msg, 5000)
  						Wait(5000)
  					end
          end
				end
			end
		end
	end
end)
