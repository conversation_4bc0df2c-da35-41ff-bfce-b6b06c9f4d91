local c_documents = {}

RegisterServerEvent('documents:createAndFill')
AddEventHandler('documents:createAndFill', function(player, name, merge_data, onSubmitCallback, onFailCallBack)
    createNewDocument(player, name, merge_data, onSubmitCallback, onFailCallBack)
end)

RegisterServerEvent('documents:createAndAutoSign')
AddEventHandler('documents:createAndAutoSign', function(player, name, merge_data, onSubmitCallback, onFailCallBack)
    createNewDocument(player, name, merge_data, onSubmitCallback, onFailCallBack, nil, true)
end)

RegisterServerEvent('documents:createAndFillFromAsOffer')
AddEventHandler('documents:createAndFillFromAsOffer', function(from_player, signer_player, name, merge_data, onSubmitCallback, onFailCallBack)
    createNewDocument(signer_player, name, merge_data, onSubmitCallback, onFailCallBack, from_player, false)
end)

function createNewDocument(signer_player, name, merge_data, onSubmitCallback, onFailCallBack, from_player, auto_sign)
    local character = exports.blrp_core:character(signer_player)
    local randomId = math.random(10000000, 900000000)
    if onSubmitCallback then -- Save callback so we know when it is accepted
        c_documents[randomId] = {
            ['from_player'] = from_player,
            ['success'] = onSubmitCallback,
            ['fail'] = onFailCallBack,
            ['business_id'] = merge_data['business_id']
        }
    end
    local base_data = {
        id = randomId,
        firstname = character.get('firstname'),
        lastname = character.get('lastname'),
        phone = character.get('phone'),
        dob = character.get('dateofbirth'),
        job_name = character.getJobName(),
        rank_name = character.getRankName(),
    }
    if auto_sign then
        TriggerClientEvent('documents:parseFormWithoutShowing', signer_player, name, base_data, merge_data)
    else
        TriggerClientEvent('documents:startNewForm', signer_player, name, base_data, merge_data)
    end
end

RegisterServerEvent('documents:submitDocument')
AddEventHandler('documents:submitDocument', function(data, player)
    if not player then player = source end
    local char_id = exports.blrp_core:character(source).get('id')
    MySQL.Async.insert("INSERT INTO evidence_documents (id, owner, data) VALUES (@id, @owner, @data)",
    {
        ['@id'] = data.id,
        ['@owner'] = char_id,
        ['@data'] = json.encode(data)
    },
    function(id)
        if c_documents[id] and c_documents[id]['success'] then
            data.requested_by_id = nil
            data.signed_by_id = char_id
            data.faction_type = 'none'
            data.business_id = c_documents[id].business_id

            if c_documents[id].from_player then
                data.requested_by_id = exports.blrp_core:character(c_documents[id].from_player).get('id')
            end
            if string.match(data.headerTitle, 'PD-') then
                data.faction_type = 'police'
            end

            if string.match(data.headerTitle, 'FD-') then
                data.faction_type = 'medical'
            end

            if string.match(data.headerTitle, 'DOJ-') then
                data.faction_type = 'justice'
            end

            saveToTablet(id, data)
            c_documents[id]['success'](id, data) -- do callback
            c_documents[id]= nil
        end
    end)
end)

RegisterServerEvent('documents:cancelSubmit')
AddEventHandler('documents:cancelSubmit', function(id)
    if c_documents[id] then
        c_documents[id]['fail'](id) -- do callback
        c_documents[id]= nil -- do callback
    end
end)

function saveToTablet(id, report)
    local lastName = ''
    local firstName = ''
    if report.headerLastName then lastName = report.headerLastName end
    if report.headerFirstName then firstName = report.headerFirstName end
    local doc_packet = {
        name = report.headerSubtitle,
        type = report.headerTitle,
        call_number = report.phone,
        requested_by = 'N/A',
        submitted_by = firstName .. ' ' .. lastName,
        game_document_id = id,
        requested_by_id = report.requested_by_id,
        signed_by_id = report.signed_by_id,
        faction_type = report.faction_type,
        business_id = report.business_id,
    }
    print('Saving document to tablet', json.encode(doc_packet))
    TriggerEvent('blrp_tablet:insertDocument', doc_packet)
end

RegisterServerEvent('documents:displayDocument')
AddEventHandler('documents:displayDocument', function(id, target)
    local player = source
    if target then player = target end
    if id ~= nil then
        MySQL.Async.fetchAll("SELECT * FROM evidence_documents WHERE id = @id",
                {['@id'] = id}, function(result)
           if result[1] ~= nil then
               local data = json.decode(result[1].data)
               TriggerClientEvent('documents:viewDocument', player, data)
            end
        end)
    end
end)

RegisterServerEvent('documents:giveDocumentToPlayer')
AddEventHandler('documents:giveDocumentToPlayer', function(player, id, copies)
    local char = exports.blrp_core:character(player)
    if id ~= nil then
        MySQL.Async.fetchAll("SELECT * FROM evidence_documents WHERE id = @id",
                {['@id'] = id}, function(result)
                    if result[1] ~= nil then
                        local data = json.decode(result[1].data)

                        local idname = 'm_document:meta:' .. id

                        local lastName = ''
                        local firstName = ''
                        if data.headerLastName then lastName = data.headerLastName end
                        if data.headerFirstName then firstName = data.headerFirstName end

                        char.give(idname, 1, {
                            document_id = id,
                            document_title = data.headerSubtitle,
                            signed_by = lastName .. ' ' .. lastName
                        })
                        -- TriggerClientEvent('documents:viewDocument', player, data)
                    end
                end)
    end
end)


-- Template creators

RegisterServerEvent('core:server:makeDocumentFromTemplate')
AddEventHandler('core:server:makeDocumentFromTemplate', function(player, template_name, merge_data)
    if not merge_data then merge_data = {} end
    TriggerEvent('documents:createAndFill', player, template_name, merge_data, function(id, report)
        exports.blrp_core:character(player).notify('Document submitted, can now print from tablet')
    end, function(id)
        exports.blrp_core:character(player).notify('Document cancelled')
    end)
end)

-- AddEventHandler('documents:createAndFillFromAsOffer', function(from_player, signer_player, name, merge_data, onSubmitCallback, onFailCallBack)
RegisterServerEvent('core:server:offerSignaturePlayer')
AddEventHandler('core:server:offerSignaturePlayer', function(signer_player, from_player, merge_data)
    if not merge_data then merge_data = {} end
    TriggerEvent('documents:createAndFillFromAsOffer', from_player, signer_player, 'citizen-contract', merge_data, function(id, report)
        exports.blrp_core:character(from_player).notify('Contract signed by citizen. You can view it under my documents')
        exports.blrp_core:character(signer_player).notify('Contract successfully signed. You can view it under my documents')
    end, function(id)
        exports.blrp_core:character(from_player).notify('Document cancelled')
        exports.blrp_core:character(signer_player).notify('Document cancelled')
    end)
end)

RegisterServerEvent('core:server:offerPlayer')
AddEventHandler('core:server:offerPlayer', function(signer_player, from_player, merge_data)
    if not merge_data then merge_data = {} end
    TriggerEvent('documents:createAndFillFromAsOffer', from_player, signer_player, 'citizen-contract', merge_data, function(id, report)
        core.character(from_player).notify('Contract signed by citizen. You can view it under my documents')
        core.character(signer_player).notify('Contract successfully signed. You can view it under my documents')
    end, function(id)
        core.character(from_player).notify('Document cancelled')
        core.character(signer_player).notify('Document cancelled')
    end)
end)

RegisterServerEvent('core:server:printPdReport')
AddEventHandler('core:server:printPdReport', function(data)
    local player = source
    local print_fields = {
        ['TYPE'] = data.type,
        ['RECORD ID'] = 'LS #' .. data.id,
        ['SUSPECT NAME'] = data.firstname .. ' ' .. data.lastname,
        ['SUBMITTED BY'] = data.inserted_by,
        ['REGISTRATION'] = data.registration,
        ['SUBMITTED'] = data.created_at,
        ['CHARGES'] = data.charges,
        ['DETAILS'] = data.details,
    }
    exports.blrp_core:character(player).notify('Document Processing')
    TriggerEvent('documents:createAndAutoSign', player, 'police-print-report', print_fields, function(document_id, report)
        TriggerClientEvent('core:client:printDocument', player, document_id)
    end, function()
        exports.blrp_core:character(player).notify('An error occurred')
    end)

end)
