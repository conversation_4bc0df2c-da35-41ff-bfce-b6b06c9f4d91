

-- entrance bottom
table.insert(Config.<PERSON>List, {
	objHash = -955445187,
	objHeading = 215.0,
	objCoords = vector3(1250.216, -1583.801, 54.73965),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['marabunta']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entrance top
table.insert(Config.DoorList, {
	objHash = -658590816,
	objHeading = 215.0,
	objCoords = vector3(1251.972, -1569.283, 58.93438),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['marabunta']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- stairs
table.insert(Config.DoorList, {
	objHash = -296755518,
	objHeading = 35.000022888184,
	objCoords = vector3(1257.955, -1574.589, 58.53965),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['marabunta']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- toilet
table.insert(Config.DoorList, {
	objHash = -296755518,
	objHeading = 35.000038146973,
	objCoords = vector3(1253.072, -1573.795, 58.53965),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['marabunta']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- meeting room
table.insert(Config.DoorList, {
	objHash = -296755518,
	objHeading = 35.000022888184,
	objCoords = vector3(1250.431, -1575.644, 58.53965),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['marabunta']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- boss office
table.insert(Config.DoorList, {
	objHash = -296755518,
	objHeading = 124.99999237061,
	objCoords = vector3(1249.264, -1577.429, 58.53965),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['marabunta']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})