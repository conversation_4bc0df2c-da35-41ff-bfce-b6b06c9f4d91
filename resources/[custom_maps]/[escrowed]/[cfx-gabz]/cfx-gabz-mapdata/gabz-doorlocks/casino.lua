

-- casino - entrance left
table.insert(Config.DoorList, {
	garage = false,
	lockpick = false,
	locked = true,
	objHash = 21324050,
	audioRemote = false,
	maxDistance = 2.0,
	slides = false,
	objHeading = 328.15661621094,
	objCoords = vector3(958.1413, 33.97816, 72.40491),
	fixText = false,
	authorizedJobs = { ['casino']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- casino - entrance right
table.insert(Config.DoorList, {
	garage = false,
	lockpick = false,
	locked = true,
	objHash = 21324050,
	audioRemote = false,
	maxDistance = 2.0,
	slides = false,
	objHeading = 148.15661621094,
	objCoords = vector3(960.2742, 32.65347, 72.40491),
	fixText = false,
	authorizedJobs = { ['casino']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- casino - management right
table.insert(Config.DoorList, {
	garage = false,
	lockpick = false,
	locked = true,
	objHash = 680601509,
	audioRemote = false,
	maxDistance = 2.0,
	slides = false,
	objHeading = 238.15664672852,
	objCoords = vector3(1018.692, 67.17648, 70.01009),
	fixText = false,
	authorizedJobs = { ['casino']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- casino - management left
table.insert(Config.DoorList, {
	garage = false,
	lockpick = false,
	locked = true,
	objHash = 680601509,
	audioRemote = false,
	maxDistance = 2.0,
	slides = false,
	objHeading = 58.156597137451,
	objCoords = vector3(1017.637, 65.47773, 70.01009),
	fixText = false,
	authorizedJobs = { ['casino']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- casino - management office right
table.insert(Config.DoorList, {
	garage = false,
	lockpick = false,
	locked = true,
	objHash = -643593781,
	audioRemote = false,
	maxDistance = 2.0,
	slides = false,
	objHeading = 328.15661621094,
	objCoords = vector3(1000.608, 61.37115, 75.21008),
	fixText = false,
	authorizedJobs = { ['casino']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- casino - management office left
table.insert(Config.DoorList, {
	garage = false,
	lockpick = false,
	locked = true,
	objHash = -643593781,
	audioRemote = false,
	maxDistance = 2.0,
	slides = false,
	objHeading = 148.15661621094,
	objCoords = vector3(1002.307, 60.3161, 75.21008),
	fixText = false,
	authorizedJobs = { ['casino']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})