

-- front entrance
table.insert(Config.<PERSON>List, {
	objHash = 903896222,
	objHeading = 229.99401855469,
	objCoords = vector3(493.0755, -1541.83, 29.44705),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['aztecas']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- back entrance
table.insert(Config.DoorList, {
	objHash = 2103001488,
	objHeading = 229.99406433105,
	objCoords = vector3(486.0135, -1530.393, 29.44705),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['aztecas']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- boss office
table.insert(Config.DoorList, {
	objHash = -1168990172,
	objHeading = 319.99404907227,
	objCoords = vector3(488.5832, -1534.103, 29.44645),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['aztecas']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- bar to hallway
table.insert(Config.DoorList, {
	objHash = -1168990172,
	objHeading = 49.994029998779,
	objCoords = vector3(491.2002, -1533.059, 29.44645),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['aztecas']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- meeting room
table.insert(Config.DoorList, {
	objHash = -1168990172,
	objHeading = 319.99404907227,
	objCoords = vector3(490.9441, -1531.29, 29.44673),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['aztecas']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- toilet
table.insert(Config.DoorList, {
	objHash = -1168990172,
	objHeading = 139.99398803711,
	objCoords = vector3(496.6208, -1530.598, 29.44705),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['aztecas']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})