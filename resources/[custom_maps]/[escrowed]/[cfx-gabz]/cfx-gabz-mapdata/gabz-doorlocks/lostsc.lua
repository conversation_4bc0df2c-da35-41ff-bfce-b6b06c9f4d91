

-- entrance door
table.insert(Config.DoorList, {
	objHash = 190770132,
	objHeading = 270.0,
	objCoords = vector3(99.6309, 3615.914, 40.63958),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['lost']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- wardrobe
table.insert(Config.DoorList, {
	objHash = 747286790,
	objHeading = 359.99996948242,
	objCoords = vector3(102.5613, 3607.151, 40.64393),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['lost']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- toilet
table.insert(Config.DoorList, {
	objHash = 747286790,
	objHeading = 90.0,
	objCoords = vector3(104.7033, 3610.458, 40.64099),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['lost']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})