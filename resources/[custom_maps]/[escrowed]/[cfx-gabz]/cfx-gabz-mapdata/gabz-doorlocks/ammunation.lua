

-- Ammu big 01 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 160.00003051758,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(16.12787, -1114.605, 29.94694),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Ammu big 01 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 340.00003051758,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(18.57201, -1115.495, 29.94694),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Ammu big 01 - store to shooting 01
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 340.00003051758,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 44561767,
	fixText = false,
	locked = true,
	objCoords = vector3(7.14657, -1101.126, 29.89739),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Ammu big 01 - store to shooting 02
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 340.00003051758,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 44561767,
	fixText = false,
	locked = true,
	objCoords = vector3(8.044436, -1098.659, 29.89739),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Ammu big 01 - shooting to range
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 340.00003051758,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 44561767,
	fixText = false,
	locked = true,
	objCoords = vector3(8.490857, -1094.39, 29.91464),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Ammu big 02 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 5.0089556680177e-06,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(813.1779, -2148.27, 29.76892),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Ammu big 02 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 179.99998474121,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(810.5768, -2148.27, 29.76892),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Ammu big 02 - store to shooting 01
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 179.99998474121,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 44561767,
	fixText = false,
	locked = true,
	objCoords = vector3(826.2278, -2157.865, 29.71938),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Ammu big 02 - store to shooting 02
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 179.99998474121,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 44561767,
	fixText = false,
	locked = true,
	objCoords = vector3(826.2278, -2160.49, 29.71938),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Ammu big 02 - shooting to range
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 179.99998474121,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 44561767,
	fixText = false,
	locked = true,
	objCoords = vector3(827.2682, -2164.654, 29.73663),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})




-- ammu-small 01 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 69.999885559082,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(244.7306, -44.08021, 70.09095),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 01 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 249.99990844727,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(243.8412, -46.52396, 70.09095),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 02 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 41.827117919922,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(-1112.073, 2691.508, 18.70404),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 02 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 221.82711791992,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(-1114.011, 2689.773, 18.70404),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 03 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 246.58126831055,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(-3164.848, 1081.393, 20.98864),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 03 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 66.581260681152,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(-3163.815, 1083.779, 20.98864),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 04 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 0.0,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(-662.6419, -944.3221, 21.97913),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 04 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 179.99998474121,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(-665.2425, -944.3221, 21.97913),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 05 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 360.0,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(845.3695, -1024.543, 28.34475),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 05 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 179.99998474121,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(842.7689, -1024.543, 28.34475),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 06 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 179.99998474121,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(2568.304, 303.3523, 108.8848),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 06 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 360.0,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(2570.905, 303.3523, 108.8848),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 07 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 75.778289794922,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(-1313.823, -389.1266, 36.84571),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 07 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 255.77828979492,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(-1314.462, -391.6476, 36.8457),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 08 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 47.391891479492,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(1699.934, 3753.422, 34.85524),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 08 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 227.39189147949,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(1698.174, 3751.508, 34.85524),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 09 - entrance left
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 224.99993896484,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = 97297972,
	fixText = false,
	locked = true,
	objCoords = vector3(-326.1146, 6075.272, 31.60468),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ammu-small 09 - entrance right
table.insert(Config.DoorList, {
	authorizedJobs = { ['Ammunation']=0 },
	maxDistance = 2.0,
	objHeading = 44.999935150146,
	garage = false,
	lockpick = false,
	audioRemote = false,
	objHash = -8873588,
	fixText = false,
	locked = true,
	objCoords = vector3(-324.2757, 6077.111, 31.60468),
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})