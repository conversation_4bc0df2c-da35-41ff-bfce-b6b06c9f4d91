-- ## GABZ - <PERSON>R<PERSON> RANGER
-- ## COORDINATES: 388.640 787.820 187.474

-- entry front
table.insert(Config.DoorList, {
	authorizedJobs = { ['police']=0 },
	objCoords = vector3(387.7515, 792.8711, 187.8491),
	objHeading = 0.0,
	objHash = -117185009, -- sanhje_parkranger_door
	maxDistance = 2.0,
	audioRemote = false,
	lockpick = false,
	locked = true,
	garage = false,
	fixText = false,
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entry side
table.insert(Config.DoorList, {
	authorizedJobs = { ['police']=0 },
	objCoords = vector3(388.6313, 799.6823, 187.8263),
	objHeading = 89.999977111816,
	objHash = -117185009, -- sanhje_parkranger_door
	maxDistance = 2.0,
	audioRemote = false,
	lockpick = false,
	locked = true,
	garage = false,
	fixText = false,
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- cells main entry
table.insert(Config.DoorList, {
	authorizedJobs = { ['police']=0 },
	objCoords = vector3(383.4079, 798.2911, 187.6118),
	objHeading = 270.0,
	objHash = 517369125, -- sanhje_parkranger_celldoor
	maxDistance = 2.0,
	audioRemote = false,
	lockpick = false,
	locked = true,
	garage = false,
	fixText = false,
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- cell 1
table.insert(Config.DoorList, {
	authorizedJobs = { ['police']=0 },
	objCoords = vector3(382.9616, 796.8287, 187.6117),
	objHeading = 1.0017911336035e-05,
	objHash = 517369125, -- sanhje_parkranger_celldoor
	maxDistance = 2.0,
	audioRemote = false,
	lockpick = false,
	locked = true,
	garage = false,
	fixText = false,
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- cell 2
table.insert(Config.DoorList, {
	authorizedJobs = { ['police']=0 },
	objCoords = vector3(378.7583, 796.8364, 187.6123),
	objHeading = 1.0017911336035e-05,
	objHash = 517369125, -- sanhje_parkranger_celldoor
	maxDistance = 2.0,
	audioRemote = false,
	lockpick = false,
	locked = true,
	garage = false,
	fixText = false,
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entry upstair
table.insert(Config.DoorList, {
	authorizedJobs = { ['police']=0 },
	objCoords = vector3(380.2174, 792.7883, 190.6414),
	objHeading = 0.0,
	objHash = -117185009, -- sanhje_parkranger_door
	maxDistance = 2.0,
	audioRemote = false,
	lockpick = false,
	locked = true,
	garage = false,
	fixText = false,
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office
table.insert(Config.DoorList, {
	authorizedJobs = { ['police']=0 },
	objCoords = vector3(384.3812, 796.0928, 190.6396),
	objHeading = 270.00003051758,
	objHash = 1704212348, -- sanhje_parkranger_door_wood
	maxDistance = 2.0,
	audioRemote = false,
	lockpick = false,
	locked = true,
	garage = false,
	fixText = false,
	slides = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})