RegisterNetEvent("PlasmaKart:SpawnedKartFunction", function(kart, kartPlate)
	Citizen.CreateThread(function()
		Wait(1000)
		SetVehicleFuelLevel(kart, 1000 + 0.0)
	end)

	SetVehicleEngineOn(kart, true, true, false)
end)

--[[
RegisterNetEvent("PlasmaKart:ExtCpPassed", function(currentShop, curCP, curLap, kart)
	-- This is called each time the kart pass a checkpoint
	-- you can add here all that you need (client side)
	-- repair function
	-- refuel
	-- etc...
	--
	-- currentShop is the place where the race as been launch (if you have only one lobby this will be the same each time)
	-- curCP  this is the number of the currentCP passed
	-- curLap this is the number of the current lap
	-- kart this is the kart entity
end)
]]
