<template>
  <transition enter-active-class="transition ease-out duration-100" enter-from-class="transform opacity-0 scale-95"
              enter-to-class="transform opacity-100 scale-100" leave-active-class="transition ease-in duration-75"
              leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">

  <div v-show="visible" class="mb-40" style="padding-bottom: 40px !important;">
    <div class="m-auto px-40 py-20 pb-40 max-w-screen-2xl">

      <div class="static">
        <div class="mt-8">
          <!-- Top Bar -->

          <div class="mb-5 bg-zinc-800 rounded-lg flex justify-between">
            <span class="flex align-end justify-start items-center ml-4">
              <span class="w-1/5 text-2xl mr-2 flex-initial w-auto bottom-5">
                <span class="font-bold mb-0.5">{{ garage.name }}</span>
              </span>
            </span>

            <!-- Filter Bar -->
            <div class="flex flex-wrap items-center gap-x-4 gap-y-2 bg-zinc-800 rounded-lg px-4 py-2">

              <!-- Hide Owned -->
              <label class="flex items-center text-white text-sm space-x-2">
                <input v-model="notOwned" type="checkbox"
                       class="w-4 h-4 rounded border-gray-300 bg-gray-100 dark:bg-gray-700 dark:border-gray-600">
                <span>Hide Owned Vehicles</span>
              </label>

              <!-- Only My Vehicles -->
              <label class="flex items-center text-white text-sm space-x-2">
                <input v-model="ownedOnly" type="checkbox"
                       class="w-4 h-4 rounded border-gray-300 bg-gray-100 dark:bg-gray-700 dark:border-gray-600">
                <span>Only my Vehicles</span>
              </label>

              <!-- Only Business Owned -->
              <label class="flex items-center text-white text-sm space-x-2">
                <input v-model="dealerOnly" type="checkbox"
                       class="w-4 h-4 rounded border-gray-300 bg-gray-100 dark:bg-gray-700 dark:border-gray-600">
                <span>Only Business Owned Vehicles</span>
              </label>

              <!-- Shared Filter -->
              <div class="flex items-center space-x-2 ml-auto">
                <span class="text-sm text-white">Shared Filter</span>
                <button @click="cycleSharedFilter"
                        class="px-3 py-1 text-sm rounded border transition-all"
                        :class="{
              'bg-blue-600 text-white border-blue-600': sharedGarageFilter === 'only',
              'bg-red-600 text-white border-red-600': sharedGarageFilter === 'hide',
              'bg-zinc-700 text-white border-gray-600': sharedGarageFilter === 'all'
            }">
                  {{ sharedGarageLabel }}
                </button>
              </div>

            </div>


          </div>

          <div class="flex justify-between flex-wrap block mb-5">

            <!-- Search -->
            <div class="w-3/5 max-w-md flex1">
              <label for="default-search"
                     class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <svg aria-hidden="true" class="w-5 h-4 text-gray-500 dark:text-gray-400" fill="none"
                       stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
                <input v-model="searchTerm" type="search" id="default-search"
                       class="block w-full p-4 pl-9 text-md text-white rounded-lg bg-gray-50 focus-within:none focus:outline-none focus-visible:none bg-zinc-800 hover:bg-zinc-900"
                       placeholder="Search by Name, Plate, Category, or Garage Name" required>
              </div>
            </div>

            <div @click="storeVehicle"
                 class="px-4 py-3 text-lg flex items-center justify-center cursor-pointer rounded-lg bg-blue-500/100 hover:bg-blue-700 transition ease-in-out text-white">
              <p>
                Store Vehicle in Garage
              </p>
            </div>

          </div>

        </div>

        <!-- Category Filters -->
        <div class="mb-6 flex end flex-wrap">
          <span
              @click="clearCategoryFilters"
              style="cursor: pointer"
              class="bg-zinc-800  hover:bg-zinc-900 transition ease-in-out text-white h-auto text-md font-medium mr-2 mb-2 px-2.5 py-2 rounded">
            All
          </span>

          <span v-for="(enabled, categoryName) of categories" :key="categoryName"
                @click="selectCategoryFilter(categoryName)"
                style="cursor: pointer"
                :class="{ 'bg-blue-500 hover:bg-blue-600 transition ease-in-out text-white font-bold':enabled,'text-white':!enabled }"
                class="bg-zinc-800 hover:bg-zinc-900 transition ease-in-out text-white h-auto text-md font-medium mr-2 mb-2 px-2.5 py-2 rounded">
            {{ _capitalizeFirstLetter(categoryName) }}
          </span>
        </div>
      </div>

      <!-- Actual Grid -->
      <div class="overflow-scroll grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-5 gap-y-5 grid-flow-row-dense auto-rows-min min-h-full max-h-full">
        <div v-for="vehile of filteredVehicles" :key="vehile.plate">
          <app-car :vehicle="vehile" :garage="garage" />
        </div>

        <!-- Add padding on bottom -->
        <div style="max-height: 800px; height: 800px">

        </div>
      </div>
    </div>
  </div>
</transition>
</template>

<script>
import AppButton from "./parts/app-button.vue";
import AppCar from "./parts/app-car.vue"
import AppInput from "./parts/app-input.vue"

const _nui = async (event, params) => {
  fetch(`https://blrp_vehicles/${ event }`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json; charset=UTF-8' },
    body: JSON.stringify(params),
  }).then(resp => resp.json()).then(resp => {
    return resp;
  });
}

window._nui = _nui

export default {
  components: { AppInput, AppCar,  AppButton },
  data() {
    return {
      visible: false,
      garage: null,
      vehicles: [],
      searchTerm: null,
      notOwned: false,
      ownedOnly: false,
      dealerOnly: false,
      sharedGarageFilter: 'all',
      categories: { }
    };
  },
  async created() {
    window.addEventListener('message', event => {
      if ( !event.data ) return false;

      if ( event.data.type === 'garage_ui:show' ) {
        this.show(event.data.vehicles, event.data.garage);
      }

      if ( event.data.type === 'garage_ui:hide' ) {
        this.hide();
      }
    });

    window.addEventListener('keydown', evt => {
      if ( !this.visible ) return false;
      evt = evt || window.event;
      if ( evt.keyCode === 27 /* escape */ || evt.keyCode === 192 /* backtick */ ) {
        this.hide();
        window._nui('hide')
      }
    });
  },
  methods: {
    cycleSharedFilter() {
      const next = {
        all: 'only',
        only: 'hide',
        hide: 'all'
      };
      this.sharedGarageFilter = next[this.sharedGarageFilter];
    },
    async show(vehicles, garage) {
      this.visible = true;
      document.body.classList.add('overflow-hidden'); // ❗ Prevent page scroll
      this.searchTerm = null;

      this.vehicles = vehicles;
      this.garage = garage;

      this.categories = {};
      this.ownedOnly = true;

      let total_vehciles_owned = 0;
      let first_category = false;

      for (const vehicle of this.vehicles) {
        if (vehicle.plate) total_vehciles_owned++;

        if (!this.categories[vehicle.category]) {
          this.categories[vehicle.category] = false;
          if (!first_category) first_category = vehicle.category;
        }
      }

      this.$nextTick(() => {
        if (total_vehciles_owned < 1) {
          this.selectCategoryFilter(first_category);
        }
      });
    },

    async hide() {
      this.visible = false;
      document.body.classList.remove('overflow-hidden'); // ❗ Restore page scroll
    },
    async doSearch() {

    },
    storeVehicle() {
      window._nui('storeVehicleInGarage')
    },

    _capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },

    selectCategoryFilter(categoryName) {
      if ( this.categories[categoryName] ) {
        this.categories[categoryName] = false
        return
      }

      for (const key in this.categories) {
        this.categories[key] = false
      }

      this.categories[categoryName] = true
    },

    clearCategoryFilters() {
      for (const key in this.categories) {
        this.categories[key] = false
      }
    },
  },
  watch: {},
  computed: {
    sharedGarageLabel() {
      switch (this.sharedGarageFilter) {
        case 'only': return 'Only Shared';
        case 'hide': return 'Hide Shared';
        default: return 'Show All';
      }
    },
    filteredVehicles() {
      if ( this.searchTerm && this.searchTerm.length >= 2 ) {
        this.ownedOnly = false
        this.clearCategoryFilters()
      }

      const category_options = Object.entries(this.categories)
          .map(([ key, value ]) => ({ key, value }))

      let allowed_categories = category_options.filter(cat => cat.value).map(cat => cat.key)

      if ( allowed_categories.length < 1 ) {
        allowed_categories = category_options.map(cat => cat.key)
      }

      let category_results = this.vehicles.filter(veh => allowed_categories.includes(veh.category))

      // Don't include unowned vehicles at property garages
      if ( this.garage.id.includes('z') ) {
        category_results = category_results.filter(veh => veh.plate);
      }

      // Apply sharedGarageFilter
      if (this.sharedGarageFilter === 'only') {
        category_results = category_results.filter(veh => veh.firstname && veh.lastname);
      } else if (this.sharedGarageFilter === 'hide') {
        category_results = category_results.filter(veh => !veh.firstname && !veh.lastname);
      }


      if ( this.notOwned ) {
        category_results = category_results.filter(veh => !veh.plate);
      }

      if ( this.ownedOnly ) {
        category_results = category_results.filter(veh => veh.plate);
      }

      if (this.dealerOnly) {
        category_results = category_results.filter(veh => veh.business);
      } else {
        category_results = category_results.filter(veh => !veh.business);
      }

      if ( this.searchTerm && this.searchTerm.length > 2 ) {
        const searchTerm = this.searchTerm.toLowerCase()
        category_results = category_results.filter(veh =>
            veh.plate?.toLowerCase().includes(searchTerm)
            || veh.label.toLowerCase().includes(searchTerm)
            || veh.garage_name?.toLowerCase().includes(searchTerm)
        )
      }

      category_results.sort((a, b) => {
        if ( a.price === null && b.price === null ) {
          return 0;
        } else if ( a.price === null ) {
          return 1;
        } else if ( b.price === null ) {
          return -1;
        } else {
          return a.price - b.price;
        }
      });

      category_results.sort((a, b) => {
        if ( a.plate === null && b.plate === null ) {
          return 0;
        } else if ( a.plate === null ) {
          return 1;
        } else if ( b.plate === null ) {
          return -1;
        } else {
          return a.plate?.localeCompare(b.plate);
        }
      });

      const currentGarage = this.garage.id;

      category_results.sort((b, a) => {
        const aFav = a.is_favourite === false && (!a.garage || a.garage === currentGarage);
        const bFav = b.is_favourite === false && (!b.garage || b.garage === currentGarage);
        // Prioritize favourites
        if (aFav && !bFav) return -1;
        if (!aFav && bFav) return 1;

        // Then fallback to garage_name sorting
        if (a.garage_name === null && b.garage_name === null) {
          return 0;
        } else if (a.garage_name === null) {
          return 1;
        } else if (b.garage_name === null) {
          return -1;
        } else {
          return a.garage_name?.localeCompare(b.garage_name);
        }
      });

      return category_results
    },
  },
}
</script>
