tVehicleTracker = T.getInstance('blrp_vehicles', 'tracker')

local tracked_vehicles = {}
local last_tracked_positions = {}

exports('GetTrackedVehiclePositions', function()
  return last_tracked_positions
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)

    for network_id, tracking_data in pairs(tracked_vehicles) do
      if (os.time() - tracking_data.last_update) > tracking_data.interval then
        local vehicle = NetworkGetEntityFromNetworkId(network_id)

        local state = Entity(vehicle).state

        if
          exports.blrp_vehicles:HasAnyVehicleFlags(state.flags, 'FLAG_TAGGED_FOR_TOW') or
          not DoesEntityExist(vehicle) or
          GetEntityType(vehicle) ~= 2 or
          GetVehicleNumberPlateText(vehicle) ~= tracking_data.plate
        then
          tVehicleTracker.deleteTrackedVehicle(-1, { network_id })
          tracked_vehicles[network_id] = nil
          last_tracked_positions[network_id] = nil
        else
          local vehicle_coords = GetEntityCoords(vehicle)
          local vehicle_plate = GetVehicleNumberPlateText(vehicle)

          tVehicleTracker.updateTrackedVehicle(-1, { network_id, vehicle_plate, vehicle_coords, tracking_data.targets, tracking_data.color })
          last_tracked_positions[network_id] = {
            plate = vehicle_plate,
            coords = { x = vehicle_coords.x, y = vehicle_coords.y },
          }
        end

        tracking_data.last_update = os.time()
      end
    end
  end
end)

exports('StartVehicleTracker', function(network_id, interval, targets, color)
  if not targets then
    targets = 'group:LEO'
  end

  if not interval then
    interval = 30
  end

  if not color then
    color = 49
  end

  local vehicle = NetworkGetEntityFromNetworkId(network_id)

  if not vehicle or vehicle <= 0 then
    return
  end

  local state = Entity(vehicle).state

  local flags = getVehicleFlags(state.flags)

  table.insert(flags, 'FLAG_TRACKER_ENABLED')

  vehicles[state.server_uid].flags = makeVehicleFlags(table.unpack(flags))
  state.flags = vehicles[state.server_uid].flags

  tracked_vehicles[network_id] = {
    last_update = 0,
    targets = targets,
    interval = interval,
    plate = GetVehicleNumberPlateText(vehicle),
    color = color,
  }
end)

exports('StopVehicleTracker', function(network_id, flag)
  if not tracked_vehicles[network_id] then
    return
  end

  local vehicle = NetworkGetEntityFromNetworkId(network_id)

  if vehicle and vehicle > 0 then
    local state = Entity(vehicle).state

    local flags_new = state.flags

    if flag then
      -- Add FLAG_TRACKER_DISABLED
      flags_new = flags_new | vehicle_flags.FLAG_TRACKER_DISABLED
    end

    -- Remove FLAG_TRACKER_ENABLED
    flags_new = flags_new & ~vehicle_flags.FLAG_TRACKER_ENABLED

    vehicles[state.server_uid].flags = flags_new
    state.flags = vehicles[state.server_uid].flags
  end

  tracked_vehicles[network_id] = nil
  last_tracked_positions[network_id] = nil
  tVehicleTracker.deleteTrackedVehicle(-1, { network_id })
end)

local pending_requests = {}
local tracker_map = {}

exports('AttemptTrackVehicle', function(plate, player)
  local character = exports.blrp_core:character(player)

  plate = string.upper(plate)

  local requested_by_name = character.get('firstname') .. ' ' .. character.get('lastname')

  if pending_requests[plate] then
    character.notifyError('There is a pending request to track this vehicle')
    return
  end

  local vehicle_data = getVehicleByPlate(plate)

  if vehicle_data and tracked_vehicles[vehicle_data.network_id] then
    character.notifyError('This vehicle is already being tracked')
    return
  end

  character.notify('Tracking request sent. Please wait')

  pending_requests[plate] = true

  Citizen.Wait(math.random(25, 35) * 1000)

  if not vehicle_data then
    character.notifyError('No response from vehicle tracker')
    pending_requests[plate] = false
    return
  end

  local vehicle = NetworkGetEntityFromNetworkId(vehicle_data.network_id)

  if not vehicle or vehicle < 0 or not DoesEntityExist(vehicle) then
    character.notifyError('No response from vehicle tracker')
    pending_requests[plate] = false
    return
  end

  if GetHashKey(vehicle_data.model) ~= GetEntityModel(vehicle) then
    character.notifyError('No response from vehicle tracker')
    pending_requests[plate] = false
    return
  end

  local state = Entity(vehicle).state

  if not vehicle_data.class or vehicle_data.class ~= 18 then
    character.notifyError('No response from vehicle tracker')
    pending_requests[plate] = false
    return
  end

  if hasAnyVehicleFlags(state.flags, 'FLAG_TRACKER_DISABLED') then
    character.notifyError('No response from vehicle tracker')
    pending_requests[plate] = false
    return
  end

  pending_requests[plate] = false
  tracker_map[plate] = vehicle_data.network_id

  character.log('VEHICLE-TRACKER', 'Started track on vehicle / plate = ' .. plate)

  exports.blrp_vehicles:StartVehicleTracker(vehicle_data.network_id, 15, 'group:LEO')

  exports.blrp_core:group('Police').alert({
    coords = GetEntityCoords(vehicle),
    location_override = '',
    badge = 'VEHICLE TRACKER',
    badge_style = 'warning',
    msg = 'Vehicle tracker activated<br/>License plate: ' .. plate .. '<br/>Requested by: ' .. requested_by_name,
    icon = 'fa-regular fa-location-crosshairs',
    allow_phone = false,
    allow_gps = true,
    can_accept = true,
    is_response = true,
    hide_location = true,
  })
end)

exports('AttemptUntrackVehicle', function(plate, player)
  local character = exports.blrp_core:character(player)

  plate = string.upper(plate)

  local network_id = tracker_map[plate]

  if not network_id then
    character.notifyError('Target plate not active')
    return
  end

  tracker_map[plate] = nil

  character.notify('Tracker disabled')

  exports.blrp_vehicles:StopVehicleTracker(network_id)
end)

exports('InstallPhysicalTracker', function(character, item_id, item_id_base, meta)
  if tSurvival.getIsInAnyVehicle(character.source) then
    character.notify('You must be outside the vehicle to install this')
    return
  end

  local network_id = tVehicles.findNearbyVehicleNetworkId(character.source)

  if not network_id then
    character.notify('No vehicle nearby')
    return
  end

  local vehicle = NetworkGetEntityFromNetworkId(network_id)

  if not vehicle or vehicle <= 0 then
    character.notify('No vehicle nearby')
    return
  end

  local state = Entity(vehicle).state

  if not hasAnyVehicleFlags(state.flags, 'FLAG_SERVER_MANAGED') then
    character.notify('You cannot put a tracker on this vehicle')
    return
  end

  if hasAnyVehicleFlags(state.flags, 'FLAG_TRACKER_ATTACHED') then
    character.notify('This vehicle already has a tracker installed')
    return
  end

  if not meta.frequency then
    character.notify('You must set a frequency on the tracker')
    return
  end

  local vehicle_data = getVehicles(function(v)
    return v.uid == state.server_uid
  end, true)

  if not vehicle_data then
    return
  end

  local model = exports.blrp_core:GetVehicleModelFromHash(GetEntityModel(vehicle)) or 'UNKNOWN'
  local plate = GetVehicleNumberPlateText(vehicle)

  if
    not character.request('Install tracker on ' .. model .. ' plate ' .. plate .. '?') or
    not character.progressPromise('Installing tracker', 6, {
      animation = {
        task = 'CODE_HUMAN_MEDIC_TEND_TO_DEAD'
      }
    }) or
    not character.take(item_id, 1)
  then
    return
  end

  local flags = getVehicleFlags(state.flags)

  table.insert(flags, 'FLAG_TRACKER_ATTACHED')

  vehicles[state.server_uid].flags = makeVehicleFlags(table.unpack(flags))
  vehicles[state.server_uid].tracker_frequency = meta.frequency
  state.flags = vehicles[state.server_uid].flags

  character.log('VEHICLE-TRACKER', 'Installed vehicle tracker', {
    vehicle_plate = vehicle_data.plate,
    vehicle_id = vehicle_data.uid,
    frequency = vehicle_data.tracker_frequency,
  })
end)

pVehicles.hasLocatorWithFrequency = function(frequency)
  return exports.blrp_core:character(source).hasGetItemMetaWithProperty('gps_locator', function(meta)
    return tostring(meta.frequency) == tostring(frequency)
  end)
end

RegisterNetEvent('blrp_vehicles:removeAttachedTracker', function(network_id, event_data)
  local character = exports.blrp_core:character(source)

  if
    not network_id or
    not event_data or
    not character.hasGroup('LEO')
  then
    return
  end

  local vehicle = NetworkGetEntityFromNetworkId(network_id)

  if not vehicle or vehicle <= 0 then
    return
  end

  local state = Entity(vehicle).state

  if not state.server_uid then
    return
  end

  local vehicle_data = getVehicles(function(v)
    return v.uid == state.server_uid
  end, true)

  if not vehicle_data then
    return
  end

  if
    not character.request('Remove tracker from vehicle?') or
    not character.progressPromise('Removing tracker', 6, {
      animation = {
        task = 'CODE_HUMAN_MEDIC_TEND_TO_DEAD'
      }
    })
  then
    return
  end

  character.give('gps_tracker', 1, {
    frequency = vehicle_data.tracker_frequency
  })

  -- End tracker if active
  if hasAnyVehicleFlags(vehicle_data.flags, 'FLAG_TRACKER_ENABLED') then
    exports.blrp_vehicles:StopVehicleTracker(network_id)
  end

  character.log('VEHICLE-TRACKER', 'Removed vehicle tracker', {
    vehicle_plate = vehicle_data.plate,
    vehicle_id = vehicle_data.uid,
    frequency = vehicle_data.tracker_frequency,
  })

  vehicles[state.server_uid].flags = vehicle_data.flags & ~vehicle_flags.FLAG_TRACKER_ATTACHED
  vehicles[state.server_uid].tracker_frequency = nil
  state.flags = vehicles[state.server_uid].flags
end)
