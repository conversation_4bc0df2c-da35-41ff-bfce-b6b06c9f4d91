local livery_variation = {
  [`nkgauntlet4`] = {
    [0] = 'b',
    [1] = 'a',
    [2] = 'none',
  },

  [`nkscout2020`] = {
    [0] = 'a',
    [1] = 'a',
    [2] = 'none',
    [3] = 'a',
  },

  [`nkscout`] = {
    [0] = 'a',
    [1] = 'a',
    [2] = 'none',
    [3] = 'a',
    [4] = 'a',
  },

  [`nkterminus`] = {
    [0] = 'a',
    [1] = 'none',
  },

  [`expoldorado`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- BCSO
    [1] = 0, -- Ranger
  },

  [`expoldom`] = {
  livery_type = 'modkit',
  color_layer = 'interior',
  components = {
    42, -- VMT_CHASSIS2
    44, -- VMT_CHASSIS4
    45, -- VMT_CHASSIS5
  },

  [-1] = 'none', -- Unmarked
  [0] = 0, -- INT
},

  [`nkcoquette`] = {
    [0] = 'a',
    [1] = 'a',
    [2] = 'none',
    [3] = 'a',
  },

  [`nkjugular`] = {
    [0] = 'a', -- LSPD
    [1] = 'none', -- Unmarked
  },

  [`nkstx`] = {
    [0] = 'a', -- LSPD
    [1] = 'a', -- BCSO
    [2] = 'none', -- Unmarked
  },

  [`expoltailgater`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SHAP
  },

  -----------------------------------
  ---- NEXT GENERATION CALLSIGNS ----
  -----------------------------------

  [`vvpi`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 'none', -- Unmarked
  },

  [`vvpi2`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 0, -- SAHP
    [3] = 0, -- SAHP Taxi
    [4] = 'none', -- Unmarked
  },

  [`polscoutp`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 'none', -- Unmarked
    [3] = 0, -- DOC -- Change back to 97 after pride
    [4] = 0, -- SAHP
  },

  [`polalamop2`] = {
    [0] = 0, -- BCSO
    [1] = 'none', -- Unmarked
    [2] = 'none', -- Unmarked
    [3] = 0, -- SAHP
    [4] = 'none', -- Downtown Security
    [5] = 0, -- BCSO
  },

  [`polfugitivep`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 'none', -- Unmarked
    [3] = 'none', -- Ranger -- Overlapping with livery
  },

  [`polsovereignp`] = {
    [0] = 134, -- LSPD
    [1] = 0, -- BCSO
  },

  [`poltorencep`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 'none', -- Unmarked
    [3] = 0, -- SAHP
  },

  [`polstalkerp`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 'none', -- Unmarked
    [3] = 0, -- Ranger
    [4] = 0, -- BCSO Ghost
  },

  [`polspeedop`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 'none', -- Unmarked
    [3] = 0, -- BCSO CSU
    [4] = 0, -- LSPD CSU
    [5] = 0, -- BCSO Commander Batte
  },

  [`polstanierp`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 'none', -- Unmarked
    [3] = 0, -- Legacy SASP
  },

  [`polbisonp`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 'none', -- Unmarked
    [3] = 0, -- Ranger
  },

  [`polcarap`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 'none', -- Unmarked
    [3] = 0, -- Ranger
  },

  [`polgresleyp`] = {
    [0] = 0, -- BCSO
    [1] = 0, -- LSPD
    [2] = 'none', -- Unmarked
    [3] = 97, -- DOC
    [4] = 'none', -- SA NOOSE
  },

  [`imperialdoc`] = {
    [0] = 97, -- DOC
  },

  [`nkhellfire`] = {
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SAHP
    [3] = 0, -- BCSO Ghost
    [4] = 0, -- LSPD Ghost
    [5] = 'none', -- Unmarked
  },

  [`nkaleutian`] = {
    [0] = 0, -- SWAT
    [1] = 'none', -- Unmarked
  },

  [`polroamerp`] = {
    [0] = 0, -- Ranger
    [1] = 'none', -- Unmarked
  },

  [`polbuffalor`] = {
    [0] = 0, -- LSPD
    [1] = 0, -- SWAT
  },

  [`riot3`] = {
    [0] = 134, -- SWAT
    [1] = 0, -- Unmarked
  },

  [`polmav`] = {
    [0] = 0, -- LSPD
    [1] = 134, -- BCSO
    [2] = 0, -- SAHP
    [3] = 0, -- LSFD
  },

  [`polgreenwd`] = {
    [0] = 0, -- LSPD
  },

  [`shergreenwd`] = {
    [0] = 0, -- BCSO
  },

  [`caddyleo`] = {
    [0] = 134, -- BCSO
    [1] = 134, -- LSPD
    [2] = 134, -- SAHP
    [3] = 0, -- DOC
  },

  [`predator3`] = {
    [0] = 0, -- LSPD
    [1] = 134, -- BCSO
    [2] = 0, -- Ranger
    [3] = 0, -- SAHP
  },

  [`expolregent`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- SAHP
  },

  [`expolr300`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- INT
  },

  [`expolgauntlet`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- INT
  },

  [`expolcoqd6`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- INT
  },

  [`expolbuff4`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SHAP
    [3] = 0, -- LSPD ghost
    [4] = 0, -- BCSO ghost
  },

  [`expolbuffsx`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SHAP
    [3] = 0, -- LSPD ghost
    [4] = 0, -- BCSO ghost
  },

  [`expolchavos`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SHAP
  },

  [`expolbuffalos`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SHAP
  },

  [`expolkomoda`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SHAP
  },

  [`expolaleutian`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- SWAT
    [1] = 0, -- LSPD
  },

  [`expoljugular`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
  },

  [`expolscout`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SHAP
    [3] = 0, -- BCSO ghost
  },

  [`expolalamo`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SHAP
    [3] = 0, -- BCSO ghost
  },

  [`expolsg7`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SHAP
    [3] = 0, -- BCSO ghost
  },

  [`expolterminus`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- BCSO
  },

  [`expoltor`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [1] = 0, -- BCSO
    [2] = 0, -- SHAP
  },

  [`expolvstr`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    -- [0] = 0, -- LSPD
    -- [1] = 0, -- BCSO
    -- [2] = 0, -- SHAP
  },

  [`expolschgt`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    -- [0] = 0, -- LSPD
    -- [1] = 0, -- BCSO
    -- [2] = 0, -- SHAP
  },

  [`expolomnisegt`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
  },

  [`gbpoltr3s`] = {
    livery_type = 'modkit',
    color_layer = 'dashboard',
    components = { -- This way for gabz cars 
      25,
      35,
      45, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- INT
  },

  [`gbpolbisonhf`] = {
    livery_type = 'modkit',
    color_layer = 'dashboard',
    components = { -- This way for gabz cars 
      2,
      5,
      6, -- VMT_CHASSIS5
    },

    [-1] = 'none', -- Unmarked
    [0] = 0, -- LSPD
    [2] = 0, -- BCSO
    [6] = 0, -- SHAP
  },

  -- EMS

  [`caddyems`] = {
    [0] = 0, -- LSFD
  },

  [`emsnspeedo`] = {
    [0] = 134, -- LSFD
    [1] = 134, -- LSFD Pride
  },

  [`sandbulance`] = {
    [0] = 134, -- LSFD
    [1] = 134, -- LSFD Pride
  },

  [`firetrukr`] = {
    [0] = 134, -- LSFD
  },

  [`emsscoutp`] = {
    [0] = 134, -- LSFD
  },

  [`emsspeedop`] = {
    [0] = 134, -- LSFD
  },

  [`emsomnisegt`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [0] = 01, -- LSFD
    [1] = 24, -- LSFD
  },

  [`emsbuffalo4`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [0] = 01, -- LSFD
    [1] = 24, -- LSFD
  },

  [`emsbisonp`] = {
    [0] = 134, -- LSFD
  },

  [`emscara`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [0] = 01, -- LSFD
    [1] = 24, -- LSFD
  },

  [`emstor`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [0] = 01, -- LSFD
    [1] = 24, -- LSFD
  },

  [`emsbuffalos`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [0] = 01, -- LSFD
    [1] = 24, -- LSFD
  },

  [`emsgranger`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [0] = 01, -- LSFD
    [1] = 01, -- LSFD
  },

  [`emsaleutian`] = {
    livery_type = 'modkit',
    color_layer = 'interior',
    components = {
      42, -- VMT_CHASSIS2
      44, -- VMT_CHASSIS4
      45, -- VMT_CHASSIS5
    },

    [0] = 01, -- LSFD
    [1] = 01, -- LSFD
  },

}

function setCallsignOnVehicle(vehicle, set_callsign)
  if not vehicle or vehicle <= 0 then
    return
  end

  local me = exports.blrp_core:me()
  local vehicle_model = GetEntityModel(vehicle)
  local vehicle_livery = GetVehicleLivery(vehicle)

  local variations = livery_variation[vehicle_model]

  if not variations then
    return
  end

  if variations.livery_type == 'modkit' then
    vehicle_livery = GetVehicleMod(vehicle, 48)
  end

  if not set_callsign then
    set_callsign = me.get('callsign')
  end

  if not set_callsign or set_callsign == '' then
    return
  end

  -- Calculate individual digits
  local numbers = {} for number in string.gmatch(set_callsign, '%d') do table.insert(numbers, number) end

  local digit_1 = tonumber(numbers[1])
  local digit_2 = tonumber(numbers[2])
  local digit_3 = tonumber(numbers[3])

  -- Force "C" as the first "digit" for DOC callsigns
  if me.hasGroup('DOC') then
    digit_3 = digit_2
    digit_2 = digit_1
    digit_1 = 10
  end

  if not GlobalState.is_dev and (not digit_1 or not digit_2 or not digit_3) then
    return
  end

  local variation = variations[vehicle_livery] or 'a'

  -- Figure out which vehicle mods are which digits
  local mod_a = 8  -- VMT_WING_L
  local mod_b = 9  -- VMT_WING_R
  local mod_c = 10 -- VMT_ROOF

  if variations.components then
    mod_a, mod_b, mod_c = table.unpack(variations.components)
  end

  -- B variations for vehicles on old system is a different color
  if variation == 'b' then
    digit_1 = digit_1 + 10
    digit_2 = digit_2 + 10
    digit_3 = digit_3 + 10
  end

  -- Unmarked variant, remove mods
  if variation == 'none' or variation == 'c-none' then
    digit_1 = -1
    digit_2 = -1
    digit_3 = -1
  end

  -- Set physical digit mods
  SetVehicleMod(vehicle, mod_a, digit_1) -- Left digit
  SetVehicleMod(vehicle, mod_b, digit_2) -- Middle digit
  SetVehicleMod(vehicle, mod_c, digit_3) -- Right digit

  -- Set dashboard color for next gen callsigns
  if type(variation) == 'number' then
    if variations.color_layer == 'color_layer' then
      SetVehicleInteriorColor(vehicle, variation)
    else
      SetVehicleDashboardColor(vehicle, variation)
    end
  end
end
exports('SetCallsignOnVehicle', setCallsignOnVehicle)

RegisterCommand('setcallsign', function(source, args, rawCommand)
  local number = tonumber(args[1])

  if not GlobalState.is_dev and not exports.blrp_core:me().hasOrInheritsGroup('staff') then
    return
  end

  if not number or number < 0 then
    print('invalid number', number)
    return
  end

  setCallsignOnVehicle(GetVehiclePedIsIn(PlayerPedId(), false), number)
end)

RegisterCommand('callsigntest', function(source, args, rawCommand)
  if not GlobalState.is_dev then
    return
  end

  local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

  if not livery_variation[GetEntityModel(vehicle)] then
    exports.blrp_core:me().notifyError('Failed test: vehicle not configured')
    return
  end

  for i = 0, 9 do
    setCallsignOnVehicle(vehicle, i .. i .. i)

    Citizen.Wait(200)
  end
end)
