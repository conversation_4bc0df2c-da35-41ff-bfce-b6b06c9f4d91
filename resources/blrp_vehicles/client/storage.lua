AddEventHandler('blrp_vehicles:client:openGlovebox', function()
  local player_ped = PlayerPedId()
  local vehicle = GetVehiclePedIsIn(player_ped, false)

  if vehicle == 0 then
    return
  end

  if exports.blrp_core:me().isInComa() then
    exports.blrp_core:me().notifyError('Your hands are too bloody')
    return
  end

  local vehicle_class = GetVehicleClass(vehicle)

  if config_storage.glovebox_class_blacklist[vehicle_class] then
    return
  end

  local driver_ped = GetPedInVehicleSeat(vehicle, -1)
  local passenger_ped = GetPedInVehicleSeat(vehicle, 0)

  if driver_ped ~= player_ped and passenger_ped ~= player_ped then
    return
  end

  local state = Entity(vehicle).state

  if not state.flags or hasAnyVehicleFlags(state.flags, 'FLAG_LOCAL', 'FLAG_PREVENT_GLOVEBOX')
    and not hasAnyVehicleFlags(state.flags, 'FLAG_LOCKPICKED', 'FLAG_HOTWIRED') then
    return
  end

  if state.flags and hasAnyVehicleFlags(state.flags, 'FLAG_LOCAL') and not hasAnyVehicleFlags(state.flags, 'FLAG_BOOSTED') then
    TriggerEvent('blrp_vehicles:client:searchVehicle', vehicle)
    return
  end

  TriggerEvent('blrp_inventory:client:isInventoryOpen', function(inventory_open)
    if inventory_open then
      return
    end

    if exports.blrp_phone:IsPhoneInputFocused() then
      return
    end

    TriggerServerEvent('blrp_vehicles:server:openGlovebox', state.server_uid)
  end)
end)

AddEventHandler('blrp_vehicles:client:openGunrack', function()
  local player_ped = PlayerPedId()
  local vehicle = GetVehiclePedIsIn(player_ped, false)

  if vehicle == 0 then
    return
  end

  if not config_storage.gunrack_class_whitelist[GetVehicleClass(vehicle)] then
    return
  end

  local driver_ped = GetPedInVehicleSeat(vehicle, -1)
  local passenger_ped = GetPedInVehicleSeat(vehicle, 0)

  if driver_ped ~= player_ped and passenger_ped ~= player_ped then
    return
  end

  local state = Entity(vehicle).state

  if not state.flags or hasAnyVehicleFlags(state.flags, 'FLAG_LOCAL', 'FLAG_PREVENT_GUNRACK') then
    return
  end

  TriggerEvent('blrp_inventory:client:isInventoryOpen', function(inventory_open)
    if inventory_open then
      return
    end

    if exports.blrp_phone:IsPhoneInputFocused() then
      return
    end

    TriggerServerEvent('blrp_vehicles:server:openGunrack', state.server_uid)
  end)
end)

-- New event for searching hotwired/lockpicked vehicles
AddEventHandler('blrp_vehicles:client:searchVehicle', function(entity, data)
  local vehicle = entity

  -- If called without entity (from command), find nearby vehicle
  if not vehicle or vehicle == 0 then
    local player_ped = PlayerPedId()
    local coords = GetEntityCoords(player_ped)
    vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 3.0, 0, 71)
  end

  if vehicle == 0 or not DoesEntityExist(vehicle) then
    exports.blrp_core:me().notifyError('No vehicle nearby to search')
    return
  end

  if exports.blrp_core:me().isInComa() then
    exports.blrp_core:me().notifyError('Your hands are too bloody')
    return
  end

  local state = Entity(vehicle).state

  if not state.flags then
    exports.blrp_core:me().notifyError('Nothing interesting to search here')
    return
  end

  -- Check if vehicle was hotwired or lockpicked
  if not hasAnyVehicleFlags(state.flags, 'FLAG_HOTWIRED', 'FLAG_LOCKPICKED') then
    exports.blrp_core:me().notifyError('This vehicle doesn\'t appear to have been tampered with')
    return
  end

  TriggerEvent('blrp_inventory:client:isInventoryOpen', function(inventory_open)
    if inventory_open then
      return
    end

    if exports.blrp_phone:IsPhoneInputFocused() then
      return
    end

    TriggerServerEvent('blrp_tablet:server:searchVehicle', NetworkGetNetworkIdFromEntity(vehicle))
  end)
end)
