local engine_states = {}

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

    if vehicle and vehicle > 0 then
      if GetSeatPedIsTryingToEnter(PlayerPedId()) == -1 or IsPedInAnyVehicle(PlayerPedId(), false) then
        local network_id = NetworkGetNetworkIdFromEntity(vehicle)

        local entity = Entity(vehicle)
        if engine_states[network_id] == nil then
          if entity.state.engineOn ~= nil then
            engine_states[network_id] = entity.state.engineOn
          else
            local running = GetIsVehicleEngineRunning(vehicle)
            engine_states[network_id] = running
            entity.state:set('engineOn', running, true)
          end
        end

      end
    end

    for vehicle_network_id, engine_state in pairs(engine_states) do
      if NetworkDoesEntityExistWithNetworkId(vehicle_network_id) then
        local vehicle = NetworkGetEntityFromNetworkId(vehicle_network_id)
        if (GetPedInVehicleSeat(vehicle, -1) == PlayerPedId()) or IsVehicleSeatFree(vehicle, -1) then
          if GetVehicleEngineHealth(vehicle) >= 27.0 then
            local vehicle_class = GetVehicleClass(vehicle)
            if vehicle_class == 15 or vehicle_class == 16 then
              SetVehicleEngineOn(vehicle, engine_state, true, false)
            else
              if engine_state then
                SetVehicleEngineOn(vehicle, engine_state, false, true)

                if IsPedInAnyVehicle(ped, false) and IsControlPressed(2, 75) and not IsEntityDead(ped) then -- vehicle stays on when getting out of car
                  SetVehicleEngineOn(vehicle, true, true, false)
                  SetVehicleLightMultiplier(vehicle, 1.0)
                  TaskLeaveVehicle(ped, vehicle, 64)
                end
              else
                SetVehicleEngineOn(vehicle, engine_state, true, false)
              end
            end
          else
            SetVehicleUndriveable(vehicle, true)
          end
        end
      end
    end
  end
end)

AddStateBagChangeHandler('engineOn', nil, function(bagName, _, value)
  local entity = GetEntityFromStateBagName(bagName)
  if not entity or not DoesEntityExist(entity) then return end

  local netId = NetworkGetNetworkIdFromEntity(entity)
  engine_states[netId] = value
end)


RegisterNetEvent('blrp_vehicles:client:toggleEngine', function()
  local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

  if exports.blrp_phone:IsPhoneInputFocused() then
    return
  end

  if not vehicle or vehicle <= 0 then
    return
  end

  if GetPedInVehicleSeat(vehicle, -1) ~= PlayerPedId() then
    return
  end

  local state = Entity(vehicle).state
  local vehicle_model = GetEntityModel(vehicle)

  local bypass_vehicle = ({
    [`kart3`] = true,
    [`veto`] = true,
    [`veto2`] = true,
  })[vehicle_model]

  if not bypass_vehicle and not state.flags then
    exports.blrp_core:me().notify('You do not have the keys to this vehicle')
    return
  end

  if
    not bypass_vehicle and
    not hasAnyVehicleFlags(state.flags, 'FLAG_HOTWIRED') and
    not canAccessVehicle(vehicle)
  then
    exports.blrp_core:me().notify('You do not have the keys to this vehicle')
    return
  end

  local network_id = NetworkGetNetworkIdFromEntity(vehicle)

  local new_state = not GetIsVehicleEngineRunning(vehicle)
  engine_states[network_id] = new_state
  Entity(vehicle).state:set('engineOn', new_state, true)

  if engine_states[network_id] then
    exports.blrp_core:me().notify('Engine turned on')
    return
  else
    exports.blrp_core:me().notify('Engine turned off')
    return
  end
end)
