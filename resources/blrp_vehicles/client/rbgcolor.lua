local initial_values = {
  p_r = 0,
  p_g = 0,
  p_b = 0,
  s_r = 0,
  s_g = 0,
  s_b = 0,
}

local color_applied = false
local cancelled = false

RegisterNetEvent('blrp_vehicles:client:colorApplied', function()
  color_applied = true
end)

RegisterNetEvent('blrp_vehicles:client:openRGBColorMenu', function()
  local vehicle = GetVehiclePedIsIn(PlayerPedId())
  local vehicle_class = GetVehicleClass(vehicle)

  if not IsPedInAnyVehicle(PlayerPedId(), false) then
    return
  end

  if GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) ~= PlayerPedId() then
    return
  end

  local me = exports.blrp_core:me()

  if vehicle_class >= 13 then
    me.notify('You cannot modify the custom color on this vehicle')
    return
  end

  local state = Entity(vehicle).state

  if
    not tonumber(me.get('id')) == state.owner_char_id and 
    not hasAnyVehicleFlags(state.flags, 'FLAG_LOCAL') and
    not has<PERSON>eys(state.server_uid)
  then
    me.notify('You do not have keys to this vehicle')
    return
  end

  initial_values.p_r, initial_values.p_g, initial_values.p_b = GetVehicleCustomPrimaryColour(vehicle)
  initial_values.s_r, initial_values.s_g, initial_values.s_b = GetVehicleCustomSecondaryColour(vehicle)

  local new_values = {
    p_r = initial_values.p_r,
    p_g = initial_values.p_g,
    p_b = initial_values.p_b,
    s_r = initial_values.s_r,
    s_g = initial_values.s_g,
    s_b = initial_values.s_b,
  }

  local menu = BMenu:new(false, {
    resource_title = '',
    section_title = 'Custom Paint Color',
    title_bg_image = 'https://i.gyazo.com/8535b0d617f577767cb50fcffccb8d0c.jpg',
  })

  for _, k in ipairs({ 'p_r', 'p_g', 'p_b', 's_r', 's_g', 's_b'}) do
    local modality = 'Primary'
    local color = 'Red'

    if string.match(k, 's_') then
      modality = 'Secondary'
    end

    if string.match(k, '_g') then
      color = 'Green'
    end

    if string.match(k, '_b') then
      color = 'Blue'
    end

    menu:addTextInput(false, modality .. ' ' .. color, initial_values[k], function(value, callback)
      value = tonumber(value)

      if not value or value < 0 or value > 255 then
        me.notify('Invalid value')
        callback(new_values[k])
        return
      end

      value = math.floor(value)

      new_values[k] = value

      if modality == 'Primary' then
        SetVehicleCustomPrimaryColour(vehicle, new_values.p_r, new_values.p_g, new_values.p_b)
      else
        SetVehicleCustomSecondaryColour(vehicle, new_values.s_r, new_values.s_g, new_values.s_b)
      end

      callback(value)
    end)
  end

  menu:addSelection(false, 'Cancel Customisation', function(value, callback)
    callback('')
    TriggerEvent('badMenu:client:hideAll')
    me.notify('Modifications cancelled')
    cancelled = true
  end)

  menu:addSelection(false, 'Apply New Color', function(value, callback)
    TriggerServerEvent('blrp_vehicles:server:applyRGBColor', NetworkGetNetworkIdFromEntity(vehicle), new_values)
    TriggerEvent('badMenu:client:hideAll')
  end)

  menu:show()
  startMonitorThreadRgb()
end)

function startMonitorThreadRgb()
  Citizen.CreateThread(function()
    Citizen.Wait(2000)

    local initial_coords = GetEntityCoords(PlayerPedId())

    while true do
      Citizen.Wait(1)

      DisableControlAction(0, 75, true) -- disable exit vehicle

      if #(initial_coords - GetEntityCoords(PlayerPedId())) > 0.5 or cancelled then
        local vehicle = GetVehiclePedIsIn(PlayerPedId())

        if not color_applied then
          exports.blrp_core:me().notify('Modifications cancelled')

          SetVehicleCustomPrimaryColour(vehicle, initial_values.p_r, initial_values.p_g, initial_values.p_b)
          SetVehicleCustomSecondaryColour(vehicle, initial_values.s_r, initial_values.s_g, initial_values.s_b)
        end

        initial_values = {
          p_r = 0,
          p_g = 0,
          p_b = 0,
          s_r = 0,
          s_g = 0,
          s_b = 0,
        }

        color_applied = false
        cancelled = false

        TriggerEvent('badMenu:client:hideAll')

        return
      end
    end
  end)
end
