local synced_bf400s = {}

RegisterNetEvent('blrp_vehicles:client:pbf400Sync', function(pickup_truck_network_id, bf400_network_id)
  synced_bf400s[pickup_truck_network_id] = bf400_network_id
end)

-- Function to find nearby compatible pickup truck
local function findNearbyPickupTruck()
  local player_coords = GetEntityCoords(PlayerPedId())
  local compatible_models = {
    [`polbisonp`] = true,
    [`expolcara`] = true,
    [`gbpolbisonhf`] = true,
    [`polstorm`] = true,
  }
  
  for _, vehicle in pairs(GetGamePool('CVehicle')) do
    local model = GetEntityModel(vehicle)
    if compatible_models[model] then
      local vehicle_coords = GetEntityCoords(vehicle)
      local distance = #(player_coords - vehicle_coords)
      
      if distance <= 5.0 then
        return vehicle
      end
    end
  end
  
  return nil
end

-- Load BF400 into pickup truck
AddEventHandler('blrp_vehicles:client:pbf400:load', function(entity, event_data)
  if not entity or entity <= 0 or GetEntityType(entity) ~= 2 or not exports.blrp_core:me().hasGroup('LEO') then
    return
  end

  if GetEntityModel(entity) ~= `policeb4` then
    exports.blrp_core:me().notify('This is not a police BF400')
    return
  end

  local network_id_bf400 = NetworkGetNetworkIdFromEntity(entity)
  local pickup_truck = findNearbyPickupTruck()

  if not pickup_truck or pickup_truck <= 0 then
    exports.blrp_core:me().notify('No compatible pickup truck found nearby')
    return
  end

  local network_id_truck = NetworkGetNetworkIdFromEntity(pickup_truck)
  local state = Entity(pickup_truck).state

  if state.bf400_network_id then
    exports.blrp_core:me().notify('This truck is already carrying a BF400')
    return
  end

  TriggerServerEvent('blrp_vehicles:server:pbf400:load', network_id_bf400, network_id_truck)
end)

-- Handle BF400 unloading (client-side detachment)
RegisterNetEvent('blrp_vehicles:client:pbf400:unload', function(bf400_network_id, truck_network_id)
  local bf400 = NetworkGetEntityFromNetworkId(bf400_network_id)
  local pickup_truck = NetworkGetEntityFromNetworkId(truck_network_id)

  if not DoesEntityExist(bf400) or not DoesEntityExist(pickup_truck) then
    return
  end

  -- Get truck position and calculate unload position
  local truck_coords = GetEntityCoords(pickup_truck)
  local truck_heading = GetEntityHeading(pickup_truck)
  local truck_forward = GetEntityForwardVector(pickup_truck)

  -- Place BF400 behind the truck
  local unload_coords = truck_coords - (truck_forward * 5.0)

  -- Detach and position BF400
  DetachEntity(bf400, false, false)
  SetEntityCoords(bf400, unload_coords.x, unload_coords.y, unload_coords.z, true, true, true, false)
  SetEntityHeading(bf400, truck_heading)
  SetVehicleOnGroundProperly(bf400)
end)

-- Handle BF400 attachment (like towing system - local attachment)
RegisterNetEvent('blrp_vehicles:client:pbf400:attach', function(bf400_network_id, truck_network_id)
  local bf400 = NetworkGetEntityFromNetworkId(bf400_network_id)
  local pickup_truck = NetworkGetEntityFromNetworkId(truck_network_id)

  if not DoesEntityExist(bf400) or not DoesEntityExist(pickup_truck) then
    return
  end

  local pickup_model = GetEntityModel(pickup_truck)

  -- Attach BF400 to pickup truck bed
  if pickup_model == `polbisonp` then
    AttachEntityToEntity(bf400, pickup_truck, GetEntityBoneIndexByName(pickup_truck, 'bodyshell'), 0.0, -1.75, 0.6, 0, 0, 15, 1, 1, 0, 0, 0, 1)
  elseif pickup_model == `expolcara` then
    AttachEntityToEntity(bf400, pickup_truck, GetEntityBoneIndexByName(pickup_truck, 'bodyshell'), 0.0, -2.1, 0.6, 0, 0, 10, 1, 1, 0, 0, 0, 1)
  elseif pickup_model == `gbpolbisonhf` then
    AttachEntityToEntity(bf400, pickup_truck, GetEntityBoneIndexByName(pickup_truck, 'bodyshell'), 0.0, -2.0, 0.6, 0, 0, 15, 1, 1, 0, 0, 0, 1)
  elseif pickup_model == `polstorm` then
    AttachEntityToEntity(bf400, pickup_truck, GetEntityBoneIndexByName(pickup_truck, 'bodyshell'), 0.0, -2.1, 0.6, 0, 0, 12, 1, 1, 0, 0, 0, 1)
  end
end)

-- No constant attachment thread needed - following towing system pattern
-- No entityRemoved handler needed - following towing system pattern