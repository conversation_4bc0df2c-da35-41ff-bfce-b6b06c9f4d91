--Roll windows. Source https://forum.fivem.net/t/release-roll-windows/53660
local windowup = true

RegisterNetEvent('blrp_vehicles:client:toggleWindows', function()
  tVehicles.rollWindows()
end)

tVehicles.rollWindows = function()
  local playerPed = PlayerPedId()

  if IsPedInAnyVehicle(playerPed, false) then
    local playerCar = GetVehiclePedIsIn(playerPed, false)
    if (GetPedInVehicleSeat(playerCar, -1) == playerPed) then
      SetEntityAsMissionEntity(playerCar, true, true )

      if windowup then
        RollDownWindow(playerCar, 0)
        RollDownWindow(playerCar, 1)
        exports.blrp_core:me().notify('Windows down')
        windowup = false
      else
        RollUpWindow(playerCar, 0)
        RollUpWindow(playerCar, 1)
        exports.blrp_core:me().notify('Windows up')
        windowup = true
      end
    end
  end
end
