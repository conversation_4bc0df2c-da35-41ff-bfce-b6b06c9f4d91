pCustoms = P.getInstance('lscustoms', 'customs')


RegisterNetEvent('blrp_vehicles:client:applyCustomisation', function(network_id, category, vehicle_data, locked_and_off, task_inside, force_unlocked)
  local me = exports.blrp_core:me()

  if task_inside == nil then
    task_inside = true
  end

  local vehicle = nil

  local max_time = GetGameTimer() + 5000

  while not vehicle do
    if NetworkDoesNetworkIdExist(network_id) then
      vehicle = NetworkGetEntityFromNetworkId(network_id)
    end

    if max_time < GetGameTimer() and not vehicle then
      me.notify('Failed to register vehicle')
      return
    end

    Citizen.Wait(200)
  end

  if not vehicle then
    return
  end

  local max_time = GetGameTimer() + 5000

  while not NetworkHasControlOfEntity(vehicle) do
    NetworkRequestControlOfEntity(vehicle)

    if max_time < GetGameTimer() and not NetworkHasControlOfEntity(vehicle) then
      me.notify('Failed to request control of vehicle')
      return
    end

    Citizen.Wait(100)
  end

  if not locked_and_off then
    SetVehicleEngineOn(vehicle, true, true)
  end

  -- Anti-despawning measures for vehicles NOT spawned by the server (server network IDs start at 65535 and decrement)
  if network_id < 60000 then
    SetNetworkIdCanMigrate(network_id, true)
    SetEntityAsMissionEntity(vehicle, true, true)
  end

  SetVehicleHasBeenOwnedByPlayer(vehicle, true)
  SetVehicleOnGroundProperly(vehicle)
  SetEntityInvincible(vehicle, false)
  SetVehicleModKit(vehicle, 0)
  SetVehicleDirtLevel(vehicle, 0)

  -- Set license plate, account for fake plate
  local plate_text = vehicle_data.registration
  local plate_index = vehicle_data.platetype

  if vehicle_data.fake_plate then
    plate_text = vehicle_data.fake_plate
    plate_index = vehicle_data.fake_plate_type
  end

  SetVehicleNumberPlateText(vehicle, plate_text)
  SetVehicleNumberPlateTextIndex(vehicle, plate_index)
  -- /License plate

  if vehicle_data.windows then
    SetVehicleWindowTint(vehicle, vehicle_data.windows)
  end

  if GetVehicleClass(vehicle) == 18 or locked_and_off then
    SetVehRadioStation(vehicle, 'OFF')
  end

  local vehicle_model = GetEntityModel(vehicle)

  if not config_vehicles.mod_protected[vehicle_model] and not locked_and_off then
    SetVehicleModColor_1(vehicle, 0, 0, 0)
    SetVehicleModColor_2(vehicle, 0, 0, 0)

    local colour = tonumber(vehicle_data.colour)
    local scolour = tonumber(vehicle_data.scolour)

    local colour_rgb = json.decode(vehicle_data.colour_rgb)
    local scolour_rgb = json.decode(vehicle_data.scolour_rgb)

    if (colour == 100000 and scolour ~= 100000) or (colour ~= 100000 and scolour == 100000) then
      me.notify('Colour mismatch. Please see a Dealership')
      SetVehicleColours(vehicle, 0, 0)
    elseif colour < 100000 and scolour < 100000 then
      SetVehicleColours(vehicle, colour, scolour)
    else
      SetVehicleCustomPrimaryColour(vehicle, colour_rgb[1], colour_rgb[2], colour_rgb[3])
      SetVehicleCustomSecondaryColour(vehicle, scolour_rgb[1], scolour_rgb[2], scolour_rgb[3])
    end

    SetVehicleExtraColours(vehicle, tonumber(vehicle_data.ecolor), tonumber(vehicle_data.ecolorextra))

    if vehicle_data.dashcolor then
      SetVehicleDashboardColor(vehicle, tonumber(vehicle_data.dashcolor))
    end

    if vehicle_data.interiorcolor then
      SetVehicleInteriorColor(vehicle, tonumber(vehicle_data.interiorcolor))
    end
  end

  local vehicle_class = GetVehicleClass(vehicle)

  if
    vehicle_model == `policeold1` or
    vehicle_model == `policeold2` or
    vehicle_model == `asea2` or
    vehicle_model == `tractor3` or
    vehicle_model == `sadler2` or
    vehicle_model == `rancherxl2` or
    vehicle_model == `mesa2` or
    vehicle_model == `emperor3` or
    vehicle_model == `burrito5`
  then -- North Yankton vehicles
    SetVehicleNumberPlateTextIndex(vehicle, 5) -- North Yankton plate
  elseif vehicle_model == `polalamop2a` then -- Casino security
    SetVehicleNumberPlateTextIndex(vehicle, 1)
  elseif (vehicle_class == 18 or vehicle_class == 19) and category ~= 'cru' then -- Emergency and military vehicles
    SetVehicleNumberPlateTextIndex(vehicle, 4) -- SA Exempt license plate
  end

  if vehicle_model == `f350` then -- DOC
    SetVehicleLivery(vehicle, 0)
    SetVehicleWindowTint(vehicle, 4)
    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 2, 0)
    SetVehicleExtra(vehicle, 3, 0)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 1)
    SetVehicleExtra(vehicle, 7, 1)
    SetVehicleExtra(vehicle, 8, 0)
    SetVehicleExtra(vehicle, 10, 0)
    SetVehicleExtra(vehicle, 11, 0)
    SetVehicleExtra(vehicle, 12, 1)

  elseif vehicle_model == `policeb4` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 1)
    end
  elseif vehicle_model == `polsovereignp` then
    if me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 1)
    end
  elseif vehicle_model == `gbpoltr3s` then
    SetVehicleExtra(vehicle, 1, 1)
    SetVehicleExtra(vehicle, 2, 0)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 6, 1)
    SetVehicleExtra(vehicle, 7, 1)
    SetVehicleExtra(vehicle, 9, 0)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `gbpolbisonhf` then
    if me.hasGroup('LSPD_Internal') then
      SetVehicleMod(vehicle, 48, 0)
    elseif me.hasGroup('Sheriff_Internal') then
      SetVehicleMod(vehicle, 48, 2)
    elseif me.hasGroup('SAHP_Internal') then
      SetVehicleMod(vehicle, 48, 6)
    end
    SetVehicleExtra(vehicle, 1, 1)
    SetVehicleExtra(vehicle, 2, 0)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 6, 1)
    SetVehicleExtra(vehicle, 7, 1)
    SetVehicleExtra(vehicle, 8, 1)
    SetVehicleExtra(vehicle, 9, 1)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `hellion2` then
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `polcoach` then
    SetVehicleColours(vehicle, 90, 90)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolregent` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expoldom` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 1) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    SetVehicleMod(vehicle, 15, -1)-- Suspension stock
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
    SetVehicleMod(vehicle, 48, 0) -- int livery
  elseif vehicle_model == `expolaleutian` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolcoqd6` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 48, 0)
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolgauntlet` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 48, 0)
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `polstorm` then
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolchavos` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolomnisegt` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 0) -- Turbo
  elseif vehicle_model == `expolbuffalos` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expoljugular` then
    if me.hasGroup('LSPD_Internal') then
      SetVehicleMod(vehicle, 48, 0)
    elseif me.hasGroup('SAHP_Internal') then
      SetVehicleMod(vehicle, 48, 1)
    end
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolcypher` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar
  elseif vehicle_model == `expolkomoda` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolscout` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `gstgrs6polng` then
    SetVehicleMod(vehicle, 25, 0)
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `gstsemxl9polng` then
    SetVehicleMod(vehicle, 25, 0)
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolalamo` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 1) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolsg7` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 1) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolschgt` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 1) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolterminus` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar
  elseif vehicle_model == `polellie` then
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolr300` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
    SetVehicleMod(vehicle, 48, 0)
    SetVehicleMod(vehicle, 6, 0)
  elseif vehicle_model == `expolcara` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolvstr` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  -- elseif vehicle_model == `expolgranger` then
  -- SetVehicleExtra(vehicle, 1, 0) -- Lightbar
  -- SetVehicleExtra(vehicle, 2, 0) -- Visor lights
  -- SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
  -- SetVehicleExtra(vehicle, 4, 0) -- Pushbar

  --   SetVehicleMod(vehicle, 11, 3) -- Engine max tune
  --   SetVehicleMod(vehicle, 12, 2) -- Brakes
  --   SetVehicleMod(vehicle, 13, 2) -- Transmission
  elseif vehicle_model == `predator3` then
     if me.hasGroup('LSPD_Internal') then
        SetVehicleLivery(vehicle, 0)
     elseif me.hasGroup('Sheriff_Internal') then
        SetVehicleLivery(vehicle, 1)
      elseif me.hasGroup('SAHP_Internal') then
        SetVehicleLivery(vehicle, 3)
     end
  elseif vehicle_model == `vvpi` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 1)
    end

    ToggleVehicleMod(vehicle, 18, true) -- Add turbo upgrade to the vehicle
    SetVehicleExtra(vehicle, 2, 0)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)
  elseif vehicle_model == `expoldorado` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `nkstx` then
    if me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 0)
   elseif me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 1)
   else
      SetVehicleLivery(vehicle, 2)
    end

    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 2, 0)
    SetVehicleExtra(vehicle, 3, 0)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)
    SetVehicleExtra(vehicle, 7, 1)
    SetVehicleExtra(vehicle, 8, 1)
    SetVehicleExtra(vehicle, 9, 0)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `nkstxum` then

    SetVehicleExtra(vehicle, 1, 1)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 0)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 1)
    SetVehicleExtra(vehicle, 6, 1)
    SetVehicleExtra(vehicle, 7, 1)
    SetVehicleExtra(vehicle, 8, 1)
    SetVehicleExtra(vehicle, 9, 1)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expoltailgater` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    SetVehicleMod(vehicle, 15, 2)-- Suspension sport
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolrebla` then
    SetVehicleExtra(vehicle, 1, 1) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 1) -- Pushbar

    SetVehicleMod(vehicle, 5, 1)
    SetVehicleMod(vehicle, 32, 0)
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `vvpi2` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 1)
    elseif me.hasGroup('SAHP_Internal') then
      SetVehicleLivery(vehicle, 2)
    else
      SetVehicleLivery(vehicle, 4)
    end

    ToggleVehicleMod(vehicle, 18, true) -- Add turbo upgrade to the vehicle
    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 2, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)
    SetVehicleExtra(vehicle, 8, 1)
    SetVehicleExtra(vehicle, 9, 1)
    SetVehicleExtra(vehicle, 10, 1)
    SetVehicleExtra(vehicle, 11, 1)
    SetVehicleExtra(vehicle, 12, 0)
  elseif vehicle_model == `hauler3` then
    SetVehicleExtra(vehicle, 1, 1)
    SetVehicleExtra(vehicle, 2, 0)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 1)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 1)
    SetVehicleExtra(vehicle, 7, 0)
    SetVehicleExtra(vehicle, 8, 1)
    SetVehicleExtra(vehicle, 9, 1)
  elseif vehicle_model == `firetrukr` then
    SetVehicleExtra(vehicle, 7, 0)
  elseif vehicle_model == `ambulance` then
    SetVehicleLivery(vehicle, 0)
    SetVehicleExtra(vehicle,1,1)
    SetVehicleExtra(vehicle,2,1)
    SetVehicleExtra(vehicle,3,1)
    SetVehicleExtra(vehicle,4,0)
    SetVehicleExtra(vehicle,5,1)
    SetVehicleExtra(vehicle,6,1)
    SetVehicleExtra(vehicle,7,1)
    SetVehicleExtra(vehicle,8,0)
    SetVehicleExtra(vehicle,9,1)
    SetVehicleExtra(vehicle,10,0)
    SetVehicleExtra(vehicle,11,1)
    SetVehicleExtra(vehicle,12,1)
  elseif vehicle_model == `polmav` then
    if me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 1)
    elseif me.hasGroup('SAHP') then
      SetVehicleLivery(vehicle, 2)
    elseif me.hasGroup('LSFD') then
      SetVehicleLivery(vehicle, 3)
    end
  elseif vehicle_model == `polbuz` then
    if me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 1)
    elseif me.hasGroup('SAHP') then
      SetVehicleLivery(vehicle, 2)
    elseif me.hasGroup('Weazel News') then
      SetVehicleLivery(vehicle, 3)
    end
  elseif vehicle_model == `rumpo` then
    SetVehicleLivery(vehicle, 2)
  elseif vehicle_model == `buccaneer2` then
    SetVehicleExtra(vehicle, 1, 1)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 1)
  elseif vehicle_model == `chino2` then
    SetVehicleExtra(vehicle, 1, 1)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 1)
  elseif vehicle_model == `peyote3` then
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 1)
  elseif vehicle_model == `cvpi13` then
    SetVehicleExtra(vehicle, 12, 1)
  elseif vehicle_model == `apoliceu` then
    SetVehicleExtra(vehicle, 1, 1)
  elseif vehicle_model == `apolice7` then
    SetVehicleExtra(vehicle, 1, 1)
  elseif vehicle_model == `taco2` then
    SetVehicleExtra(vehicle, 1, 0) -- Roof sign
  elseif vehicle_model == `polvigerop` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
      SetVehicleExtra(vehicle, 1, 1)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 0)
      SetVehicleExtra(vehicle, 1, 1)
    else
      SetVehicleLivery(vehicle, 0)
      SetVehicleExtra(vehicle, 1, 1)
    end
  elseif vehicle_model == `nkcoquette` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 0)
    else
      SetVehicleLivery(vehicle, 0)
    end

    SetVehicleExtra(vehicle, 1, 0) -- Roof
    SetVehicleExtra(vehicle, 2, 0) -- Roof Lightbar
    SetVehicleExtra(vehicle, 3, 0) -- Front Bumper Lights
    SetVehicleExtra(vehicle, 4, 0) -- Front Visor Lights
    SetVehicleExtra(vehicle, 5, 0) -- Back Visor Lights
    SetVehicleExtra(vehicle, 6, 1) -- Left Spotlight
    SetVehicleExtra(vehicle, 7, 1) -- Right Spotlight
  elseif vehicle_model == `poldmntp` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 0)
    else
      SetVehicleLivery(vehicle, 0)
    end
  elseif vehicle_model == `politaligto` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 2)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 1)
    elseif me.hasGroup('SAHP') then
      SetVehicleLivery(vehicle, 0)
    end
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `polstingertt` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 0)
    else
      SetVehicleLivery(vehicle, 0)
    end
    --SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    --SetVehicleMod(vehicle, 12, 2) -- Brakes
    --SetVehicleMod(vehicle, 13, 2) -- Transmission
    --ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `17silv` then
    SetVehicleExtra(vehicle, 1, 1) -- Lightbar TA Left
    SetVehicleExtra(vehicle, 2, 1) -- Lightbar TA Right
    SetVehicleExtra(vehicle, 3, 0) -- Lightbar TA Dual
    SetVehicleExtra(vehicle, 4, 0) -- Lightbar Front
    SetVehicleExtra(vehicle, 5, 0) -- Arrow Left
    SetVehicleExtra(vehicle, 6, 0) -- Arrow Right
    SetVehicleExtra(vehicle, 7, 0) -- Push Bumper
    SetVehicleExtra(vehicle, 8, 1) -- Arrow Board

  elseif vehicle_model == `bcsocvpi` then
    SetVehicleLivery(vehicle, 1)
  elseif vehicle_model == `benson2` then
    SetVehicleExtra(vehicle, 1, 1)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 10, 1)
    SetVehicleExtra(vehicle, 11, 1)
    SetVehicleExtra(vehicle, 12, 1)
  elseif vehicle_model == `pranger` then
    SetVehicleColours(vehicle, 111, 111)
    SetVehicleExtraColours(vehicle, 0, 0)
  elseif vehicle_model == `sandbulance` then
    SetVehicleColours(vehicle, 111, 111)
  elseif vehicle_model == `pol_hellion` then
    SetVehicleLivery(vehicle, 0)

    SetVehicleExtra(vehicle, 1, 1) -- Roof Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor Lights
    SetVehicleExtra(vehicle, 3, 0) -- Dashboard Light
    SetVehicleExtra(vehicle, 4, 0) -- Grill Light
    SetVehicleExtra(vehicle, 5, 0) -- Rear Lightbar
    SetVehicleExtra(vehicle, 6, 0) -- Antennas
  elseif vehicle_model == `pol_kuruma` then
    SetVehicleLivery(vehicle, 0)

    SetVehicleExtra(vehicle, 1, 0) -- Roof Lightbar
    SetVehicleExtra(vehicle, 2, 1) -- Visor Lights
    SetVehicleExtra(vehicle, 3, 1) -- Dashboard Light
    SetVehicleExtra(vehicle, 4, 0) -- Grill Light
    SetVehicleExtra(vehicle, 5, 0) -- Rear Lightbar
    SetVehicleMod(vehicle, 11, 3) -- Engine
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo

    SetVehicleNumberPlateText(vehicle, 'AY0FR3M3')
  elseif vehicle_model == `bcsosheriff` or vehicle_model == `bcsosheriff2` or vehicle_model == `bcsosheriff3` then
    SetVehicleColours(vehicle, 111, 111)
  elseif vehicle_model == `nkgauntlet4` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 1)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 1)
    else
      SetVehicleLivery(vehicle, 1)
    end

    SetVehicleExtra(vehicle, 1, 0) -- Roof Lightbar
    SetVehicleExtra(vehicle, 2, 1) -- Left spotlight
    SetVehicleExtra(vehicle, 3, 1) -- Right spotlight
    SetVehicleExtra(vehicle, 4, 1) -- Unused spotlight
    SetVehicleExtra(vehicle, 5, 1) -- Unused spotlight
    SetVehicleExtra(vehicle, 6, 0) -- Push bumper
  elseif vehicle_model == `nkscout2020` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 1)
    else
      SetVehicleLivery(vehicle, 2)
    end

    SetVehicleExtra(vehicle, 1, 0) -- Roof Lightbar
    SetVehicleExtra(vehicle, 2, 0)
    SetVehicleExtra(vehicle, 3, 0)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)
    SetVehicleExtra(vehicle, 7, 1)
    SetVehicleExtra(vehicle, 8, 1)
    SetVehicleExtra(vehicle, 9, 0)
  elseif vehicle_model == `sheriffsar` then
    SetVehicleLivery(vehicle, 0)

    SetVehicleExtra(vehicle, 1, 0) -- Front Bumper
    SetVehicleExtra(vehicle, 2, 0) -- Push Bumper
    SetVehicleExtra(vehicle, 3, 1) -- Utility Bumper
    SetVehicleExtra(vehicle, 5, 1) -- Traffic Arrow
  elseif vehicle_model == `shamal` then
    if me.hasGroup('LSIA') then
      SetVehicleLivery(vehicle, 3)
    else
      SetVehicleLivery(vehicle, 0)
    end
  elseif vehicle_model == `rentalbus` then
    SetVehicleColours(vehicle, 111, 111)
    SetVehicleNumberPlateTextIndex(vehicle, 4)

    if me.hasGroup('LSIA') then
      SetVehicleLivery(vehicle, 5)
    else
      SetVehicleLivery(vehicle, 0)
    end
  elseif
    ({
      [`airportcaddy`] = true,
      [`airportcaddyxl`] = true,
      [`airportferoci`] = true,
      [`airportperennial`] = true,
      [`airportutil`] = true,
      [`airtug`] = true,
      [`aviationtanker`] = true,
      [`catering`] = true,
      [`at300`] = true,
    })[vehicle_model]
  then
    SetVehicleColours(vehicle, 111, 111)
    SetVehicleNumberPlateTextIndex(vehicle, 4)
    SetVehicleLivery(vehicle, 3)

    if vehicle_model == `at300` then
      if me.hasGroup('Rum Runners') then
        SetVehicleLivery(vehicle, 9)
      end

      SetVehicleExtraColours(vehicle, 0, 0, 44)
    end
  elseif vehicle_model == `polgreenwd` or vehicle_model == `shergreenwd` then
    SetVehicleColours(vehicle, 111, 111)

    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 1, 1)
    SetVehicleExtra(vehicle, 1, 1)
    SetVehicleExtra(vehicle, 1, 1)
  elseif vehicle_model == `dickgreenwd` then
    SetVehicleExtra(vehicle, 1, 1)
    SetVehicleExtra(vehicle, 11, 0)
    SetVehicleExtra(vehicle, 10, 1)
    SetVehicleExtra(vehicle, 12, 1)
  elseif vehicle_model == `mule5` or vehicle_model == `apoliceub` then
    SetVehicleNumberPlateTextIndex(vehicle, 1)
  elseif vehicle_model == `mgt` then
    SetVehicleLivery(vehicle, 0)
  elseif vehicle_model == `polroamerp` or vehicle_model == `polroamerp2` then
    SetVehicleLivery(vehicle, 0)
  elseif vehicle_model == `rcmpstaniersnow` then
    SetVehicleLivery(vehicle, 2)
  elseif vehicle_model == `kamacho2` then
    SetVehicleMod(vehicle, 48, 0, false)
    SetVehicleMod(vehicle, 11, 3)
    SetVehicleMod(vehicle, 13, 1)
    SetVehicleMod(vehicle, 12, 2)
    ToggleVehicleMod(vehicle, 18, 1)

    SetVehicleExtra(vehicle, 1, false)
    SetVehicleExtra(vehicle, 4, false)
  elseif vehicle_model == `boxville2` then
    SetVehicleColours(vehicle, 131, 83)
  elseif vehicle_model == `polstalkerp` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 1)
    end
  elseif vehicle_model == `polpanto` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 1)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 0)
    end
  elseif vehicle_model == `riot3` then
    SetVehicleExtra(vehicle, 1, false)
    SetVehicleMod(vehicle, 2, 1) -- Mounted ram bar
  elseif vehicle_model == `expolinsurgent` then
    SetVehicleExtra(vehicle, 1, true)
    SetVehicleExtra(vehicle, 2, true)
    SetVehicleExtra(vehicle, 3, true)
    SetVehicleExtra(vehicle, 4, true)
  elseif vehicle_model == `stockade4` then
    SetVehicleColours(vehicle, 134, 0)
    SetVehicleLights(vehicle, 2) -- Always on
    SetVehicleLightsMode(vehicle, 2) -- Always on
    SetVehicleWindowTint(vehicle, 1) -- Pure black windows
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo

    SetVehicleExtra(vehicle, 10, 1) -- Disable hand cart
    SetVehicleExtra(vehicle, 11, 1) -- Disable money bags

    if me.hasGroup('Diamond Casino') then
      SetVehicleMod(vehicle, 48, 0, false)
      SetVehicleNumberPlateTextIndex(vehicle, 1)
    end
  elseif vehicle_model == `caddyleo` then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 1)
    elseif me.hasGroup('SAHP_Internal') then
      SetVehicleLivery(vehicle, 2)
    elseif me.hasGroup('DOC') then
      SetVehicleLivery(vehicle, 3)
    end

    SetVehicleExtra(vehicle, 2, 0)
  elseif vehicle_model == `seasharkb` then
    SetVehicleCustomPrimaryColour(vehicle, 39, 133, 210)
    SetVehicleCustomSecondaryColour(vehicle, 39, 133, 210)
  elseif vehicle_model == `emsbuffalo4` then -- EMS fleet starts here
    if me.hasGroup('LSFD') then
      SetVehicleMod(vehicle, 48, 0)
    end
    SetVehicleMod(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)
  elseif vehicle_model == `emstor` then
    if me.hasGroup('LSFD') then
      SetVehicleMod(vehicle, 48, 0)
    end
    SetVehicleMod(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `emsaleutian` then
    if me.hasGroup('LSFD') then
      SetVehicleMod(vehicle, 48, 0)
    end
    SetVehicleMod(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `emsgranger` then
    if me.hasGroup('LSFD') then
      SetVehicleMod(vehicle, 48, 0)
    end
    SetVehicleMod(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `emsbuffalos` then
    if me.hasGroup('LSFD') then
      SetVehicleMod(vehicle, 48, 0)
    end
    SetVehicleMod(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `emscara` then
    if me.hasGroup('LSFD') then
      SetVehicleMod(vehicle, 48, 0)
    end
    SetVehicleMod(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `emsomnisegt` then
    if me.hasGroup('LSFD') then
      SetVehicleMod(vehicle, 48, 0)
    end
    SetVehicleMod(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 1, 0)
    SetVehicleExtra(vehicle, 2, 1)
    SetVehicleExtra(vehicle, 3, 1)
    SetVehicleExtra(vehicle, 4, 0)
    SetVehicleExtra(vehicle, 5, 0)
    SetVehicleExtra(vehicle, 6, 0)

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  else
    if category ~= 'policefleet' then
      SetVehicleLivery(vehicle, 0)
      SetVehicleExtra(vehicle, 2, 0) -- Push bumper
      SetVehicleExtra(vehicle, 3, 0) -- Prisoner cage
      SetVehicleExtra(vehicle, 4, 0) -- Lightbar
    end
  end

  if category == 'militaryair' then
    SetVehicleColours(vehicle, 106, 106)
    SetVehicleWindowTint(vehicle, 1) -- Pure black windows

    if vehicle_model == `strikeforce` then
      SetVehicleMod(vehicle, 48, 1, false) -- The Snake livery
    elseif vehicle_model == `hunter` then
      SetVehicleMod(vehicle, 48, 1, false) -- Shark Teeth Bat livery
    elseif vehicle_model == `avenger` then
      SetVehicleMod(vehicle, 48, 1, false) -- Zancudo camo livery
    end
  end

  -- New EMS fleet 2022

  if ({
    [`emsalamop`] = true,
    [`emsbisonp`] = true,
    [`emsbuffalop`] = true,
    [`emscarap`] = true,
    [`emsgresleyp`] = true,
    [`emsroamerp`] = true,
    [`emsscoutp`] = true,
    [`emsspeedop`] = true,
    [`emsstalkerp`] = true,
    [`emsstanierp`] = true,
    [`kamacho2`] = true,
    [`hellion2`] = true,
  })[vehicle_model] then

    SetVehicleExtra(vehicle, 3, 1) -- Prisoner cage
    SetVehicleExtra(vehicle, 4, 0) -- Roof Lightbar
  end


  -- New police fleet 2021

  if category == 'policefleet' then
    if me.hasGroup('Sheriff_Internal') then
      SetVehicleLivery(vehicle, 0)
    elseif me.hasGroup('LSPD_Internal') then
      SetVehicleLivery(vehicle, 1)
    elseif me.hasGroup('DOC') then
      SetVehicleLivery(vehicle, 3)
    else
      SetVehicleLivery(vehicle, 2)
    end

    if vehicle_model == `21f150` or vehicle_model == `polbisonp` then
      SetVehicleExtra(vehicle, 11, 1) -- Police Bike
      SetVehicleExtra(vehicle, 12, 1) -- Box Seats
    end
    if ({
      [`polalamop`] = true,
      [`polalamop2`] = true,
      [`polbisonp`] = true,
      [`polbuffalop2`] = true,
      [`polcarap`] = true,
      [`polcoquettep`] = true,
      [`poldmntp`] = true,
      [`polfugitivep`] = true,
      [`polgauntletp`] = true,
      [`polgresleyp`] = true,
      [`polscoutp`] = true,
      [`polspeedop`] = true,
      [`polstalkerp`] = true,
      [`polstanierp`] = true,
    })[vehicle_model] then
      SetVehicleExtra(vehicle, 2, 0) -- Push bumper
      SetVehicleExtra(vehicle, 3, 0) -- Prisoner cage
      SetVehicleExtra(vehicle, 4, 0) -- Lightbar
    else
      SetVehicleExtra(vehicle, 1, 0) -- Roof Lightbar
      SetVehicleExtra(vehicle, 2, 1) -- Dashboard Light
      SetVehicleExtra(vehicle, 3, 0) -- Grill Lights
      SetVehicleExtra(vehicle, 4, 0) -- Rear Lightbar
      SetVehicleExtra(vehicle, 5, 0) -- Skirt Lights
      SetVehicleExtra(vehicle, 6, 1) -- Visor Lights
      SetVehicleExtra(vehicle, 7, 0) -- Marked Assets
      SetVehicleExtra(vehicle, 8, 0) -- Push Bumper

      if vehicle_model == `fpiu16` then
        SetVehicleExtra(vehicle, 9, 1) -- Push Bumper Upper
        SetVehicleExtra(vehicle, 11, 1) -- Push Bumper Lower
      end
    end
  end

  if
    (
      vehicle_model == `polalamop2`
    ) and
    me.hasGroup('SAHP')
  then
    SetVehicleLivery(vehicle, 3)
  end

  if
    vehicle_model == `polscoutp` and
    me.hasGroup('SAHP')
  then
    SetVehicleLivery(vehicle, 4)
  end

  if vehicle_model == `polnovak` then
    SetVehicleMod(vehicle, 48, 10, false)

    SetVehicleNumberPlateText(vehicle, 'SEIZED')
  end

  if vehicle_model == `imperialdoc` then
    SetVehicleMod(vehicle, 1, 0, false)
    SetVehicleMod(vehicle, 2, 0, false)
    SetVehicleMod(vehicle, 48, 0, false)
    SetVehicleColours(vehicle, 0, 0)
  end

  -- Lowrider roof RNG
  local lowrider_roof_options = {
    [`chino2`] = {
      2, -- Hard top
      3, -- Soft top
    },
    [`buccaneer2`] = {
      2, -- Soft top black
      3, -- Soft top grey
      4, -- Hard top
    },
    [`peyote2`] = {
      2, -- These all look the same to me
      3,
      4,
    },
  }

  local lr_options = lowrider_roof_options[vehicle_model]

  if lr_options then
    local roll = math.random(0, #lr_options)

    for _, extra_number in pairs(lr_options) do
      SetVehicleExtra(vehicle, extra_number, true)
    end
    -- 0 = no roof
    if roll > 0 then
      local extra_number = lr_options[roll]

      SetVehicleExtra(vehicle, extra_number, false)
    end
  end

  -- Vehicle mods
  local mod_blacklist = exports.lscustoms:getModBlacklist()
  local mods_has_changed = false
  local mods_removed = {}

  -- Disable mule6/7 diffuser and refrigeration unit, enable later if needed
  if vehicle_model == `mule6` or vehicle_model == `mule7` then
    SetVehicleExtra(vehicle, 1, true)
    SetVehicleExtra(vehicle, 2, true)
    SetVehicleExtra(vehicle, 4, true) -- Underneath stuff
  end

  if vehicle_model == `rrtow` then
    if me.hasGroup('Rocky Road Towing') then
      SetVehicleLivery(vehicle, 0)
      SetVehicleExtra(vehicle, 5, true)
    end
  end

  if vehicle_model == `elegyrh5` then
    -- Roll a random number for the floor mat
    local floorMat = math.random(2, 3)

    SetVehicleExtra(vehicle, 1, true) -- Foresell sign
    SetVehicleExtra(vehicle, 4, true) -- Drinks
    -- Set all Matts to false first, then enable only the selected one
    SetVehicleExtra(vehicle, 2, true) -- Floor mat
    SetVehicleExtra(vehicle, 3, true)-- Floor mat


    -- Activate the selected floor mat and disable others
    if floorMat == 2 then
        SetVehicleExtra(vehicle, 3, false)
    elseif floorMat == 3 then
        SetVehicleExtra(vehicle, 2, false)
    end
end

  if vehicle_data.mods then
    vehicle_data.mods = json.decode(vehicle_data.mods)
    if type(vehicle_data.mods) == 'table' then
      for k, v in pairs(vehicle_data.mods) do
        if k == '22' then
          -- Xenon headlights - do nothing, handled below
        elseif k == '18' then -- support toggle mods turbo
          ToggleVehicleMod(vehicle, tonumber(k), tonumber(v.mod))
        elseif k == '15' then -- suspension
          SetVehicleMod(vehicle, tonumber(k), tonumber(v.mod))
        elseif k == '23' then -- custom tires
          SetVehicleWheelType(vehicle, tonumber(vehicle_data.wheels))
          SetVehicleMod(vehicle, tonumber(k), tonumber(v.mod), v.variation)
          vehicle_data.mods[k].variation = v.variation
        elseif IsThisModelABike(GetEntityModel(vehicle)) and k == '24' then  -- For rear wheel if it's a bike with custom tires
          SetVehicleMod(vehicle, tonumber(k), tonumber(v.mod), v.variation)
          vehicle_data.mods[k].variation = v.variation
        elseif k == '20' then  -- tire smoke
          ToggleVehicleMod(vehicle, 20, true)
          SetVehicleTyreSmokeColor(vehicle, tonumber(vehicle_data.smokecolor1), tonumber(vehicle_data.smokecolor2), tonumber(vehicle_data.smokecolor3))
        elseif k == '48' then
          local livery_number = tonumber(v.mod)

          -- Enable mule6/7 diffuser and refrigeration unit, further disablement done by carcols
          if (vehicle_model == `mule6` or vehicle_model == `mule7`) and livery_number > -1 then
            SetVehicleExtra(vehicle, 1, false)
            SetVehicleExtra(vehicle, 2, false)
          end

          SetVehicleMod(vehicle, 48, livery_number, false)
        elseif k == '8' then -- fenders
          local isBlacklistedCategory = mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][tonumber(k)] and type(mod_blacklist[vehicle_model][tonumber(k)]) == 'boolean'
          local isBlacklistedMod = mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][tonumber(k)] and type(mod_blacklist[vehicle_model][tonumber(k)]) == 'table' and mod_blacklist[vehicle_model][tonumber(k)][tonumber(v.mod)]
          if (not isBlacklistedCategory) and (not isBlacklistedMod) and not ({
            ['vigero2'] = true,
            ['buffalo4a'] = true,
            ['jestgpr'] = true,
            ['jester'] = true,
            ['jester2'] = true,
            ['sentinel3'] = true,
            ['dominator'] = true,
            ['dominator7'] = true,
            ['dominator8'] = true,
            ['zr350'] = true,
            ['zrgpr'] = true,
            ['elegyrh5'] = true,
            ['elegyrh6'] = true,
            ['tenf'] = true,
            ['tenf2'] = true,
            ['buffalo5'] = true,
            ['r300'] = true,
            ['granger2'] = true,
            ['jb7002'] = true,
            ['deity'] = true,
            ['champion'] = true,
            ['jubilee'] = true,
            ['Imperator'] = true,
            ['Yosemite3'] = true,
          })[vehicle_model] then -- has bulletproof fenders
            SetVehicleMod(vehicle, tonumber(k), tonumber(v.mod), false)
          elseif isBlacklistedCategory or isBlacklistedMod then
            vehicle_data.mods[k].mod = -1  -- Remove the blocked mod from the data
            print('Blocked mod installation for vehicle', vehicle_model, 'mod category', k, 'mod index', v.mod)
            mods_has_changed = true
            mods_removed[k] = true
          end
        elseif k == '0' then -- spoiler
          local isBlacklistedCategory = mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][tonumber(k)] and type(mod_blacklist[vehicle_model][tonumber(k)]) == 'boolean'
          local isBlacklistedMod = mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][tonumber(k)] and type(mod_blacklist[vehicle_model][tonumber(k)]) == 'table' and mod_blacklist[vehicle_model][tonumber(k)][tonumber(v.mod)]
          if (not isBlacklistedCategory) and (not isBlacklistedMod) and vehicle_model ~= 'hellion' then -- hellion has bulletproof window panels
            SetVehicleMod(vehicle, tonumber(k), tonumber(v.mod), false)
          elseif isBlacklistedCategory or isBlacklistedMod then
            vehicle_data.mods[k].mod = -1  -- Remove the blocked mod from the data
            print('Blocked mod installation for vehicle', vehicle_model, 'mod category', k, 'mod index', v.mod)
            mods_has_changed = true
            mods_removed[k] = true
          end
        else
          local isBlacklistedCategory = mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][tonumber(k)] and type(mod_blacklist[vehicle_model][tonumber(k)]) == 'boolean'
          local isBlacklistedMod = mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][tonumber(k)] and type(mod_blacklist[vehicle_model][tonumber(k)]) == 'table' and mod_blacklist[vehicle_model][tonumber(k)][tonumber(v.mod)]

          if (not isBlacklistedCategory) and (not isBlacklistedMod) then
            SetVehicleMod(vehicle, tonumber(k), tonumber(v.mod), false)
          elseif isBlacklistedCategory or isBlacklistedMod then
            vehicle_data.mods[k].mod = -1  -- Remove the blocked mod from the data
            print('Blocked mod installation for vehicle', vehicle_model, 'mod category', k, 'mod index', v.mod)
            mods_has_changed = true
            mods_removed[k] = true
          end
        end
      end
    end

    if mods_has_changed then
      vehicle_data.mods = json.encode(vehicle_data.mods)
      pCustoms.updateMods({vehicle_data.registration, vehicle_data.mods, mods_removed})
    end
  end

  -- Callsign enabled police vehicles
  setCallsignOnVehicle(vehicle)

  -- Neon
  if not vehicle_data.neon or tonumber(vehicle_data.neon) == 0 then
    SetVehicleNeonLightEnabled(vehicle, 0, false)
    SetVehicleNeonLightEnabled(vehicle, 1, false)
    SetVehicleNeonLightEnabled(vehicle, 2, false)
    SetVehicleNeonLightEnabled(vehicle, 3, false)
  else
    SetVehicleNeonLightsColour(vehicle, tonumber(vehicle_data.neoncolor1), tonumber(vehicle_data.neoncolor2), tonumber(vehicle_data.neoncolor3))
    SetVehicleNeonLightEnabled(vehicle, 0, true)
    SetVehicleNeonLightEnabled(vehicle, 1, true)
    SetVehicleNeonLightEnabled(vehicle, 2, true)
    SetVehicleNeonLightEnabled(vehicle, 3, true)
  end

  -- Xenon headlights

  if vehicle_data.xenon_enabled then
    ToggleVehicleMod(vehicle, 22, true)
    SetVehicleXenonLightsColor(vehicle, vehicle_data.xenon_color)
  else
    ToggleVehicleMod(vehicle, 22, false)
  end

  SetModelAsNoLongerNeeded(vehicle_model)

  -- Damage

  SetVehicleEngineHealth(vehicle, math.max(tonumber(vehicle_data.engineDamage) + 0.001, 25))
  SetVehicleBodyHealth(vehicle, math.max(tonumber(vehicle_data.bodyDamage) + 0.001, 25))
  SetVehiclePetrolTankHealth(vehicle, math.max(tonumber(vehicle_data.fuelDamage) + 0.001, 25))

  local door_damage = json.decode(vehicle_data.doorDamage)

  if door_damage and door_damage.doorCount and door_damage.doorCount > 0 and door_damage.list then
    for door = 0, door_damage.doorCount, 1 do
      if door_damage.list[tostring(door)] then
        SetVehicleDoorBroken(vehicle, door, true)
      end
    end
  end

  if vehicle_model == `winky2` then
    SetVehicleNumberPlateTextIndex(vehicle, 5)

    SetVehicleColours(vehicle, 100, 100) -- Metallic moss brown

    -- Livery RNG
    if math.random(1, 100) <= 50 then
      local livery = ({ 0, 1, 3, 6, 8, 20, 22 })[math.random(1, 7)]

      SetVehicleMod(vehicle, 48, livery, false)
    end

    SetVehicleMod(vehicle, 0, 1) -- Antennas
    SetVehicleMod(vehicle, 1, 4) -- Front bumper
    SetVehicleMod(vehicle, 2, 1) -- Rear
    SetVehicleMod(vehicle, 3, math.random(-1, 5)) -- Headlight covers
    SetVehicleMod(vehicle, 5, math.random(-1, 9)) -- Front bumper upper
    SetVehicleMod(vehicle, 7, math.random(-1, 5)) -- Hood
    SetVehicleMod(vehicle, 8, math.random(-1, 2)) -- Fenders

    -- Roof search light RNG
    if math.random(1, 100) <= 25 then
      SetVehicleMod(vehicle, 10, 0)
    end
  end

  if vehicle_model == `squaddie2` then
    SetVehicleNumberPlateTextIndex(vehicle, 5)

    SetVehicleColours(vehicle, 100, 100) -- Metallic moss brown

    if math.random(1, 100) <= 50 then
      local livery = ({ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19 })[math.random(1, 20)]

      SetVehicleMod(vehicle, 48, livery, false)
    end

    SetVehicleMod(vehicle, 0, math.random(-1, 12)) -- Spoiler
    SetVehicleMod(vehicle, 3, math.random(-1, 13)) -- Skirts
    SetVehicleMod(vehicle, 4, math.random(-1, 8)) -- Exhaust
    SetVehicleMod(vehicle, 5, math.random(-1, 4)) -- Roll Cage
    SetVehicleMod(vehicle, 6, math.random(-1, 18)) -- Grille
    SetVehicleMod(vehicle, 10, math.random(-1, 8)) -- Roof
  end

  if vehicle_model == `polalamop2a` then -- Casino security
    if me.hasGroup('Security') then
      SetVehicleLivery(vehicle, 1)
      SetVehicleColours(vehicle, 0, 0)
    else
      SetVehicleLivery(vehicle, 0)
      SetVehicleNumberPlateTextIndex(vehicle, 1)
    end
  end

  if vehicle_model == `expoltor` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolbuff4` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 1) -- Pushbar

    SetVehicleMod(vehicle, 11, -1) -- Engine max tune
    SetVehicleMod(vehicle, 13, -1) -- Transmission
    ToggleVehicleMod(vehicle, 18, false) -- Turbo
  elseif vehicle_model == `expolbuffsx` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 1) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  elseif vehicle_model == `expolvigero` then
    SetVehicleExtra(vehicle, 1, 0) -- Lightbar
    SetVehicleExtra(vehicle, 2, 0) -- Visor lights
    SetVehicleExtra(vehicle, 3, 0) -- Rear lightbar
    SetVehicleExtra(vehicle, 4, 0) -- Pushbar

    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
    SetVehicleMod(vehicle, 48, 0)
  elseif vehicle_model == `openwheel1` then
    SetVehicleMod(vehicle, 11, 3) -- Engine max tune
    SetVehicleMod(vehicle, 12, 2) -- Brakes
    SetVehicleMod(vehicle, 13, 2) -- Transmission
    ToggleVehicleMod(vehicle, 18, 1) -- Turbo
  end

--[[   if vehicle_model == `emerus` then
    SetVehicleMod(vehicle, 0, 0) -- Spoiler as the car handling gets scuffed as the stock Spoiler is not modkit one
  end ]]

  -- Put the person inside
  if vehicle_model ~= `stretcher` and not locked_and_off and task_inside then
    local allow_enter, message = exports.blrp_core:CheckCanEnterModel(vehicle_model)

    if allow_enter then
      SetPedIntoVehicle(PlayerPedId(), vehicle, -1)
    elseif message ~= '' then
      exports.blrp_core:me().notifyError(message)
    end
  else
    TriggerServerEvent('blrp_vehicles:server:setLocks', network_id, 2)
  end

  if force_unlocked then
    TriggerServerEvent('blrp_vehicles:server:setLocks', network_id, 1)
  end
end)
