<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>Lock‐Pick Hack</title>
  <link rel="stylesheet" href="css/style.css" />
  <link rel="stylesheet"
        href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
  <link rel="stylesheet"
        href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />
</head>
<body>
  <div class="hacking-container" id="hackingContainer">
    <div class="stage-screen" id="stageLockpick" style="display:none;">

      <!-- guide circles -->
      <div class="guide-circle middle"></div>
      <div class="guide-circle inner"></div>

      <!-- circular timer -->
      <svg class="timer-circle" viewBox="0 0 100 100">
        <circle class="timer-bg" cx="50" cy="50" r="45" />
        <circle class="timer-fg" cx="50" cy="50" r="45" />
      </svg>

      <!-- radial dividers -->
      <div id="guideLines"></div>

      <!-- shape rings -->
      <div id="outerRing" class="ring outer"></div>
      <div id="innerRing" class="ring inner"></div>

      <!-- sequence counters -->
      <div id="sequenceCounters" class="sequence-counters"></div>

      <!-- controls -->
      <div class="controls">
        <div id="rotateLeft"  class="triangle-control left">
          <i class="fas fa-play"></i><span>A</span>
        </div>
        <div id="submitBtn"   class="enter-control">
          <span>Enter</span>
        </div>
        <div id="rotateRight" class="triangle-control right">
          <i class="fas fa-play"></i><span>D</span>
        </div>
      </div>
    </div>
  </div>

  <audio id="beep"   src="sounds/check-beep.ogg"></audio>
  <audio id="finish" src="sounds/finish.ogg"></audio>
  <audio id="fail"   src="sounds/fail.ogg"></audio>
  <audio id="timer"  src="sounds/timer.ogg"></audio>
  <audio id="correct"  src="sounds/correct.ogg"></audio>

  <script src="js/app.js"></script>
</body>
</html>
