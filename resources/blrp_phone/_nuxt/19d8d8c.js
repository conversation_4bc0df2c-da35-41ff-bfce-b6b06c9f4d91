(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[214],{

/***/ 1557:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1559);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("34d8399e", content, true, {"sourceMap":false});

/***/ }),

/***/ 1558:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_tabs_vue_vue_type_style_index_0_id_08df9312_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1557);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_tabs_vue_vue_type_style_index_0_id_08df9312_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_tabs_vue_vue_type_style_index_0_id_08df9312_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1559:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".tablet-phone .v-tabs{background-color:transparent!important;margin-top:8px!important;padding:0 4px!important}.tablet-phone .v-tabs .v-tab{background-color:transparent!important;border-radius:8px!important;margin:0 2px!important;min-width:0!important;padding:8px 6px!important;transition:all .2s ease!important}.tablet-phone .v-tabs .v-tab:before{display:none!important}.tablet-phone .v-tabs .v-tab.v-tab--active{background-color:hsla(0,0%,100%,.15)!important;color:#fff!important}.tablet-phone .v-tabs .v-tab.v-tab--active .v-icon{color:inherit!important}.tablet-phone .v-tabs .v-tab:not(.v-tab--active):hover{background-color:hsla(0,0%,100%,.08)!important}.tablet-phone .v-tabs .v-tab .v-icon{font-size:18px!important;margin:0!important}.tablet-phone .v-tabs .v-tab span{margin:0!important;padding:0!important}.tablet-phone .v-tabs .v-tabs-slider-wrapper{display:none!important}.tablet-phone .v-tabs .v-tabs-bar{background-color:transparent!important;border:none!important}.tablet-phone .v-tabs .v-tab{flex:1 1 auto!important;max-width:none!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1560:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList = __webpack_require__(462);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(301);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VMenu/VMenu.js
var VMenu = __webpack_require__(461);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTooltip/VTooltip.js
var VTooltip = __webpack_require__(1522);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-tabs.vue?vue&type=template&id=08df9312










var app_tabsvue_type_template_id_08df9312_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VTabs["a" /* default */], {
    attrs: {
      "grow": ""
    }
  }, _vm._l(_vm.compiledTabs, function (tab) {
    return tab.visible && !tab.check || tab.check && tab.check() ? _c(VTab["a" /* default */], {
      key: tab.name,
      attrs: {
        "to": tab.route
      }
    }, [tab.route ? _c('span', [_c(VTooltip["a" /* default */], {
      attrs: {
        "top": ""
      },
      scopedSlots: _vm._u([{
        key: "activator",
        fn: function fn(_ref) {
          var on = _ref.on,
            attrs = _ref.attrs;
          return [_c('span', _vm._g(_vm._b({}, 'span', attrs, false), on), [tab.icon ? _c(VIcon["a" /* default */], {
            attrs: {
              "color": tab.iconColor,
              "left": "",
              "small": ""
            }
          }, [_vm._v("\n                " + _vm._s(tab.icon) + "\n              ")]) : _vm._e(), _vm._v(" "), tab.name && _vm.mode === 'tablet' ? _c('span', {
            style: {
              color: tab.color
            }
          }, [_vm._v("\n                " + _vm._s(tab.name) + "\n              ")]) : _vm._e()], 1)];
        }
      }], null, true)
    }, [_vm._v(" "), _c('div', [_vm._v("\n            " + _vm._s(tab.name) + "\n          ")])])], 1) : _c(VMenu["a" /* default */], {
      attrs: {
        "bottom": "",
        "left": ""
      },
      scopedSlots: _vm._u([{
        key: "activator",
        fn: function fn(_ref2) {
          var on = _ref2.on,
            attrs = _ref2.attrs;
          return [_c(VBtn["a" /* default */], _vm._g(_vm._b({
            staticClass: "align-self-center mr-4",
            attrs: {
              "text": "",
              "large": "",
              "width": "100%"
            }
          }, 'v-btn', attrs, false), on), [_vm._v("\n          ...\n          "), _c('i', {
            staticClass: "fa-solid fa-menu-down"
          })])];
        }
      }], null, true)
    }, [_vm._v(" "), _c(VList["a" /* default */], _vm._l(tab.children, function (childTab) {
      return _c(VListItem["a" /* default */], {
        key: childTab.name,
        attrs: {
          "to": childTab.route
        }
      }, [childTab.route ? _c('span', [_vm._v("\n            " + _vm._s(childTab.name) + "\n          ")]) : _vm._e()]);
    }), 1)], 1)], 1) : _vm._e();
  }), 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-tabs.vue?vue&type=template&id=08df9312

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(51);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-tabs.vue?vue&type=script&lang=ts


















function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/* harmony default export */ var app_tabsvue_type_script_lang_ts = ({
  name: 'app-tabs',
  props: ['tabs'],
  computed: _objectSpread({
    shouldShowTabNames: function shouldShowTabNames() {
      return this.mode === 'tablet';
    },
    compiledTabs: function compiledTabs() {
      var tabs = Object(toConsumableArray["a" /* default */])(this.tabs);
      var _iterator = _createForOfIteratorHelper(tabs),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var tab = _step.value;
          tab.visible = !tab.requires || this.hasAnyGroup(tab.requires) || tab.requires.length < 1;
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      return tabs;
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    hasAnyGroup: 'auth/hasAnyGroup',
    currentFaction: 'auth/currentFaction',
    mode: 'system/mode'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-tabs.vue?vue&type=script&lang=ts
 /* harmony default export */ var Common_app_tabsvue_type_script_lang_ts = (app_tabsvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./components/Common/app-tabs.vue?vue&type=style&index=0&id=08df9312&prod&lang=scss
var app_tabsvue_type_style_index_0_id_08df9312_prod_lang_scss = __webpack_require__(1558);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-tabs.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_tabsvue_type_script_lang_ts,
  app_tabsvue_type_template_id_08df9312_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_tabs = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 2199:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social/leaderboards/most-replies.vue?vue&type=template&id=01ed8200
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('crudy-table', {
    staticClass: "mt-4",
    attrs: {
      "name": "CitizenSocial",
      "view": "leaderboardsTopRepliers"
    }
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/social/leaderboards/most-replies.vue?vue&type=template&id=01ed8200

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./components/Common/app-tabs.vue + 4 modules
var app_tabs = __webpack_require__(1560);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social/leaderboards/most-replies.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var most_repliesvue_type_script_lang_js = ({
  components: {
    AppTabs: app_tabs["a" /* default */],
    CrudyTable: crudy_table["a" /* default */]
  },
  props: [],
  data: function data() {
    return {
      tabs: [{
        name: 'All Crews',
        route: '/social/groups/all',
        icon: 'fa-regular fa-globe'
      }, {
        name: 'My Crews',
        route: '/social/groups/my',
        icon: 'fa-regular fa-heart'
      }]
    };
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./pages/social/leaderboards/most-replies.vue?vue&type=script&lang=js
 /* harmony default export */ var leaderboards_most_repliesvue_type_script_lang_js = (most_repliesvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/social/leaderboards/most-replies.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  leaderboards_most_repliesvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var most_replies = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);