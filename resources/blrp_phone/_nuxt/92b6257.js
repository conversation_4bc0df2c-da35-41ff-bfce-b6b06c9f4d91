(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[229],{

/***/ 2215:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/town-center/agency-notices.vue?vue&type=template&id=ad419d7a
var render = function render() {
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _c('crudy-table', {
    attrs: {
      "simplified": true,
      "name": "Cad<PERSON>lert",
      "view": "index"
    }
  });
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/town-center/agency-notices.vue?vue&type=template&id=ad419d7a

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/town-center/agency-notices.vue?vue&type=script&lang=ts


/* harmony default export */ var agency_noticesvue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  components: {
    CrudyTable: crudy_table["a" /* default */]
  }
}));
// CONCATENATED MODULE: ./pages/town-center/agency-notices.vue?vue&type=script&lang=ts
 /* harmony default export */ var town_center_agency_noticesvue_type_script_lang_ts = (agency_noticesvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/town-center/agency-notices.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  town_center_agency_noticesvue_type_script_lang_ts,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var agency_notices = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);