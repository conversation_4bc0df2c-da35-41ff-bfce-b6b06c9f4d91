pPhone = { }
P.bindInstance('phone', pPhone)

tPhone = T.getInstance('blrp_phone', 'phone')
tTablet = T.getInstance('blrp_tablet', 'tablet')

pPhone.fetchMyTextThreads = function()
  local character = exports.blrp_core:character(source)

  return MySQL.Sync.fetchAll([[
        WITH RankedMessages AS (
            SELECT
                *,
                ROW_NUMBER() OVER (PARTITION BY transmitter ORDER BY id DESC) as rn
            FROM phone_messages
            WHERE receiver = @phone AND deleted_at IS NULL
        )
        SELECT id, transmitter, receiver, message, time, isRead, owner, deleted_at
        FROM RankedMessages
        WHERE rn = 1
        ORDER BY id DESC
    ]], {
    ['@phone'] = character.get('phone'),
  })
end

pPhone.fetchMyTextMessages = function(targetPhone)
  local character = exports.blrp_core:character(source)

  return MySQL.Sync.fetchAll([[
    SELECT *
    FROM (
        SELECT *
        FROM phone_messages
        WHERE
            (
                (receiver = @phone AND transmitter = @targetPhone)
            )
            AND deleted_at IS NULL
        ORDER BY id DESC
        LIMIT 100
    ) AS recent_messages
    ORDER BY id ASC
  ]], {
    ['@phone'] = character.get('phone'),
    ['@targetPhone'] = targetPhone,
  })
end

pPhone.removeMyTextMessage =  function(messageId)
    MySQL.Sync.execute("UPDATE phone_messages SET deleted_at = NOW() WHERE `id` = @id", {
        ['@id'] = messageId
    })
end

pPhone.removeMyTextThread = function(targetPhone)
    local character = exports.blrp_core:character(source)

    MySQL.Sync.execute("UPDATE phone_messages SET deleted_at = NOW() WHERE `receiver` = @mePhoneNumber and `transmitter` = @phone_number", {
        ['@mePhoneNumber'] = character.get('phone'),
        ['@phone_number'] = targetPhone
    })
end

pPhone.fetchMyContacts = function()
    local character = exports.blrp_core:character(source)
    return MySQL.Sync.fetchAll("SELECT * FROM phone_users_contacts WHERE phone_users_contacts.character_number = @characterNumber", {
        ['@characterNumber'] = tostring(character.get('id'))
    })
end

pPhone.clearMyContacts = function()
    local character = exports.blrp_core:character(source)
    local characterId = tostring(character.get('id'))

    -- Execute the DELETE query to remove all contacts for this character
    MySQL.Async.execute("DELETE FROM phone_users_contacts WHERE character_number = @characterNumber", {
        ['@characterNumber'] = characterId
    }, function(affectedRows)
        local message
        if affectedRows > 0 then
            message = ("Cleared %d contacts for character ID %d"):format(affectedRows, characterId)
        else
            message = ("No contacts found to clear for character ID %d"):format(characterId)
        end

        -- Log the action
        character.log('PHONE', message)
    end)
end

pPhone.addMyContact = function(targetPhone, name)
    local character = exports.blrp_core:character(source)

    MySQL.Async.insert("INSERT INTO phone_users_contacts (`character_number`, `number`,`display`, `favorite`, `silence_texts`, `silence_calls`) VALUES(@character_number, @number, @display, @favorite, @silence_texts, @silence_calls)", {
        ['@character_number'] = character.get('id'),
        ['@number'] = targetPhone,
        ['@favorite'] = 0,
        ['@silence_texts'] = 0,
        ['@silence_calls'] = 0,
        ['@display'] = name,
    },function()
         --notifyContactChange(sourcePlayer, char_number)
    end)
end

pPhone.shareMyContact = function()
  local character = exports.blrp_core:character(source)
  local target_source = character.targetClosePlayer(3)

  if not target_source then
    character.notifyError('No people nearby')
    return
  end

  local target_character = exports.blrp_core:character(target_source)

  if not target_character then
    character.notifyError('No people nearby')
    return
  end

  if not target_character.request('Someone is trying to share their phone number with you, accept?') then
    return
  end

  TriggerEvent('gcPhone:sendMessage_Anonymous', character.get('phone'), target_character.get('phone'), 'Hi there, sharing my contact information!')
end

pPhone.fetchMyCallHistory = function()
    local character = exports.blrp_core:character(source)

    return MySQL.Sync.fetchAll("SELECT * FROM phone_calls WHERE phone_calls.owner = @num ORDER BY time DESC LIMIT 40", {
        ['@num'] = character.get('phone')
    })
end

pPhone.phoneExists = function(phone)
    local results = MySQL.Sync.fetchAll("SELECT id FROM characters WHERE phone = @phone", {
        ['@phone'] = phone
    })

    if results[1] then return true end

    return false
end

pPhone.hasPhoneInInventory = function()
  local tint_value = -1
  local html_color = 'silver'
  local item_id = exports.blrp_core:character(source).hasItemFromCategory('phones')
  local model_injection = false

  if item_id then
    local item_definition = exports.blrp_core:GetItemDefinition(item_id)

    if item_definition and item_definition.tint_value then
      tint_value = item_definition.tint_value
    end

    if item_definition and item_definition.html_color then
      html_color = item_definition.html_color
    end

    if item_definition and item_definition.injected_model then
        model_injection = item_definition.injected_model
    end
  end


  return item_id, tint_value, model_injection, html_color
end

pPhone.updateContact = function(id, data)
    --print('doin update id: ', id, 'values:', data.phone, data.display, data.silence_texts, data.silence_calls, data.color, data.avatar_url, data.favorite)
    --print('decoded', json.encode(data))

    MySQL.Async.insert("UPDATE phone_users_contacts SET number = @number, display = @display, favorite = @favorite, silence_texts = @silence_texts, silence_calls = @silence_calls, color = @color,  avatar_url = @avatar_url, favorite = @favorite  WHERE id = @id", {
        ['@number'] = data.phone,
        ['@display'] = data.display,
        ['@silence_texts'] = data.silence_texts or 0,
        ['@silence_calls'] = data.silence_calls or 0,
        ['@favorite'] = data.favorite or 0,
        ['@avatar_url'] = data.avatar_url,
        ['@color'] = data.color,
        ['@id'] = id,
    })
end

pPhone.removeMyContact = function(id)
    local character = exports.blrp_core:character(source)

    MySQL.Async.execute("DELETE FROM phone_users_contacts WHERE `character_number` = @character_number AND `id` = @id", {
        ['@character_number'] = character.get('id'),
        ['@id'] = id,
    })
end



--- Final sending of a message
---
--- When a text message is sent
---     The sender is sent the message, marked as owner
---     The receiver is sent the message, not marked as owner
pPhone.sendTextMessage = function(toPhone, content, fromPhone, extra)
    if not extra then extra = {} end

    local isFromRealPerson = false
    local fromCharacter = nil

    toPhone = __correctPhoneNumber(toPhone)
    local fromSource = exports.blrp_core:GetSourceByPhone(fromPhone)
    local toSource = exports.blrp_core:GetSourceByPhone(toPhone)

    if fromSource then
        fromCharacter = exports.blrp_core:character(fromSource)
    end

    if not fromPhone then
        return
        -- return print('[pPhone.sendTextMessage] Error: From sending message to ' .. toPhone .. '. to Phone number must be provided')
    end

    if not content then
        return
        -- return print('[pPhone.sendTextMessage Error: called without message content')
    end

    if #content > 255 then
        if fromCharacter then
            fromCharacter.notify('Message is too big to send')
        else
          print('[pPhone.sendTextMessage Error: Tried to send a message that was too big', content)
        end

        return
    end

    -- print('[pPhone.sendTextMessage] Sending message from ' .. fromPhone .. ' to ' .. toPhone)


    -- If the from phone number matches the from character's phone number
    if fromCharacter and fromPhone == fromCharacter.get('phone') then
        isFromRealPerson = true
    end

    if content == '%pos%' then
        content = json.encode({
          embed = 'position',
          packet = extra.position,
        })
    end

    if content == '%bank%' then
        content = json.encode({
          embed = 'bank',
          packet = extra.bank_details,
        })
    end

    -- Add text message to database and send client update to from target
    if fromCharacter and isFromRealPerson then

        -- We don't want to save messages if the sender is not a real person
        local addedOwnerMessage = _addPhoneMessage(toPhone, fromPhone, content, true)

        TriggerEvent('blrp_phone:server:events:characterSentMessage', fromCharacter.source, fromPhone, toPhone, content)

        -- print(string.format('dispatching message sent update to sender (who is logged in, and a real person) [%s]', fromPhone))

        tPhone.dispatchVueState(fromCharacter.source, { 'text/addMessage', {
            fromPhone = fromPhone,
            toPhone = toPhone,
            message = addedOwnerMessage,
        }})
    else
        -- print(string.format('message sent from [%s] was sent as a fake phone number, ignoring save', fromPhone))
    end

    -- Add text message to database and send client update to to target
    local addedTargetMessage = _addPhoneMessage(fromPhone, toPhone, content, false)

    -- If player is online -- and (fromCharacter and fromCharacter.source ~= toSource)
    if toSource  and tonumber(toSource) > 0  then
        -- print(string.format('dispatching message sent update to receiver (who is logged in, and a real person) [%s]', toPhone))

        tPhone.dispatchVueState(toSource, { 'text/addMessage', {
            fromPhone = fromPhone,
            toPhone = toPhone,
            message = addedTargetMessage,
        }})
    end

    -- If they are offline
    if not toSource then
        -- If they phone number doesn't exist
        if not pPhone.phoneExists(toPhone) then
            -- return pPhone.sendTextMessage(fromPhone, 'The message sent was unable to be delivered to the specified phone number.', toPhone)
        end
    end
end

AddEventHandler('gcPhone:sendMessage_Anonymous', function(fromPhone, toPhone, message)
    pPhone.sendTextMessage(toPhone, message, fromPhone)
end)

pPhone.setMyReadPhoneNumber = function(phone)
    local fromCharacter = exports.blrp_core:character(source)

    MySQL.Sync.execute("UPDATE phone_messages SET phone_messages.isRead = 1 WHERE phone_messages.receiver = @receiver AND phone_messages.transmitter = @transmitter", {
        ['@receiver'] = fromCharacter.get('phone'),
        ['@transmitter'] = phone
    })
end

pPhone.removeAllTextMessages = function(phone)
    MySQL.Sync.execute("UPDATE phone_messages SET deleted_at = NOW() WHERE `receiver` = @mePhoneNumber", {
        ['@mePhoneNumber'] = phone
    })
end


-- Phone callables
pPhone.sendMessageNoNumberFromClient = function(extra)
    -- print('sendMessageNoNumberFromClient', extra)

    if not extra then extra = {} end

    local character = exports.blrp_core:character(source)

    local fromPhoneNumber = character.get('phone')
    local toPhoneNumber = character.prompt('Enter phone number', '')
    local message character.prompt('Enter message content', '')

    if fromPhoneNumber and toPhoneNumber and message then
        pPhone.sendTextMessage(toPhoneNumber, message, fromPhoneNumber, extra)
    else
        error('sendMessageNoNumberFromClient failed, invalid data given')
    end
end

pPhone.addContactExistingThreadFromClient = function(phone)
    local character = exports.blrp_core:character(source)

    local name = character.prompt('Contact name', '')

    if name then
        pPhone.addMyContact(character.source, phone, name)
    else
        error('addContactExistingThreadFromClient failed, invalid data given')
    end
end


-- Database transactions
_addPhoneMessage = function(fromPhone, toPhone, message, isOwner)
    local bindings = {
        id = nil,
        transmitter = fromPhone,
        receiver = toPhone,
        message = message,
        time = nil,
        isRead = isOwner,
        owner = isOwner,
        deleted_at = nil,
    }

    local insert_id = MySQL.Sync.insert('INSERT INTO phone_messages (`transmitter`, `receiver`,`message`, `isRead`,`owner`) VALUES(@transmitter, @receiver, @message, @isRead, @owner)', bindings)

    bindings.id = insert_id
    bindings.time = os.date('%Y-%m-%d %H:%M:%S')

    return bindings
end

__correctPhoneNumber = function(phone)
    tostring(phone)

    if not string.find(phone, '-') and #phone == 7 then
        phone = phone:sub(1, 3) .. '-' .. phone:sub(4, 3)
    end

    return phone
end
