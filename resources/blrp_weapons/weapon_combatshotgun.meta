<?xml version="1.0" encoding="UTF-8"?>

<CWeaponInfoBlob>
  <SlotNavigateOrder>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="285" />
          <Entry>SLOT_COMBATSHOTGUN</Entry>
        </Item>
      </WeaponSlots>
    </Item>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="285" />
          <Entry>SLOT_COMBATSHOTGUN</Entry>
        </Item>
      </WeaponSlots>
    </Item>
  </SlotNavigateOrder>
  <SlotBestOrder>
    <WeaponSlots>
      <Item>
        <OrderNumber value="143" />
        <Entry>SLOT_COMBATSHOTGUN</Entry>
      </Item>
    </WeaponSlots>
  </SlotBestOrder>
  <TintSpecValues>
    <Item>
      <Name>TINT_COMBATSHOTGUN</Name>
      <Tints>
        <Item>
          <SpecFresnel value="0.750000" />
          <SpecFalloffMult value="40.000000" />
          <SpecIntMult value="0.750000" />
          <Spec2Factor value="80.000000" />
          <Spec2ColorInt value="1.000000" />
          <Spec2Color value="0x00FDFAEC" />
        </Item>
        <Item>
          <SpecFresnel value="0.750000" />
          <SpecFalloffMult value="40.000000" />
          <SpecIntMult value="0.750000" />
          <Spec2Factor value="80.000000" />
          <Spec2ColorInt value="1.000000" />
          <Spec2Color value="0x00E7E3D1" />
        </Item>
        <Item>
          <SpecFresnel value="0.750000" />
          <SpecFalloffMult value="40.000000" />
          <SpecIntMult value="0.750000" />
          <Spec2Factor value="80.000000" />
          <Spec2ColorInt value="1.000000" />
          <Spec2Color value="0x00FFBA00" />
        </Item>
        <Item>
          <SpecFresnel value="0.750000" />
          <SpecFalloffMult value="40.000000" />
          <SpecIntMult value="0.750000" />
          <Spec2Factor value="80.000000" />
          <Spec2ColorInt value="1.000000" />
          <Spec2Color value="0x00FFF9F7" />
        </Item>
        <Item>
          <SpecFresnel value="0.750000" />
          <SpecFalloffMult value="40.000000" />
          <SpecIntMult value="0.750000" />
          <Spec2Factor value="80.000000" />
          <Spec2ColorInt value="1.000000" />
          <Spec2Color value="0x00DBD5BB" />
        </Item>
        <Item>
          <SpecFresnel value="0.750000" />
          <SpecFalloffMult value="40.000000" />
          <SpecIntMult value="0.750000" />
          <Spec2Factor value="80.000000" />
          <Spec2ColorInt value="1.000000" />
          <Spec2Color value="0x00CFE7FC" />
        </Item>
        <Item>
          <SpecFresnel value="0.750000" />
          <SpecFalloffMult value="40.000000" />
          <SpecIntMult value="0.750000" />
          <Spec2Factor value="80.000000" />
          <Spec2ColorInt value="1.000000" />
          <Spec2Color value="0x00FBEFDE" />
        </Item>
        <Item>
          <SpecFresnel value="0.750000" />
          <SpecFalloffMult value="40.000000" />
          <SpecIntMult value="0.750000" />
          <Spec2Factor value="80.000000" />
          <Spec2ColorInt value="1.000000" />
          <Spec2Color value="0x00FFFFFF" />
        </Item>
      </Tints>
    </Item>
  </TintSpecValues>
  <FiringPatternAliases />
  <UpperBodyFixupExpressionData />
  <AimingInfos />
  <Infos>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos>
        <Item type="CWeaponInfo">
          <Name>WEAPON_COMBATSHOTGUN</Name>
          <Model>w_sg_pumpshotgunh4</Model>
          <Audio>audio_item_combat_shotgun</Audio>
          <Slot>SLOT_COMBATSHOTGUN</Slot>
          <DamageType>BULLET</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>INSTANT_HIT</FireType>
          <WheelSlot>WHEEL_SHOTGUN</WheelSlot>
          <Group>GROUP_SHOTGUN</Group>
          <AmmoInfo ref="AMMO_SHOTGUN" />
          <AimingInfo ref="RIFLE_LO_BASE_STRAFE" />
          <ClipSize value="6" />
          <AccuracySpread value="10.000000" />
          <AccurateModeAccuracyModifier value="0.300000" />
          <RunAndGunAccuracyModifier value="45.000000" />
          <RunAndGunAccuracyMinOverride value="12.000000" />
          <RecoilAccuracyMax value="1.500000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="0.000000" />
          <MaxHeadShotDistancePlayer value="0.000000" />
          <HeadShotDamageModifierPlayer value="0.000000" />
          <Damage value="10.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <VehicleDamageModifier value="1.000000" />
          <Force value="16.000000" />
          <ForceHitPed value="50.000000" />
          <ForceHitVehicle value="350.000000" />
          <ForceHitFlyingHeli value="120.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="20.000000" />
              <ForceBack value="20.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="25.000000" />
              <ForceBack value="20.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="20.000000" />
              <ForceBack value="20.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="20.000000" />
              <ForceBack value="20.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="500.000000" />
          <Penetration value="0.010000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="14" />
          <BatchSpread value="0.08500000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="1.000000" />
          <AnimReloadRate value="0.5600000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.650000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="0.350000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.375000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_SHOTGUN</EffectGroup>
            <FlashFx>muz_shotgun</FlashFx>
            <FlashFxAlt />
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx>muz_smoking_barrel_shotgun</MuzzleSmokeFx>
            <MuzzleSmokeFxFP>muz_smoking_barrel_fp</MuzzleSmokeFxFP>
            <MuzzleSmokeFxMinLevel value="0.300000" />
            <MuzzleSmokeFxIncPerShot value="0.100000" />
            <MuzzleSmokeFxDecPerSec value="0.200000" />
            <MuzzleOverrideOffset x="0.000000" y="0.000000" z="0.000000" />
            <MuzzleUseProjTintColour value="false" />
            <ShellFx>eject_shotgun</ShellFx>
            <ShellFxFP>eject_shotgun_fp</ShellFxFP>
            <TracerFx />
            <PedDamageHash>ShotgunLarge</PedDamageHash>
            <TracerFxChanceSP value="0.150000" />
            <TracerFxChanceMP value="0.750000" />
            <TracerFxIgnoreCameraIntersection value="false" />
            <FlashFxChanceSP value="1.000000" />
            <FlashFxChanceMP value="1.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="0.700000" />
            <FlashFxLightEnabled value="true" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.000000" />
            <FlashFxLightRGBAMin x="255.000000" y="93.000000" z="25.000000" />
            <FlashFxLightRGBAMax x="255.000000" y="100.000000" z="50.000000" />
            <FlashFxLightIntensityMinMax x="1.000000" y="2.000000" />
            <FlashFxLightRangeMinMax x="2.000000" y="2.500000" />
            <FlashFxLightFalloffMinMax x="1024.000000" y="1536.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="200" />
          <InitialRumbleIntensity value="0.950000" />
          <InitialRumbleIntensityTrigger value="0.950000" />
          <RumbleDuration value="150" />
          <RumbleIntensity value="0.850000" />
          <RumbleIntensityTrigger value="0.950000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="150" />
          <InitialRumbleIntensityFps value="0.950000" />
          <RumbleDurationFps value="150" />
          <RumbleIntensityFps value="0.850000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.000000" />
          <LockOnRange value="35.000000" />
          <WeaponRange value="40.000000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <BulletDirectionPitchOffset value="0.000000" />
          <BulletDirectionPitchHomingOffset value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="5.000000" />
          <DamageFallOffRangeMax value="40.000000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>SHOTGUN_AIM_CAMERA</DefaultCameraHash>
          <AimCameraHash />
          <FireCameraHash />
          <CoverCameraHash>SHOTGUN_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash>SHOTGUN_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
          <CinematicShootingCameraHash>SHOTGUN_CINEMATIC_SHOOTING_CAMERA</CinematicShootingCameraHash>
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <PovTurretCameraHash />
          <CameraFov value="45.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="30.000000" />
          <FirstPersonScopeAttachmentFov value="0.000000" />
          <FirstPersonDrivebyIKOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="1.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="-2.000000" y="0.000000" z="1.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.045000" z="-0.002000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.000000" z="0.015000" />
          <FirstPersonScopeRotationOffset x="-0.450000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="-0.050000" y="0.000000" z="-0.050000" />
          <FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="-0.075000" />
          <FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.100000" z="-0.050000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.035000" y="0.000000" z="-0.075000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="-0.050000" y="0.100000" z="-0.050000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <FirstPersonScopeAttachmentData />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash>SHOTGUN_RECOIL_SHAKE</RecoilShakeHash>
          <RecoilShakeHashFirstPerson>FPS_SHOTGUN_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="3.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.020000" />
          <IkRecoilDisplacementScope value="0.000005" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <ReticuleHudPositionOffsetForPOVTurret x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.265000" y="0.175000" z="0.550000" />
          <AimProbeLengthMin value="0.400000" />
          <AimOffsetMax x="0.150000" y="-0.175000" z="0.500000" />
          <AimProbeLengthMax value="0.350000" />
          <AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000" />
          <AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000" />
          <AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000" />
          <AimOffsetMinFPSLT x="0.180000" y="0.231000" z="0.669000" />
          <AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.409000" />
          <AimOffsetMinFPSRNG x="0.120000" y="0.275000" z="0.509000" />
          <AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.518000" />
          <AimOffsetMinFPSScope x="0.090000" y="0.078000" z="0.531000" />
          <AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000" />
          <AimOffsetEndPosMinFPSIdle x="-0.284000" y="0.612000" z="-0.205000" />
          <AimOffsetEndPosMedFPSIdle x="-0.178000" y="0.639000" z="0.616000" />
          <AimOffsetEndPosMaxFPSIdle x="-0.217000" y="-0.096000" z="0.887000" />
          <AimOffsetEndPosMinFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMedFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMaxFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeRadiusOverrideFPSIdle value="0.000000" />
          <AimProbeRadiusOverrideFPSIdleStealth value="0.000000" />
          <AimProbeRadiusOverrideFPSLT value="0.000000" />
          <AimProbeRadiusOverrideFPSRNG value="0.000000" />
          <AimProbeRadiusOverrideFPSScope value="0.000000" />
          <TorsoAimOffset x="-1.200000" y="0.550000" />
          <TorsoCrouchedAimOffset x="0.100000" y="0.120000" />
          <LeftHandIkOffset x="0.200000" y="0.050000" z="-0.050000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="0.000000" />
          <ReticuleStyleHash>WEAPONTYPE_SHOTGUN</ReticuleStyleHash>
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_COMBATSHOTGUN</PickupHash>
          <MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
          <HumanNameHash>WT_CMBSHGN</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>CMBSHGN</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Shotgun</NmShotTuningSet>
          <AttachPoints>
            <Item>
              <AttachBone>WAPClip</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_COMBATSHOTGUN_CLIP_01</Name>
                  <Default value="true" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPFlshLasr_2</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_AR_FLSH</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPSupp</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_AR_SUPP</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
          </AttachPoints>
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>CarriedInHand ApplyBulletForce Gun CanLockonOnFoot CanLockonInVehicle CanFreeAim TwoHanded AnimReload AnimCrouchFire UsableOnFoot UsableInCover UseLoopedReloadAnim OnlyFireOneShotPerTriggerPress HasLowCoverReloads HasLowCoverSwaps ProcessGripAnim TorsoIKForWeaponBlock LongWeapon UseFPSAimIK UseFPSSecondaryMotion AllowFireInterruptWhenReady ResponsiveRecoilRecovery</WeaponFlags>
          <TintSpecValues ref="TINT_COMBATSHOTGUN" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="DEFAULT" />
          <AmmoDiminishingRate value="3" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="1.000000" />
          <StealthFiringBreathingAdditiveWeight value="1.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="1.000000" />
          <StealthFiringLeanAdditiveWeight value="1.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="75" />
          <HudSpeed value="48" />
          <HudCapacity value="6" />
          <HudAccuracy value="25" />
          <HudRange value="20" />
          <VehicleAttackAngle value="25.000000" />
          <TorsoIKAngleLimit value="-1.000000" />
          <MeleeDamageMultiplier value="-1.000000" />
          <MeleeRightFistTargetHealthDamageScaler value="-1.000000" />
          <AirborneAircraftLockOnMultiplier value="1.000000" />
          <ArmouredVehicleGlassDamageOverride value="12.500000" />
          <CamoDiffuseTexIdxs />
          <RotateBarrelBone />
          <RotateBarrelBone2 />
          <FrontClearTestParams>
            <ShouldPerformFrontClearTest value="false" />
            <ForwardOffset value="0.000000" />
            <VerticalOffset value="0.000000" />
            <HorizontalOffset value="0.000000" />
            <CapsuleRadius value="0.000000" />
            <CapsuleLength value="0.000000" />
          </FrontClearTestParams>
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos />
    </Item>
  </Infos>
  <VehicleWeaponInfos />
  <WeaponGroupDamageForArmouredVehicleGlass />
  <Name>DLC - Combat Shotgun</Name>
</CWeaponInfoBlob>