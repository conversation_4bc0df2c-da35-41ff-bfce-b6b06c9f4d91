<?xml version="1.0" encoding="UTF-8"?>

<CWeaponComponentInfoBlob>
  <Data>
  </Data>
  <Infos>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SERVICEPISTOL_9MM_CLIP_01</Name>
      <Model>w_pi_servicepistol_mag1</Model>
      <LocName>WCT_CLIP1</LocName>
      <LocDesc>WCD_P_CLIP1</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="17" />
      <ReloadData ref="RELOAD_DEFAULT_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SERVICEPISTOL_9MM_CLIP_02</Name>
      <Model>w_pi_servicepistol_mag2</Model>
      <LocName>WCT_CLIP2</LocName>
      <LocDesc>WCD_P_CLIP2</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="33" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="17" />
      <ReloadData ref="RELOAD_LARGE_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SERVICEPISTOL_9MM_CLIP_03</Name>
      <Model>w_pi_servicepistol_mag3</Model>
      <LocName>WCT_CLIP_DRM</LocName>
      <LocDesc>WCD_P_CLIP2</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="70" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="20" />
      <ReloadData ref="RELOAD_LARGE_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SERVICEPISTOL_AUTO_CLIP_01</Name>
      <Model>w_pi_servicepistol_auto_mag1</Model>
      <LocName>WCT_CLIP1</LocName>
      <LocDesc>WCD_P_CLIP1</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="17" />
      <ReloadData ref="RELOAD_DEFAULT_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SERVICEPISTOL_AUTO_CLIP_02</Name>
      <Model>w_pi_servicepistol_auto_mag2</Model>
      <LocName>WCT_CLIP2</LocName>
      <LocDesc>WCD_P_CLIP2</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="33" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="33" />
      <ReloadData ref="RELOAD_LARGE_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SERVICEPISTOL_AUTO_CLIP_03</Name>
      <Model>w_pi_servicepistol_auto_mag3</Model>
      <LocName>WCT_CLIP_DRM</LocName>
      <LocDesc>WCD_P_CLIP2</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="70" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="50" />
      <ReloadData ref="RELOAD_LARGE_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SERVICEPISTOL_45_CLIP_01</Name>
      <Model>w_pi_servicepistol_mag1</Model>
      <LocName>WCT_CLIP1</LocName>
      <LocDesc>WCD_P_CLIP1</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="18" />
      <ReloadData ref="RELOAD_DEFAULT_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SERVICEPISTOL_45_CLIP_02</Name>
      <Model>w_pi_servicepistol_mag2</Model>
      <LocName>WCT_CLIP2</LocName>
      <LocDesc>WCD_P_CLIP2</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="33" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="19" />
      <ReloadData ref="RELOAD_LARGE_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SERVICEPISTOL_45_CLIP_03</Name>
      <Model>w_pi_servicepistol_mag3</Model>
      <LocName>WCT_CLIP_DRM</LocName>
      <LocDesc>WCD_P_CLIP2</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="70" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="40" />
      <ReloadData ref="RELOAD_LARGE_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentSuppressorInfo">
      <Name>COMPONENT_SERVICEPISTOL_SUPP</Name>
      <Model>w_pi_servicepistol_supp</Model>
      <LocName>WCT_SUPP</LocName>
      <LocDesc>WCD_PI_SUPP</LocDesc>
      <AttachBone>AAPSupp</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="CWeaponDamageModifier">
        <DamageModifier value="1.000000" />
      </DamageModifier>
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="-5" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="-5" />
      <MuzzleBone>Gun_SuMuzzle</MuzzleBone>
      <FlashFx>muz_pistol_silencer</FlashFx>
    </Item>
    <Item type="CWeaponComponentSuppressorInfo">
      <Name>COMPONENT_SERVICEPISTOL_COMP</Name>
	  <Model>w_pi_servicepistol_comp</Model>
      <LocName>WCT_COMP</LocName>
      <LocDesc>WCD_COMP</LocDesc>
      <AttachBone>AAPSupp</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="5" />
      <HudRange value="0" />
      <MuzzleBone>Gun_SuMuzzle</MuzzleBone>
	  <FlashFX />
	  <ShouldSilence value="false" />
      <RecoilShakeAmplitudeModifier value="0.500000" />
    </Item>	
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_FLSH</Name>
      <Model>w_pi_servicepistol_flsh</Model>
      <LocName>WCT_FLASH</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="6.000000" />
      <MainLightColor value="0xFFC9C9FF" />
      <MainLightRange value="30.000000" />
      <MainLightFalloffExponent value="32.000000" />
      <MainLightInnerAngle value="0.000000" />
      <MainLightOuterAngle value="20.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.100000" />
      <MainLightVolumeExponent value="70.000000" />
      <MainLightVolumeOuterColor value="0xFF050E3B" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="4.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="8.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_LSR_R</Name>
      <Model>w_pi_servicepistol_lsr</Model>
      <LocName>WCT_SRVPSTL_LSR_R</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="12.000000" />
      <MainLightColor value="0xFFFF0000" />
      <MainLightRange value="50.000000" />
      <MainLightFalloffExponent value="64.000000" />
      <MainLightInnerAngle value="5.000000" />
      <MainLightOuterAngle value="0.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.001300" />
      <MainLightVolumeExponent value="64.000000" />
      <MainLightVolumeOuterColor value="0xFFFF0000" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="0.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="1.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_LSR_G</Name>
      <Model>w_pi_servicepistol_lsr</Model>
      <LocName>WCT_SRVPSTL_LSR_G</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="12.000000" />
      <MainLightColor value="0xFF00FF00" />
      <MainLightRange value="50.000000" />
      <MainLightFalloffExponent value="64.000000" />
      <MainLightInnerAngle value="5.000000" />
      <MainLightOuterAngle value="0.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.001300" />
      <MainLightVolumeExponent value="64.000000" />
      <MainLightVolumeOuterColor value="0xFF00FF00" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="0.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="1.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_LSR_B</Name>
      <Model>w_pi_servicepistol_lsr</Model>
      <LocName>WCT_SRVPSTL_LSR_B</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="12.000000" />
      <MainLightColor value="0xFF0080FF" />
      <MainLightRange value="50.000000" />
      <MainLightFalloffExponent value="64.000000" />
      <MainLightInnerAngle value="5.000000" />
      <MainLightOuterAngle value="0.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.001300" />
      <MainLightVolumeExponent value="64.000000" />
      <MainLightVolumeOuterColor value="0xFF0080FF" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="0.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="1.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_LSR_M</Name>
      <Model>w_pi_servicepistol_lsr</Model>
      <LocName>WCT_SRVPSTL_LSR_M</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="12.000000" />
      <MainLightColor value="0xFFFF00FF" />
      <MainLightRange value="50.000000" />
      <MainLightFalloffExponent value="64.000000" />
      <MainLightInnerAngle value="5.000000" />
      <MainLightOuterAngle value="0.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.001300" />
      <MainLightVolumeExponent value="64.000000" />
      <MainLightVolumeOuterColor value="0xFFFF00FF" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="0.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="1.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_LSR_Y</Name>
      <Model>w_pi_servicepistol_lsr</Model>
      <LocName>WCT_SRVPSTL_LSR_Y</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="12.000000" />
      <MainLightColor value="0xFFFFFF00" />
      <MainLightRange value="50.000000" />
      <MainLightFalloffExponent value="64.000000" />
      <MainLightInnerAngle value="5.000000" />
      <MainLightOuterAngle value="0.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.001300" />
      <MainLightVolumeExponent value="64.000000" />
      <MainLightVolumeOuterColor value="0xFFFFFF00" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="0.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="1.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_LSR2_R</Name>
      <Model>w_pi_servicepistol_lsr2</Model>
      <LocName>WCT_SRVPSTL_LSR_R</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="12.000000" />
      <MainLightColor value="0xFFFF0000" />
      <MainLightRange value="50.000000" />
      <MainLightFalloffExponent value="64.000000" />
      <MainLightInnerAngle value="5.000000" />
      <MainLightOuterAngle value="0.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.001300" />
      <MainLightVolumeExponent value="64.000000" />
      <MainLightVolumeOuterColor value="0xFFFF0000" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="0.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="1.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_LSR2_G</Name>
      <Model>w_pi_servicepistol_lsr2</Model>
      <LocName>WCT_SRVPSTL_LSR_G</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="12.000000" />
      <MainLightColor value="0xFF00FF00" />
      <MainLightRange value="50.000000" />
      <MainLightFalloffExponent value="64.000000" />
      <MainLightInnerAngle value="5.000000" />
      <MainLightOuterAngle value="0.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.001300" />
      <MainLightVolumeExponent value="64.000000" />
      <MainLightVolumeOuterColor value="0xFF00FF00" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="0.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="1.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_LSR2_B</Name>
      <Model>w_pi_servicepistol_lsr2</Model>
      <LocName>WCT_SRVPSTL_LSR_B</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="12.000000" />
      <MainLightColor value="0xFF0080FF" />
      <MainLightRange value="50.000000" />
      <MainLightFalloffExponent value="64.000000" />
      <MainLightInnerAngle value="5.000000" />
      <MainLightOuterAngle value="0.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.001300" />
      <MainLightVolumeExponent value="64.000000" />
      <MainLightVolumeOuterColor value="0xFF0080FF" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="0.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="1.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_LSR2_M</Name>
      <Model>w_pi_servicepistol_lsr2</Model>
      <LocName>WCT_SRVPSTL_LSR_M</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="12.000000" />
      <MainLightColor value="0xFFFF00FF" />
      <MainLightRange value="50.000000" />
      <MainLightFalloffExponent value="64.000000" />
      <MainLightInnerAngle value="5.000000" />
      <MainLightOuterAngle value="0.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.001300" />
      <MainLightVolumeExponent value="64.000000" />
      <MainLightVolumeOuterColor value="0xFFFF00FF" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="0.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="1.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentFlashLightInfo">
      <Name>COMPONENT_SERVICEPISTOL_LSR2_Y</Name>
      <Model>w_pi_servicepistol_lsr2</Model>
      <LocName>WCT_SRVPSTL_LSR_Y</LocName>
      <LocDesc>WCD_FLASH</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
	  <ApplyWeaponTint value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <MainLightIntensity value="12.000000" />
      <MainLightColor value="0xFFFFFF00" />
      <MainLightRange value="50.000000" />
      <MainLightFalloffExponent value="64.000000" />
      <MainLightInnerAngle value="5.000000" />
      <MainLightOuterAngle value="0.000000" />
      <MainLightCoronaIntensity value="3.000000" />
      <MainLightCoronaSize value="0.200000" />
      <MainLightVolumeIntensity value="0.300000" />
      <MainLightVolumeSize value="0.001300" />
      <MainLightVolumeExponent value="64.000000" />
      <MainLightVolumeOuterColor value="0xFFFFFF00" />
      <MainLightShadowFadeDistance value="10.000000" />
      <MainLightSpecularFadeDistance value="10.000000" />
      <SecondaryLightIntensity value="0.000000" />
      <SecondaryLightColor value="0xFFFFFFFF" />
      <SecondaryLightRange value="6.000000" />
      <SecondaryLightFalloffExponent value="64.000000" />
      <SecondaryLightInnerAngle value="0.000000" />
      <SecondaryLightOuterAngle value="40.000000" />
      <SecondaryLightVolumeIntensity value="0.300000" />
      <SecondaryLightVolumeSize value="0.400000" />
      <SecondaryLightVolumeExponent value="24.000000" />
      <SecondaryLightVolumeOuterColor value="0xFF000F85" />
      <SecondaryLightFadeDistance value="10.000000" />
      <fTargetDistalongAimCamera value="1.000000" />
      <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
      <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
      <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
    </Item>
    <Item type="CWeaponComponentScopeInfo">
      <Name>COMPONENT_SERVICEPISTOL_SIGHT</Name>
	  <Model>w_pi_servicepistol_sight</Model>
      <LocName>WCT_SRVPSTL_SIGHT</LocName>
      <LocDesc>WCD_SCOPE_PI</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="5" />
      <HudRange value="0" />
	  <CameraHash />
      <RecoilShakeAmplitude value="0.280000" />
      <ExtraZoomFactorForAccurateMode value="1.200000" />
      <Reticule value="0" />
      <ReticuleHash />
	  <SpecialScopeType />
    </Item>
    <Item type="CWeaponComponentScopeInfo">
      <Name>COMPONENT_SERVICEPISTOL_NIGHTSIGHT</Name>
	    <Model>w_pi_servicepistol_nightsight</Model>
      <LocName>WCT_SRVPSTL_NIGHTSIGHT</LocName>
      <LocDesc>WCD_SCOPE_PI</LocDesc>
      <AttachBone>AAPFlsh</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="5" />
      <HudRange value="0" />
	    <CameraHash />
      <ExtraZoomFactorForAccurateMode value="1.000000" />
      <Reticule value="0" />
      <ReticuleHash />
	  <SpecialScopeType />
    </Item>
  </Infos>
  <InfoBlobName>DLC - Glocks</InfoBlobName>
</CWeaponComponentInfoBlob>