ESX = nil
QBCore = nil

pVehicles = P.getInstance('blrp_vehicles', 'vehicles')

CreateThread(function()
    if Config.FrameWork == 1 then
        ESX = GetEsxObject()
    end

    if Config.FrameWork == 2 then
        QBCore = Config.GetQBCoreObject()

        ESX = {}
        ESX.Game = {}
        ESX.Game.GetVehicleProperties = function(vehicle)
            return QBCore.Functions.GetVehicleProperties(vehicle)
        end
    end
end)


function GetVehicleVIN(vehicle)
  local vehicleNetwork = NetworkGetNetworkIdFromEntity(vehicle)
  local VehicleVIN = pVehicles.GetVehicleVINFromNetworkID({ vehicleNetwork })
  if not VehicleVIN then
    VehicleVIN = exports.blrp_vehicles:GetVehicleNumberPlateTextTrimmed(vehicle)
  end

  return VehicleVIN
end

-- this will send information to server.
function CheckPlayerCar(vehicle)
    if exports.blrp_core:me().isInComa() or exports.blrp_core:me().isHandcuffed() then
        return
    end

    if exports.blrp_phone:IsPhoneInputFocused() then
        return false
    end

    if exports.blrp_core:IsInterfaceOpen() then
        return
    end
    local vehicle = GetVehiclePedIsIn(PlayerPedId())
    local plate = GetVehicleVIN(vehicle)

    local model = GetEntityModel(vehicle)

  if model == GetHashKey('pbus2') then -- This could be better by checking the perm that business's asasign, but is that stored on the client??
      if exports.blrp_core:me().hasGroup('Vanilla Unicorn')
      or exports.blrp_core:me().hasGroup('Bahama Mamas')
      or exports.blrp_core:me().hasGroup('TequiLaLa') then
        TriggerServerEvent("rcore_radiocar:openUI", plate, model)
    end

    return
  end

    TriggerServerEvent("rcore_radiocar:openUI", plate, model)
end

function GetVehiclePlate()
    if ESX then
        local spz = ESX.Game.GetVehicleProperties(GetVehiclePedIsIn(PlayerPedId())).plate
        return spz
    else
      local vehicle = GetVehiclePedIsIn(PlayerPedId())
      local plate = GetVehicleVIN(vehicle)

      if Config.ForceTrim then
          plate = Trim(plate)
      end

      return plate
    end
    return "none"
end

AddEventHandler("rcore_radiocar:updateMusicInfo", function(data)
  local vehicle = GetVehiclePedIsIn(PlayerPedId())
  local plate = GetVehicleVIN(vehicle)

  TriggerServerEvent("rcore_radiocar:updateMusicInfo", data.label, data.url, plate, data.oldLabel, data.oldURL)
end)

RegisterNUICallback("removeSong", function(data, cb)

    local vehicle = GetVehiclePedIsIn(PlayerPedId())
    local plate = GetVehicleVIN(vehicle)

    TriggerServerEvent("rcore_radiocar:removeMusic", plate, data.oldLabel, data.oldURL)

    if cb then cb('ok') end
end)