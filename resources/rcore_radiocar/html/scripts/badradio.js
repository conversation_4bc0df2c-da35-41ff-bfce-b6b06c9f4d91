    const socketURL = 'wss://listennetradio.blrp.net/api/live/nowplaying/websocket';
    const BadRadioURL = 'https://listennetradio.blrp.net/listen/blrp_radio/radio.mp3';
    let socket = null;
    let reconnectInterval = null;
    var CachedID = 0;
    var SongStartTime = 0
    var SongDuration = 0
    var isStreamLive = false
    var radioDescription = null;

    function connectWebSocket() {
    socket = new WebSocket(socketURL);

    socket.onopen = function(event) {
    //console.log('WebSocket connection opened');

    socket.send(JSON.stringify({
    "subs": {
    "station:blrp_radio": {}
}
}));
};

    socket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    const np = data?.pub?.data?.np || null;
    if (np) {
    if (SoundPlayerInstance != null) {
    if (SoundPlayerInstance.url == BadRadioURL) {
    if (np.now_playing.sh_id != CachedID || $(".nameSong").text().includes('radio')) {
    var TrackText = np.now_playing.song.text;

    CachedID = np.now_playing.sh_id;
    var IsLive = np.live.is_live;
    var DJName = np.now_playing.streamer;
    if(IsLive == true){
    radioDescription = 'BadRadio LIVE: ' + DJName + '</br>' + TrackText
    $(".radioName").html(radioDescription);
    isStreamLive = true;
    console.log('update' + radioDescription)
}
    else{
    radioDescription = 'BadRadio: ' + TrackText
    $(".radioName").text(radioDescription);
    isStreamLive = false;
}


    SongStartTime = np.now_playing.played_at;
    SongDuration = np.now_playing.duration;
}
}
}
}
};

    socket.onclose = function(event) {
    //console.log('BadRadio Stats - connection closed');
    reconnectWebSocket();
};

    socket.onerror = function(error) {
    //console.error('WebSocket error:', error);
};
}

    function reconnectWebSocket() {
    const reconnectIntervalTime = 30000; // 30 seconds

    if (!reconnectInterval) {
    reconnectInterval = setInterval(function() {
    if (socket.readyState === WebSocket.CLOSED) {
    //console.log('BadRadio Stats - Attempting to reconnect...');
    connectWebSocket();
}
}, reconnectIntervalTime);
}
}


    function calculateSongTimeRemaining(playedAt, currentEpochTime, duration) {
    const elapsedSeconds = currentEpochTime - playedAt;
    const remainingSeconds = duration - elapsedSeconds;

    return remainingSeconds;
}

    connectWebSocket();

    function updateSongTime() {
    setInterval(function() {
        // Get the current epoch time and calculate the remaining time
        if (SoundPlayerInstance != null) {
            if (SoundPlayerInstance.url == BadRadioURL) {
                const currentEpochTime = Math.floor(Date.now() / 1000); // Current epoch time in seconds
                var remainingTime = calculateSongTimeRemaining(SongStartTime, currentEpochTime, SongDuration);
                if (remainingTime < 0) {
                    remainingTime = 0;
                }
                // Format the remaining time if needed
                const formattedTime = formatTime(remainingTime);
                if(isStreamLive){
                    $(".radioTime").text("LIVE");
                }
                else
                {
                    $(".radioTime").text(formattedTime);
                }
                if(radioDescription != null) {
                    $(".radioName").html(radioDescription);
                }


            }
        }
    }, 800);
}
    function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}


    // Example usage
    updateSongTime();