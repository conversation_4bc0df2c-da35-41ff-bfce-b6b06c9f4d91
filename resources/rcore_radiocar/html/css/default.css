/* Handle */
.graystyle ::-webkit-scrollbar-thumb {
    background: #888;
    border: 2px solid white;
}

/* Handle on hover */
.graystyle ::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.graystyle{
    background: url(./img/radio.png);
    background-size: 100% 100%;

    height: 590px;
    width: 950px;
    color: white;
}

.gray_time{
    position: absolute;
    top: 60px;
    right: 392px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 900;
    font-size: 20px;
}

.box svg{
    fill: white;
}

.box{
    height: 69px;
    width: 118px;
    float: left;
    text-align: center;
    font-size: 31px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.box.full svg{
    height: 60px;
    display: unset;
}


.box.full{
    height: 306px;
    width: 50%;
    display: unset;
}

.box:hover{
	cursor: pointer;
}

.box:hover svg{
	fill: #ff7878;
	cursor: pointer;
}

.box:hover i{
	color: #ff7878;
	cursor: pointer;
}

.playSong{
    font-size: 44px;
    height: 52px;
    margin: auto;
    width: 50%;
}


.playSong svg{
    text-align: center;
    height: 40px;
    fill: white;
}

.playSong svg:hover{
	fill: #ff7878;
    cursor: pointer;
}

.playCustom{
    position: absolute;
    height: 306px;
    width: 713px;
    top: 107px;
    left: 146px;
}

.playCustom p{
    font-family: 'Montserrat', sans-serif;
    font-weight: 900;
    text-align: center;
    font-size: 21px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.playCustom input{
    color: black;
    font-family: 'Montserrat', sans-serif;
    font-weight: 900;
    width: 330px;
    text-align: center;
    border: solid 3px;
    border-radius: 5px;
    margin-top: 10px;
}

.music{
    height: 108px;
    width: 179px;
    float: left;
    text-align: center;
    font-size: 31px;
    display: block;
    margin-bottom: 25px;
    padding: 20px;
}

.musicStored{
    height: 103px;
    width: 167px;
    padding: 18px;
    margin-left: 2px;
    position: relative;
    left: 14px;
}

.queMusic{
    height: 94px;
    width: 169px;
    position: relative;
    left: 17px;
}

.music p{
    font-size: 16px;
    position: relative;
    top: 14px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 900;
    text-decoration: underline;
    height: 27px;
    text-overflow: ellipsis;
    overflow: hidden;
    height: 45px;
}

.music svg{
    height: 48px;
}

.music svg{
	fill: white;
}

.music span{
    color: white;
    position: relative;
    top: 3px;
}

.music svg:hover{
	fill: #ff7878;
	cursor: pointer;
}

.music span:hover{
	color: #30f96e;
	cursor: pointer;
}

::-webkit-scrollbar {
  width: 20px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.editSong p{
    font-family: 'Montserrat', sans-serif;
    font-weight: 900;
    font-size: 20px;
	text-align: center;
}

.editSong svg{
    fill: white;
	height: 45px;
    position: relative;
    top: 15px;
    left: 36px;
}

.editSong svg:hover{
	fill: #30f96e;
	cursor: pointer;
}

.editSong{
    position: absolute;
    height: 193px;
    width: 179px;
    top: 17px;
    left: 44px;
    right: 0;
    bottom: 82px;
    margin: auto;
}

.radioimage{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
}

#useless{
    position: absolute;
    top: 60px;
    right: 99px;
    font-size: 20px;
	display: flex;
}

.UrlForQue{
	text-align: center;
	color: black;
    width: 350px;
    position: relative;
    right: 81px;
}

.non_active_que {
    fill: white !important;
}

.active_que {
    fill: #08ff08 !important;
}

.non_active_que:hover {
    fill: #ff7878 !important;
}

.active_que:hover {
    fill: #ff7878 !important;
}

.AddName {
	text-align: center;
	color: black;
    width: 350px;
    position: relative;
    right: 81px;
}
.AddUrl{
    text-align: center;
    color: black;
    width: 350px;
    position: relative;
    right: 81px;
}

.trash{
	height: 60px !important;
	top: 21px !important;
}

#menu{
    height: 69px;
    width: 710px;
    position: absolute;
    bottom: 105px;
    right: 91px;
}

#menu svg{
    height: 40px;
}

.musicList{
    height: 308px;
    width: 709px;
    position: absolute;
    top: 105px;
    left: 148px;
    overflow: overlay;
}

.center-box{
    display: block;
    height: 50%;
    width: 100%;
    transform: translate(0, 50%);
}