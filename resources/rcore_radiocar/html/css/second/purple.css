#second-style-button-purple svg{ fill: #b085f5; }
#second-style-line-purple{ background: #6a0080; }
#second-style-volume-label-purple{ fill: #a86eff; color: #a86eff; }
#second-style-time-label-purple{ color: #a86eff; }
#second-style-playing-music-purple{ color: #7a527a; }
#second-style-music-list-purple{ color: #a86eff; fill: #a86eff; }

#second-style-button-purple {
    border: 2px solid #8d6a8d;
    background: linear-gradient(0deg, rgba(66,0,80,1) 4%, rgba(33,8,38,1) 20%, rgba(15,8,15,1) 70%);
    transition: background 0.3s ease-out;
}
#second-style-button-purple:hover {
    background: linear-gradient(0deg, rgba(66,0,80,1) 16%, rgba(33,8,38,1) 50%, rgba(15,8,15,1) 100%);
    fill: #8a1eff;
}

#second-style-container-purple{ color: #a86eff; }
#second-style-box-purple svg{ fill:  #a86eff; }
#second-style-play-control-purple svg{ fill: #a86eff; }
#second-style-music-box-purple svg{ fill: #a86eff; }
#second-style-music-box-purple span{ color: #a86eff; }
#second-style-edit-song-purple svg{ fill:  #a86eff; }
#second-style-play-control-purple svg:hover{ fill: #8a1eff; cursor: pointer; }
#second-style-button-purple:hover, #second-style-button-purple:hover svg { fill: #8a1eff; }
#second-style-container-purple ::-webkit-scrollbar-thumb { background: #6a0080; border: 2px solid white; }
#second-style-container-purple ::-webkit-scrollbar-thumb:hover { background: #400040; }
#second-style-box-purple:hover{ color: #8a1eff; }
#second-style-box-purple:hover svg{ fill: #8a1eff; }
#second-style-box-purple:hover i{ color: #ff7878; }
#second-style-edit-song-purple svg:hover{ fill: #8a1eff; }
#second-style-music-box-purple span:hover{ fill: #8a1eff; }
#second-style-music-box-purple svg:hover{ fill: #8a1eff !important; }

#non_active_que-purple { fill: #a86eff; }
#active_que-purple { fill: #8a1eff !important; }

#active_que-purple:hover { fill: #8a1eff !important; }
#non_active_que-purple:hover { fill: #8a1eff !important; }
