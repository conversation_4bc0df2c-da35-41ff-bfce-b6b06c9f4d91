Config = {}

-- translation
Config.Locale = "en"

-- What mysql should be active?
-- What type of mysql you want?
-- -1 automatic detection
-- 0 No mysql
-- 1 oxmysql (if you have older version where mysql-async bridge does not exists turn this on)
-- 2 Mysql async
-- 3 ghmattimysql
Config.MysqlType = -1

-- 0 standalone
-- 1 ESX
-- 2 QBCore
Config.FrameWork = 0

-- ###################################################################
-- ##### read me if you want player choose their own radio style #####
-- ###################################################################
-- simply zse one of the optional resource either "rcore_buyradio" or "rcore_itemradio"

-- this will force certain look for every car
-- 0 = random per vehicle model
-- 1 = gray style
-- 2 = blue style
Config.RadioStyle = 2

-- change specific model radio style
-- 1 = gray style
-- 2 = blue style
Config.RadioStyleSpecificModel = {
    [GetHashKey("sultan")] = 2,
}

-- will force trim even on standalone
Config.ForceTrim = true

-- Force ACE permission to open radio
-- you will need this "add_ace" permission and "add_principal" -> you can find them in readme.md
-- you can view more what the ace is here: https://forum.cfx.re/t/basic-aces-principals-overview-guide/90917
Config.ForcePermission = false

-- well.. Do i have to explain what this blacklist?
Config.BlacklistedURL = {
    -- I am not responsible for you wanting puppy after watching this video
    "https://www.youtube.com/watch?v=o-YBDTqX_ZU",
}

-- this is domain name so allow anything that you would like to be allowed the blacklist will still take a place for certain music on youtube/other
Config.WhitelistedURL = {
    "youtube",
    "youtu",
    "discordapp",
    "blrp.net",
}

-- do you want to disable saving this stuff to the mysql?
-- when you create your own playlist it will be there even after restart
Config.DisableLoadingOfPlayList = false

-- save interval for the playlist
Config.AutoSaveInterval = 1000 * 60 * 5 -- saves every 5 mins

-- esx object share
Config.ESX = "esx:getSharedObject"

-- es_extended resource name
Config.esx_resource_name = "es_extended"

-- qbcore object
Config.QBCore = "QBCore:GetObject"

-- es_extended resource name
Config.qbcore_resource_name = "qb-core"

-- Debug
Config.Debug = false

-- enable playing music after reconnect for all cars
-- a few more notes this will just play the same music player heard before on the same time
Config.PlayMusicAfterReconnect = true

-- Thread Debug
Config.ThreadDebug = false

-- Function Debug
Config.FunctionDebug = false

-- this will disable radio for all vehicles no expection even police etc.
Config.DisableGTARadio = false

-- how much volume will adjust each +/- button
Config.VolumeAdjust = 0.025

-- Should this be opened only from command ?
Config.EnableCommand = false

-- Name for the command ?
Config.CommandLabel = "radiocar"

-- Key to open radio
Config.KeyForRadio = "F9"

-- description of the key
Config.KeyDescription = "Vehicle Car Radio"

-- Distance playing from car
Config.DistancePlaying = 4.0

-- Distance playing from car if windows are closed / or if he has open any door
Config.DistancePlayingWindowsOpen = 5.0

--  if the engine is off the music will be disabled until the engine is on
Config.DisableMusicAfterEngineIsOFF = true

-- Only owner of the car can play a music from the vehicle.
Config.OnlyOwnerOfTheCar = false

-- Radio in car can be used only for people who own the car
-- this can prevent from trolling streamers, i believe many kids
-- will try play some troll music and try to get streamer banned.
Config.OnlyOwnedCars = false

-- this will only let use cars that have installed radio as an item in the car
-- means no car without installed radio before can use it..
-- you have to implement it somewhere by yourself.
-- if you wish to know more about this, please read "readme.md"
Config.OnlyCarWhoHaveRadio = true


-- this will just ignore the option above
Config.PermittedVehiclesForOwnedRadios = {
    "pbus2",
}

-- the update of position will stop after 35km/h because there is no point of updating it for other players if they're driving fast
-- to the point they wont be able to hear it when they drive by.
Config.StopUpdateAfterCertainSpeed = 15

-- exception from the above ( if vehicle have greater playing distance it does not make sense to deny the update )
Config.ForceUpdateForSpecificModel = {
    GetHashKey("pbus2"),
}

-- Default music volume
Config.defaultVolume = 0.2

-- who can touch the radio from what seat?
-- https://docs.fivem.net/natives/?_0xBB40DD2270B65366
Config.AllowedSeats = {
    [-1] = true,
    [0] = true,
}

-- if you have some car that has big speakers or something like that
-- you can increase/decrease distance of playing music
Config.CustomDistanceForVehicles = {
    [GetHashKey("pbus2")] = 55,
    [GetHashKey("tug")] = 25,
}

-- Blacklisted vehicles
Config.blacklistedCars = {
    -- bikes
    GetHashKey("bmx"),
    GetHashKey("cruiser"),
    GetHashKey("fixter"),
    GetHashKey("scorcher"),
    GetHashKey("tribike"),
    GetHashKey("tribike2"),
    GetHashKey("tribike3"),

    -- other
    GetHashKey("thruster"),
}

-- this will allow any car to have radio even if its blacklisted category
Config.whitelistedCars = {-- car
    --GetHashKey("car name here"),
}

-- true  = enabled
-- false = disabled
-- Blacklisted categories vehicles
Config.blackListedCategories = {
    anyVehicle = true,
    anyBoat = true,
    anyHeli = true,
    anyPlane = true,
    anyCopCar = true,
    anySub = false,
    anyTaxi = true,
    anyTrain = true,
}

local songs = {
  { 'FDF The Album', 'https://www.youtube.com/watch?v=l8cXWhXWsqk&list=PL3vB9UIHHykvzWgs4es-z-b2SvUpmQdcy&index=1' },
  { 'Community Bank Account- Dub P', 'https://www.youtube.com/watch?v=XA22Yxar9OM&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=3' },
  { 'Richard Cheese- Dub P', 'https://www.youtube.com/watch?v=ZGC4pkuQoII&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=5' },
  { 'November- Dub P', 'https://www.youtube.com/watch?v=AIKqp1pEfrY&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=8' },
  { 'Shout out my dad- Dub P', 'https://www.youtube.com/watch?v=kFO54InNvtQ&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=9' },
  { 'Moon- Dub P & Barlow', 'https://www.youtube.com/watch?v=DEQkNRsMAD8&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=11' },
  { 'Phobias- Dub P & Barlow', 'https://www.youtube.com/watch?v=fo22X-ccldU&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=12' },
  { 'Conversations- Dub P', 'https://www.youtube.com/watch?v=PiwTTQXgVVU&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=6' },
  { 'Don\'t Call Me Dante - Dub P ', 'https://www.youtube.com/watch?v=UyRI4BFzFzM&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index =13' },
  { 'Bricked Up- Dub P', 'https://www.youtube.com/watch?v=G1T33NobuiU&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=7' },
  { 'Mrs. E- Dub P', 'https://www.youtube.com/watch?v=xN95QXXcnVc&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=4' },
  { 'Great Day for Community- Dub P', 'https://www.youtube.com/watch?v=MMUhdKl-7x8&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=15' },
  { 'The Christmas Shoes- Dub P', 'https://www.youtube.com/watch?v=3GNuG0wi9Yk&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=16' },
  { 'Good Morning- Dub P', 'https://www.youtube.com/watch?v=-QJT-Hp2JCs&list=PL3zvPTCFdtN6BlPrVmFLbtvpH4IErUTbl&index=2' },
  { 'KING- Marcel Paquette', 'https://www.youtube.com/watch?v=ivC-jsB-EgQ' },
  { 'Paleto- Dub P', 'https://www.youtube.com/watch?v=6bdqqUNWaDc' },
  { 'Heartbeat Fast- Zedd', 'https://www.youtube.com/watch?v=Meo-D32H4kA' },
  { 'De Colline- Mez', 'https://youtu.be/ig-4J7aIsyg' },
  { 'The KY Jelly Thunders - cum as U R', 'https://www.youtube.com/watch?v=-lmmoW9pc5I&list=PLL8lMgpTZLV60DLwhCNPVG5z5Ib2q7ug1&index=1' },
  { 'Gallamer Zedd - Cebliminal', 'https://www.youtube.com/watch?v=B6C1gJR88Vg&list=PLL8lMgpTZLV60DLwhCNPVG5z5Ib2q7ug1&index=2' },
  { 'Gallamer Zedd - Until Dawn', 'https://www.youtube.com/watch?v=VZrE2zMeAEo&list=PLL8lMgpTZLV60DLwhCNPVG5z5Ib2q7ug1&index=4' },
  { 'Gallamer Zedd - Father', 'https://www.youtube.com/watch?v=CSUygSEadwY&list=PLL8lMgpTZLV60DLwhCNPVG5z5Ib2q7ug1&index=6' },
  { 'Rascals (ft. Zedd) - Hibernating (Panda Diss)', 'https://www.youtube.com/watch?v=90cI7bPvFhU&list=PLL8lMgpTZLV60DLwhCNPVG5z5Ib2q7ug1&index=8'},
  { 'J-Diggs - Ups & Downs', 'https://www.youtube.com/watch?v=V2S-5ld3gSM&list=PL3vB9UIHHykvzWgs4es-z-b2SvUpmQdcy&index=3'},
  { 'Joji - Next 2 U', 'https://www.youtube.com/watch?v=VPVYZBwV9P8&list=PL3vB9UIHHykvzWgs4es-z-b2SvUpmQdcy&index=4'},
  { 'Treyski - Off The Leash (Feat. MariBandz)', 'https://www.youtube.com/watch?v=tjDXx6Y6BX0&list=PL3vB9UIHHykvzWgs4es-z-b2SvUpmQdcy&index=5'},
  { 'Nardo Riggs - Wicked Flow (Feat. Treyski)', 'https://www.youtube.com/watch?v=rMkwX1gdIS8&list=PL3vB9UIHHykvzWgs4es-z-b2SvUpmQdcy&index=6' },
  { 'Joji - Yuh Yuh', 'https://www.youtube.com/watch?v=sJCgKSpUFfI&list=PL3vB9UIHHykvzWgs4es-z-b2SvUpmQdcy&index=7' },
  { 'J-Diggs - Collard Greens (Feat. MariBandz & Treyski)', 'https://www.youtube.com/watch?v=j_QeF5OCLjE&list=PL3vB9UIHHykvzWgs4es-z-b2SvUpmQdcy&index=8'},
  { 'MariBandz - They Mad (Feat. Nardo Riggs)', 'https://www.youtube.com/watch?v=pf5dDgfSKYM&list=PL3vB9UIHHykvzWgs4es-z-b2SvUpmQdcy&index=9'},
  { 'Joji - 1 Op', 'https://www.youtube.com/watch?v=W1o1OBnmUcs'},
  { 'Joji - Psycho', 'https://www.youtube.com/watch?v=_jFvpl-bzWA'},
  { 'FDF Riggs - Wedding Cake', 'https://www.youtube.com/watch?v=CQWvkQDfULg'},
  { 'Rascal Roll Call - Dub P', 'https://www.youtube.com/watch?v=HOT1zAgVvXo'},
  { 'Kanjo - Dub P', 'https://www.youtube.com/watch?v=nUPK1Qxe61M'},
  { 'Race Satan\'s Son - Dub P', 'https://www.youtube.com/watch?v=WCA-pNCtViM&list=PL3zvPTCFdtN6zcPoPVEAJYoFlp6OKszjD&index=4'},
  { 'Racing Satanists - Dub P ft Zedd', 'https://www.youtube.com/watch?v=CvcLKWsdgHc&list=PL3zvPTCFdtN6zcPoPVEAJYoFlp6OKszjD&index=5'},
  { 'Love Stoop - Dub P', 'https://www.youtube.com/watch?v=BIK06ES4SHo&list=PL3zvPTCFdtN6zcPoPVEAJYoFlp6OKszjD&index=6'},
  { 'Drive - Dub P ft Tara', 'https://www.youtube.com/watch?v=8KI_dG-wyLw&list=PL3zvPTCFdtN6zcPoPVEAJYoFlp6OKszjD&index=7'},
  { 'Forever - Dub P', 'https://www.youtube.com/watch?v=ocGGfrajAdA&list=PL3zvPTCFdtN6zcPoPVEAJYoFlp6OKszjD&index=8'},
  { 'Don\'t Belong - Dub P', 'https://www.youtube.com/watch?v=rKWeB3BZdtg&list=PL3zvPTCFdtN6zcPoPVEAJYoFlp6OKszjD&index=9'},
  { 'Weston\'s Room - Dub P ft Zato', 'https://www.youtube.com/watch?v=W4rOHUJEALc&list=PL3zvPTCFdtN6zcPoPVEAJYoFlp6OKszjD&index=10'},
  { 'Two Hours - Dub P', 'https://www.youtube.com/watch?v=j4sYGZv0cyI&list=PL3zvPTCFdtN6zcPoPVEAJYoFlp6OKszjD&index=11'},
  { 'OK - Dub P', 'https://www.youtube.com/watch?v=XoF5Pzea2nc&list=PL3zvPTCFdtN6zcPoPVEAJYoFlp6OKszjD&index=12'},
  { 'Riggs - I don\'t Give A Fuck', 'https://www.youtube.com/watch?v=m4VFlQ4dVBA' },
  { 'Tan and Green - Joji & Dub P', 'https://www.youtube.com/watch?v=A6KXOCYTmsE'},
  { 'Gallamer Zedd - Until Dawn', 'https://www.youtube.com/watch?v=VZrE2zMeAEo'},
  { 'Gallamer Zedd - No Ragrets', 'https://www.youtube.com/watch?v=qf5QV7Q6-bc&list=PLL8lMgpTZLV4fEXrEK1BRhosLRzuumEv5&index=4'},
  { 'Joji - Lonely', 'https://www.youtube.com/watch?v=d2bPcgivXcY'},
  { 'Mikey McPikey - Christmas Zone', 'https://www.youtube.com/watch?v=nPlLK8qqEq4&list=PLL8lMgpTZLV6N-ixM--lhkaJCM-vQRwUE&index=4'},
  { 'Gallamer Zedd - sumwere only we no (Cover 4 Coner & Ema)', 'https://www.youtube.com/watch?v=LwvhhpMXtSk'},
  {'Joji - AztecaK', 'https://www.youtube.com/watch?v=oXy56vTRVFU'},
  {'Joji - Outta my Pockets', 'https://www.youtube.com/watch?v=AVdKrSQfM6U'},
}

Citizen.CreateThread(function()
  local song_list = { }

  song_list = {
    {
      label = "BadRadio",
      url = "https://listennetradio.blrp.net/listen/blrp_radio/radio.mp3",
    },
  }

  for i = 1, 7 do
    local song = songs[math.random(#songs)]
    table.insert(song_list, {
      label = song[1],
      url = song[2]
    })
  end

  Config.defaultList = song_list
end)

-- List default station for radio
Config.defaultList = {
    {
        label = "2010s Nostalgia",
        url = "https://www.youtube.com/watch?v=kK0AHd9N7dk&ab_channel=RoseateMixes",
    },
    {
        label = "Lofi hip hop radio",
        url = "https://www.youtube.com/watch?v=jfKfPfyJRdk",
    },
    {
        label = "Night jazz",
        url = "https://www.youtube.com/watch?v=aixaT5NjGo8&ab_channel=RelaxingJazzBGM",
    },
    {
        label = "90s live radio",
        url = "https://www.youtube.com/watch?v=1Ep2eiM5X9U&ab_channel=BestofMix",
    },
    {
        label = "80s live radio",
        url = "https://www.youtube.com/watch?v=R6_3OchvW_c",
    },
    {
        label = "Doom live radio",
        url = "https://www.youtube.com/watch?v=JEuAYnjtJP0",
    },
}

-- lowpass intensity the lower the number is the more hard it will be to hear the sound
-- like it is far away behind door
-- the higher the number will be the opposite will happen
Config.LowpassIntensity = 500

Config.IgnoreLowpassOnCertainModels = {
    -- example bike doesnt have any windows / doors so there cant be applied lowpass filter
    "akuma",
}

-- this is for vehicles that their trunk does  not  connect interior of the vehicle and the sound have to be still like behind wall
-- if the car should not sound like behind the wall -> make an expection here
Config.IgnoreLowpassForCertainModelsWithTrunkNotFullyOpen = {
    [GetHashKey("sultan")] = true,
}

-- if set true you will see on your screen in what interior ID you're in
Config.InteriorDebug = false

-- Put ID of the interior you want to ignore the lowpass for
Config.IgnoreInteriorIdentifiersForLowPass = {
    -- those are entrances of tunnels I found across map (there might be more but this will do for now)
    [154369] = true,
    [155393] = true,
    [182017] = true,
    [183297] = true,
    [193025] = true,
    [192257] = true,
    [199681] = true,

    [248577] = true,
    [250369] = true,
    [194561] = true,
    [194817] = true,
    [195841] = true,
    [195585] = true,
    [55298] = true,
    [135169] = true,
}

-- How much ofter the player position is updated ?
Config.RefreshTime = 300

-- how much close player has to be to the sound before starting updating position ?
Config.distanceBeforeUpdatingPos = 40

-- distance for checking vehicle if its close to the player and play music from cache
-- NOTE: keep in mind that FiveM onesync infinity will removed from player client after X meters away.
Config.DistanceVehicleCacheChecker = 50

-- Message list
Config.Messages = {
    ["streamer_on"] = "Streamer mode is on. From now you will not hear any music/sound.",
    ["streamer_off"] = "Streamer mode is off. From now you will be able to listen to music that players might play.",
}

-- if you want xsound separated from radiocar then turn this on.
Config.UseExternalxSound = false

-- if you want to use high_3dsounds
Config.UseHighSound = false

-- if you want to use mx-surround
Config.MXSurround = false

-- name of the lib
Config.xSoundName = "xsound"

if Config.MXSurround then
    Config.UseExternalxSound = true
    Config.xSoundName = "mx-surround"
end

if Config.UseHighSound then
    Config.UseExternalxSound = true
    Config.xSoundName = "high_3dsounds"

    Config.DistancePlaying = Config.DistancePlaying * 3
    Config.DistancePlayingWindowsOpen = Config.DistancePlayingWindowsOpen * 3
end