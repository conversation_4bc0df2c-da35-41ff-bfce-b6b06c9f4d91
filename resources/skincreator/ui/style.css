input[type=range] {
  -webkit-appearance: none;
  /* Hides the slider so that custom slider can be made */
  width: 280px;
  /* Specific width is required for Firefox. */
  background: transparent;
  /* Otherwise white in Chrome */
  vertical-align: middle;
  padding: 0 10px; }

input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none; }

input[type=range]:focus {
  outline: none;
  /* Removes the blue border. You should probably do some kind of focus styling for accessibility reasons though. */ }

input[type=range]::-ms-track {
  width: 100%;
  cursor: pointer;
  /* Hides the slider so custom styles can be added */
  background: transparent;
  border-color: transparent;
  color: transparent; }

/* Special styling for WebKit/Blink */
input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 12px;
  width: 12px;
  border-radius: 100%;
  background: #ffffff;
  cursor: pointer;
  margin-top: -6px;
  /* You need to specify a margin in Chrome, but in Firefox and IE it is automatic */ }

/* All the same stuff for Firefox */
input[type=range]::-moz-range-thumb {
  height: 12px;
  width: 12px;
  border-radius: 100%;
  background: #ffffff;
  cursor: pointer; }

/* All the same stuff for IE */
input[type=range]::-ms-thumb {
  height: 12px;
  width: 12px;
  border-radius: 100%;
  background: #ffffff;
  cursor: pointer; }

input[type=range]::-webkit-slider-runnable-track {
  width: 330px;
  height: 2px;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.2); }

input[type=range]:focus::-webkit-slider-runnable-track {
  background: rgba(255, 255, 255, 0.2); }

input[type=range]::-moz-range-track {
  width: 330px;
  height: 2px;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.2); }

input[type=range]::-ms-track {
  width: 330px;
  height: 2px;
  cursor: pointer;
  background: transparent;
  border-color: transparent;
  color: transparent; }

input[type=range]::-ms-fill-lower {
  background: rgba(255, 255, 255, 0.2); }

input[type=range]:focus::-ms-fill-lower {
  background: rgba(255, 255, 255, 0.2); }

input[type=range]::-ms-fill-upper {
  background: rgba(255, 255, 255, 0.2); }

input[type=range]:focus::-ms-fill-upper {
  background: rgba(255, 255, 255, 0.2); }

input[type="radio"] {
  display: none; }

.type-radio {
  margin-bottom: 10px; }
  .type-radio label {
    display: inline-block;
    height: 18px; }
  .type-radio .color {
    width: 30px;
    height: 30px;
    display: inline-block;
    margin-right: -2px;
    font-size: 0;
    border: 1px solid rgba(228, 228, 228, 0.3);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 200px; }
    .type-radio .color:after {
      opacity: 0;
      content: '';
      position: absolute;
      top: 9px;
      left: 7px;
      width: 13px;
      height: 9px;
      background: url("assets/radio-check.png") no-repeat;
      background-size: cover;
      -webkit-transition: all .15s ease-in-out;
      -o-transition: all .15s ease-in-out;
      transition: all .15s ease-in-out; }

input[type="radio"]:checked + span:after {
  opacity: 1; }

@font-face {
  font-family: 'Circular';
  src: url("fonts/Circular-Bold.woff2") format("woff2"), url("fonts/Circular-Bold.woff") format("woff"), url("fonts/Circular-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal; }

@font-face {
  font-family: 'Circular';
  src: url("fonts/Circular-Book.woff2") format("woff2"), url("fonts/Circular-Book.woff") format("woff"), url("fonts/Circular-Book.ttf") format("truetype");
  font-weight: normal;
  font-style: normal; }

* {
  position: relative;
  margin: 0;
  padding: 0;
  outline: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

body {
  font-family: 'Circular', sans-serif;
  font-weight: normal;
  overflow: hidden;
  height: 100%; }

#cursorPointer {
  position: absolute;
  z-index: 999999;
  display: none; }

::-webkit-scrollbar {
  display: none; }

.skinCreator {
  position: absolute;
  width: 420px;
  min-height: calc(100% - 100px);
  height: 0px;
  color: #FFF;
  top: 50px;
  right: 90px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background-color: rgba(24, 27, 32, 0.9);
  border-radius: 20px;
  overflow: hidden; }

form {
  width: 100%;
  height: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow-y: auto;
  padding-bottom: 200px; }

.tab {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start; }
  .tab a {
    color: white;
    font-weight: normal;
    text-decoration: none;
    font-size: 14px;
    text-align: center;
    line-height: 160px;
    width: calc(100% / 3);
    height: 110px;
    -webkit-transition: all 0.4s ease-in-out;
    -o-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out; }
  .tab .active {
    color: white;
    font-weight: bold;
    background: #6770DA; }
  .tab .identity::before {
    content: '';
    width: 30px;
    height: 38px;
    background: url(assets/identity.png) no-repeat;
    position: absolute;
    top: calc(50% - 10px);
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%); }
  .tab .visage::before {
    content: '';
    width: 32px;
    height: 37px;
    background: url(assets/head.png) no-repeat;
    position: absolute;
    top: calc(50% - 10px);
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%); }
  .tab .pilosite::before {
    content: '';
    width: 26px;
    height: 39px;
    background: url(assets/pilosite.png) no-repeat;
    position: absolute;
    top: calc(50% - 10px);
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%); }
  .tab .vetements::before {
    content: '';
    width: 25px;
    height: 38px;
    background: url(assets/clothes.png) no-repeat;
    position: absolute;
    top: calc(50% - 10px);
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%); }

.block {
  display: none; }
  .block .group {
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2); }
    .block .group:last-child {
      border-bottom: none; }
  .block h2 {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    font-weight: normal;
    text-transform: uppercase;
    letter-spacing: .5px;
    padding-bottom: 20px; }
  .block .input {
    padding: 10px 0; }
    .block .input .label {
      color: white;
      font-size: 12px;
      text-transform: uppercase;
      letter-spacing: .5px; }
    .block .input .label-value {
      position: absolute;
      top: 0px;
      font-size: 14px;
      color: white;
      right: 0px;
      opacity: .5; }
    .block .input .type-range {
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      border-radius: 200px;
      padding: 12px 15px 15px;
      background-color: rgba(56, 58, 64, 0.6); }
      .block .input .type-range .arrow {
        width: 14px;
        height: 14px;
        text-decoration: none;
        font-size: 0;
        display: inline-block;
        vertical-align: middle; }
      .block .input .type-range .arrow-left {
        background: url("assets/arrow-left.png") no-repeat;
        background-size: cover;
        margin-left: 5px; }
      .block .input .type-range .arrow-right {
        background: url("assets/arrow-right.png") no-repeat;
        background-size: cover; }
    .block .input .type-img {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -ms-flex-flow: row wrap;
      flex-flow: row wrap;
      -webkit-box-pack: start;
      -ms-flex-pack: start;
      justify-content: flex-start; }
      .block .input .type-img label {
        width: calc(100% / 4); }
        .block .input .type-img label:nth-child(4n) .img {
          border-right: 1px solid #3d3d3d; }
      .block .input .type-img .img {
        width: 100%;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        border-top: 1px solid #3d3d3d;
        border-left: 1px solid #3d3d3d; }
        .block .input .type-img .img img {
          width: 100%;
          height: auto; }
        .block .input .type-img .img:after {
          opacity: 0;
          content: '';
          position: absolute;
          bottom: 5px;
          right: 5px;
          width: 10px;
          height: 10px;
          background: white url("assets/radio-check-black.png") no-repeat center center;
          border-radius: 100%;
          padding: 4px;
          background-size: 8px 6px;
          -webkit-transition: all .15s ease-in-out;
          -o-transition: all .15s ease-in-out;
          transition: all .15s ease-in-out; }
      .block .input .type-img input[type="radio"]:checked + .img {
        border: 2px solid white; }
        .block .input .type-img input[type="radio"]:checked + .img:after {
          opacity: 1; }
  .block h2 + .input {
    padding-top: 0px; }

.block.active {
  display: block; }

.label {
  display: block;
  margin-bottom: 10px; }

.submit {
  width: 420px;
  height: 70px;
  display: block;
  border: none;
  background-color: #6770DA;
  font-size: 16px;
  line-height: 70px;
  color: white;
  position: fixed;
  bottom: 50px;
  z-index: 100;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  -webkit-transition: all .2s ease-in-out;
  -o-transition: all .2s ease-in-out;
  transition: all .2s ease-in-out; }
  .submit:hover {
    background-color: #6770DA; }

.rotation {
  position: absolute;
  bottom: 60px;
  right: 550px;
  z-index: 100;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  font-size: 16px;
  line-height: 40px;
  color: white; }
  .rotation .button {
    background-color: rgba(0, 0, 0, 0.75);
    text-align: center;
    vertical-align: middle;
    width: 40px;
    height: 40px;
    border-radius: 5px;
    margin: 0 5px; }
  .rotation p {
    margin-left: 10px; }

.popup {
  display: none;
  position: absolute;
  z-index: 200;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: white;
  width: 450px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 5px;
  padding: 20px 0 25px; }
  .popup p {
    margin-bottom: 10px; }
  .popup .buttons {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start; }
    .popup .buttons .button {
      background-color: rgba(0, 0, 0, 0.75);
      text-align: center;
      vertical-align: middle;
      width: 60px;
      height: 40px;
      border-radius: 5px;
      margin: 0 5px;
      padding: 0 10px;
      color: white;
      line-height: 40px; }
    .popup .buttons .yes {
      background-color: #6770da; }

.vetements h2 {
  color: white; }

.vetements .label-value {
  position: absolute;
  top: 18px;
  font-size: 14px;
  color: white;
  right: 30px;
  opacity: .5; }

.vetements ul {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 200px;
  padding: 12px 15px 15px;
  background-color: rgba(56, 58, 64, 0.6);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center; }
  .vetements ul li {
    display: none; }
  .vetements ul .active {
    display: block; }
  .vetements ul .arrow {
    width: 14px;
    height: 14px;
    text-decoration: none;
    font-size: 0;
    display: inline-block;
    vertical-align: middle;
    border: none; }
  .vetements ul .arrowvetement-left {
    background: url("assets/arrow-left.png") no-repeat;
    background-size: cover;
    margin-left: 5px; }
  .vetements ul .arrowvetement-right {
    background: url("assets/arrow-right.png") no-repeat;
    background-size: cover; }
