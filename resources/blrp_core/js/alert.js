import nui from './nui.js'

export default new class Alert {

    policeAlerts = [];
    publishedPoliceAlerts = [];
    policeAlertsShowing = false;

    __construct() {
    }

    add(packet) {
        this.policeAlerts.push(packet);
        this.pushPendingNotifications();
    }

    pushPendingNotifications() {
        let a = this.checkConfig(this.policeAlerts.pop());
        let message = this.makeMessage(a);
        let buttons = this.makeButtons(a);
        this.publishedPoliceAlerts.push(a);

        let title = `&nbsp;&nbsp;` + a.title + `&nbsp;&nbsp;`;

        if(a.badge != '') {
          title = title + `<span class="badge badge-${a.badge_style}">${a.badge}</span>`;
        }

        title = title + `<br />`

        iziToast.show({
            id: a.id,
            zindex: 999999,
            // backgroundColor: 'rgba(0, 0, 0, 0.7)',
            class: 'police-notify',
            titleLineHeight: 25,
            icon: a.icon + ' fa-fw',
            iconColor: a.icon_color,
            close: false,
            theme: 'dark',
            maxWidth: 360,
            position: 'topRight',
            transition: false,
            transitionOut: false,
            drag: false,
            title: title,
            message: message,
            timeout: false,
            progressBarColor: a.bar_color,
            buttons: buttons,
            pauseOnHover: false,
            onOpening: (instance, toast) => {
                if (a.flash) $(toast).addClass('flash');
                setTimeout(() => {
                    // this.startFlashFade(toast)
                    // $(toast).attr('class', 'iziToast police-notify iziToast-theme-dark iziToast-opened');
                    $(toast).removeClass('flash');
                    $(toast.parentElement).addClass('already-finished-alert');
                    if(!this.policeAlertsShowing) $(toast.parentElement).hide();
                }, a.show_for) // a.show_for
            },
            onOpened: (instance, toast) => {
            },
            onClosed: (instance, toast, closedBy) => {
                // if (this.policeAlertsShowing) {
                //     $(toast).attr('class', 'iziToast police-notify iziToast-theme-dark iziToast-opened');
                // }
                // let $surround = $("<div>");
                // $surround.attr('class', 'already-finished-alert iziToast-capsule')
                // $surround.attr('style', 'height: auto')
                // $(toast).attr('class', 'iziToast police-notify iziToast-theme-dark iziToast-opened')
                // if(!this.policeAlertsShowing) $surround.hide();
                // $('.iziToast-wrapper').prepend($surround.append(toast));
            }
        });
    }

    hideOlderAlerts() {
        if(this.publishedPoliceAlerts.length > 30) {
            let toRemove = this.publishedPoliceAlerts.shift();
            $(`#${toRemove.id}`).remove()
        }
    }

    checkConfig(a) {
        if (!a.show_for) a.show_for = 5000;
        if (!a.badge) a.badge = '';
        if (!a.badge_style) a.badge_style = 'primary';
        if (!a.bar_color) a.bar_color = '#0b6ef6';
        if (!a.hide_location) a.hide_location = false;
        if (!a.icon_color) a.icon_color = '';
        // a.show_for = 1200000
        return a;
    }

    makeMessage(a) {
        let message = ``
        message += `<div class='message-contents float-left' style='min-width: 160px'>`

        let date = new Date();
        let hour = date.getHours()

        if(hour < 10) {
          hour = "0" + hour
        }

        let minute = date.getMinutes()

        if(minute < 10) {
          minute = "0" + minute
        }

        let second = date.getSeconds()

        if(second < 10) {
          second = "0" + second
        }

        message += `<i class="far fa-clock fa-fw"></i> <span class="location text-white"><b>${hour}:${minute}:${second}</b></span><br>`;

        if (!a.hide_location) {
            if (a.location_override !== null && a.location_override !== undefined && a.location_override !== "") {
                message += `<i class="far fa-map-marker fa-fw"></i> <span class="location text-white"><b>${a.location_override}</b></span><br>`;
            } else {
              if (a.location_a && a.location_b) {
                 message += `<i class="far fa-map-marker fa-fw"></i> <span class="location text-white"><b>${a.location_a}</b> / <b>${a.location_b}</b></span><br>`;
              } else if (a.location_a) {
                 message += `<i class="far fa-map-marker fa-fw"></i> <span class="location text-white"><b>${a.location_a}</b></span><br>`;
              }
            }

            if(a.zone !== null && a.zone !== 'NULL') {
              message += `<i class="far fa-globe fa-fw"></i> <span class="location text-white"><b>${a.zone}</b></span><br>`;
            }
        }

        if (a.msg) {
            message += `<span class="meta" style="margin-top: 2px;">${a.msg}</span><br>`;
        }

        message += `<div class="responding-units"></div></div>`;
        return message;
    }

    makeButtons(alert) {
        let buttons = [];

        if (alert.can_accept) {
            buttons.push([
                `<button class="btn btn-success accept-button"><kbd>F1</kbd> Accept</button>`, (instance, toast) => {
                    this.getAcceptButton(alert.id).remove();
                    nui.request('acceptAlert', {
                        id: alert.id
                    })
                }, false], // true to focus
            )
        }

        // buttons.push([
        //     `<button class="btn btn-danger"><kbd>F2</kbd>Deny</button>`, (instance, toast) => {
        //         this.initDenyButton(alert.pos);
        //     }, false], // true to focus
        // )

        if (alert.allow_local_ems) {
          buttons.push([
              `<button class="local-ems-button">Local EMS</button>`, (instance, toast) => {
                  nui.request('dispatchLocalEms', {
                      id: alert.id
                  })
              }, false],
          );
        }


        if(alert.identity && alert.identity.phone && alert.allow_phone) {
            buttons.push([
                `<button>Call</button>`, (instance, toast) => {
                    nui.request('callAlertNumber', {
                        phone: alert.identity.phone
                    })
                }, false], // true to focus
            );
        }

        if(alert && alert.pos && alert.allow_gps) {
            buttons.push([
                `<button><kbd>F2</kbd> GPS Route</button>`, (instance, toast) => {
                    nui.request('routeAlertPosition', {
                        pos: alert.pos
                    })
                }, false], // true to focus
            )
        }

        if (alert.service_name === 'staff') {
            buttons.push([
                `<button class='btn'>Teleport to</button>`, (instance, toast) => {
                    // this.getAcceptButton(alert.id).remove();
                    nui.request('teleport', {
                        pos: alert.pos
                    })
                }, false], // true to focus
            )
        }

        if (alert.service_name === 'staff') {
            buttons.push([
                `<button class='btn'>Direct Msg</button>`, (instance, toast) => {
                    nui.request('directmessage', {
                        vrp_id: alert.vrp_id
                    })
                }, false], // true to focus
            )
        }

        if (alert.service_name === 'staff-dm') {
            buttons.push([
                `<button class='btn'>Reply</button>`, (instance, toast) => {
                    nui.request('staff-dm-reply', {
                        vrp_id: alert.vrp_id
                    })
                }, false], // true to focus
            )
        }

        if(alert.allow_dismiss) {
          buttons.push([
              `<button><i class="far fa-times"><i></button>`, (instance, toast) => {
                  $(toast).remove()
              }, false], // true to focus
          )
        }

        return buttons;
    }

    addRespondingUnit(packet) {
        const unit = packet.unit_to_add;

        let subTextBody = $(`#${packet.identifier}`).find('.responding-units');

        if (unit.includes('Patient moved from scene'))
        {
            $(subTextBody).prepend(`<br><span class="responding-unit"><i class="fa-regular fa-triangle-exclamation text-warning"></i> ${unit}</span>`)

            this.removeLocalEmsButton(packet.identifier)
        }
        else if (unit.includes('Local EMS offered'))
        {
            $(subTextBody).prepend(`<br><span class="responding-unit"><i class="fa-regular fa-triangle-exclamation text-warning"></i> ${unit}</span>`)

            this.removeLocalEmsButton(packet.identifier)
        }
        else if (unit.includes('Local EMS accepted'))
        {
            $(subTextBody).append(`<br><span class="responding-unit"><i class="fa-regular fa-truck-medical text-success"></i> ${unit}</span>`)

            this.removeLocalEmsButton(packet.identifier)
        }
        else if (unit.includes('Local EMS declined'))
        {
            $(subTextBody).append(`<br><span class="responding-unit"><i class="fa-regular fa-truck-medical text-danger"></i> ${unit}</span>`)

            this.removeLocalEmsButton(packet.identifier)
        }
        else
        {
            $(subTextBody).append(`<br><span class="responding-unit"><i class="fa-duotone fa-scrubber text-primary"></i> ${unit}</span>`)
        }
    }

    getAcceptButton(identifier) {
      return $($(`#${identifier}`).find('.accept-button'));
    }

    removeLocalEmsButton(alert_id) {
      let local_ems_button = $(`#${alert_id}`).find('.local-ems-button')

      if(local_ems_button.length > 0) {
        local_ems_button.remove();
      }
    }

    removeAlert(alert_id) {
        $(`#${alert_id}`).remove()
    }

    showFinishedAlerts() {
        this.policeAlertsShowing = true
        $('.already-finished-alert').show();
    }

    hideFinishedAlerts() {
        this.policeAlertsShowing = false
        $('.already-finished-alert').hide();
    }

    clearAll() {
      this.policeAlerts = [];
      this.publishedPoliceAlerts = [];

      $('.iziToast-capsule').remove();
    }
}
