CoreAnticheat.TeleportListener = {
  BypassNextTeleportCheck = false,

  _onResourceStart = function()
    -- core:client:anticheat:bypassNextTeleportCheck
    RegisterNetEvent('ccabNTC')
    AddEventHandler('ccabNTC', function(flag)
      CoreAnticheat.TeleportListener.BypassNextTeleportCheck = flag
    end)
  end,

  _onAnticheatStart = function()
    Citizen.CreateThread(function()
      Citizen.Wait(30000)

      while CoreAnticheat.Started and not core.me().isStaff() and not GlobalState.is_dev do
        Citizen.Wait(0)

        local first_ped = PlayerPedId()
        local first_coords = GetEntityCoords(first_ped)
        local first_still = IsPedStill(first_ped)
        local first_velocity = GetEntitySpeed(first_ped)

        Citizen.Wait(3000)

        if not CoreAnticheat.TeleportListener.BypassNextTeleportCheck then
          if not core.me().isInPrison() and not core.me().isBeingEscorted() and not core.me().isBeingCarried() and not CoreAnticheat.InTrunk then
            local second_ped = PlayerPedId()
            local second_coords = GetEntityCoords(second_ped)
            local distance = #(first_coords - second_coords)

            if distance > 600 and first_still == IsPedStill(second_ped) and first_velocity == GetEntitySpeed(second_ped) and first_ped == second_ped then
              if #(first_coords - vector3(5336.036, -5189.271, 82.77821)) > 20.0 and #(second_coords - vector3(5336.036, -5189.271, 82.77821)) > 20.0 then -- North Yankton fix
                -- TriggerServerEvent('csaeBcR', 'teleport', second_coords, 'Player teleport/noclip detected / distance = ' .. distance .. ' / first_coords = ' .. first_coords .. ' / second_coords = ' .. second_coords)
              end
            end
          end
        end

        CoreAnticheat.TeleportListener.BypassNextTeleportCheck = false
      end
    end)
  end
}

exports('BypassNextCheck', function()
  CoreAnticheat.TeleportListener.BypassNextTeleportCheck = true
end)
