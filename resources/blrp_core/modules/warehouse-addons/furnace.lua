-- 1 ton of steel = 1000kg of steel = 2000 steel produced
-- 350 - 700 kWh/ton of steel produced
-- 0.175 - 0.35 kWh/1 steel (0.5kg)
-- BC hydro 350kWh@$0.127/kWh = $44.45
-- BC hydro 700kWh@$0.127/kWh = $88.90
-- Canned corn (IRL) = $2.69
-- Canned corn  (IG) = $50.00
-- Canned corn inflation factor = 18.59x
-- LSDWP 350kWh = $826.33 = $2.36093/kWh
-- LSDWP 700kWh = $1,652.651 = $2.36093/kWh
-- BC hydro basic day = $0.3694
-- LSDWP basic day = $6.867146

-- Tiered pricing model
-- Tier 1 0 - 1350 kWh = $2.36093/kWh
-- Tier 2 1350 - + kWh = $3.57166/kWh

local furnace_cooldowns = {}

Citizen.CreateThread(function()
  while true do
    for _, furnace in pairs(exports.housing:GetInstancesOfModel('prop_bl_arcfurnace') or {}) do
      if not furnace_cooldowns[furnace.id] or furnace_cooldowns[furnace.id] < GetGameTimer() then
        -- Add cooldown for furnace
        -- furnace_cooldowns[furnace.id] = GetGameTimer() + (math.random(30, 60) * 1000) -- TODO: uncomment - dev
        furnace_cooldowns[furnace.id] = GetGameTimer() + (10 * 1000)

        local storage_name = 'warehouse:furnace:' .. furnace.id
        local chest = core.chest(storage_name)
        local contents = chest.fetchContents()

        local function getFluxAmount(flux_id, flux_type)
          if flux_type == 'item' then
            local flux_data = contents[flux_id]

            if not flux_data or not flux_data.amount then
              return 0
            end

            return flux_data.amount
          elseif flux_type == 'durability' then
            for k, v in pairs(contents) do
              if string.match(k, flux_id) then
                if v.meta and v.meta.dur_cur and v.meta.dur_cur > 0 then
                  return v.meta.dur_cur, k
                end
              end
            end

            return 0
          end
        end

        local added_products = {}
        local removed_reagents = {}

        local function processFurnaceItem(item_id, item_data)
          local item_definition = GItems.getItemDefinition(item_id)

          local reagent_amount = (item_definition and item_definition.smelting_reagent_amount) or 2

          if
            not item_definition or
            not (
              item_definition.category == 'metal_raw' or
              item_definition.category == 'metal_intermediate'
            ) or
            not item_definition.smelts_into or
            not item_definition.power_consumption or
            contents[item_id].amount < reagent_amount
          then
            return
          end

          -- Check and take flux if needed
          local flux_id = item_definition.smelting_flux_id

          if flux_id then
            local flux_needed = item_definition.smelting_flux_amount
            local flux_type = 'item'

            if item_definition.smelting_flux_dur then
              flux_needed = item_definition.smelting_flux_dur
              flux_type = 'durability'
            end

            local flux_amount, flux_item_id = getFluxAmount(flux_id, flux_type)

            if flux_amount < flux_needed then
              return
            end

            if flux_type == 'item' then
              contents[flux_id].amount = contents[flux_id].amount - flux_needed
            else
              contents[flux_item_id].meta.dur_cur = math.max(0, contents[flux_item_id].meta.dur_cur - 1)

              if contents[flux_item_id].meta.dur_cur <= 0 then
                contents[flux_item_id].amount = math.max(0, contents[flux_item_id].amount - 1)

                if contents[flux_item_id].amount <= 0 then
                  contents[flux_item_id] = nil
                end
              end
            end
          end

          -- Consume input item
          contents[item_id].amount = contents[item_id].amount - reagent_amount

          -- Consumed last item, remove from inventory
          if contents[item_id].amount <= 0 then
            table.insert(removed_reagents, item_id)
          end

          local product_amount = item_definition.smelting_product_amount or 1

          -- Increment product amount by defined product amount
          added_products[item_definition.smelts_into] = product_amount

          -- Add consumption to electrical bill
          addElectricalUsage(furnace.property_id, item_definition.power_consumption * reagent_amount)
        end

        for item_id, item_data in pairs(contents) do
          processFurnaceItem(item_id, item_data)
        end

        -- Remove dead reagents
        for _, item_id in pairs(removed_reagents) do
          contents[item_id] = nil
        end

        -- Add products
        for item_id, amount in pairs(added_products) do
          if not contents[item_id] then
            contents[item_id] = { amount = 0 }
          end

          contents[item_id].amount = contents[item_id].amount + amount
        end

        chest.setContents(contents)
      end
    end

    Citizen.Wait(1000)
  end
end)

RegisterNetEvent('core:server:warehouse:openFurnace', function(_, event_data)
  local character = core.character(source)
  local instance = character.getInstance()

  if
    not event_data or
    not event_data.position or
    not event_data.entity_hash or
    event_data.entity_hash ~= `prop_bl_arcfurnace` or
    not string.match(instance, 'property')
  then
    return
  end

  local furniture_row = MySQL.single.await('SELECT * FROM core_character_furniture WHERE id = ?', {
    event_data.filter_value
  })

  if not furniture_row or event_data.entity_hash ~= `prop_bl_arcfurnace` or not furniture_row.placed then
    return
  end

  instance = string.gsub(instance, 'property%-', '')
  instance = tonumber(instance) or -1

  local property = exports.housing:GetProperty(instance)

  if not property then
    return
  end

  -- Permission check
  if not property.business_id or not exports.blrp_core:CheckUserCanAccess(character, property.business_id, 'warehouse') then
    character.notify('You do not have permission to access this')
    return
  end

  local pivot = property.shell_spawn_coords

  local offset = vector3(furniture_row.offset_x, furniture_row.offset_y, furniture_row.offset_z)
  local target_coords = pivot + offset

  if #(target_coords - event_data.position) > 5.0 then
    return
  end

  character.openChest('warehouse:furnace:' .. furniture_row.id, 1000.0, false, {
    whitelisted_categories = { 'metal_raw', 'metal_intermediate', 'metal_fluxes' },
  })
end)

-----------------------------------
---------- FURNACE QUEST ----------
-----------------------------------

local QUEST_ID = '2023:Foundry'

local quest_dialogue = {
  [1] = {
    running = {
      { 'SELF', false, "Hey, just here to use the equipment" },
      { false, 'FoundryWorker', "Ugh, my boss says he's going to fire me if I keep letting people in here!" },
      { 'SELF', false, "Come on, I'll be in and out before you boss even knows. No harm no foul" },
      { false, 'FoundryWorker', "There would be harm to my job! He already suspended me once!" },
      { 'SELF', false, "Well where else am I going to smelt all this metal?" },
      { false, 'FoundryWorker', "Well, you could get an arc furnace for your warehouse" },
      { 'SELF', false, "How much is that going to set me back?" },
      { false, 'FoundryWorker', "Well you can't order one without being a member of the Arc Furnace of the Month club" },
      { 'SELF', false, "What kind of loser would be in that club? Who would need an arc furnace every MONTH?" },
      { false, 'FoundryWorker', "Umm..." },
      { false, 'FoundryWorker', "Uhh..." },
      { false, 'FoundryWorker', "Err..." },
      { false, 'FoundryWorker', "Anyways, if you can bring me some raw materials I can see what I can do about ordering one for you" },
      { 'SELF', false, "What do you need?" },
    },
  },

  [2] = {
    running = {
      { false, 'FoundryWorker', "That's all the metal I need!" },
      { false, 'FoundryWorker', "Slag Canyon Steel Company stocks are about to be through the roof!" },
      { false, 'FoundryWorker', "I'll have the Arc Furnace of the Month club deliver the furnace to your warehouse!" },
      { 'SELF', false, "Pleasure doing business with you" },
      { false, 'FoundryWorker', "Don't forget to pay your electrical bill!" },
    },
  },

  [3] = {
    running = {
      { false, 'FoundryWorker', "I thought you were finished coming around here!" },
    },
  },

  no_start = {
    { false, 'FoundryWorker', "This area is not open to the public!" },
  },
}

local function buildItemsDialogue(stage, items_needed)
  local dialogue = {}

  local first_part = 'I need '

  if stage > 1 then
    first_part = 'I still need '
  end

  for _, item_id in pairs({
    'raw_aluminum',
    'raw_steel',
    'raw_iron',
    'gold_ore',
    'raw_titanium',
    'raw_platinum',
    'raw_cobalt',
  }) do
    table.insert(dialogue, { 'FoundryWorker', 'item:' .. item_id, first_part .. items_needed[item_id] .. ' ' .. GItems.getItemName(item_id) })
  end

  return dialogue
end

AddEventHandler('blrp_quests:broadcastTalkToPed', function(character, ped_id, event_data)
  if ped_id ~= 'FoundryWorker' then
    return
  end

  local stage = tonumber(character.getUserData(QUEST_ID .. ':stage', false) or 1)

  -- Only run warehouse checks on stage 1. Stage 2+ will only be accessible if these checks pass
  if stage == 1 then
    local properties, owned_warehouses = getAccessibleProperties(character.source)
    local owns_suitable_warehouse = false

    for _, v in pairs(owned_warehouses) do
      if v.interior_shell == 'blrp_warehouse_medium_01' or v.interior_shell == 'blrp_warehouse_large_01' then
        owns_suitable_warehouse = true
      end
    end

    -- Check if the person owns any warehouse. If so, make sure it's suitable (medium or large model)
    if #owned_warehouses == 0 or not owns_suitable_warehouse then
      tQuests.runDialogue(character.source, { quest_dialogue.no_start })
      return
    end
  end

  local items_needed = character.getUserData(QUEST_ID .. ':items', false, true) or false

  if not items_needed then
    items_needed = {
      ['raw_aluminum'] = math.random(750, 1000),
      ['raw_steel'] = math.random(375, 750),
      ['raw_iron'] = math.random(150, 400),
      ['gold_ore'] = math.random(150, 400),
      ['raw_titanium'] = math.random(150, 400),
      ['raw_platinum'] = math.random(10, 30),
      ['raw_cobalt'] = math.random(2, 4),
    }

    character.setUserData(QUEST_ID .. ':items', items_needed, true, true)
  end

  if stage == 1 then
    local response = tQuests.runDialogue(character.source, { quest_dialogue[stage].running })

    if not response then
      return
    end

    response = tQuests.runDialogue(character.source, { buildItemsDialogue(stage, items_needed) })

    if not response then
      return
    end

    character.log('QUESTS', 'Started arc furnace quest')
    character.setUserData(QUEST_ID .. ':stage', 2, true)
    return
  elseif stage == 2 then
    local still_needs = false

    for _, item_id in pairs({
      'raw_aluminum',
      'raw_steel',
      'raw_iron',
      'gold_ore',
      'raw_titanium',
      'raw_platinum',
      'raw_cobalt',
    }) do
      local has_quantity = character.getItemQuantity(item_id)
      local needed_quantity = items_needed[item_id]
      local item_name = GItems.getItemName(item_id)

      if needed_quantity > 0 then
        local taking_quantity = math.min(has_quantity, needed_quantity)

        local dialogue = {
          { 'FoundryWorker', 'item:' .. item_id, 'I still need ' .. needed_quantity .. ' ' .. item_name }
        }

        if has_quantity > 0 then
          table.insert(dialogue, { 'item:' .. item_id, false,
           {
             'Hand over ' .. taking_quantity .. ' ' .. item_name,
             'Keep ' .. item_name,
           },
          })
        end

        local response = tQuests.runDialogue(character.source, { dialogue })

        if not response then
          return
        end

        if has_quantity > 0 and response == 1 and character.take(item_id, taking_quantity, false) then
          items_needed[item_id] = items_needed[item_id] - taking_quantity

          character.setUserData(QUEST_ID .. ':items', items_needed, true, true)
          character.animate({{ 'mp_common', 'givetake1_a', 1 }}, true, false)
          character.log('QUESTS', 'Gave metal for arc furnace quest', {
            item_id = item_id,
            quantity = taking_quantity,
          })
        end
      end

      -- Double check if we still need more of this item after handing some in
      if items_needed[item_id] > 0 then
        still_needs = true
      end
    end

    if still_needs then
      return
    end

    tQuests.runDialogue(character.source, { quest_dialogue[stage].running })

    local properties, owned_warehouses = getAccessibleProperties(character.source)

    local suitable_warehouses = {}

    -- Figure out which warehouses the player owns are suitable for the furnace
    for _, v in pairs(owned_warehouses) do
      if v.interior_shell == 'blrp_warehouse_medium_01' or v.interior_shell == 'blrp_warehouse_large_01' then
        table.insert(suitable_warehouses, v.address)
      end
    end

    -- If none, error. Don't complete the quest
    if #suitable_warehouses == 0 then
      character.notify('You have no warehouses suitable to complete this quest')
      return
    end

    local selected_business_id = nil

    -- If one, use that business ID
    if #suitable_warehouses == 1 then
      for _, v in pairs(owned_warehouses) do
        if v.address == suitable_warehouses[1] then
          selected_business_id = v.business_id
        end
      end
    elseif #suitable_warehouses > 1 then
      local selected_address = tUi.triggerFormWait(character.source, {
        {
          header = 'Select Warehouse to Receive Arc Furnace',
          fields = {
            {
              id = 'address',
              txt = 'Target Warehouse',
              options = suitable_warehouses,
              grid_column_start = 'span 3',
            }
          }
        }
      })

      if not selected_address then
        return
      end

      for _, v in pairs(owned_warehouses) do
        if v.address == selected_address.address then
          selected_business_id = v.business_id
        end
      end
    end

    if not selected_business_id then
      character.notify('You have no warehouses suitable to complete this quest')
      return
    end

    local existing_furnace = MySQL.single.await('SELECT id FROM core_character_furniture WHERE business_id = ? and name_model = "prop_bl_arcfurnace"', {
      selected_business_id
    })

    if existing_furnace then
      character.notify('This property already has an arc furnace and cannot have another')
      return
    end

    -- Add furniture
    MySQL.insert.await('INSERT INTO core_character_furniture (business_id, name_model, name_display) VALUES (?, ?, ?)', {
      selected_business_id, 'prop_bl_arcfurnace', 'Arc Furnace'
    })

    character.setUserData(QUEST_ID .. ':stage', 3, true)
    character.log('QUESTS', 'Completed arc furnace quest, added furnace to warehouse', {
      business_id = selected_business_id
    })
  elseif stage == 3 then
    tQuests.runDialogue(character.source, { quest_dialogue[stage].running })
  end
end)
