player_data_flags = {
  group = {
    ['LEO'] = 1,
    ['LSFD'] = 2,
    ['LSPD'] = 4,
    ['Sheriff'] = 8,
    ['DOC'] = 16,
    ['FIB'] = 32,
    ['SAHP'] = 64,
    ['NYSP'] = 128,
    ['Security'] = 256,
    ['<PERSON>'] = 512,
    ['Dispatch'] = 1024,
    ['DOJ'] = 2048,
    ['Lawyer'] = 4096,
    ['Ranger_Internal'] = 8192,
    ['TMU_Internal'] = 16384,
    ['Rocky Road Towing'] = 32768,
    ['Tow Truck'] = 65536,
  },
}

function makePlayerFlags(context, ...)
  local context_flags = player_data_flags[context]

  if not context_flags then
    return 0
  end

  local target_flags = 0

  for _, target_flag in pairs({...}) do
    if context_flags[target_flag] then
      target_flags = target_flags | context_flags[target_flag]
    end
  end

  return target_flags
end

function hasAnyPlayerFlags(context, flags, ...)
  local context_flags = player_data_flags[context]

  if not context_flags then
    return
  end

  if not flags then
    return false
  end

  for _, target_flag in pairs({...}) do
    if context_flags[target_flag] and (flags & context_flags[target_flag] ~= 0) then
      return true
    end
  end

  return false
end

function getPlayerFlags(context, flags)
  local context_flags = player_data_flags[context]

  if not context_flags then
    return
  end

  local flags_array = {}

  for flag, flag_value in pairs(context_flags) do
    if flags & flag_value ~= 0 then
      table.insert(flags_array, tostring(flag))
    end
  end

  return flags_array
end
