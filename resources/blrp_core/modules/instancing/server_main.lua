local instances = {}
local instance_debug = false
local instance_idx = 10000 -- Start at 10000 to deconflict with PlasmaGame instancing

function getInstanceInformation(bucket)
  return instances[bucket]
end

local instance_global = 0 -- Global instance (non-instanced) bucket

Citizen.CreateThread(function()
  SetRoutingBucketEntityLockdownMode(instance_global, 'no_dummy')

  for _, player in pairs(GetPlayers()) do
    local state = Player(player).state

    state.bucket_number = 0
    state.bucket_name = 'global'
  end
end)

function SetPlayerRoutingBucketInternal(player, instance_number, instance_name, lockdown_mode, population_mode)
  player = tonumber(player)

  SetPlayerRoutingBucket(player, instance_number)

  if lockdown_mode ~= nil then
    SetRoutingBucketEntityLockdownMode(tonumber(instance_number), lockdown_mode)
  end

  if population_mode ~= nil then
    SetRoutingBucketPopulationEnabled(tonumber(instance_number), population_mode)
  end

  local state = Player(player).state

  state.bucket_number = instance_number
  state.bucket_name = instance_name
end

core.event('instance:enterInstance', function(instance_name, individual, persist, lockdown_mode, population_mode)
  if instance_debug then
    print('^3[INSTANCES] enterInstance Before', json.encode(instances))
  end

  local player_source = tostring(source)

  if individual then
    instance_name = instance_name .. '-' .. player_source
  end

  TriggerEvent('instance:setNamed', instance_name, player_source, persist, lockdown_mode, population_mode)

  if instance_debug then
    print('^3[INSTANCES] enterInstance After', json.encode(instances))
  end
end)

core.event('instance:leaveInstance', function(instance_name, individual)
  if instance_debug then
    print('^3[INSTANCES] leaveInstance Before', json.encode(instances))
  end

  local player_source = tostring(source)

  if individual then
    instance_name = instance_name .. '-' .. player_source
  end

  TriggerEvent('instance:removeFromNamed', instance_name, player_source)

  if instance_debug then
    print('^3[INSTANCES] leaveInstance After', json.encode(instances))
  end
end)

AddEventHandler('instance:deletePersistentInstance', function(instance_name)
  if instance_debug then
    print('^3[INSTANCES] deletePersistentInstance Before', json.encode(instances))
  end

  local bucket_target = nil

  for bucket_number, bucket_data in pairs(instances) do
    if bucket_data.name == instance_name then
      bucket_target = bucket_number
    end
  end

  if not bucket_target then
    return
  end

  local bucket = instances[bucket_target]

  if not bucket then
    return
  end

  if not bucket.persist then
    return
  end

  for target_key, target_source in ipairs(bucket.sources) do
    TriggerEvent('instance:removeFromNamed', instance_name, tostring(target_source))
  end

  instances[bucket_target] = nil

  if instance_debug then
    print('^3[INSTANCES] deletePersistentInstance After', json.encode(instances))
  end
end)

-- do_switch parameter is used internally for handling when one script wants to
-- move a player into an instance when they're already in an instance
-- do_switch = true will remove from all instances, so if the player is switching
-- between two non-global instances and do_switch = true, they will be visible
-- for a short time
core.event('instance:removeFromAll', function(player, do_switch)
  if not player then player = source end

  if do_switch == nil then do_switch = true end

  local player_source = tostring(player)

  for bucket_number, bucket_data in pairs(instances) do
    for target_key, target_source in ipairs(bucket_data.sources) do
      if target_source == player_source then
        table.remove(instances[bucket_number].sources, target_key)
      end
    end

    if #instances[bucket_number].sources == 0 and not instances[bucket_number].persist then
      instances[bucket_number] = nil
    end
  end

  if do_switch then
    TriggerClientEvent('core:client:instancing:routingBucketChanging', tonumber(player_source), instance_global, 'global')
    TriggerEvent('core:server:instancing:routingBucketChanging', tonumber(player_source), instance_global, 'global')

    SetTimeout(200, function()
      SetPlayerRoutingBucketInternal(player_source, instance_global, 'global')
      TriggerClientEvent('core:client:instancing:routingBucketChanged', tonumber(player_source), instance_global, 'global')
      TriggerEvent('core:server:instancing:routingBucketChanged', tonumber(player_source), instance_global, 'global')
    end)
  end
end)

function removeFromInstance(instance_name, player)
  if not player then player = source end

  local player_source = tostring(player)

  for bucket_number, bucket_data in pairs(instances) do
    if bucket_data.name == instance_name then
      for target_key, target_source in ipairs(bucket_data.sources) do
        if target_source == player_source then
          table.remove(instances[bucket_number].sources, target_key)

          if GetPlayerRoutingBucket(player_source) == bucket_number then
            TriggerClientEvent('core:client:instancing:routingBucketChanging', tonumber(player_source), instance_global, 'global')
            TriggerEvent('core:server:instancing:routingBucketChanging', tonumber(player_source), instance_global, 'global')

            SetTimeout(200, function()
              SetPlayerRoutingBucketInternal(player_source, instance_global, 'global')
              TriggerClientEvent('core:client:instancing:routingBucketChanged', tonumber(player_source), instance_global, 'global')
              TriggerEvent('core:server:instancing:routingBucketChanged', tonumber(player_source), instance_global, 'global')
            end)
          end
        end
      end

      if #instances[bucket_number].sources == 0 and not instances[bucket_number].persist then
        instances[bucket_number] = nil
      end
    end
  end
end

core.event('instance:removeFromNamed', removeFromInstance)

-- Non-networked on purpose
function setInstanceNamed(instance_name, player, persist, lockdown_mode, population_mode)
  if instance_debug then
    print('^3[INSTANCES] setInstanceNamed Before', json.encode(instances))
  end

  if not player then
    player = source
  end

  local state = Player(player).state

  if state.bucket_name == instance_name then
    return false
  end

  local player_source = tostring(player)

  -- This will prevent the player from exsiting in two routing buckets in this
  -- module
  TriggerEvent('instance:removeFromAll', player_source, false)

  local bucket_target = nil
  local bucket_new = false

  for bucket_number, bucket_data in pairs(instances) do
    if bucket_data.name == instance_name then
      bucket_target = bucket_number
    end
  end

  if not bucket_target then
    bucket_new = true
    instance_idx = instance_idx + 1
    bucket_target = instance_idx
  end

  if bucket_new then
    local bucket_data = {
      name = instance_name,
      sources = {}
    }

    if persist then
      bucket_data.persist = true
    end

    instances[bucket_target] = bucket_data
  end

  table.insert(instances[bucket_target].sources, player_source)

  TriggerClientEvent('core:client:instancing:routingBucketChanging', tonumber(player_source), bucket_target, instance_name)
  TriggerEvent('core:server:instancing:routingBucketChanging', tonumber(player_source), bucket_target, instance_name)

  SetTimeout(200, function()
    SetPlayerRoutingBucketInternal(player_source, bucket_target, instance_name, lockdown_mode, population_mode)
    TriggerClientEvent('core:client:instancing:routingBucketChanged', tonumber(player_source), bucket_target, instance_name)
    TriggerEvent('core:server:instancing:routingBucketChanged', tonumber(player_source), bucket_target, instance_name)
  end)

  if instance_debug then
    print('^3[INSTANCES] setInstanceNamed After', json.encode(instances))
  end
end

RegisterNetEvent('instance:setNamed', setInstanceNamed)
exports('SetInstanceNamed', setInstanceNamed)

AddEventHandler('playerDropped', function()
  local player_source = tostring(source)

  for bucket_number, bucket_data in pairs(instances) do
    for target_key, target_source in ipairs(bucket_data.sources) do
      if target_source == player_source then
        table.remove(instances[bucket_number].sources, target_key)
      end
    end

    if #instances[bucket_number].sources == 0 and not instances[bucket_number].persist then
      instances[bucket_number] = nil
    end
  end
end)
