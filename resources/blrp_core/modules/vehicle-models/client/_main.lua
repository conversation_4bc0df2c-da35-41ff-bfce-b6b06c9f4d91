function localiseVehicleModel(model_name)
  local model_hash = GetHashKey(model_name)

  -- Bypass for direct hashes
  if tonumber(model_name) == model_name then
    model_hash = model_name
  end

  local vehicleModelName = GetDisplayNameFromVehicleModel(model_hash)
  local vehicleModelNameLocalised = GetLabelText(vehicleModelName)

  if vehicleModelNameLocalised and vehicleModelNameLocalised ~= 'NULL' then
    return vehicleModelNameLocalised
  end

  return nil
end

local make_display = {
  ['ALBANY'] = 'Albany',
  ['ANNIS'] = 'Annis',
  ['BENEFAC'] = 'Benefactor',
  ['BF'] = 'BF',
  ['BOLLOKAN'] = 'Bollokan',
  ['BRAVADO'] = 'Bravado',
  ['BRUTE'] = 'Brute',
  ['BUCKING'] = 'Bukingham',
  ['CANIS'] = 'Canis',
  ['CHARIOT'] = 'Chariot',
  ['CHEVAL'] = 'Cheval',
  ['COIL'] = 'Coil',
  ['DECLASSE'] = 'Declasse',
  ['DEWBAUCH'] = 'Dewbauchee',
  ['DINKA'] = 'Dinka',
  ['DUNDREAR'] = 'Dundreary',
  ['EMPEROR'] = 'Emperor',
  ['ENUS'] = 'Enus',
  ['FATHOM'] = 'Fathom',
  ['GALLIVAN'] = 'Gallivanter',
  ['GROTTI'] = 'Grotti',
  ['HIJAK'] = 'Hijak',
  ['HVY'] = 'HVY',
  ['IMPONTE'] = 'Imponte',
  ['INVERTO'] = 'Invetero',
  ['JACKSHP'] = 'Jacksheepe',
  ['JOBUILT'] = 'Jobuilt',
  ['KARIN'] = 'Karin',
  ['KRAKEN'] = 'Kraken',
  ['LAMPADA'] = 'Lampadati',
  ['LCC'] = 'LCC',
  ['MAIBATSU'] = 'Maibatsu',
  ['MAMMOTH'] = 'Mammoth',
  ['MAXWELL'] = 'Maxwell',
  ['MTL'] = 'MTL',
  ['NAGASAKI'] = 'Nagasaki',
  ['OBEY'] = 'Obey',
  ['OCELOT'] = 'Ocelot',
  ['OVERFLOD'] = 'Overflod',
  ['PEGASSI'] = 'Pegassi',
  ['PFISTER'] = 'Pfister',
  ['PRINCIPL'] = 'Principe',
  ['PROGEN'] = 'Progen',
  ['RUNE'] = 'Rune',
  ['SCHYSTER'] = 'Schyster',
  ['SHITZU'] = 'Shitzu',
  ['SPEEDOPH'] = 'Speedophile',
  ['STANLEY'] = 'Stanley',
  ['TRUFFADE'] = 'Truffade',
  ['UBERMACH'] = 'Ubermacht',
  ['VAPID'] = 'Vapid',
  ['VULCAR'] = 'Vulcar',
  ['VYSSER'] = 'Vysser',
  ['WEENY'] = 'Weeny',
  ['WESTERN'] = 'Western',
  ['WILLARD'] = 'Willard',
  ['ZIRCONIU'] = 'Zirconium',
}

tCore.getVehicleModelInformation = function()
  local models = GetAllVehicleModels()
  local classes = {}
  local makes = {}
  local names = {}

  for _, model in pairs(models) do
    local hash = GetHashKey(model)

    classes[model] = GetVehicleClassFromName(hash)

    local make = GetMakeNameFromVehicleModel(hash) or ''

    make = string.upper(make)

    if make == 'CARNOTFOUND' or make == '' then
      make = 'UNK'
    end

    if make_display[make] then
      make = string.upper(make_display[make])
    end

    makes[model] = make
    names[model] = string.upper(localiseVehicleModel(model) or 'UNK')
  end

  return models, classes, makes, names
end
