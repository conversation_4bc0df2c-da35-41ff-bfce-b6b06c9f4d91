-- RegisterNetEvent('blrp_core:server:itemEvents:troll', function(_, event_data)
--   local character = core.character(source)
--   local rand = math.random(10000, 90000)
--   local paper_id = tonumber('8630' .. rand)
--   local image, recipe, paper_title

--   if event_data.item == 'AK-47' then
--       image = "https://i.gyazo.com/448c3ebf85541a2234a2b51c15a0e659.jpg"
--       paper_title = "AK-47"
--       recipe = {
--         ['craft_gun_oil'] = 4,
--         ['craft_spring'] = 4,
--         ['craft_steel_pipe'] = 3,
--         ['craft_metal_gear_pieces'] = 8,
--         ['electric_torch'] = 1,
--         ['firearm_body'] = 1,
--       }
--   elseif event_data.item == '7.62x39 Ammo' then
--       image = "https://i.gyazo.com/1a5cf18e3a6745cf84c9c514b6193041.jpg"
--       paper_title = "7.62x39 Ammo"
--       recipe = {
--         ['craft_copper'] = 4,
--         ['craft_nitrate'] = 2,
--         ['craft_charcoal'] = 5,
--       }
--   else
--       character.notifyNew('Invalid item type.', 5000, 'error')
--       return
--   end

--   local paper_contents = string.format([[
--   [nobg]
--   <table style="border-collapse: collapse; margin-left: 50%%;">
--   <tr>
--   <td colspan="3" style="text-align: center; border: 3px solid black; padding: 10px;">
--       <img src="%s" alt="Logo" width="150" style="display: block; margin-left: auto; margin-right: auto;">
--   </td>
--   </tr>
--   </table>
--   ]], image)

--   local has_items, missing_items = character.hasItemQuantities(recipe)

--   if not has_items then
--       for _, missing_item_data in ipairs(missing_items) do
--           character.notify('Missing ' .. missing_item_data.amount .. ' ' .. GItems.getItemName(missing_item_data.name))
--       end
--       return false
--   end

--   local progress = character.progressPromise('Crafting ' ..event_data.item, 20, {
--     animation = {
--       animDict = 'blanim@amb@clubhouse@tutorial@bkr_tut_ig3@',
--       anim = 'machinic_loop_mechandplayer',
--       flags = 49,
--     }
--   })

--   if not progress then
--       return
--   end

--   -- Take the required items
--   for item, amount in pairs(recipe) do
--       character.take(item, amount)
--   end

--   -- Give the crafted item
--   TriggerEvent('blrp_paper:server:givePaperDirectSafe', character.source, paper_id, paper_title, paper_contents, 1)
--   character.notifyNew('Ill just keep these materials', 5000, 'error')
-- end)
