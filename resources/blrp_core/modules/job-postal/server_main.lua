pGoPostal = {}
P.bindInstance('gopostal', pGoPostal)

local active_routes = {}

Citizen.CreateThread(function()
  Citizen.Wait(1)

  for _, type in pairs(postal_config.mail_types) do
    beginRegisterCategory('postal')

    registerItem('gopostal_' .. type.type, type.type:gsub("^%l", string.upper), type.weight, {
      {
        name = 'Open',
        callback = function(character, item_id)
          TriggerEvent('blrp_postoffice:server:tryOpenMail', character.source, item_id)
        end
      }
    })

    registerItem('gopostal_' .. type.type .. '_open', 'Opened ' .. type.type:gsub("^%l", string.upper), type.open_weight)

    endRegisterCategory()
  end
end)

AddEventHandler('core:server:registerSelectedPlayer', function(source, character)
  SetTimeout(100, function()
    core.character(source).drawInteracts(postal_config.interactions)
  end)
end)

AddEventHandler('playerDropped', function()
  source_cleanup(source)
end)

core.event('core:server:postal:complete', function()
  source_cleanup(source)
end)

core.event('core:server:postal:clockout', function(player)
  source_cleanup(player)
end)

core.event('core:server:postal:returnMail', function()
  local character = core.character(source)

  local items = character.getAllItems()

  for idname, item in pairs(items) do
    if string.find(idname, "gopostal_") then
      character.take(idname, item.amount, true)
    end
  end

  source_cleanup(source)
end)

pGoPostal.tryDeliverMail = function(destination)
  local character = core.character(source)

  if not character.hasGroup('Postal Worker') then
    character.notify('You must be clocked in as a postal worker to deliver mail')
    return
  end

  character.take('gopostal_' .. destination.type, destination.item_count, true, function(taken)
    if taken then
      -- Adjust base payment to be within the defined minimum and maximum range (see config)

      local minimum_payout = postal_config.mail_types[destination.type].minimum_payout
      local maximum_payout = postal_config.mail_types[destination.type].maximum_payout

      local base_payment = math.max(minimum_payout, math.ceil(destination.depot_distance * destination.coefficient * 0.11))
      base_payment = math.min(maximum_payout, base_payment)

      local payout = base_payment * destination.item_count

      -- Call out to postoffice if this is a player-bound piece of mail
      if destination.mail_id ~= nil then
        TriggerEvent('blrp_postoffice:server:tryDeliverMail', source, destination.mail_id)
      end

      if destination.item_count <= 0 or destination.item_count > postal_config.mail_types[destination.type].maximum then
        character.ban('scripting perm', 0, '[Injection] Attempted to inject GoPostal job by modifying item count above maximum', true)
        return
      end

      local playerCoords = GetEntityCoords(GetPlayerPed(source))
      local distance = #(vector3(destination.x, destination.y, destination.z) - playerCoords)
      if distance > 10.0 then
        character.log("MAIL", "Tried to deliver mail but was not near the destination", {
          destination_data = destination,
          playerCoords = playerCoords,
        })
        return
      end

      if character.get('has_felonies') then
        payout = math.floor(payout * 0.70)
      end

      character.log("MAIL", "Delivered " .. destination.item_count .. " " .. destination.type)
      character.dpEmote('pickup')
      character.notify('$' .. payout .. ' has been deposited into your bank account')
      character.log('MAIL', 'Paid $' ..payout.. ' to bank for delivering' .. destination.item_count .. ' ' .. destination.type)
      character.giveBankMoney(payout)
      character.client("core:client:postal:nextDestination")
    else
      character.notifyError('You don\'t seem to have the mail for this address, have you checked your mail truck?')
    end
  end)
end

core.event('core:server:postal:routeAndLoadMail', function(entity_nid, event_data)
  local character = core.character(source)
  local entity = NetworkGetEntityFromNetworkId(entity_nid)
  local state = Entity(entity).state
  local veh_character_id = state.owner_char_id
  local server_uid = state.server_uid
  local character_id = character.get('id')

  local can_access = true

  if not state.business and veh_character_id ~= character_id then
    can_access = false
  end

  if state.business and not character.hasGroup(state.business) then
    can_access = false
  end

  if not can_access then
    character.notify('You can only load mail into your own vehicle, or business vehicles')
    return
  end

  if not character.hasGroup('Postal Worker') then
    character.notify('You must be clocked in as a postal worker to pick up mail')
    return
  end

  local target_weight = math.random(postal_config.minimum_load_kg, postal_config.maximum_load_kg)

  local route_weight = 0
  local route_types = {}
  local route_type_counts = {}

  local block_pickup = false

  for _, v in ipairs(active_routes) do
    if v.source_id == source then
      block_pickup = true
    end
  end

  if block_pickup then
    character.notify('You already have mail to deliver!')
    return
  end

  -- Inject player-bound mail
  MySQL.Async.fetchAll("SELECT * FROM mail_sent WHERE system_location = 'vinewood' AND sent = true AND delivered = false AND sender_identifier != @id ORDER BY checkout_time LIMIT 1", {
    ['@id'] = character.get('identifier')
  }, function(result)
    local player_mail_addition = nil

    if #result > 0 then
      result = result[1]

      MySQL.Async.execute('UPDATE mail_sent SET checkout_time = NOW() WHERE id = @id', { ['@id'] = result.id })

      player_mail_addition = {
        type = result.mail_type,
        weight = postal_config.mail_types[result.mail_type].weight,
        coefficient = postal_config.mail_types[result.mail_type].coefficient,

        item_count = 1,
        total_weight = postal_config.mail_types[result.mail_type].weight,

        x = result.x,
        y = result.y,
        z = result.z,

        mail_id = result.id
      }

      if route_type_counts[result.mail_type] == nil then
        route_type_counts[result.mail_type] = 0
      end

      route_type_counts[result.mail_type] = route_type_counts[result.mail_type] + 1
    end

    if player_mail_addition ~= nil then
      route_weight = route_weight + player_mail_addition.total_weight
    end

    -- Build packages to be delivered based on weighted random
    while route_weight < target_weight do
      local mail_type = get_random_mail_type(target_weight - route_weight)

      route_weight = route_weight + mail_type.total_weight

      if route_type_counts[mail_type.type] == nil then
        route_type_counts[mail_type.type] = 0
      end

      route_type_counts[mail_type.type] = route_type_counts[mail_type.type] + mail_type.item_count

      table.insert(route_types, mail_type)
    end

    -- Grab dropoff locations
    MySQL.Async.fetchAll('SELECT * FROM mail_locations ORDER BY RAND() LIMIT @limit', { ['@limit'] = #route_types }, function(result)
      local _route = table_merge(result, route_types)

      if player_mail_addition ~= nil then
        table.insert(_route, player_mail_addition)
      end

      local active_routing = {}

      local function has_been_routed(id)
          for index, value in ipairs(active_routing) do
              if value.id == id then
                  return true
              end
          end

          return false
      end

      local sorted = 0
      local initial_location = nil

      for _, depot_coords in pairs(postal_config.depot_locations) do
        if #(event_data.position - depot_coords) < 100.0 then
          initial_location = depot_coords
        end
      end

      if not initial_location then
        character.notify('Error generating mail route')
        return
      end

      local last_point = initial_location

      -- Sort route
      -- Attempts to organize points into the shortest distance between subsequent points
      while sorted < #_route do
        local cached_point = {distance = -1}

        for _, route_point in ipairs(_route) do
          if not has_been_routed(route_point.id) then
            local distance = get_3d_distance(last_point, route_point)

            if cached_point.distance == -1 or distance < cached_point.distance then
              route_point['distance'] = distance
              cached_point = route_point
            end
          end
        end

        sorted = sorted + 1
        cached_point['sequence'] = sorted
        cached_point['depot_distance'] = get_3d_distance(initial_location, cached_point)
        table.insert(active_routing, cached_point)
        last_point = cached_point
      end

      character.progress('Loading mail', 1, 9, function()
        character.dpEmote('pickup')
        table.insert(active_routes, {source_id = character.source})
        character.log("MAIL", "Loaded mail")
        character.client('core:client:postal:receiveRouting', active_routing)

        local boxvilleChestName = 'vehicle:' .. server_uid .. ':tr'

        core.chest(boxvilleChestName).fetchContents(function(items)
            if not items then items = {} end

            for type_name, type_count in pairs(route_type_counts) do
              local inventory_name = 'gopostal_' .. type_name

              items[inventory_name] = { amount = type_count }

              character.notify("Loaded " .. type_count .. " " .. type_name:gsub("^%l", string.upper) .. "(s)")
            end

            core.chest(boxvilleChestName).setContents(items)
        end)
      end)
    end)
  end)
end)

function source_cleanup(source)
  local character = core.character(source)

  for k, v in ipairs(active_routes) do
    if v.source_id == source then
      active_routes[k] = nil
    end
  end

  character.client('core:client:postal:cleanup')
end

function get_random_mail_type(max_weight)
  local n = 0
  local random = math.random(1, 100)
  local selected_type = nil

  for _, type in pairs(postal_config.mail_types) do
    n = n + type.frequency
    selected_type = type

    if n >= random then
      break
    end
  end

  local item_count = math.random(selected_type.minimum, selected_type.maximum)

  local return_type = {
    type = selected_type.type,
    weight = selected_type.weight,
    coefficient = selected_type.coefficient,

    item_count = item_count,
    total_weight = selected_type.weight * item_count,
  }

  if return_type.total_weight > max_weight then
    return get_random_mail_type(max_weight)
  end

  return return_type
end

function get_3d_distance(point1, point2)
  return #(vector3(point2.x, point2.y, point2.z) - vector3(point1.x, point1.y, point1.z))
end
