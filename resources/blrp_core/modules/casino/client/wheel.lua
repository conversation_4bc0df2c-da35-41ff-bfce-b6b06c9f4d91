tCasinoWheel = {}
T.bindInstance('casinowheel', tCasinoWheel)

local wheel_root_coords = vector3(989.5837, 58.36107, 72.96479)

local wheel_comp_base = 0
local wheel_comp_wheel = 0
local wheel_comp_light_win = 0
local wheel_comp_light_jackpot = 0

function getWheelComponentObject(hash)
  return GetClosestObjectOfType(wheel_root_coords.x, wheel_root_coords.y, wheel_root_coords.z, 2.5, hash, 0, 0, 0)
end

function loadWheelComponents()
  wheel_comp_base = getWheelComponentObject(`vw_prop_vw_luckywheel_01a`)
  wheel_comp_wheel = getWheelComponentObject(`blrp_casino_wheel`)
  wheel_comp_light_win = getWheelComponentObject(`vw_prop_vw_luckylight_on`)
  wheel_comp_light_jackpot = getWheelComponentObject(`vw_prop_vw_jackpot_on`)
end

Citizen.CreateThread(function()
  while true do
    if #(GetEntityCoords(PlayerPedId()) - wheel_root_coords) <= 100.0 then
      loadWheelComponents()
    end

    Citizen.Wait(5000)
  end
end)

RegisterNetEvent('core:client:registerSelectedPlayer', function()
  RequestScriptAudioBank('DLC_VINEWOOD\\CASINO_GENERAL', false)
end)

function requestAndReturnGenderedAnimDict()
  local dict = 'anim_casino_a@amb@casino@games@lucky7wheel@male'

  if GetEntityModel(PlayerPedId()) == `mp_f_freemode_01` then
    dict = 'anim_casino_a@amb@casino@games@lucky7wheel@female'
  end

  RequestAnimDict(dict)

  while not HasAnimDictLoaded(dict) do
    Citizen.Wait(1)
  end

  return dict
end

tCasinoWheel.spinAnimation = function()
  local spin_position = GetObjectOffsetFromCoords(GetEntityCoords(wheel_comp_base), GetEntityHeading(wheel_comp_base), -0.9, -0.8, -1.0)

  local dict = requestAndReturnGenderedAnimDict()

  SetEntityHeading(PlayerPedId(), GetEntityHeading(wheel_comp_base))
  TaskPlayAnim(PlayerPedId(), dict, 'enter_right_to_baseidle', 8.0, -8.0, -1, 0, 0, false, false, false)

  while IsEntityPlayingAnim(PlayerPedId(), dict, 'enter_right_to_baseidle', 3) do
    Citizen.Wait(0)
    DisableAllControlActions(0)
    EnableControlAction(0, 249, true) -- VOIP PTT
  end

  TaskPlayAnim(PlayerPedId(), dict, 'enter_to_armraisedidle', 8.0, -8.0, -1, 0, 0, false, false, false)

  while IsEntityPlayingAnim(PlayerPedId(), dict, 'enter_to_armraisedidle', 3) do
    Citizen.Wait(0)
    DisableAllControlActions(0)
    EnableControlAction(0, 249, true) -- VOIP PTT
  end

  TaskPlayAnim(PlayerPedId(), dict, 'armraisedidle_to_spinningidle_high', 8.0, -8.0, -1, 0, 0, false, false, false)
end

tCasinoWheel.spinToSlice = function(slice_index, win, jackpot_win, win_sound)
  PlaySoundFromEntity(-1, 'Spin_Start', wheel_comp_wheel, 'dlc_vw_casino_lucky_wheel_sounds', false, 1)

  SetEntityVisible(wheel_comp_light_jackpot, false)
  SetEntityVisible(wheel_comp_light_win, false)

  local slice_rotation = (slice_index - 1) * 18 + 0.0

  local time_start = GetGameTimer()
  local desired_time = 9 -- seconds for the wheel to spin (desired, will be +/- 1 second depending on client performance)
  local tps = math.floor(1100 / desired_time)

  local target_time = 10
  local target_degrees = 360

  for i = 1, 1100 do
    SetEntityRotation(wheel_comp_wheel, 0, target_degrees, -1.184222, 0)

    if i < 50 then
      target_degrees = target_degrees - 1.5
    elseif i < 100 then
      target_degrees = target_degrees - 2.0
    elseif i < 150 then
      target_degrees = target_degrees - 2.5
    elseif i > 1060 then
      target_degrees = target_degrees - 0.3
    elseif i > 1030 then
      target_degrees = target_degrees - 0.6
    elseif i > 1000 then
      target_degrees = target_degrees - 0.9
    elseif i > 970 then
      target_degrees = target_degrees - 1.2
    elseif i > 940 then
      target_degrees = target_degrees - 1.5
    elseif i > 910 then
      target_degrees = target_degrees - 1.8
    elseif i > 880 then
      target_degrees = target_degrees - 2.1
    elseif i > 850 then
      target_degrees = target_degrees - 2.4
    elseif i > 820 then
      target_degrees = target_degrees - 2.7
    else
      target_degrees = target_degrees - 3.0
    end

    if i == 850 then
      target_degrees = math.random(slice_rotation - 4, slice_rotation + 9) + 0.0
    end

    if target_degrees > 360 then
      target_degrees = target_degrees + 0
    end

    if target_degrees < 0 then
      target_degrees = target_degrees + 360
    end

    local second = math.floor((GetGameTimer() - time_start) / 1000)
    local expected_tick = (second * tps)

    local waited = false

    if second >= 9 or i >= expected_tick then
      waited = true
      Citizen.Wait(0)
    end
  end

  if win then
    --[[
      Valid sounds:
        Win_Car
        Win_Cash
        Win_Chips
        Win_Clothes
        Win_Mystery
        Win
    ]]
    if win_sound == 'car' then
      PlaySoundFromEntity(-1, 'Win_Car', wheel_comp_wheel, 'dlc_vw_casino_lucky_wheel_sounds', false, 1)
    elseif win_sound == 'cash' then
      PlaySoundFromEntity(-1, 'Win_Cash', wheel_comp_wheel, 'dlc_vw_casino_lucky_wheel_sounds', false, 1)
    elseif win_sound == 'clothes' then
      PlaySoundFromEntity(-1, 'Win_Clothes', wheel_comp_wheel, 'dlc_vw_casino_lucky_wheel_sounds', false, 1)
    else
      PlaySoundFromEntity(-1, 'Win', wheel_comp_wheel, 'dlc_vw_casino_lucky_wheel_sounds', false, 1)
    end

    SetEntityVisible(wheel_comp_light_jackpot, false)
    SetEntityVisible(wheel_comp_light_win, false)

    Citizen.Wait(100)

    local flashes = 0

    while flashes <= 5 do
      if jackpot_win then
        SetEntityVisible(wheel_comp_light_jackpot, true)
      end

      SetEntityVisible(wheel_comp_light_win, true)

      Citizen.Wait(300)

      SetEntityVisible(wheel_comp_light_jackpot, false)
      SetEntityVisible(wheel_comp_light_win, false)

      Citizen.Wait(300)

      flashes = flashes + 1
    end
  end

  return true
end

RegisterNetEvent('core:client:casino:spinToSlice', function(target_player, slice_index, win, jackpot_win, win_sound)
  -- Don't do the event logic if we are the person spinning or we are too far from casino the ever see the spin happen
  if target_player == GetPlayerServerId(PlayerId()) or #(GetEntityCoords(PlayerPedId()) - wheel_root_coords) > 100.0 then
    return
  end

  tCasinoWheel.spinToSlice(slice_index, win, jackpot_win, win_sound)
end)
