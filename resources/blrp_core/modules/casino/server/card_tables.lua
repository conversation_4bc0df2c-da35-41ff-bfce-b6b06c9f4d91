pCasino.openCardTable = function(_, event_data)
  local character = core.character(source)
  local chest_id = 'dc_card_table_' .. event_data.position

  local item_ids = GItems.getItemsByCategory('card_decks')

  table.insert(item_ids, 'printed_paper')
  table.insert(item_ids, 'cash')
  table.insert(item_ids, 'poker_chip')

  character.openChest(chest_id, 100.0, false, {
    whitelisted_items = item_ids,
    max_slots = 50,
  })
end
