local casino_business_id = 0

Citizen.CreateThread(function()
  while casino_business_id == 0 do
    local definition = getCustomBusinessDefFromName('Diamond Casino')

    if definition and definition.id then
      casino_business_id = definition.id
    end

    Citizen.Wait(1000)
  end
end)

casino_employee_active_count = 0

exports('GetCasinoEmployeeActiveCount', function()
  return casino_employee_active_count
end)

function calculateCasinoEmployeeCount()
  local casino_employees = core.group('Diamond Casino Floor').activePlayers()
  local employee_count = 0

  for _, casino_employee in pairs(casino_employees) do
    if tCore.getInteriorNameAtPed(casino_employee.source) == 'Diamond Casino & Resort' then
      employee_count = employee_count + 1
    end
  end

  print('Setting active casino employees', employee_count)
  casino_employee_active_count = employee_count
end

Citizen.CreateThread(function()
  Citizen.Wait(5000)

  while true do
    calculateCasinoEmployeeCount()

    Citizen.Wait(60 * 1000)
  end
end)

RegisterNetEvent('core:server:casino:clockIn', function()
  local character = core.character(source)

  if not character.hasGroup('Diamond Casino') or character.hasGroup('Diamond Casino Floor') then
    return
  end

  TriggerEvent('vrp:server:addUserGroup', tonumber(character.get('identifier')), 'Diamond Casino Floor')
  character.notify('You have clocked into floor staff')
  calculateCasinoEmployeeCount()

  if casino_business_id ~= 0 then
    logBusinessAction(character, casino_business_id, 'Clocked into floor staff', 'DEFAULT')
  end
end)

RegisterNetEvent('core:server:casino:clockOut', function()
  local character = core.character(source)

  if not character.hasGroup('Diamond Casino Floor') then
    return
  end

  TriggerEvent('vrp:server:removeUserGroup', tonumber(character.get('identifier')), 'Diamond Casino Floor')
  character.notify('You have clocked out of floor staff')
  calculateCasinoEmployeeCount()

  if casino_business_id ~= 0 then
    logBusinessAction(character, casino_business_id, 'Clocked out of floor staff', 'DEFAULT')
  end
end)
