--local is_jailed = false
--
----core.event('jailbreak:client:setJailedState', function(state)
----    is_jailed = state
----end)
--
--local tamper_box_locations = {
--    { x = 1652.3989257813, y = 2564.3002929688, z = 45.553550720215 },
--}
--
--Citizen.CreateThread(function()
--    while true do
--        Citizen.Wait(1)
--        handleTamperPowerBoxesThread()
--    end
--end)
--
--function handleTamperPowerBoxesThread()
--    for _, coords in pairs(tamper_box_locations) do
--        doWhenPedInDistanceOfSet(4, coords, function()
--            doWhenPressed(coords, 'E', 'Press {key} to tamper with', function()
--                selectClosestPlayer(3, function(target)
--                    -- TriggerServerEvent('core:server:scanTargetFingerprint', target)
--                end)
--            end)
--        end)
--    end
--end