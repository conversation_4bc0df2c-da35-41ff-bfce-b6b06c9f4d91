PoliceDrugStings = {}

local active_enforcement = false

local function HasCelltowa(character) 
    for _, item_name in pairs({
        'phone_ct_a',
        'phone_ct_b',
        'phone_ct_c',
        'phone_ct_d',
        'phone_ct_e',
        'phone_ct_f',
        'phone_ct_g',
        'phone_ct_h'
    }) do
       if character.hasItemQuantity(item_name, 1) then
          return true
       end
    end

    return false
end


local Drug_Enforcement_Dialogue = {
    not_unlocked = function(ped_id)
        return{
            { false, ped_id, "Hey, I don't have anything for you right now, come back another time." },
        }
    end,

    sting_unlocked = function(ped_id)

    end,

    convert_prepaid = function()

    end,

    received_zaines_phone =function(ped_id)
        return{
            { 'SELF', false, "Hey <PERSON>, we just completed a drug operation and apprehended <PERSON><PERSON>." },
            { false, ped_id, "Hmm, this is <PERSON><PERSON> phone? What do you want me to do with it?" },
            { 'SELF', false, "Try to pull all his recent contacts so we can perform some stings. Can you do that?" },
            { 'item:zaines_prepaid', false, 'You hand him Zaines phone.' },
            { false, ped_id, "Certainly, come back with a Celltowa cellphone." },
        }
    end,

    receive_msri_listening = function(ped_id)
        return{
            { false, ped_id, "Ah you have the phone, perfect give it to me." },
            { 'SELF', false, "What is it for?" },
            { false, ped_id, "It's needed for the IMSI listener im going to give you." },
            { false, ped_id, "An IMSI Listener is a device that pretends to be a cell tower, when cellphones are used in range of it they connect to it and it listens to the data." },
            { false, ped_id, "You can't obviously listen to anything you want however, this specifically only tries to capture the numbers in Zaines Contacts that we received authorization to monitor." },
            { false, ped_id, "When a drug dealer you're watching receives a text from one of them, we're able to get that data and get the location of potential deals." },
            { false, ped_id, "But for the entirety of the device being on, you need to be stationary. You can't drive around." },
            { 'SELF', false, "How do I know what to look for?" },
            { false, ped_id, "These dealers will usually be in places somewhat hidden. Backyards, alley's and sometimes subway stations." },
            { false, ped_id, "You want to wait till you see the hand off, then BAM! Trap the dealer." },
            { 'item:imsi_catcher', false, 'You receive an IMSI Listener...' },
            { false, ped_id, "Good luck out there." },
        }
    end,

}
AddEventHandler('drug-enforcement:police-listen-deal', function(character)
    local PoliceCharacter = character
    local PolicePed = GetPlayerPed(PoliceCharacter.source)
    local position = GetEntityCoords(PolicePed);

    if not position then
        return
    end

    PoliceDrugStings[position] = {
        listening_LEO = PoliceCharacter.get('id'),
        deal_intercept = nil,
        deal_position = nil,
        deal_amt = nil,
        drug_item = nil,
    }

    character.notify('Listening to IMSI in range..')
end)

AddEventHandler('drug-enforcement:police-stop-listen', function(character)
    local PoliceCharacter = character
    local PolicePed = GetPlayerPed(PoliceCharacter.source)
    local position = GetEntityCoords(PolicePed);
    local target_data = nil
    for _ , sting_data in pairs(PoliceDrugStings) do
        print(sting_data.listening_LEO)
        print(PoliceCharacter.get('id'))
        if sting_data.listening_LEO == PoliceCharacter.get('id') then
            target_data = _
            break
        end

    end
    if target_data then
        PoliceDrugStings[target_data] = nil
        character.notify('Shutting down IMSI Listener..')
    end
end)

AddEventHandler('blrp_quests:broadcastTalkToPed', function(character, ped_id, event_data)
    if not ({
      ['VSPD_IT_GUY'] = true,
      })[ped_id] then
        return
    end

    if character.hasItemQuantity('zaines_prepaid',1) then
        tQuests.runDialogue(character.source, { Drug_Enforcement_Dialogue.received_zaines_phone('VSPD_IT_GUY')})    
        character.take('zaines_prepaid', 1)
        active_enforcement = true
        return
    end

    if active_enforcement then
        if not character.getUserData('DrugEnforcement') then
            if not HasCelltowa(character) then
                tQuests.runDialogue(character.source, { Drug_Enforcement_Dialogue.not_unlocked('VSPD_IT_GUY') })    
                return
            else
                tQuests.runDialogue(character.source, { Drug_Enforcement_Dialogue.receive_msri_listening('VSPD_IT_GUY') })
                character.give('imsi_catcher', 1)
                return
            end
        else

        end
    end
    
end)

Citizen.CreateThread(function()
    while true do
        for position, sting_data in pairs(PoliceDrugStings)do
            local listening_leo = core.characterFromId(sting_data.listening_LEO)
            local PolicePed = GetPlayerPed(listening_leo.source)
            local leo_position = GetEntityCoords(PolicePed);

            if not leo_position or listening_leo.distanceFrom(position) > 10 then
                listening_leo.notify('Turning off IMSI, moved too far from target.')
                PoliceDrugStings[position] = nil
                break
            end
        end
        Citizen.Wait(5000)
    end
end)