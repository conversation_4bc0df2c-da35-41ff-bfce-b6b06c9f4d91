iHunting = {}
T.bindInstance('hunting', iHunting)

local default_zones = {
  -- Zone map: https://i.redd.it/5cw11krz9kcz.jpg
  -- Legal places

  ['CALAFB']    = true, -- Calafia Bridge
  ['CANNY']     = true, -- Raton Canyon
  ['CCREAK']    = true, -- Cassidy Creek
  ['CMSW']      = true, -- Chiliad Mountain State Wilderness
  ['MTCHIL']    = true, -- Mount Chiliad
  ['MTJOSE']    = true, -- Mount Josiah
  ['NCHU']      = true, -- North Chumash
  ['PALFOR']    = true, -- Paleto Forest
  ['TONGVAH']   = true, -- Tongva Hills
  ['ZANCUDO']   = true, -- Zancudo River

  -- Illegal places

  ['PALHIGH']   = true, -- Palomino Highlands
  ['SANCHIA']   = true, -- San Chianski Mountain Range
}

local spawned_animals = {}
local monitor_threads = {}

iHunting.spawn = function(model, number, zone)
  local count = 0

  local spawned_netids = {}

  if not spawned_animals[zone] then
    spawned_animals[zone] = {}
  end

  local spawned_infected = 0

  while count <= number do
    Citizen.Wait(1000)

    local pos = GetEntityCoords(GetPlayerPed(-1), 1)

    -- Jenkins Farms
    if model == 'a_c_cow' or model == 'a_c_pig' then
      pos = vector3(2159.562, 4980.263, 41.388)
    end

    if model == 'a_c_hen' then
      pos = vector3(2126.112, 5012.872, 41.394)
    end

    if math.random(1, 2) == 1 then
      pos = pos + vector3(math.random(-175,-30), 0.0, 0.0)
    else
      pos = pos + vector3(math.random(30, 175), 0.0, 0.0)
    end

    if math.random(1, 2) == 1 then
      pos = pos + vector3(0.0, math.random(-175,-30), 0.0)
    else
      pos = pos + vector3(0.0, math.random(30, 175), 0.0)
    end

    pos = pos + vector3(0.0, 0.0, 999.0)

    local heading = math.random(0, 359) + .0
    local ground, posZ = GetGroundZFor_3dCoord(pos.x, pos.y, pos.z, false)
    local in_water, _ = GetWaterHeightNoWaves(pos.x, pos.y, posZ)
    local on_road = IsPointOnRoad(pos.x, pos.y, posZ)

    -- Jenkins Farms ranch restrictions
    if model == 'a_c_cow' or model == 'a_c_pig' or model == 'a_c_hen' then
      pos = getRanchSpawnPosition(model)
      posZ = pos.z
    end

    if ground and not in_water and not on_road then
      -- Yankton 2023 infected deer spawn
      local model_actual = model

      local function getMaxInfected()
        local stage = (exports.blrp_core:me().get('ny23quest:Animals:stage') or 1)

        if stage >= 7 then
          return 2
        elseif stage >= 9 then
          return 3
        end

        return 1
      end

      if
        GetResourceState('blrp_yankton') == 'started' and
        model == 'a_c_deer' and
        (
          (
            spawned_infected < getMaxInfected() and
            math.random(1, 100) <= 20
          ) or
          (
            count == (number - 1) and
            spawned_infected == 0
          )
        )
      then
        model_actual = model_actual .. '_02'
        spawned_infected = spawned_infected + 1
      end

      while not HasModelLoaded(model_actual) or not HasCollisionForModelLoaded(model_actual) do
        RequestModel(model_actual)
        Wait(1)
      end

      local animal = CreatePed(28, model_actual, pos.x, pos.y, posZ, heading, true, true)

      SetEntityAsMissionEntity(animal, true, true)

      Entity(animal).state:set('hunting_animal', true, true)

      if model == 'a_c_cow' or model == 'a_c_pig' then
        SetPedFleeAttributes(animal, 0, 0)
        SetBlockingOfNonTemporaryEvents(animal, true)
        TaskSetBlockingOfNonTemporaryEvents(animal, true)
      end

      if
        model == 'a_c_mtlion'
      then
        RegisterHatedTargetsAroundPed(animal, 200.0)
        SetCanAttackFriendly(animal, true, false)
      end

      TaskWanderStandard(animal, 10.0, 10)
      SetModelAsNoLongerNeeded(animal)
      SetForcePedFootstepsTracks(animal)

      count = count + 1
      local network_id = NetworkGetNetworkIdFromEntity(animal)

      if not pHunting.registerAnimal({ network_id }) then
        DeleteEntity(animal)
      else
        table.insert(spawned_animals[zone], { GetEntityModel(animal), network_id })
      end
    end
  end

  cleanupMonitor(zone)
end

function cleanupMonitor(zone)
  if monitor_threads[zone] then
    return
  end

  monitor_threads[zone] = true

  Citizen.CreateThread(function()
    while monitor_threads[zone] do
      Citizen.Wait(5 * 60 * 1000)
      if GetNameOfZone(GetEntityCoords(GetPlayerPed(-1))) ~= zone then
        monitor_threads[zone] = false

        for _, v in ipairs(spawned_animals[zone]) do
          local model, network_id = table.unpack(v)

          if NetworkDoesEntityExistWithNetworkId(network_id) then
            local entity = NetworkGetEntityFromNetworkId(network_id)

            if GetEntityModel(entity) == model then
              TriggerServerEvent("core:server:hunting:delete", network_id)
            end
          end
        end

        spawned_animals[zone] = {}
      end
    end
  end)
end

local mlionhash = `a_c_mtlion`

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(1000)
    local player = GetPlayerPed(-1)
    local zone = GetNameOfZone(GetEntityCoords(player))
    if zone ~= nil and default_zones[zone] then
      local target = GetClosestPed(25.0)
      if GetEntityModel(target) == mlionhash then
        TaskCombatPed(target, player, 0, 16)
      end
    end
  end
end)

function getRanchSpawnPosition(animal_name)
  -- The x/y below are funky so that they can be used accurately in math.random
  -- If decimals are applied, numbers are precise to 3 digits
  local pens = {
    ['a_c_cow'] = {
      {
        bound_sw = vector3(2164790, 4948542, 41.463),
        bound_ne = vector3(2183572, 4984231, 41.446),
      }
    },

    ['a_c_pig'] = {
      {
        bound_sw = vector3(2135835, 4977648, 41.439),
        bound_ne = vector3(2158360, 5010507, 41.438),
      }
    },

    ['a_c_hen'] = {
      {
        bound_sw = vector3(2118698, 4994476, 41.198),
        bound_ne = vector3(2133637, 5029491, 42.013),
      }
    }
  }

  if not pens[animal_name] then
    return nil
  end

  local pen = pens[animal_name][math.random(1, #pens[animal_name])]

  local x = math.random(pen.bound_sw.x, pen.bound_ne.x) / 1000
  local y = math.random(pen.bound_sw.y, pen.bound_ne.y) / 1000

  return vector3(x, y, math.max(pen.bound_sw.z, pen.bound_ne.z))
end
