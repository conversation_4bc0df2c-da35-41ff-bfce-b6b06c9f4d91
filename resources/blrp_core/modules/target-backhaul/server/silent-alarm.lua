RegisterNetEvent('core:server:target-backhaul:activatePanicAlarm', function(_, event_data)
  if not event_data.location_override then
    return
  end

  local character = core.character(source)

  if not event_data.groups or not event_data.groups[1] or not character.hasGroup(event_data.groups[1]) then
    return
  end

  local coords_initial = character.getCoordinates()

  if not character.request('Activate Panic Alarm?') then
    return
  end

  local coords_final = character.getCoordinates()

  if #(coords_initial - coords_final) > 1.5 then
    character.notify('You got too far from the button to press it')
    return
  end

  character.animate({ {'pickup_object', 'putdown_low', 1} })

  character.log('ACTION', 'Activated panic alarm / location = ' .. event_data.location_override)

  Citizen.Wait(math.random(5000, 9000))

  local msg = 'Panic Alarm Activated'

  if event_data.location_specific then
    msg = msg .. '<br/>Location: ' .. event_data.location_specific
  end

  core.group('Police').alert({
    coords = event_data.position,
    location_override = event_data.location_override,
    badge = 'Priority 2',
    badge_style = 'warning',
    msg = msg,
    icon = 'far fa-bell-exclamation',
    sound = 'ToneP2'
  })
end)
