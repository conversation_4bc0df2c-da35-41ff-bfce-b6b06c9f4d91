config_aptitudes = { }

function registerAptitude(id, name, definition)
  if not id or not name or not definition then
    print_r('Failed to define aptitude due to missing initial parameter', id, name, definition)
    return
  end

  for _, v in pairs({ 'max_level', 'xptolevel', 'leveltoxp' }) do
    if not definition[v] then
      print('Failed to define aptitude due to missing definition parameter', id, v)
      return
    end
  end

  definition.name = name

  config_aptitudes[id] = definition
end

exports('RegisterAptitude', registerAptitude)

registerAptitude('strength', 'Strength', {
  notify = true,
  max_level = 4,
  xptolevel = function(xp)
    return math.floor(math.max(1, ((math.sqrt(1 + 8 * xp / 14) - 1) / 2)))
  end,
  leveltoxp = function(level)
    return 14 * level * (level + 1) / 2
  end,
})

registerAptitude('boosting', 'Boosting', {
  notify = false,
  max_level = 20,
  xptolevel = function(xp)
    return math.floor((334 + math.sqrt(111556 + 1336 * xp)) / 668)
  end,
  leveltoxp = function(level)
    return 334 * (level * level) - (334 * level)
  end,
})

registerAptitude('trucking', 'Trucking', {
  notify = false,
  max_level = 11,
  xptolevel = function(xp)
    local level = math.floor((math.log((xp/25000), 1.5))+1)
    if level < 0 then level = 0 end
    return level
  end,
  leveltoxp = function(level)
    local xp = math.floor((25000 * math.pow(1.5, level-1)))
    if xp < 0 then xp = 0 end
    return xp
  end,
})

registerAptitude('gold-panning', 'Gold Panning', {
  notify = true,
  max_level = 5,
  xptolevel = function(xp)
    local baseXP = 75
    local multiplier = 4
    local level = 0

    while xp >= baseXP do
      xp = xp - baseXP
      baseXP = baseXP * multiplier
      level = level + 1
    end

    return level
  end,
  leveltoxp = function(level)
    local baseXP = 75
    local multiplier = 4
    local xp = 0

    for i = 1, level do
      xp = xp + baseXP
      baseXP = baseXP * multiplier
    end

    return xp
  end,
})

registerAptitude('drugruns', 'Drug Running', {
  notify = false,
  max_level = 20,

  xptolevel = function(xp)
    local last_level_xp = 0

    for level = 2, 20 do
      local level_xp = math.max(0, (math.floor(820 * (2 ^ (level / 2.5)) + (1/8) * (level * (level - 1)) - 1159)))

      if xp >= last_level_xp and xp < level_xp then
        return level - 1
      end
    end

    return 1
  end,

  leveltoxp = function(level)
    return math.max(0, (math.floor(820 * (2 ^ (level / 2.5)) + (1/8) * (level * (level - 1)) - 1159)))
  end,
})

registerAptitude('chop-shop', 'Chop Shop', {
  notify = false,
  max_level = 20,
  xptolevel = function(xp)
    return math.floor((334 + math.sqrt(111556 + 1336 * xp)) / 668)
  end,
  leveltoxp = function(level)
    return 334 * (level * level) - (334 * level)
  end,
})

