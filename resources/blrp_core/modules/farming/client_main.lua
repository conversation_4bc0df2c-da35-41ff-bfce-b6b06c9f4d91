local growing = false
local started = false
local timeout = 0
local action_performed = 0
local successful_steps = 0
local plant_coordinates = nil
local plant_hash = nil

local farming = {}
T.bindInstance('farming', farming)

farming.isStarted = function()
  return started
end

farming.plantSeed = function(seed_id)
  local seed_config = farming_config.flows[seed_id]

  if not seed_config then
    return false, 0
  end

  if started then
    return false, 0
  end

  started = true
  successful_steps = 0

  local ped_coordinates = GetEntityCoords(PlayerPedId())
  local ped_forward_vector = GetEntityForwardVector(PlayerPedId())

  plant_coordinates = ped_coordinates + (1.0 * ped_forward_vector)

  local plant_hashes = {}

  if seed_config.plant_hashes then
    plant_hashes = seed_config.plant_hashes
  else
    for i = 1, seed_config.steps do
      plant_hashes[i] = GetHashKey(seed_config.prop_name)
    end
  end

  for _, plant_hash in pairs(plant_hashes) do
    RequestModel(plant_hash)

    while not HasModelLoaded(plant_hash) do
      Citizen.Wait(1)
    end
  end

  local current_step = 0
  local failures = 0

  local plant_hash = plant_hashes[1]

  local object = CreateObject(plant_hash, plant_coordinates.x, plant_coordinates.y, plant_coordinates.z - seed_config.prop_offset_initial, true, true, false)

  while current_step < seed_config.steps and failures < 3 do
    if stepGrowth(seed_config.steps) then
      successful_steps = successful_steps + 1
      current_step = current_step + 1

      plant_hash = plant_hashes[current_step + 1]

      local reduction = seed_config.prop_offset_initial - (seed_config.prop_offset_increment * (current_step + 1))
      DeleteObject(object)
      object = CreateObject(plant_hash, plant_coordinates.x, plant_coordinates.y, plant_coordinates.z - reduction, true, true, false)
    else
      failures = failures + 1
    end
  end

  DeleteObject(object)

  started = false

  return true, successful_steps
end

function stepGrowth(total_steps)
  growing = true
  timeout = 10
  action_performed = 0

  local random_max = 4

  if total_steps == 3 then
    random_max = 3
  end

  local random = math.random(1, random_max)

  if random == 1 then
    condition = "The leaves are looking dry"
  elseif random == 2 then
    condition = "The soil is looking old"
  elseif random == 3 then
    condition = "Plant is looking overgrown"
  elseif random == 4 then
    condition = "Things are looking good"
  end

  while growing do
    Citizen.Wait(0)
    DisableControlAction(0, 174, true)
    DisableControlAction(0, 173, true)
    DisableControlAction(0, 175, true)

    if #(GetEntityCoords(PlayerPedId(), true) - plant_coordinates) <= 2 then
      DrawText3d(plant_coordinates.x,plant_coordinates.y,plant_coordinates.z+1,condition.." "..timeout.."s to complete action",0.35)
      missionText("~r~Left Arrow~w~ water plant~n~~r~Down Arrow~w~ turn soil~n~~r~Right Arrow~w~ prune leaves~n~", 1)

      if IsDisabledControlJustPressed(0, 174) then
        RequestAnimDict("weapon@w_sp_jerrycan")
        while not HasAnimDictLoaded("weapon@w_sp_jerrycan") do
          Citizen.Wait(100)
        end
        TaskPlayAnim(PlayerPedId(),"weapon@w_sp_jerrycan","fire", 8.0, -8, -1, 49, 0, 0, 0, 0)
        Citizen.Wait(5000)
        ClearPedTasks(PlayerPedId())
        growing = false
        timeout = 0
        action_performed = 1
      end

      if IsDisabledControlJustPressed(0, 173) then
        TaskStartScenarioInPlace(PlayerPedId(), "WORLD_HUMAN_GARDENER_PLANT", 0, 1)
        Citizen.Wait(5000)
        ClearPedTasks(PlayerPedId())
        growing = false
        timeout = 0
        action_performed = 2
      end

      if IsDisabledControlJustPressed(0, 175) then
        TaskStartScenarioInPlace(PlayerPedId(), "PROP_HUMAN_PARKING_METER", 0, 1)
        Citizen.Wait(5000)
        ClearPedTasks(PlayerPedId())
        growing = false
        timeout = 0
        action_performed = 3
      end

      if timeout < 1 then
        growing = false

        if action_performed == 0 then
          action_performed = 4
        end
      end
    elseif timeout < 1 then
      growing = false

      if action_performed == 0 then
        action_performed = -1
      end
    end
  end

  if action_performed == random then
    core.me().notify('Correct action performed')
    return true
  elseif action_performed == -1 then
    core.me().notify('You were too far away from the plant and missed a step')
    return false
  else
    core.me().notify('Incorrect action performed')
    return false
  end
end

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)
    if timeout > 0 then
      timeout = timeout - 1
    end
  end
end)

function DrawText3d(x, y, z, text, scale, r, g, b)
  local r = r or 255
  local g = g or 255
  local b = b or 255
  local onScreen,_x,_y=World3dToScreen2d(x,y,z)
  local px,py,pz=table.unpack(GetGameplayCamCoords())

  if onScreen then
    SetTextScale(scale, scale)
    SetTextFont(0)
    SetTextProportional(1)
    SetTextColour(r,g,b,255)
    SetTextDropshadow(0, 0, 0, 0, 55)
    SetTextEdge(2, 0, 0, 0, 150)
    SetTextDropShadow()
    SetTextOutline()
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x,_y)
  end
end

function missionText(text, time)
	ClearPrints()
	SetTextEntry_2("STRING")
	AddTextComponentString(text)
	DrawSubtitleTimed(time, 1)
end
