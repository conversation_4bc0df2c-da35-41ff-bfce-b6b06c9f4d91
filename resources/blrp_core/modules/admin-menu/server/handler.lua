local function sendToDiscord(name, message)
  if GlobalState.is_dev then message = '[DEVELOPMENT SERVER] '..message end
  local discordUrl = GetConvar('discord_url_string')
  if discordUrl ~= nil and discordUrl ~= "" then
    PerformHttpRequest(discordUrl, function(err, text, headers)
    end, 'POST', json.encode({ username = name, content = message }), { ['Content-Type'] = 'application/json' })
  end
end

function teleportTo(me_character, target_character)
  if not me_character.hasPermissionCore('admin.tp.to') then
    return
  end

  local destination_coords = target_character.getCoordinates()

  if target_character.get('esp_hidden') and not me_character.hasGroup('superadmin') then
    return
  end

  -- if in instance
  local bucket = target_character.getBucket()

  if bucket ~= 0 then
    local instance_info = getInstanceInformation(bucket)

    if
      instance_info and
      string.match(instance_info.name, 'property-') and
      me_character.request('Target player is inside a property. Teleport into instance?')
    then
      local instance_number = string.gsub(instance_info.name, 'property%-', '')
      TriggerEvent('blrp_housing:enterHouse', tonumber(instance_number), true, me_character.source)
    end

    return
  end

  me_character.setCoordinates(destination_coords)

  me_character.log('ADMIN', 'Teleported to ' .. target_character.get('identifier') .. ' / coords = ' .. destination_coords)
end

function teleportToMe(me_character, target_vrp_ids)
  if not me_character.hasPermissionCore('admin.tp.here') then
    return
  end

  local destination_coords = me_character.getCoordinates()

  for _, vrp_id in pairs(target_vrp_ids) do
    local target_character = core.characterFromVrp(vrp_id)

    if target_character and not target_character.get('esp_hidden') then
      target_character.setCoordinates(destination_coords)

      me_character.log('ADMIN', 'Teleported ' .. target_character.get('identifier') .. ' to self / coords = ' .. destination_coords)
    end
  end
end

function revivePlayer(me_character, target_character)
  if not me_character.hasPermissionCore('player.adminrevive') then
    return
  end

  target_character.varyHealth(100)
  target_character.client('mythic_hospital:client:RemoveBleed')
  target_character.client('mythic_hospital:client:ResetLimbs')
  target_character.client('mythic_hospital:client:ResetPlayer')
  target_character.client('vrp:client:isRevived')

  me_character.notify('Revived ' .. target_character.get('identifier'))
  me_character.log('ADMIN', 'Revived single player ' .. target_character.get('identifier'))
end

pAdmin.teleportToFromClient = function(target_vrp_id)
  local character = core.character(source)
  local target_source = core.getCitizenSourceByVrpId(target_vrp_id)
  local target_character = core.character(target_source)

  teleportTo(character, target_character)
end

pAdmin.teleportToMeFromClient = function(target_vrp_id)
  local character = core.character(source)

  teleportToMe(character, { target_vrp_id })
end

pAdmin.follow = function(target_vrp_id)
  local character = core.character(source)
  TriggerEvent('core:server:admin-follow:attach', character.source, target_vrp_id)
end

pAdmin.reviveTargetFromClient = function(target_vrp_id)
  local character = core.character(source)
  local target_source = core.getCitizenSourceByVrpId(target_vrp_id)
  local target_character = core.character(target_source)

  revivePlayer(character, target_character)
end

pAdmin.showUserAdmin = function(target_vrp_id)
  local character = core.character(source)

  if character.hasPermissionCore('user.admin') then
    TriggerEvent('core:server:user-admin:openMenu', character.source, target_vrp_id)
  else
    character.notify('You can not use this')
  end
end

pAdmin.directMessageTargetFromClient = function(target_vrp_id, character, message, send_alert)
  if not character then
    character = core.character(source)
  end

  if not send_alert then
  send_alert = false;
  end

  local target_source = core.getCitizenSourceByVrpId(target_vrp_id)
  local target_character = core.character(target_source)

  local staff_id = target_character.get('recent-staff-contact')
  local last_staff_char = core.characterFromVrp(staff_id)
  local staff_name = ''
  if last_staff_char then
    staff_name = last_staff_char.get('fullname')
  end
  local last_contact = target_character.get('latest-staffcontact-timestamp') or 0
  local minutes = math.floor((os.time() - last_contact) / 60)

  if not character.hasPermissionCore('admin.chat.helpmessage') then
    return
  end

  if staff_id and staff_id ~= character.get('identifier') and last_contact and last_contact > 0 and not character.request('This player was DMed by '..staff_name..' '..minutes..' minutes ago, continue?') then
    return
  end


  if not message then -- only opens if DM is initiated from button press, otherwise use command input
    message = character.prompt('Type message to '..target_character.get('fullname'))
  end

  if string.len(message) < 2 then
    character.notifyError('Not enough characters in message to player (min: 2)')
    return
  end

  target_character.set('dm-can-reply', true)
  target_character.set('recent-staff-contact', character.get('identifier'))
  target_character.set('latest-staffcontact-timestamp', os.time())

  exports.blrp_core:sendChatMessage('sendSingle', 'staff-directmsg',
    {
      target_source = target_character.source,
      message = message,
      message_staff = '('..character.get('identifier')..') '..message
    },
    character.get('identifier'))

  exports.blrp_core:sendChatMessage('sendSingle', 'info',
    {
      target_source = character.source,
      message = 'Staff DM Sent to '..target_character.get('fullname')..': '..message,
    },
    character.get('identifier'))

  if send_alert then
    target_character.client('blrp_core:client:prepareAlert', {
      sender = character.source,
      receiver = target_character.get('id'),
      service_name = 'staff-dm',
      flash = true,
      badge = 'warning',
      title = 'Staff Message',
      msg = [[
          <h3 style="color: #ffcc00; text-align: center;">Message from Staff</h2>
          <p style="font-size: 14px; line-height: 1.5;">
            <b>]]..message..[[</b>
          </p>
          <hr style="border: 1px solid #ffcc00;">
          <p style="font-size: 10px;">
            Type <b>/reply</b> in your chatbox to respond or click reply.<br>
            You can <b>/clearchat</b> to clear your chat history.<br>
            Press <b>F1</b> to acknowledge this message.<br>
            Press <b>F4</b> to interact with this alert.
          </p>
      ]],
      icon = 'fa-solid fa-bell',
      can_accept = true,
      hide_location = true,
      pos = target_character.getCoordinates(),
      sound = 'ToneP2',
      allow_dismiss = true,
      show_for = 60000,
    })
  end


  exports.blrp_core:logDMtoDatabase(
    character.get('fullname')..' ('..character.get('identifier')..')',
    character.get('identifier'),
    target_character.get('fullname')..' ('..target_character.get('identifier')..')',
    target_character.get('identifier'),
    message
  )

  character.log('STAFFDM', 'Sent Staff DM', {
    recipient_vrp = target_character.get('identifier'),
    recipient_char = target_character.get('id'),
    message = message,
  })

  TriggerClientEvent('InteractSound_CL:PlayOnOne', target_character.source, 'notification', 0.1)

  sendToDiscord('STAFF DM',
    '**'..character.get('fullname')..' ('..character.get('identifier')..')** to **'..target_character.get('fullname')..' ('..target_character.get('identifier')..'):** '..message)


end

pAdmin.replyToStaff = function(character, message)
  if not character then
    character = core.character(source)
  end

  local staff_id = character.get('recent-staff-contact')
  local can_reply = character.get('dm-can-reply')
  local last_contact = character.get('latest-staffcontact-timestamp')

  if not can_reply or (can_reply and os.time() - last_contact > 600) then
    character.notifyError('You have not been recently messaged by a staff member.')
    return
  end

  if not message then
    message = character.prompt('Type your reply message:')
  end

  if string.len(message) < 2 then
    character.notifyError('Response must be at least 2 characters.')
    return
  end

  local character_target = core.characterFromVrp(tonumber(staff_id))

  exports.blrp_core:ScanInputForBadWords(character.source, 'DM Reply', message)

  exports.blrp_core:sendChatMessage('sendSingleWithOptions', 'directmsg-reply', {
    target_source = character_target.source,
    message_source = character.source,
    is_staff_only = true,
    message_staff = message,
  }, character.get('identifier'))

  character.notify('Your response has been sent! Keep an eye on the chat box for a reply.')

  exports.blrp_core:sendChatMessage('sendSingle', 'directmsg-reply', {
    target_source = character.source,
    message_source = character.source,
    message = message,
  }, character.get('identifier'))

  logDMtoDatabase(
    character.get('fullname') .. ' (' .. character.get('identifier') .. ')',
    character.get('identifier'),
    character_target.get('fullname') .. ' (' .. character_target.get('identifier') .. ')',
    character_target.get('identifier'),
    message
  )

  character.log('STAFFDM', 'Replied to Staff DM', {
    recipient_vrp = character_target.get('identifier'),
    recipient_char = character_target.get('id'),
    message = message,
  })

  character.set('dm-can-reply', false)

  sendToDiscord('STAFF DM REPLY',
    '** Reply from ' .. character.get('fullname') .. ' (' .. character.get('identifier') .. ') to ' .. staff_id .. '**: ' .. message
  )
end
