tCocaine = {}
T.bindInstance('cocaine', tCocaine)

local pCocaine = P.getInstance('blrp_core', 'cocaine')

local task_in_progress = false
local lab_values = {}

local function taskAnimation()
  Citizen.CreateThread(function()
    task_in_progress = true
    TriggerEvent('vrp:client:playAnimation', true, {{ 'mp_common', 'givetake2_a', 1}}, false)
    Citizen.Wait(2000)
    TriggerEvent('vrp:client:stopAnimation', true)
    task_in_progress = false
  end)
end

local actions = {
  ['power'] = function(lab_id)
    lab_values[lab_id]['power'] = not lab_values[lab_id]['power']
  end,

  ['heat'] = function(lab_id)
    if lab_values[lab_id]['heat'] < 50000 then
      lab_values[lab_id]['heat'] = lab_values[lab_id]['heat'] + 10000
      taskAnimation()
    end
  end,

  ['coca'] = function(lab_id)
    if lab_values[lab_id]['coca'] < 5 and pCocaine.takeCocaPaste() then
      lab_values[lab_id]['coca'] = lab_values[lab_id]['coca'] + 1
      taskAnimation()
    end
  end,

  ['ph'] = function(lab_id)
    if lab_values[lab_id]['acid'] > 0 and lab_values[lab_id]['ph'] > 2 then
      lab_values[lab_id]['acid'] = lab_values[lab_id]['acid'] - 1
      lab_values[lab_id]['ph'] = lab_values[lab_id]['ph'] - 2
      taskAnimation()
    end
  end,

  ['acid'] = function(lab_id)
    if lab_values[lab_id]['acid'] < 10 then
      lab_values[lab_id]['acid'] = lab_values[lab_id]['acid'] + 2
      taskAnimation()
    end
  end,

  ['product'] = function(lab_id)
    if pCocaine.tryPayout({ lab_id }) then
      taskAnimation()
    end
  end,

  ['destroy'] = function(lab_id)
    if pCocaine.destroyProduct({ lab_id }) then
      taskAnimation()
    end
  end
}

tCocaine.getProductCounts = function(lab_id)
  if not lab_values[lab_id] then
    return nil, nil
  end

  return lab_values[lab_id]['product_poor'], lab_values[lab_id]['product_pure']
end

tCocaine.clearProduct = function(lab_id)
  if not lab_values[lab_id] then
    return
  end

  lab_values[lab_id]['product'] = 0
  lab_values[lab_id]['product_poor'] = 0
  lab_values[lab_id]['product_pure'] = 0
end

RegisterNetEvent('core:client:cocaine:destroyProduct', function(lab_id)
  if not lab_values[lab_id] then
    return
  end

  lab_values[lab_id]['product_pure'] = 0
  lab_values[lab_id]['product_poor'] = 0
end)

Citizen.CreateThread(function()
  for lab_id, lab_data in pairs(cocaine_config.labs) do
    local component_count = 0
    local lab_center = vector3(0, 0, 0)

    for component_id, component_coords in pairs(lab_data.components_civilian) do
      component_count = component_count + 1
      lab_center = lab_center + component_coords
    end

    cocaine_config.labs[lab_id].center_coords = (lab_center / component_count)

    lab_values[lab_id] = {
      ['power'] = false,
      ['heat'] = 0,
      ['coca'] = 0,
      ['ph'] = 7,
      ['acid'] = 0,

      ['product'] = 0,
      ['product_poor'] = 0,
      ['product_pure'] = 0,
    }
  end

  local interval = 1000

  while true do
    Citizen.Wait(interval)

    local player_coords = GetEntityCoords(PlayerPedId())

    local found_lab = false

    for lab_id, lab_data in pairs(cocaine_config.labs) do
      -- Degenerate ph and heat values
      if lab_values[lab_id]['ph'] < 14 then
        lab_values[lab_id]['ph'] = lab_values[lab_id]['ph'] + 0.0004
      end

      if lab_values[lab_id]['heat'] > 0 then
        lab_values[lab_id]['heat'] = lab_values[lab_id]['heat'] - 2
      end

      -- Calculate total product count
      lab_values[lab_id]['product'] = lab_values[lab_id]['product_poor'] + lab_values[lab_id]['product_pure']

      if not found_lab and #(lab_data.center_coords - player_coords) < 30.0 then
        interval = 0

        found_lab = true

        local components = lab_data.components_civilian

        if core.me().hasGroup('LEO') then
          components = lab_data.components_police
        end

        local found_component = false

        for component_id, component_coords in pairs(components) do
          if not found_component and #(component_coords - player_coords) <= 2 then
            found_component = true

            if not task_in_progress then
              DisplayHelpText('Press ~INPUT_CONTEXT~ to ' .. cocaine_config.component_labels[component_id])

              if actions[component_id] and IsControlJustReleased(1, 51) then
                Citizen.CreateThread(function()
                  actions[component_id](lab_id)
                end)
              end
            end

            local r, g, b = 255, 255, 255
            local text = cocaine_config.component_labels[component_id]

            if component_id == 'power' then
              if lab_values[lab_id]['power'] then
                r, g, b = 0, 255, 0
              else
                r, g, b = 255, 0, 0
              end
            elseif lab_values[lab_id][component_id] then
              local value = lab_values[lab_id][component_id]

              if component_id == 'ph' then
                value = roundDecimals(value, 2)
              end

              text = text .. ' (' .. value .. ')'
            end

            DrawText3d(component_coords.x, component_coords.y, component_coords.z, text, 0.35, r, g, b)
          end
        end
      end
    end
  end
end)

--[[
  -- Description: Serves as a method to allow processing cocaine
  --   Lob equipment must be maintained to produce quality cocaine
  --   0 errors = pure cocaine, 1 error = poor quality cocaine
  --   2+ errors = no cocaine
  --   Heat: 10,000-50,000
  --   Acid: 2pH-7pH
]]

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(30000)

    if not core.me().hasGroup('LEO') then
      local player_coords = GetEntityCoords(PlayerPedId())

      local found_lab = false

      for lab_id, lab_data in pairs(cocaine_config.labs) do
        if not found_lab and #(lab_data.center_coords - player_coords) < 30.0 then
          local values = lab_values[lab_id]

          if values.power then
            local errors = 0

            if values.coca > 0 then
              if values.ph < 2 then
                core.me().notify('The solution is too acidic')
                errors = errors + 1
              end

              if values.ph > 7 then
                core.me().notify('The solution is too basic')
                errors = errors + 1
              end

              if values.heat > 50000 then
                core.me().notify('The solution is too hot')
                errors = errors + 1
              end

              if values.heat < 10000 then
                core.me().notify('The solution is too cold')
                errors = errors + 1
              end

              lab_values[lab_id].coca = lab_values[lab_id].coca - 1

              local amount = 1

              if lab_data.product_multiplier then
                amount = math.floor(amount * lab_data.product_multiplier)
              end

              if errors == 0 then
                lab_values[lab_id].product_pure = lab_values[lab_id].product_pure + amount

                core.me().notify('Produced pure cocaine')
              elseif errors == 1 then
                lab_values[lab_id].product_poor = lab_values[lab_id].product_poor + amount

                core.me().notify('Produced crack cocaine')
              else
                core.me().notify('The cocaine was ruined')
              end
            else
              core.me().notify('The machine does not have any Coca Paste to process.')
            end
          end
        end
      end
    end
  end
end)
