pAnimations = { }
P.bindInstance('animations', pAnimations)

local isSmartEnabled = true
local activeAnimations = { }
local pendingAccept = { }

pAnimations.getDebug = function ()
    local character = core.character(source)
    if not character.hasGroup('staff') then return end

    return {
        activeAnimations = activeAnimations,
    }
end

RegisterNetEvent('core:server:registerSelectedPlayer', function(source)
    Citizen.Wait(2500)
    tAnimations.toggleEnabled(source, { isSmartEnabled })
end)

pAnimations.syncAnimationAdded = function(packet)
    activeAnimations[packet.uuid] = packet
end

pAnimations.syncAnimationRemoved = function(uuid)
    if activeAnimations[uuid] ~= nil then
        activeAnimations[uuid] = nil
    end
end

-- Try to give item from player to another when up arrow pressed
pAnimations.tryGiveEmoteItem = function(targetSource)
    local target = core.character(targetSource)
    local me = core.character(source)

    if pendingAccept[target.source] then
        return me.notify('You are waiting on a response from someone already')
    end

    for uuid, packet in pairs(activeAnimations) do -- Already doing an anim
        if packet.player == target.source then
            -- print('core:server:animation:tryGiveEmoteItem already doing an anim')
            return
        end
    end

    pendingAccept[target.source] = true

    local foundAnimation = false

    for uuid, packet in pairs(activeAnimations) do
      if packet.player == me.source and packet.type == 'emote' then
            if not target.request('Someone wants you to do an animation:, ' .. packet.name .. ' accept?') then
              pendingAccept[target.source] = false
              return me.notify('They did not want to do that.')
            end

            foundAnimation = true
            me.notify('They agreed')
            target.notify('You started doing an emote')

            tAnimations.queueAnimation(target.source, { table.copy(packet) })
            tAnimations.wipePlayer(me.source)
        end
    end

  pendingAccept[target.source] = false

  if not foundAnimation then
        me.notify('no anim found')
        target.notify('no anim found')
    end
end

-- Try take emote item from another player when down arrow pressed
pAnimations.tryTakeEmoteItem = function(targetSource)
    local me = core.character(source)
    local target = core.character(targetSource)

    for uuid, packet in pairs(activeAnimations) do -- Already doing an anim
        if packet.player == source and packet.active then
            -- print('already doing an anim')
            return
        end
    end

    for uuid, packet in pairs(activeAnimations) do
        if packet.player == target.source and packet.type == 'emote' then
            me.notify('You took something from someone')
            target.notify('Someone took something from you')

            tAnimations.queueAnimation(source, { table.copy(packet) })
            tAnimations.wipePlayer(target.source, { })
        end
    end
end

RegisterCommand('disable-anim-smart', function(source)
    isSmartEnabled = false
    TriggerClientEvent('core:client:animation:toggleEnabled', -1, false)
end)

RegisterCommand('enable-anim-smart', function(source)
    isSmartEnabled = true
    TriggerClientEvent('core:client:animation:toggleEnabled', -1, true)
end)


function table.copy(t)
    local u = { }
    for k, v in pairs(t) do
        u[k] = v
    end
    return setmetatable(u, getmetatable(t))
end

function tableLength(T)
    local count = 0
    for _ in pairs(T) do count = count + 1 end
    return count
end
