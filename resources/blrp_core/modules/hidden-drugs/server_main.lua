local refreshTime = 60 * 1000

local item_loot_tables = {}
local clients_interacting = {}

SetTimeout(100, function()
  updateDrugInformation()
end)

function updateDrugInformation()
  MySQL.Async.fetchAll("SELECT * FROM vrp_crafting WHERE shutdown = false AND (type = 'product' OR type = 'ingredient')", {}, function(rows)
    -- Remove all existing interacts
    for _, loot_table in pairs(item_loot_tables) do
      TriggerClientEvent('core:client:removeInteract', -1, {
        coords = loot_table.location
      })
    end

    item_loot_tables = {}

    if #rows > 0 then
      for k, v in pairs(rows) do
        item_loot_tables[v.id] = {}

        if v.type == 'ingredient' then
          item_loot_tables[v.id].refresh = true
          item_loot_tables[v.id].quantity = tonumber(v.max_loot_capacity)
          item_loot_tables[v.id].max_loot_capacity = tonumber(v.max_loot_capacity)
        else
          item_loot_tables[v.id].refresh = false
          item_loot_tables[v.id].quantity = 0
          item_loot_tables[v.id].max_loot_capacity = 0
        end

        item_loot_tables[v.id].lastLooted = 0
        item_loot_tables[v.id].lastLootRefreshSeconds = tonumber(v.lastLootRefreshSeconds)
        item_loot_tables[v.id].type = v.type
        item_loot_tables[v.id].recipe = json.decode(v.recipe)
        item_loot_tables[v.id].reward_item = json.decode(v.reward_item)
        item_loot_tables[v.id].animation = json.decode(v.animation)
        item_loot_tables[v.id].detect_range = v.detect_range
        item_loot_tables[v.id].prompt_text = v.prompt_text
        item_loot_tables[v.id].action_duration = v.action_duration
        item_loot_tables[v.id].progress_label = v.progress_label
        item_loot_tables[v.id].can_be_shutdown = v.can_be_shutdown

        local location = json.decode(v.location)

        location = vector3(location.x, location.y, location.z)

        item_loot_tables[v.id].location = location
      end
    end

    TriggerEvent('core:server:hidden_drugs:getInteracts', -1)

    refreshLootTable()
  end)
end

function refreshLootTable()
  for k,v in pairs(item_loot_tables) do
    if v.refresh and ((os.time() - v.lastLooted) > v.lastLootRefreshSeconds) then
      if v.quantity < v.max_loot_capacity then
        local amount = 1
        v.quantity = v.quantity + amount
        if v.quantity > v.max_loot_capacity then
          v.quantity = v.max_loot_capacity
        end
      end
    end
  end

  SetTimeout(refreshTime, refreshLootTable)
end

RegisterNetEvent('core:server:hidden_drugs:getInteracts', function(player)
  if not player then
    player = source
  end

  for loot_table_id, loot_table in pairs(item_loot_tables) do
    if loot_table.detect_range > 0 then
      TriggerClientEvent('core:client:registerInteract', player, {
        coords = loot_table.location,
        call = 'core:server:hidden_drugs:interact',
        args = loot_table_id,
        hotkey = 'E',
        distance = loot_table.detect_range,
        lang = loot_table.prompt_text,
      })
    end
  end
end)

RegisterNetEvent('core:server:hd:interactTarget', function(_, event_data)
  local character = core.character(source)

  if not event_data or not event_data.location_id then
    return
  end

  local location_data = item_loot_tables[event_data.location_id]

  if not location_data or location_data.detect_range > 0 then
    return
  end

  if #(event_data.position - location_data.location) > 1.0 then
    return
  end

  if #(character.getCoordinates() - location_data.location) > 5.0 then
    return
  end

  TriggerEvent('core:server:hidden_drugs:interact', event_data.location_id, character.source)
end)

RegisterNetEvent('core:server:hidden_drugs:interact', function(location_id, player)
  if not player then
    player = source
  end

  local character = core.character(player)

  local location = nil

  for loot_table_id, loot_table in pairs(item_loot_tables) do
    if loot_table_id == location_id then
      location = loot_table
      break
    end
  end

  if not location then
    return
  end

  if clients_interacting[character.source] then
    return
  end

  if tSurvival.getIsInAnyVehicle(character.source) then
    character.notify('You cannot do this from a vehicle')
    return
  end

  -- Gather permission check

  local permission_check_failed = false

  if not character.hasPermissionCore('citizen.gather') and not character.hasPermissionCore('police.service') then
    permission_check_failed = true
  end

  if permission_check_failed then
    character.notify("You don't feel right doing this while clocked into a legal job")
    return
  end

  clients_interacting[character.source] = true

  -- Cancel all upper and non-upper animations
  character.client('vrp:client:stopAnimation', true)
  character.client('vrp:client:stopAnimation', false)

  character.progressCustom(location.progress_label, location.action_duration, function(cancelled)
    clients_interacting[character.source] = false

    if not cancelled then
      local loot_table = item_loot_tables[location_id]

      if not loot_table then
        return
      end

      local recipe_consume = {}
      local recipe_missing = {}
      local passed_recipe_check = true
      local should_cause_burns = false

      for _, recipe_item in ipairs(loot_table.recipe) do
        if recipe_item.consume then
          recipe_consume[recipe_item.name] = {
            amount = recipe_item.amount
          }
        end
        if not character.hasItemQuantity(recipe_item.name, recipe_item.amount, false, true) then
          passed_recipe_check = false
          if recipe_item.causes_burns then
            should_cause_burns = true
          end

          if recipe_item.hint_missing then
            table.insert(recipe_missing, recipe_item)
          end
        end
        if recipe_item.name == 'chemical_gloves' then
          local items = character.getAllItems()
          local itemid = nil
          local itemdata = nil


          for item_id, item_data in pairs(items) do
            if string.match(item_id, 'chemical_gloves') then
              itemid = item_id
              itemdata = item_data
            end
          end
          if itemid then
            local meta = character.hasGetItemMeta(itemid)
            if not meta.dur_start then
              meta.dur_start = 80
              modifyItemMeta(character.source, itemid, 'dur_start', meta.dur_start)
            end
            if not meta.dur_initial then
              meta.dur_initial = 80
            end
            if not meta.dur_cur then
              meta.dur_cur = 80
            end
            if meta.dur_cur < 1 then
              passed_recipe_check = false
              should_cause_burns = true
              character.notifyError('Your chemical gloves are destroyed')
              modifyItemMeta(character.source, itemid, 'dur_cur', 0)
            else
              modifyItemMeta(character.source, itemid, 'dur_cur', meta.dur_cur - 1)
            end
          end
        end
      end

      local reward_items = {}

      for rw_name, rw_amount in pairs(loot_table.reward_item) do
        reward_items[rw_name] = {
          amount = rw_amount
        }
      end

      if loot_table.type == 'ingredient' then
        if not loot_table.quantity or loot_table.quantity <= 0 then
          character.notify("There is nothing left here to collect")
          return
        end

        if not passed_recipe_check then
          if should_cause_burns then
            character.notifyError("You don't have the right tool to collect this, and received horrible chemical burns as a result")
            character.varyHealth(-30)
          else
            character.notify("You don't have the right tool to collect this")
          end

          return
        end

        item_loot_tables[location_id].quantity = math.max(0, loot_table.quantity - 1)
        item_loot_tables[location_id].lastLooted = os.time()
      elseif loot_table.type == 'product' then
        if not passed_recipe_check then
          for _, missing_item in ipairs(recipe_missing) do
            local missing_item_name = GItems.getItemName(missing_item.name)
            character.notify('You require ' .. missing_item.amount .. ' ' .. missing_item_name)
          end

          if should_cause_burns then
            character.notifyError('You received horrible chemical burns as a result of your carelessness')
            character.varyHealth(-30)
          end

          return
        end
      end

      if passed_recipe_check then
        local inventory_weight = character.getInventoryCurrentWeight()
        local max_weight = character.getInventoryMaxWeight()
        local recipe_weight = GItems.getTotalWeight(recipe_consume)
        local reward_weight = GItems.getTotalWeight(reward_items)

        if inventory_weight - recipe_weight + reward_weight > max_weight then
          character.notify('Your inventory is full')
          return
        end

        for recipe_item_name, recipe_item_data in pairs(recipe_consume) do
          character.take(recipe_item_name, recipe_item_data.amount)
        end

        for reward_name, reward_data in pairs(reward_items) do
          character.give(reward_name, reward_data.amount)
          character.log('HIDDENDRUGS', loot_table.type .. ': ' .. reward_data.amount .. ' ' .. reward_name)
        end
      end
    end
  end, {
    animation = location.animation,
    prop = {
      model = 'prop_paper_bag_small',
      coords = {
        x = 0.05,
        y = 0,
        z = 0.5
      }
    }
  })
end)

core.event('core:server:hidden_drugs:shutdown', function(source, player_coords)
  local character = core.character(source)
  local location_id = nil
  local location = nil

  for loot_table_id, loot_table in pairs(item_loot_tables) do
    if #(vector3(player_coords.x, player_coords.y, player_coords.z) - vector3(loot_table.location.x, loot_table.location.y, loot_table.location.z)) < 5 then
      location_id = loot_table_id
      location = loot_table
      break
    end
  end

  if not location or location.can_be_shutdown == 0 then
    return
  end

  if not character.hasPermissionCore('police.shutdownhidden') then
    return
  end

  character.progressCustom('Shutting Down Location', 30, function(cancelled)
    if cancelled then
      return
    end

    MySQL.Async.execute('UPDATE vrp_crafting SET shutdown = 1 WHERE id = @id', {id = location_id}, function(rowsChanged)
      if rowsChanged > 0 then
        character.notify('Location shut down')
      end

      character.log('HIDDENDRUGS', 'Location shut down: ' .. location_id .. '. Caused by: ' .. character.get('id'))
      updateDrugInformation()
    end)
  end)
end)

AddEventHandler('rconCommand', function(commandName, args)
  if commandName == 'refreshDrugs' then
    updateDrugInformation()
    CancelEvent()
  end
end)
