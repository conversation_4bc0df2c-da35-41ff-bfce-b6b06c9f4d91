tMugging = { }
T.bindInstance('mugging', tMugging)

pMugging = P.getInstance('blrp_core', 'mugging')

local enabled = true

local is_mugging = false
local mugging_ped_id = nil
local my_heat_rating = 0

local seconds_spent_mugging = 0
local total_seconds_spent_step_one = 0
local mugging_step = 0

local checked_heat_on = { }
local ran_heat_action_on = { }

-- Main mugging gun point thread
Citizen.CreateThread(function()
  while true and enabled do
    Citizen.Wait(1000)
    local ped = PlayerPedId()
    local _, i_am_armed = GetCurrentPedWeapon(ped, true)

    local blacklisted_weapons = {
      GetHash<PERSON>ey("WEAPON_FLASHLIGHT"),
      GetHashKey("WEAPON_FLASHLIGHT_UV"),
    }

    local function isBlacklistedWeapon(weapon)
      if not weapon then return false end -- Ensure weapon isn't nil
      for _, blacklisted in ipairs(blacklisted_weapons) do
        if weapon == blacklisted then
          return true
        end
      end
      return false
    end

    if core.me().hasGroup('LEO') then goto continue end

    if isBlacklistedWeapon(i_am_armed) then goto continue end

    if IsPlayerFreeAiming(PlayerId()) and not is_mugging and i_am_armed then
      local aiming, target_ped = GetEntityPlayerIsFreeAimingAt(PlayerId())
      if aiming and IsEntityAPed(target_ped) then
        local dist = #(GetEntityCoords(ped) - GetEntityCoords(target_ped))
        if dist < c_mugging.distance_to_init then
          if
            not IsPedInAnyVehicle(target_ped, false) and
            not exports.blrp_target:PedHasSpecialAssignment(target_ped) and
            not exports.blrp_core:IsMissionPed(target_ped) and
            not exports.blrp_core:IsPlayerDealing() and
            not IsPedDeadOrDying(target_ped) and
            GetPedType(target_ped) ~= 28 and
            NetworkGetEntityIsNetworked(target_ped)
          then
            startMuggingWithPed(target_ped)
            Citizen.Wait(1000 * 30)
          end
        end
      end
    end
    ::continue:: -- Jump point to continue loop
  end
end)


tMugging.setMyHeatLevel = function(level)
  Citizen.CreateThread(function()
    if level then
      my_heat_rating = tonumber(level)
    else
      my_heat_rating = 0
    end
  end)
end

-- Heat level reactions
Citizen.CreateThread(function()
  while true and enabled do
    Citizen.Wait(1000)

    -- Disable heat reactions for now
    if my_heat_rating > 999 then
      for ped in EnumeratePeds() do
        if ped ~= PlayerPedId() and DoesEntityExist(ped) then
          local within_distance = #(GetEntityCoords(PlayerPedId()) - GetEntityCoords(ped)) < 5
          if within_distance then

            -- Chances
            local chance_ped_to_flee = chance(0.02 * my_heat_rating)
            local chance_ped_attack = chance(0.01 * my_heat_rating)
            local change_ped_spawn_weapon = chance(0.005 * my_heat_rating)

            -- Lock peds as they are passed so it can't roll twice on one ped
            if not checked_heat_on[ped] and not IsPedAPlayer(ped) then
              checked_heat_on[ped] = true

              if not exports.blrp_target:PedHasSpecialAssignment(ped) and GetPedType(ped) ~= 28 then
                if not IsPedInAnyVehicle(ped, false) and chance_ped_attack and not ran_heat_action_on[ped] then
                  local net_id = NetworkGetNetworkIdFromEntity(ped)
                  SetEntityAsMissionEntity(ped, true, true)
                  ran_heat_action_on[ped] = true

                  pMugging.triggerHeatReactionAttackOnPed({ net_id, 'attack' })

                  TaskCombatPed(ped, PlayerPedId())

                  normalizePed(ped)

                  Citizen.Wait(5000)
                end

                if not IsPedInAnyVehicle(ped, false) and chance_ped_to_flee and not ran_heat_action_on[ped] then
                  if not IsPedFleeing(ped) then

                    local net_id = NetworkGetNetworkIdFromEntity(ped)
                    SetEntityAsMissionEntity(ped, true, true)
                    ran_heat_action_on[ped] = true

                    pMugging.triggerHeatReactionFleeOnPed({ net_id, 'flee' })

                    TaskReactAndFleePed(ped, ped)

                    normalizePed(ped)

                    Citizen.Wait(5000)
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end)

tMugging.havePedSayMessage = function(ped_network_id, message)
  if not NetworkDoesEntityExistWithNetworkId(ped_network_id) then
    return
  end

  local target_ped = NetworkGetEntityFromNetworkId(ped_network_id)

  if not DoesEntityExist(target_ped) then
    return
  end

  local me_coords = GetEntityCoords(PlayerPedId())
  local ped_coords = GetEntityCoords(target_ped)

  if #(me_coords - ped_coords) > 10 then
    return
  end

  local phrase = message

  local showing_message_time_remaining = 500 -- 5 seconds

  Citizen.CreateThread(function()
    while showing_message_time_remaining > 10 do
      Citizen.Wait(1)
      local ped_coords_moving = GetEntityCoords(target_ped)
      showing_message_time_remaining = showing_message_time_remaining - 1
      DrawText3Ds(ped_coords_moving.x, ped_coords_moving.y, ped_coords_moving.z + 0.2, phrase)
    end
  end)
end

-- Cancel if player stops aiming
Citizen.CreateThread(function()
  while true and enabled do
    Citizen.Wait(1000)
    if is_mugging then
      local ped = PlayerPedId()
      if not IsPlayerFreeAiming(PlayerId()) and mugging_step == 1 then
        cancelMugging()
      end
    end
  end
end)

-- Cancel mugging if more than 80 seconds is spent in mugging state
Citizen.CreateThread(function()
  while true and enabled do
    Citizen.Wait(1000)
    if is_mugging then
      seconds_spent_mugging = seconds_spent_mugging + 1

      if seconds_spent_mugging > 40 then
        cancelMugging()
      end
    end
  end
end)

function startMuggingWithPed(target_ped)
  local me_ped = PlayerPedId()
  local is_ped_in_vehicle = IsPedInAnyVehicle(target_ped)

  local flags = Entity(target_ped).state.flags or 0

  if flags & PEF_NOT_MUGGABLE ~= 0 then
    return
  end

  if IsPedAPlayer(target_ped) then
    return
  end

  local ped_type = GetPedType(target_ped)

  local ped_network_id = NetworkGetNetworkIdFromEntity(target_ped)
  local can_hold = pMugging.startHoldingSomeone({ ped_network_id }) -- 100ms

  if not can_hold then
    return
  end

  SetEntityAsMissionEntity(target_ped, true, true)
  local state = Entity(target_ped).state

  if state and state.being_mugged then
    return
  end

  is_mugging = true
  mugging_ped_id = target_ped
  total_seconds_spent_step_one = 0

  NetworkRequestControlOfEntity(target_ped)

  ClearPedTasks(target_ped)
  ClearPedSecondaryTask(target_ped)
  SetPedAlertness(target_ped, 0)
  TaskSetBlockingOfNonTemporaryEvents(target_ped, true)
  SetPedFleeAttributes(target_ped, 0, 0)
  SetPedCombatAttributes(target_ped, 17, 1)

  if chance(0.1) then
    TaskCombatPed(target_ped, me_ped, 0, 16)
    return
  end


  ---
  --- First Step (Part 1)
  ---

  mugging_step = 1
  loadAnimDict('missfbi5ig_22')
  TaskPlayAnim(target_ped, "missfbi5ig_22", "hands_up_anxious_scientist", 1.0, 1.0, -1, 51, 0, 0, 0, 0)

  while total_seconds_spent_step_one < c_mugging.mug_time_seconds and is_mugging do
    Citizen.Wait(1000)
    total_seconds_spent_step_one = total_seconds_spent_step_one + 1
    local time_remaining = c_mugging.mug_time_seconds - total_seconds_spent_step_one

    ClearPrints()
    SetTextEntry_2("STRING")
    AddTextComponentString("Continue holding them up for " .. time_remaining .. ' more seconds')
    DrawSubtitleTimed(1000, 1)
  end

  -- State check
  if not is_mugging then
    return
  end

  ---
  --- Second Step (Part 2)
  ---
  mugging_step = 2
  loadAnimDict('random@arrests@busted')
  TaskPlayAnim(target_ped, "random@arrests@busted", "idle_a", 8.0, 1.0, -1, 9, 0, 0, 0, 0)

  -- State check
  if not is_mugging then
    return
  end

  local ms_seconds_spent_on_step_two = 0

  pMugging.startMuggingSomeone({ ped_network_id })

  while mugging_step == 2 do
    Citizen.Wait(1)
    local target_ped_coords = GetEntityCoords(target_ped)

    ms_seconds_spent_on_step_two = ms_seconds_spent_on_step_two + 1

    if ms_seconds_spent_on_step_two > (30 * 1000) then
      cancelMugging()
    end

    doWhenPedInDistanceOfSet(1, target_ped_coords, function()
      doWhenPressed(target_ped_coords, 'G', '[G] Search', function()
        exports['mythic_progbar']:Progress({
          name = "search_mugging_ped",
          duration = 4000,
          label = "Searching",
          useWhileDead = false,
          canCancel = true,
          controlDisables = {
            disableMovement = true,
            disableCarMovement = true,
            disableMouse = false,
            disableCombat = true,
          },
          animation = {
            animDict = "mini@repair",
            anim = "fixing_a_ped",
            flags = 49,
          },
        }, function(cancelled)
          if cancelled then
            cancelMugging()
            return
          end

          if not cancelled then
            mugging_step = 3
          end
        end)

      end)
    end)
  end

  if not is_mugging then
    cancelMugging()
    return
  end

  local coords = GetEntityCoords(PlayerPedId())
  local zone = GetZoneAtCoords(coords.x, coords.y, coords.z)
  local zone_scum_index = GetZoneScumminess(zone)

  pMugging.doMuggingSearch({ ped_network_id, zone_scum_index })
end

function cancelMugging()
  -- Mission text wait
  Citizen.Wait(900)

  if mugging_ped_id then
    TaskReactAndFleePed(mugging_ped_id, mugging_ped_id)
    Citizen.CreateThread(function()
      Citizen.Wait(1000)
      normalizePed(mugging_ped_id)
    end)
  end

  is_mugging = false
  mugging_ped_id = nil
  seconds_spent_mugging = 0
  mugging_step = 0
end

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1)
    if is_mugging and mugging_ped_id then
      local time_remaining = 20 - total_seconds_spent_step_one
    end
  end
end)

function chance(probability)
  return math.random() <= probability
end

function DrawText3Ds(x, y, z, text)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z)
  local pX, pY, pZ = table.unpack(GetGameplayCamCoords())
  local dist = GetDistanceBetweenCoords(pX, pY, pZ, x, y, z, 1)

  local scale = 0.4
  --local fov = (1/GetGameplayCamFov())*100
  --local scale = scale*fov

  if onScreen then
    SetTextScale(scale, scale)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextEntry("STRING")
    SetTextCentre(1)
    SetTextColour(255, 255, 255, 215)
    AddTextComponentString(text)
    DrawText(_x, _y)

    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0150, 0.030 + factor, 0.025, 41, 11, 41, 100)
  end
end


-- 3rd eye
RegisterNetEvent('blrp_core:client:mugging:offerFood')
AddEventHandler('blrp_core:client:mugging:offerFood', function(entity)
  local net_id = NetworkGetNetworkIdFromEntity(entity)
  pMugging.hasGiveFoodToPed({ net_id })
end)

-- 3rd eye
RegisterNetEvent('blrp_core:client:mugging:taunt')
AddEventHandler('blrp_core:client:mugging:taunt', function(entity)
  if not ran_heat_action_on[entity] then
    SetEntityAsMissionEntity(entity, true, true)
    ran_heat_action_on[entity] = true
    TaskCombatPed(entity, PlayerPedId(), 0, 16)
    normalizePed(entity)
  else
    core.me().notify('They are not interested in you')
  end
end)

-- 3rd eye
RegisterNetEvent('blrp_core:client:mugging:calmDown')
AddEventHandler('blrp_core:client:mugging:calmDown', function(entity)
  local ped = GetPlayerPed(-1)
  local coords = GetEntityCoords(entity)

  -- Make the ped look at the player
  TaskLookAtCoord(ped, coords.x, coords.y, coords.z + 1.0, -1, 2048, 3)

  -- Play a talking animation on the ped
  RequestAnimDict("random@street_race")
  while not HasAnimDictLoaded("random@street_race") do
    Citizen.Wait(0)
  end

  TaskPlayAnim(entity, "random@street_race", "hey_stunner", 8.0, -8.0, -1, 1, 0, false, false, false)

  Citizen.Wait(1000)

  normalizePed(entity, true)
end)

local map = {
  [0] = 'SCUMMINESS_POSH',
  [1] = 'SCUMMINESS_NICE',
  [2] = 'SCUMMINESS_ABOVE_AVERAGE',
  [3] = 'SCUMMINESS_BELOW_AVERAGE',
  [4] = 'SCUMMINESS_CRAP',
  [5] = 'SCUMMINESS_SCUM',
}
