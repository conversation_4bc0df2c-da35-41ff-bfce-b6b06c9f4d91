tCore.swipeKeycardScene = function(keypad_coords, keypad_model, card_model, rpc_id)
  local dict = 'anim_heist@hs3f@ig3_cardswipe@male@' -- fm_mission_controller_2020.c @ 363776

  if not card_model then
    card_model = `ch_prop_swipe_card_01a`
  end

  while
    not HasAnimDictLoaded(dict) or
    not HasModelLoaded(card_model)
  do
    RequestAnimDict(dict)
    RequestModel(card_model)
    Wait(0)
  end

  local keypad = GetClosestObjectOfType(keypad_coords.x, keypad_coords.y, keypad_coords.z, 0.1, keypad_model, false, false, false)
  local rotation = GetEntityRotation(keypad)
  local card = CreateObject(card_model, GetEntityCoords(PlayerPedId()), 1, 1, 0)

  local scene = NetworkCreateSynchronisedScene(keypad_coords, vector3(0, 0, rotation.z), 2, false, false, 1065353216, 0, 1.3)
  NetworkAddPedToSynchronisedScene(PlayerPedId(), scene, dict, 'success_var01', 1.5, -4.0, 1, 16, 1148846080, 0)
  NetworkAddEntityToSynchronisedScene(card, scene, dict, 'success_var01_card', 4.0, -8.0, 1)

  local duration = GetAnimDuration(dict, 'success_var01') * 1000
  NetworkStartSynchronisedScene(scene)

  Citizen.Wait(1800)

  T.fireStage(rpc_id, 'swiped')

  Citizen.Wait(1200)

  DeleteObject(card)
end
