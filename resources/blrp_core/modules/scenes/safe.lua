tCore.safeScene = function(safe_model, safe_coords, reward_model, rpc_id)
  local dict = 'anim@scripted@heist@ig15_safe_crack@male@'

  while
    not HasModelLoaded(safe_model) or
    not HasModelLoaded(reward_model) or
    not HasAnimDictLoaded(dict)
  do
    RequestModel(safe_model)
    RequestModel(reward_model)
    RequestAnimDict(dict)
    Citizen.Wait(0)
  end

  local ped = PlayerPedId()

  local safe = GetClosestObjectOfType(safe_coords.x, safe_coords.y, safe_coords.z, 1.5, safe_model, false, false, false)

  local safe_coords = GetEntityCoords(safe)
  local safe_rotation = GetEntityHeading(safe)

  local safe_obj = CreateObject(safe_model, safe_coords, 1, 0, 0)

  SetEntityHeading(safe_obj, safe_rotation)

  local reward_obj = CreateObject(reward_model, safe_coords, 1, 0, 0)

  if rpc_id then
    T.fireStage(rpc_id, 'hide')
  end

  SetEntityHeading(reward_obj, safe_rotation)
  SetEntityCoords(reward_obj, safe_coords.x, safe_coords.y, safe_coords.z + 1.03721)

  while not NetworkHasControlOfEntity(safe_obj) do
    Citizen.Wait(1)
    NetworkRequestControlOfEntity(safe_obj)
  end

  local scene_enter = NetworkCreateSynchronisedScene(safe_coords.x, safe_coords.y, safe_coords.z, 0, 0, safe_rotation, 2, true, false, 1065353216, -1, 1.0)
  NetworkAddPedToSynchronisedScene(ped, scene_enter, dict, 'enter_player', 1.5, -4.0, 1, 16, 1148846080, 0)
  NetworkAddEntityToSynchronisedScene(safe_obj, scene_enter, dict, 'enter_safe', 4.0, -8.0, 1)

  local scene_loop = NetworkCreateSynchronisedScene(safe_coords.x, safe_coords.y, safe_coords.z, 0, 0, safe_rotation, 2, true, false, 1065353216, -1, 1.0)
  NetworkAddPedToSynchronisedScene(ped, scene_loop, dict, 'idle_player', 1.5, -4.0, 1, 16, 1148846080, 0)
  NetworkAddEntityToSynchronisedScene(safe_obj, scene_loop, dict, 'idle_safe', 4.0, -8.0, 1)

  local scene_open = NetworkCreateSynchronisedScene(safe_coords.x, safe_coords.y, safe_coords.z, 0, 0, safe_rotation, 2, true, false, 1065353216, -1, 1.0)
  NetworkAddPedToSynchronisedScene(ped, scene_open, dict, 'door_open_player', 1.5, -4.0, 1, 16, 1148846080, 0)
  NetworkAddEntityToSynchronisedScene(safe_obj, scene_open, dict, 'door_open_safe', 4.0, -8.0, 1)

  local scene_exit = NetworkCreateSynchronisedScene(safe_coords.x, safe_coords.y, safe_coords.z, 0, 0, safe_rotation, 2, true, false, 1065353216, -1, 1.0)
  NetworkAddPedToSynchronisedScene(ped, scene_exit, dict, 'exit_player', 1.5, -4.0, 1, 16, 1148846080, 0)

  NetworkStartSynchronisedScene(scene_open)

  local duration = GetAnimDuration(dict, 'door_open_player') * 1000

  local wait1 = 2400

  Wait(wait1)

  if rpc_id then
    T.fireStage(rpc_id, 'swap', NetworkGetNetworkIdFromEntity(reward_obj))
  end

  Wait(duration - wait1)

  NetworkStartSynchronisedScene(scene_exit)

  Wait(GetAnimDuration(dict, 'exit_player') * 1000)

  NetworkStopSynchronisedScene(scene_exit)

  DeleteEntity(safe_obj)

  return NetworkGetNetworkIdFromEntity(reward_obj)
end
