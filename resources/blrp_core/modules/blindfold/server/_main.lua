RegisterNetEvent('core:server:blindfold:remove', function(network_id, event_data)
  if not network_id or not event_data.target_source then
    return
  end

  local character = core.character(source)
  local character_target = core.character(event_data.target_source)

  if not character_target or not character_target.state then
    character.notify('Unable to find the target player state')
    return
  end

  if not character_target.state.blindfolded then
    return
  end

  if not character.progressPromise('Removing blindfold', 4, {
    animation = {
      animDict = 'pickup_object',
      anim = 'putdown_low',
      flags = 49,
    },
  }) then
    return
  end

  local initial_coords = character_target.getCoordinates()

  if #(character_target.getCoordinates() - initial_coords) > 5.0 then
    character.notify('Your target moved too far from you to take the blindfold off')
    return
  end

  TriggerClientEvent('core:client:blindfold:remove', character_target.source)

  character_target.state.blindfolded = false
  character.log('ACTION', 'Removed blindfold from player ' .. character_target.get('identifier'))
end)