AddEventHandler('core:server:businesses:management:openManagement', function(character, business_id, bypass)
  local business = getBaseBusinessDefFromId(business_id)
  local custom_business = getCustomBusinessDefFromId(business_id)

  if not business then
    return
  end

  if not checkUserCanAccess(character, business_id, 'management') and not bypass then
    character.notify('You do not have permission to access this')
    return
  end

  local resource_title = ''

  if custom_business then
    resource_title = custom_business.name
  end

  local menu = BMenu:new(true, {
    resource_title = resource_title,
    section_title = 'Business Management',
    title_bg_image = 'https://i.gyazo.com/8535b0d617f577767cb50fcffccb8d0c.jpg',
  })

  -- Custom options (defined in config_custom.lua)
  if custom_business and custom_business.custom_computer_options then
    for _, custom_computer_option in ipairs(custom_business.custom_computer_options) do
      menu:addSelection(false, custom_computer_option.name, function(source, value, callback)
        callback('')
        custom_computer_option.callback(character, custom_computer_option.name)
      end)
    end
  end

  -- Employee management option
  if checkUserCanAccess(character, business_id, 'hire') or checkUserCanAccess(character, business_id, 'fire') or checkUserCanAccess(character, business_id, 'permissions') or bypass then
    menu:addSelection(false, 'Employee Management', function(source, value, callback)
      callback('')
      TriggerEvent('core:server:businesses:management:openEmployeeManagement', character, business_id, bypass)
    end)
  end

  -- Pay warehouse electrical bill option
  if custom_business and checkUserCanAccess(character, business_id, 'warehousemanage') or (custom_business and bypass) then
    for _, property in pairs(G_houses) do
      if property.business_id == business_id then
        menu:addSelection(false, 'Pay electrical bills - ' .. property.address, function(source, value, callback)
          callback('')
          TriggerEvent('core:server:businesses:management:openElectricalManagement', character, business_id, property, bypass)
        end)
      end
    end
  end

  -- Set bank account option
  if custom_business and checkUserCanAccess(character, business_id, 'management') or (custom_business and bypass) then
    local account_str = 'None'

    if custom_business.bank_account_number then
      local account = MySQL.single.await('SELECT * FROM bank_accounts WHERE account_number = ?', { custom_business.bank_account_number })

      if account then
        account_str = account.account_number

        if account.account_name and account.account_name ~= '' then
          account_str = account_str .. ' (' .. account.account_name .. ')'
        end
      end
    end

    menu:addSelection(false, 'Business Bank Account: ' .. account_str, function(source, value, callback)
      callback('close')

      local accounts = exports.blrp_banking:GetAccounts(character)

      local remove_option = '(Remove Account)'
      local options = { remove_option }

      for _, account in pairs(accounts) do
        if account.account_type == 'Shared' or account.account_type == 'Business' then
          table.insert(options, account.account_number .. ' - ' .. account.account_name)
        end
      end

      local account_select = tUi.triggerFormWait(character.source, {
        {
          header = 'Select Business Account',
          fields = {
            {
              id = 'account',
              txt = 'Account',
              options = options,
              grid_column_start = 'span 3',
            },
          }
        }
      })

      if not account_select or not account_select.account then
        TriggerEvent('core:server:businesses:management:openManagement', character, business_id, bypass)
        return
      end

      if account_select.account == remove_option then
        account_select.account = nil
      else
        account_select.account = string.sub(account_select.account, 1, string.find(account_select.account, '-') - 2)
      end

      local affected_rows = MySQL.update.await('UPDATE businesses SET bank_account_number = ? WHERE id = ? LIMIT 1', {
        account_select.account, custom_business.id
      })

      if affected_rows <= 0 then
        character.notify('Error setting business bank account')
        return
      end

      reloadBusinesses()
      TriggerEvent('core:server:businesses:management:openManagement', character, business_id, bypass)
      character.notify('Business bank account set successfully')
    end)


    if not exports.blrp_core:doesBusinessHaveRadios(business_id) and string.lower(custom_business.type or '') ~= 'police' then
      menu:addSelection(false, 'Purchase Encrypted Radio - $150,000', function(source, value, callback)
        callback('close')

        if not character.request('Do you want to purchase an encrypted radio channel for '..custom_business.name..' for $150,000?  This will be deducted from your personal account.') then
          return
        end

        if not character.tryPayment(150000, true, true, false, false) then
          return
        end

        if exports.blrp_core:purchaseBusinessRadio(business_id) then
          character.notify('Encrypted Radio Frequency successfully purchased.')
          character.log('BUSINESS', 'Purchased encrypted radios for $150,000', {
            business_id = business_id,
          })
        else
          character.giveBankMoney(150000)
          character.notifyError('Encrypted Radio Frequency purchase failed.')
          character.log('BUSINESS', 'Purchased failed for encrypted radios - $150,000 refunded', {
            business_id = business_id,
          })
        end
      end)
    end

    if exports.blrp_core:doesBusinessHaveRadios(business_id) and string.lower(custom_business.type) ~= 'police' then
      local manage_radio_menu = 'Manage Radio Channels'
      menu:addSubMenu(false, manage_radio_menu)


      local radios = exports.blrp_core:getBusinessRadioFrequencies(business_id)
      local count = 1
      for _, radio_data in pairs(radios) do
        if radio_data.active == 1 then

          if string.len(radio_data.alias) == 0 then
            radio_data.alias = custom_business.name..' '..count
          end
          count = count + 1
          local radio_name = (10000 + radio_data.frequency)..' MHz | ACTIVE | '..radio_data.alias
          if string.len(radio_data.permissions) > 0 then
            radio_name = radio_name..' | '..radio_data.permissions
          end
          menu:addSubMenu(manage_radio_menu, radio_name)

          menu:addTextInput(radio_name, 'Set Alias', '',function(source, value, callback)
            callback('close')

            exports.blrp_core:ScanInputForBadWords(character.source, 'Radio Alias', value)

            if string.len(value) > 24 then
              character.notifyError("The alias must be 24 characters or less.")
              return
            end

            if exports.blrp_core:updateRadioAlias(radio_data.frequency, value) then
              character.notify((10000 + radio_data.frequency)..' MHz alias Updated to '..value)
              character.log('BUSINESS', 'Update radio alias', {
                frequency = radio_data.frequency,
                business_id = business_id,
                alias = value,
              })
            end
          end)

          local perm_menu = 'Restrict '..(10000 + radio_data.frequency)..' MHz to Permission'
          menu:addSubMenu(radio_name, perm_menu)


          for _, permission_data in pairs(businesses_config.permissions) do
            local permission_name = permission_aliases[business.name .. permission_data.db_suffix] or permission_data.name

            if permission_name then
              menu:addSelection(perm_menu, permission_name, function(player, value, callback)
                callback('close')
                if exports.blrp_core:updateRadioPermission(radio_data.frequency, permission_data.db_suffix) then
                  character.notify((10000 + radio_data.frequency)..' MHz restricted to '..value)
                  character.log('BUSINESS', 'Restricted radio frequency', {
                    frequency = radio_data.frequency,
                    business_id = business_id,
                    permission = permission_data.db_suffix,
                  })
                end
              end)
            end
          end

          if custom_business and custom_business.custom_computer_options then
            for _, custom_computer_option in ipairs(custom_business.custom_computer_options) do
              menu:addSelection(perm_menu, custom_computer_option.name, function(source, value, callback)
                callback('close')
                if exports.blrp_core:updateRadioPermission(radio_data.frequency, custom_computer_option.db_suffix) then
                  character.notify((10000 + radio_data.frequency)..' MHz restricted to '..value)
                  character.log('BUSINESS', 'Restricted radio frequency', {
                    frequency = radio_data.frequency,
                    business_id = business_id,
                    permission = permission_data.db_suffix,
                  })
                end
              end)
            end
          end

          menu:addSelection(perm_menu, 'Reset Permission', function(source, value, callback)
            callback('close')
            if exports.blrp_core:updateRadioPermission(radio_data.frequency, '') then
              character.notify((10000 + radio_data.frequency)..' MHz permission unrestricted')
              character.log('BUSINESS', 'Unrestricted radio frequency', {
                frequency = radio_data.frequency,
                business_id = business_id,
              })
            end
          end)

        else
          local radio_name = (10000 + radio_data.frequency)..' MHz | Purchase - $75,000'
          menu:addSelection(manage_radio_menu, radio_name, function(source, value, callback)
            callback('close')

            if not character.request('Do you want to purchase '..(10000 + radio_data.frequency)..' MHz for '..custom_business.name..' for $75,000?  This will be deducted from your personal account.') then
              return
            end

            if not character.tryPayment(75000, true, true, false, false) then
              return
            end

            if exports.blrp_core:purchaseBusinessRadio(business_id, radio_data.frequency) then
              character.notify('Encrypted Radio Frequency successfully purchased.')
              character.log('BUSINESS', 'Purchased radio frequency for $75,000', {
                frequency = radio_data.frequency,
                business_id = business_id,
              })
            else
              character.giveBankMoney(75000)
              character.notifyError('Encrypted Radio Frequency purchase failed.')
              character.log('BUSINESS', 'Radio frequency purchase failed, money returned', {
                frequency = radio_data.frequency,
                business_id = business_id,
              })
            end

          end)
        end
      end
    end
  end

  if checkUserCanAccess(character, business_id, 'logs') or bypass then
    menu:addSelection(false, 'Business Log', function(source, value, callback)
      callback('')
      TriggerEvent('core:server:businesses:management:openBusinessLog', character, business_id, bypass)
    end)
  end

  menu:show(character.source)
end)

AddEventHandler('core:server:businesses:management:openElectricalManagement', function(character, business_id, property, bypass)
  local business = getBaseBusinessDefFromId(business_id)
  local custom_business = getCustomBusinessDefFromId(business_id)

  if not business or not custom_business then
    return
  end

  local records = MySQL.query.await('SELECT * FROM core_electrical_bills WHERE property_id = ? ORDER BY id DESC', {
    property.id
  })

  local menu = BMenu:new(true, {
    resource_title = custom_business.name,
    section_title = 'Electrical Bills',
    title_bg_image = 'https://i.gyazo.com/8535b0d617f577767cb50fcffccb8d0c.jpg',
  })

  menu:addSelection(false, '◀️ Back to business management', function(source, value, callback)
    callback('')
    TriggerEvent('core:server:businesses:management:openManagement', character, business_id, bypass)
  end)

  if #records == 0 then
    menu:addSelection(false, '(No electrical bills to show)', function(source, value, callback)
      callback('')
    end)
  else
    for _, record in pairs(records) do
      local action_str = 'Select to pay'

      if record.paid then
        action_str = 'Paid'
      end

      local usage_dollars = ''

      if record.usage_dollars then
        usage_dollars = ' - $' .. record.usage_dollars
      else
        action_str = 'Current billing cycle'
      end

      menu:addSelection(false, record.year .. ' Week ' .. record.week .. usage_dollars .. ' - ' .. record.usage_kwh .. 'kWh (' .. action_str .. ')', function(source, value, callback)
        if record.paid or not record.usage_dollars then
          callback('')
          return
        end

        callback('close')

        -- Open bank with relevant information
        TriggerEvent('blrp_banking:server:open', false, {
          access_type = 'bank',
          extra = {
            modality = 'Transfer',
            transferAllowSelectSource = true,
            amount = record.usage_dollars,
            target = ******** + tonumber(property.id),
            transferAccountManual = 'LSDWP Accounts',
            transferAccountManualVerify = 'LSDWP Accounts',
            transferReadOnly = true,
            note = 'LSDWP Electical Bill - 1069 Occupation Ave - 2024 Week 22'
          }
        }, character.source)
      end)
    end
  end

  menu:show(character.source)
end)

AddEventHandler('core:server:businesses:management:openEmployeeManagement', function(character, business_id, bypass)
  local business = getBaseBusinessDefFromId(business_id)
  local custom_business = getCustomBusinessDefFromId(business_id)

  if not business then
    return
  end

  if not checkUserCanAccess(character, business_id, 'hire') and not checkUserCanAccess(character, business_id, 'fire') and not checkUserCanAccess(character, business_id, 'permissions') and not bypass then
    character.notify('You do not have permission to access this')
    return
  end

  local resource_title = ''

  if custom_business then
    resource_title = custom_business.name
  end

  local menu = BMenu:new(true, {
      resource_title = resource_title,
      section_title = 'Employee Management',
      title_bg_image = 'https://i.gyazo.com/8535b0d617f577767cb50fcffccb8d0c.jpg',
  })

  menu:addSelection(false, '◀️ Back to business management', function(source, value, callback)
    callback('')
    TriggerEvent('core:server:businesses:management:openManagement', character, business_id, bypass)
  end)

  local employees = MySQL.query.await([[
    SELECT * FROM core_character_business
      JOIN characters c on core_character_business.character_id = c.id AND core_character_business.user_id = c.identifier
      WHERE business_id = ?
  ]], { business_id })

  local function processHirePlayer(dl_number)
    local check_character = MySQL.single.await('SELECT * FROM characters WHERE dlnumber = ?', { dl_number })

    if not check_character then
      character.notify('Drivers license number not found')
      return
    end

    if tonumber(character.get('identifier')) == tonumber(check_character.identifier) then
      -- character.notify('You cannot hire yourself')
      -- return
    end

    local employee_exists = false

    for _, employee in ipairs(employees) do
      if employee.dlnumber == dl_number then
        employee_exists = true
      end
    end

    if employee_exists then
      character.notify('This person is already an employee')
      return
    end

    character.client('badMenu:client:hideAll')

    if not character.request('Confirm hiring ' .. check_character.firstname .. ' ' .. check_character.lastname .. ' (' .. check_character.identifier .. ')') then
      return
    end

    local rows_affected = MySQL.update.await('INSERT INTO core_character_business (user_id, character_id, business_id) VALUES (?, ?, ?)', {
      check_character.identifier, check_character.id, business_id
    })

    if rows_affected <= 0 then
      character.notify('Error hiring employee')
      return
    end

    character.notify('Employee hired successfully')
    character.log('BUSINESS', 'Hired employee: business_id = ' .. business.id .. ' / name = ' .. check_character.firstname .. ' ' .. check_character.lastname .. ' / vrp_id = ' .. check_character.identifier .. ' / char_id = ' .. check_character.id)
    -- TODO: add to business log

    local target_source = core.getCitizenSourceByCharId(check_character.id)

    -- Sync the hired employee's business data
    if target_source ~= nil then
      local target_character = core.character(target_source)

      target_character.notify('You have been hired as an employee of ' .. business.user_id .. "'s business")
      TriggerEvent('core:server:businesses:base:loadUserBusinesses', target_character.source, target_character.get('identifier'), target_character.get('id'), true)
    end
  end

  -- Hire by DL # option
  if checkUserCanAccess(character, business_id, 'hire') or bypass then
    menu:addSelection(false, 'Hire employee from nearby', function(player, value, callback)
      callback('close')

      local nearby_player = character.targetClosePlayer(3)

      if not nearby_player or nearby_player <= 0 then
        return
      end

      local nearby_character = core.character(nearby_player)

      processHirePlayer(nearby_character.get('dlnumber'))
    end)

    menu:addTextInput(false, 'Hire employee by DL #', '', function(source, value, callback)
      callback('')

      local dl_number = tonumber(value)

      -- String length check because DL # is always 8 digits
      if not dl_number or string.len(value) ~= 8 then
        character.notify('Invalid drivers license number')
        return
      end

      processHirePlayer(dl_number)
    end)
  end
  -- / Hire by DL #

  if #employees == 0 then
    menu:addSelection(false, 'No employees', function(source, value, callback)
      callback('')
    end)
  else
    for _, employee in ipairs(employees) do
      local employee_submenu_name = employee.firstname .. ' ' .. employee.lastname .. ' (' .. employee.identifier .. ')'

      menu:addSubMenu(false, employee_submenu_name)

      local options_added = false

      -- Fire
      if checkUserCanAccess(character, business_id, 'fire') or bypass then
        options_added = true
        menu:addSelection(employee_submenu_name, 'Fire employee', function(source, value, callback)
          callback('')

          if tonumber(employee.identifier) == tonumber(character.get('identifier')) then
            character.notify("You can't fire yourself")
            return
          end

          if tonumber(business.user_id) == tonumber(employee.identifier) and tonumber(business.character_id) == tonumber(employee.id) then
            character.notify("You can't fire the business owner")
            return
          end

          character.client('badMenu:client:hideAll')

          if not character.request('Confirm firing ' .. employee.firstname .. ' ' .. employee.lastname .. ' (' .. employee.identifier .. ')') then
            return
          end

          MySQL.Async.execute('DELETE FROM core_character_business WHERE user_id = @user_id AND character_id = @character_id AND business_id = @business_id LIMIT 1', {
            user_id = employee.identifier,
            character_id = employee.id,
            business_id = business_id
          }, function(rows_affected)
            if rows_affected == 0 then
              character.notify('Error firing employee')
              return
            end

            character.notify('Employee fired successfully')
            character.log('BUSINESS', 'Fired employee: business_id = ' .. business.id .. ' / name = ' .. employee.firstname .. ' ' .. employee.lastname .. ' / vrp_id = ' .. employee.identifier .. ' / char_id = ' .. employee.id)

            local target_source = core.getCitizenSourceByCharId(employee.id)

            -- Sync the fired employee's business data
            if target_source ~= nil then
              local target_character = core.character(target_source)

              target_character.notify('You have been fired from ' .. business.user_id .. "'s business")
              TriggerEvent('core:server:businesses:base:loadUserBusinesses', target_character.source, target_character.get('identifier'), target_character.get('id'), true)
            end
          end)
        end)
      end
      -- / Fire

      -- Permissions
      if
        (checkUserCanAccess(character, business_id, 'permissions') and
        (
          tonumber(employee.identifier) ~= tonumber(character.get('identifier')) or
          tonumber(business.character_id) == tonumber(character.get('id'))
        )) or bypass
      then
        options_added = true

        local selected_permissions = {}

        local selected_casino_access_level = nil
        local initial_casino_access_level = nil

        for _, permission_data in pairs(businesses_config.permissions) do
          if
            bypass or
            (
              not permission_data.extra and
              not string.match(permission_data.db_suffix, 'veh_')
            ) or
            (
              business.custom and
              permission_data.extra and
              tonumber(business.user_id) == tonumber(character.get('identifier')) and
              tonumber(business.character_id) == tonumber(character.get('id'))
            ) or
            (
              business.custom and
              custom_business and
              custom_business.can_own_vehicles and
              string.match(permission_data.db_suffix, 'veh_')
            )
          then
            local permission_name = permission_aliases[business.name .. permission_data.db_suffix] or permission_data.name

            if permission_data.extra and permission_name then
              selected_permissions[permission_data.db_suffix] = hasAnyBusinessExtraFlags(employee.extra_flags, permission_data.db_suffix)
            else
              selected_permissions[permission_data.db_suffix] = employee['perm_' .. permission_data.db_suffix]
            end

            if permission_name then
              menu:addCheckbox(employee_submenu_name, permission_name, selected_permissions[permission_data.db_suffix], function(player, value, callback)
                selected_permissions[permission_data.db_suffix] = value

                callback(value)
              end)
            end
          end
        end

        if business.name == 'Diamond Casino' and (tonumber(business.character_id) == tonumber(character.get('id')) or bypass) then
          local levels = {}

          for i = 1, 10 do
            table.insert(levels, i)
          end

          local casino_access_level = DataStore.getUser(employee.identifier, 'core:casinoAccessLevel' .. employee.id)

          if not casino_access_level then
            casino_access_level = 1
            DataStore.storeUser(employee.identifier, 'core:casinoAccessLevel' .. employee.id, 1, true)
          end

          casino_access_level = tonumber(casino_access_level)

          selected_casino_access_level = casino_access_level
          initial_casino_access_level = casino_access_level

          menu:addMultipleChoice(employee_submenu_name, 'Security Access Level', casino_access_level, levels, function(player, value, callback)
            selected_casino_access_level = value

            callback(value)
          end)
        end

        menu:addSelection(employee_submenu_name, 'Save permissions', function(source, value, callback)
          callback('')

          if selected_casino_access_level and selected_casino_access_level ~= initial_casino_access_level then
            DataStore.storeUser(employee.identifier, 'core:casinoAccessLevel' .. employee.id, tonumber(selected_casino_access_level), true)
            character.log('BUSINESS', 'Updated casino access level for employee: name = ' .. employee.firstname .. ' ' .. employee.lastname .. ' / vrp_id = ' .. employee.identifier .. ' / char_id = ' .. employee.id .. ' / casino_access_level = ' .. selected_casino_access_level)
          end

          local value_string = ''

          local extra_permissions = {}

          for db_suffix, db_value in pairs(selected_permissions) do
            if string.match(db_suffix, 'extra') then
              if db_value == true then
                table.insert(extra_permissions, db_suffix)
              end
            else
              if value_string ~= '' then
                value_string = value_string .. ', '
              end

              value_string = value_string .. 'perm_' .. db_suffix .. ' = @' .. db_suffix
            end
          end

          selected_permissions.extra_flags = makeBusinessExtraFlags(table.unpack(extra_permissions))

          local query_string = 'UPDATE core_character_business SET ' .. value_string .. ', extra_flags = @extra_flags WHERE user_id = @user_id AND character_id = @character_id AND business_id = @business_id'

          selected_permissions.user_id = employee.identifier
          selected_permissions.character_id = employee.id
          selected_permissions.business_id = business.id

          MySQL.Async.execute(query_string, selected_permissions, function(rows_affected)
            if rows_affected == 0 then
              character.notify('Error updating employee permissions')
              return
            end

            character.notify('Employee permissions updated successfully')
            character.log('BUSINESS', 'Updated permissions for employee: business_id = ' .. business.id .. ' / name = ' .. employee.firstname .. ' ' .. employee.lastname .. ' / vrp_id = ' .. employee.identifier .. ' / char_id = ' .. employee.id .. ' / permissions = ' .. json.encode(selected_permissions))

            local target_source = core.getCitizenSourceByCharId(employee.id)

            -- Sync the employee's business data
            if target_source ~= nil then
              local target_character = core.character(target_source)

              -- Sync owned vehicles if needed
              if custom_business and custom_business.can_own_vehicles then
                TriggerEvent('blrp_vehicles:server:syncOwnedVehicles', target_source)
              end

              TriggerEvent('core:server:businesses:base:loadUserBusinesses', target_character.source, target_character.get('identifier'), target_character.get('id'), true)
            end
          end)

          character.client('badMenu:client:hideAll')
        end)
      end
      -- / Permissions

      -- Stub for people who can hire (can see entire list), but not manage permissions or fire
      if not options_added then
        menu:addSelection(employee_submenu_name, 'No options available', function(source, value, callback)
          callback('')
        end)
      end
    end
  end

  menu:show(character.source)
end)

AddEventHandler('core:server:businesses:management:openBusinessLog', function(character, business_id, bypass)
  local business = getBaseBusinessDefFromId(business_id)
  local custom_business = getCustomBusinessDefFromId(business_id)

  if not business then
    return
  end

  if not checkUserCanAccess(character, business_id, 'logs') and not bypass then
    character.notify('You do not have permission to access this')
    return
  end

  local resource_title = ''

  if custom_business then
    resource_title = custom_business.name
  end

  local menu = BMenu:new(true, {
      resource_title = resource_title,
      section_title = 'Business Log',
      title_bg_image = 'https://i.gyazo.com/8535b0d617f577767cb50fcffccb8d0c.jpg',
  })

  menu:addSelection(false, '◀️ Back to business management', function(source, value, callback)
    callback('')
    TriggerEvent('core:server:businesses:management:openManagement', character, business_id, bypass)
  end)

  MySQL.Async.fetchAll('SELECT * FROM businesses WHERE id = @business_id', {
    business_id = business.id
  }, function(businesses)
    if #businesses == 0 then
      character.notify('Unable to access business log')
      return
    end

    MySQL.Async.fetchAll('SELECT * FROM business_logs WHERE business_id = @business_id AND category = "DEFAULT" ORDER BY id DESC LIMIT 100', {
      business_id = business.id
    }, function(rows)
      if #rows == 0 then
        menu:addSelection(false, 'No results', function(source, value, callback)
          callback('')
        end)
      else
        for _, row in ipairs(rows) do
          menu:addSelection(false, '[' .. row.time .. '] ' .. row.employee .. ' ' .. row.action, function(source, value, callback)
            callback('')
          end)
        end
      end

      menu:show(character.source)
    end)
  end)
end)
