local bobcat_safe_status = {
  [1] = 0,
  [2] = 0,
  [3] = 0,
  [4] = 0,
  [5] = 0,
}

local bobcat_safe_dialer_entities = {
  [1] = false,
  [2] = false,
  [3] = false,
  [4] = false,
  [5] = false,
}

local model = `prop_plg_safe_dialer`
local dict = 'clip@prop_plg_safe_dialer'
local clip = 'dial'

RegisterNetEvent('core:client:bobcat:resetSafes', function()
  for _, coords in pairs(bobcat_config.safe_coords) do
    tCore.removeModelHide(coords, 0.25, `plogint_vau_safe`, true)
    tCore.removeModelSwap(coords, 0.25, `plogint_vau_safe`, `plogint_vau_safe_open`)
  end

  TriggerEvent('core:client:bobcat:syncSafes', {
    [1] = 5,
    [2] = 5,
    [3] = 5,
    [4] = 5,
    [5] = 5,
  })

  Citizen.Wait(100)

  TriggerEvent('core:client:bobcat:syncSafes', {
    [1] = 5,
    [2] = 5,
    [3] = 5,
    [4] = 5,
    [5] = 5,
  })
end)

RegisterNetEvent('core:client:bobcat:syncSafes', function(status)
  local status_old = bobcat_safe_status

  for safe_id, status_new in pairs(status) do
    local status_old = bobcat_safe_status[safe_id]
    local dialer_entity = bobcat_safe_dialer_entities[safe_id]
    local safe_coords = bobcat_config.safe_coords[safe_id]
    local safe_entity = GetClosestObjectOfType(safe_coords.x, safe_coords.y, safe_coords.z, 0.25, `plogint_vau_safe`, false)

    if status_old ~= status_new then
      if status_old == SDF_NONE and status_new == SDF_WORKING then
        -- Dialer didn't exist but now does. Create and animate the dialer entity
        while not HasModelLoaded(model) or not HasAnimDictLoaded(dict) do
          RequestModel(model)
          RequestAnimDict(dict)
          Wait(0)
        end

        dialer_entity = CreateObject(model, 885.778, -2285.058, 30.0, false, false, false)

        SetEntityHeading(dialer_entity, GetEntityHeading(safe_entity))
        SetEntityCoords(dialer_entity, GetEntityCoords(safe_entity))
        PlayEntityAnim(dialer_entity, 'dial', 'clip@prop_plg_safe_dialer', 1000.0, true, true, false, 0, 1024)

        bobcat_safe_dialer_entities[safe_id] = dialer_entity
      elseif (status_old == SDF_WORKING or status_old == SDF_FINISHED) and status_new == SDF_REMOVED then
        -- Dialer status removed, delete entity if it exists
        if dialer_entity and DoesEntityExist(dialer_entity) then
          DeleteEntity(dialer_entity)
        end

        bobcat_safe_dialer_entities[safe_id] = false
      elseif status_old == SDF_WORKING and (status_new == SDF_JAMMED or status_new == SDF_FINISHED) then
        -- Dialer is jammed or done, stop the animation
        if dialer_entity and DoesEntityExist(dialer_entity) then
          StopEntityAnim(dialer_entity, clip, dict)
        end
      elseif status_old == SDF_JAMMED and status_new == SDF_WORKING then
        -- Dialer was jammed, is now working. Start animation again
        if entity and DoesEntityExist(entity) then
          while not HasAnimDictLoaded(dict) do
            RequestAnimDict(dict)
            Wait(0)
          end

          PlayEntityAnim(dialer_entity, 'dial', 'clip@prop_plg_safe_dialer', 1000.0, true, true, false, 0, 1024)
        end
      end
    end
  end

  bobcat_safe_status = status
end)

exports('GetBobcatSafeStatus', function(coords)
  local safe_id = false

  for check_id, check_coords in pairs(bobcat_config.safe_coords) do
    if #(check_coords - coords) < 0.1 then
      safe_id = check_id
      break
    end
  end

  if not safe_id then
    return false
  end

  return bobcat_safe_status[safe_id]
end)
