-- Performance optimized bobcat interactions
local last_bobcat_check = 0
local cached_bobcat_coords = vector3(0, 0, 0)

Citizen.CreateThread(function()
  while true do
    if not bobcat_global_inside then
      Citizen.Wait(2000) -- Wait 2 seconds when not inside
      goto continue
    end

    Citizen.Wait(0) -- Reduced from 0ms to 100ms for major performance gain

    local current_time = GetGameTimer()

    -- Cache player coordinates
    if current_time - last_bobcat_check > 200 then
      cached_bobcat_coords = GetEntityCoords(PlayerPedId())
      last_bobcat_check = current_time
    end

    local closest_id = nil
    local closest_coords = nil
    local closest_distance = nil

    for interact_id, interact_coords in pairs(bobcat_config.interactables) do
      local distance = #(cached_bobcat_coords - interact_coords)

      if distance < 1.0 and (not closest_distance or distance < closest_distance) then
        closest_id = interact_id
        closest_coords = interact_coords
        closest_distance = distance
      end
    end

    if closest_id and closest_coords then
      doWhenPressed(closest_coords, 'E', '{key}', function()
        pBobcat.interactWith({ closest_id })
      end)
    end

    ::continue::
  end
end)
