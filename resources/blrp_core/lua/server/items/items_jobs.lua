beginRegisterCategory('jobs')

registerItem('compost', 'Compost', 0.5)
registerItem('filled_trash_bag', 'Filled Trash Bag', 2)
registerItem('electric_torch', 'Electric Torch', 0.1)
registerItem('oil_empty_drum', 'Liquid Container', 0.1)
registerItem('oil_filled_drum', 'Oil', 0.5)
registerItem('oil_refined_filled_drum', 'Refined Oil', 1.5)
registerItem('repair_parts', 'Repair Parts', 0.01, {}, {
  ['aluminum'] = 1
}, {
  recipe_allow_target = true,
  recipe_items_per = 25,
})
registerItem('compost_bag', 'Bag of Compost', 0, {}, {}, {
  unstackable = true,
  item_durability = {
    dur_initial = 20,
    dur_cur = 20,
    dur_weight_per = 0.35,
  }
})

local last_gold_pos = {}
registerItem('sift_pan', 'Gold Pan', 0.2, {
  {
    name = 'Use',
    callback = function(character, item_id, item_id_base, meta)

      if not meta.dur_initial then
        meta.dur_initial = 80
      end

      if not meta.dur_cur then
        meta.dur_cur = 80
      end

      if meta.dur_cur <= 0 then
        character.notifyError('The pan is broken.')
        return
      end

      if tSurvival.getIsInAnyVehicle(character.source) then
        character.notifyError('You cannot do this from a vehicle')
        return
      end

      local in_water = tSurvival.isInWater(character.source)
      if not tZones.isInsideMatchedZone(character.source, { 'GoldPanningArea' }) or not in_water then
        character.notify('No gold to be found here.')
        return
      end

      local last_pos = last_gold_pos[character.get('id')]
      if last_pos and #(last_pos - character.getCoordinates()) < 15.0 then
        character.notify('You\'ve already panned in this area.')
        return
      end

      character.animate({{ 'amb@world_human_gardener_plant@female@base', 'base_female', 1 }}, false, true)

      if not character.progressPromise('Panning for Gold', 15) then
        character.stopAnimation(false)
        return
      end

      local panning_level = character.getAptitudeLevel('gold-panning')
      local skill_iterations = math.clamp(5 - panning_level, 2, 5)
      local skill_check = tLockpick.lockpick(character.source, { skill_iterations })

      if not skill_check then
        character.stopAnimation(false)
        character.notify('You didn\'t find anything here.')
        character.varyAptitudeXp('gold-panning', -1)
        modifyItemMeta(character.source, item_id, 'dur_cur', meta.dur_cur - 1)
        return
      end

      math.randomseed(tonumber(GetGameTimer()) + tonumber(character.get('id')))

      local success_chance = math.random(0, 100) + (panning_level * 5)
      local reward = math.random(1, 17 + (panning_level * 2))

      character.stopAnimation(false)

      if success_chance > 50 then
        if not character.hasRoomFor('gold_ore', reward) then
          local chest_coords = character.getCoordinates()
          chest_coords = vector3(chest_coords.x, chest_coords.y, chest_coords.z + 0.5)
          character.log('GOLD PANNING', "Successfully panned for gold but dropped on ground due to full inventory", {
              amount_rewarded = reward,
              skill_level = panning_level,
              chest_coords = chest_coords,
            })
          exports.blrp_inventory:DropGroundItem(chest_coords, 'gold_ore', reward)
          character.notify('You found '..reward..' gold nuggets amongst the mud.')
          character.notifyError('You did not have enough room for the gold and dropped it on the ground')
        else
          character.give('gold_ore', reward, false, false)
          character.log('GOLD PANNING', 'Successfully panned for gold', {
            amount_rewarded = reward,
            skill_level = panning_level,
          })
          character.notify('You found '..reward..' gold nuggets amongst the mud.')
        end

        character.varyAptitudeXp('gold-panning', 2)
      else
        character.notify('You didn\'t find anything here.')
      end

      modifyItemMeta(character.source, item_id, 'dur_cur', meta.dur_cur - 1)
      last_gold_pos[character.get('id')] = character.getCoordinates()

    end
  }
}, {}, {
  unstackable = true,
  item_durability = {
    dur_initial = 80,
    dur_cur = 80,
    dur_weight_per = 0.00,
  }
})

registerItem('gold_processed', 'Processed Gold', 0.5)
registerItem('gold_catalyst', 'Gold Catalyst', 0.5)
registerItem('hemp_cloth', 'Hemp Cloth', 0.5)
registerItem('prospecting_kit', 'Prospecting Kit', 0.5)
registerItem('sapphire', 'Sapphire', 0.5)
registerItem('emerald', 'Emerald', 0.5)
registerItem('old_boot', 'Old Boot', 0.5)
registerItem('ruby', 'Ruby', 0.5)
registerItem('gold_coin', 'Gold Coin', 0.5)
registerItem('diamond', 'Diamond', 0.5)
registerItem('blood_diamond', 'Blood Diamond', 0.5)
registerItem('common_artifact', 'Common Artifact', 0.5)
registerItem('rare_artifact', 'Rare Artifact', 0.5)
registerItem('diamond_ring', 'Diamond Ring', 0.1)
registerItem('gold_brick', 'Gold Brick', 1.0)
registerItem('dia_box', 'Diamond Box', 1.0)
registerItem('repairkit', 'Engineering Kit', 0.5)
registerItem('inv_pack', 'Inventory Pack', 0.5)
registerItem('inv_pack_mini_1', 'Drink Pack', 2.0)
registerItem('inv_pack_mini_2', 'Food Pack', 2.0)
registerItem('taco_ingredient', 'Taco Meat', 0.05)

registerItem('mech_diagnose_tool', 'Diagnose Tool', 1.0, {
  {
    name = 'Use',
    callback = function(character, item_id)
      local allowed = character.hasGroup("Angels Autocare") or character.hasGroup("Flywheels")
      if allowed then
        character.client('blrp_menu:diagnoseVehicle')
      else
        character.notify("You are not sure how to use this")
      end
    end
  }
}, {
  ['aluminum'] = 1
}, {
  recipe_allow_target = true,
})

registerItem('mech_engine_wrench', 'Engine Wrench', 1.0, {
  {
    name = 'Use',
    callback = function(character, item_id)
      local allowed = character.hasGroup("Angels Autocare") or character.hasGroup("Flywheels")
      if allowed then
        character.client('blrp_menu:repairEngine')
      else
        character.notify("You are not sure how to use this")
      end
    end
  }
}, {
  ['titanium'] = 1
}, {
  recipe_allow_target = true,
})

registerItem('mech_body_fixer', 'Body Fixer', 1.0, {
  {
    name = 'Use',
    callback = function(character, item_id)
      local allowed = character.hasGroup("Angels Autocare") or character.hasGroup("Flywheels")
      if allowed then
        character.client('blrp_menu:repairBody')
      else
        character.notify("You are not sure how to use this")
      end
    end
  }
}, {
  ['steel'] = 1
}, {
  recipe_allow_target = true,
})

registerItem('mech_fuel_tools', 'Fuel Tools', 1.0, {
  {
    name = 'Use',
    callback = function(character, item_id)
      local allowed = character.hasGroup("Angels Autocare") or character.hasGroup("Flywheels")
      if allowed then
        character.client('blrp_menu:repairFuel')
      else
        character.notify("You are not sure how to use this")
      end
    end
  }
}, {
  ['craft_copper'] = 1
}, {
  recipe_allow_target = true,
})

registerItem('mech_squeegee', 'Car Squeegee', 1.0, {
  {
    name = 'Use',
    callback = function(character, item_id)
      local vehicle = GetVehiclePedIsIn(GetPlayerPed(character.source), false)

      if vehicle and vehicle > 0 then
        character.notifyError('You can\'t do this inside a vehicle.')
        return
      end

      character.client('client:doCarWash')
    end
  }
}, {
  ['paper'] = 1,
  ['craft_charcoal'] = 5,
}, {
  recipe_allow_target = true,
})

local blockedCategories = { -- prevent from installing a radio in these categories
  8,
  21,
  22,
  13,
}

local blockedModels = { -- prevent from installing a radio in these models
  'wheelchair',
  'mower',
  'tractor',
  'veto',
  'veto2',
}

local whitelistedModels = { -- allow  installing a radio in these models from a blocked category
  'eg',
}

registerItem('car_radio', 'Car Radio', 1.0, {
  {
    name = 'Install',
    callback = function(character, item_id)
      local server_uid = tVehicles.getServerUidOfVehiclePedIsIn(character.source)

      if not server_uid then
        character.notify('You must be inside the vehicle to install this')
        return
      end

      local selfInstall = false

      if character.hasItemQuantity('digiden_wire_harness', 1) then -- else will be installed by LST
        selfInstall = true
      end

      local vehicleModel = tSurvival.getVehicleModelPedIsIn(character.source)
      local excludedVehicle = false

      for _, class in pairs(blockedCategories) do
        if exports.blrp_core:GetVehicleClassFromName(vehicleModel) == class then
          excludedVehicle = true
        end
      end

      for _, model in pairs(blockedModels) do
        if vehicleModel == GetHashKey(model) then
          excludedVehicle = true
        end
      end

      for _, model in pairs(whitelistedModels) do
        if vehicleModel == GetHashKey(model) then
          excludedVehicle = false
        end
      end

      if excludedVehicle then
        character.notify('You can\'t install a radio in this vehicle.')
        return
      end

      if selfInstall then
        if not character.progressPromise('Installing', 10, {
          animation = {
            animDict = 'mini@repair',
            anim = 'fixing_a_ped',
            flags = 49,
          },
          controlDisables = {
            disableMovement = true,
            disableCarMovement = true,
            disableMouse = false,
            disableCombat = true,
          },
        }) then
          return false
        end

        if not character.take('digiden_wire_harness', 1) then
          return
        end


      end

      if not character.hasItemQuantity('car_radio', 1) then -- in case they lost the radio while installing
        return
      end

      local success = exports.blrp_vehicles:InstallVehicleAddon(character.source, 'car_radio', server_uid, selfInstall)

      if selfInstall and success then -- consume radio for self installs
        character.take('car_radio', 1)
        character.log('VEHICLE-ADDON', 'Installed Car Radio on vehicle / Plate: ' .. GetVehicleNumberPlateText(GetVehiclePedIsIn(GetPlayerPed(character.source), false)))
        character.notify('Vehicle Radio successfully installed')
      elseif selfInstall and not success then -- self install failed, car may already have a radio
        character.give('digiden_wire_harness', 1)
      end
    end
  }
}, {
  ['aluminum'] = 2,
  ['craft_battery'] = 3,
}, {
  recipe_allow_target = true,
  recipe_items_per = 1,
})

registerItem('waybill_printer', 'Waybill Printer', 1.0, {
  {
    name = 'Create',
    callback = function(character)

    character.hideInventory(true)

    local shipperName = character.prompt('Enter manufacturer name (max 25 chars)')
    local receiverName = character.prompt('Enter receiver name (max 25 chars)')
    local trailerLoad = character.prompt('Enter load (note what you are transporting)')

    if not shipperName or not receiverName or not trailerLoad or trailerLoad == '' or receiverName == '' or shipperName == '' then
      character.notify('Invalid entry')
      return
    end

    if string.len(shipperName) > 25 or string.len(receiverName) > 25 or string.len(trailerLoad) > 60 then
      character.notify('Invalid entry')
      return
    end

    local paper_id = tonumber('99' .. math.random(1000000, 9000000))
    --HTML Template for Waybill
    local currentTime = os.time()

    -- Format the current time as a human-readable string
    local timeDateShipped = os.date("%B %d, %Y %I:%M:%S %p", currentTime)
    local contentsDescription = trailerLoad .. " manufactured by " .. shipperName

    -- Define the HTML template with placeholders for the variable values
local paper_content = [[
[nobg]<table style="border-collapse: collapse; margin: auto;">
<tr><td colspan="3" style="text-align: center; border: 1px solid black;"><h1>Pacific Shipping Solutions</h1></br><h6>We'll make your cargo feel like it's on a luxury cruise, minus the umbrella drinks</h6></td></tr>
<tr><td colspan="3" style="text-align: center; border: 1px solid black;"><h4>Waybill / Bill of Lading</h4></td></tr>
<tr><td colspan="3" style="text-align: center; border: 1px solid black;"><p>%s</p></td></tr>
<tr>
<td style="border: 1px solid black;">
<h3>Shipper's Information:</h3>
<table style="border-collapse: collapse;">
<tr><td><p>Shipper's Name:</p></td><td><p>%s</p></td></tr>
</table>
</td>
<td style="border: 1px solid black;">
<h3>Receiver's Information:</h3>
<table style="border-collapse: collapse;">
<tr><td><p>Receiver's Name:</p></td><td><p>%s</p></td></tr>
</table>
</td>
</tr>
<tr><td colspan="3" style="border: 1px solid black;"><h2>Description of Contents:</h2></td></tr>
<tr><td colspan="3" style="border: 1px solid black;"><p>%s</p></td></tr>
</table>
]]

    -- Use string.format to replace the placeholders with the variable values
    local htmlWithValues = string.format(paper_content, timeDateShipped, shipperName, receiverName, contentsDescription)

    TriggerEvent('blrp_paper:server:givePaperDirectSafe', character.source, paper_id, 'Job Waybill', htmlWithValues, 1)
  end
  }
}, {}, {
})

endRegisterCategory()
