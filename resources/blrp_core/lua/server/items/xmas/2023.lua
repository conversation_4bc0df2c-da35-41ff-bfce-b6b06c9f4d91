beginRegisterCategory('xm23_bazaar')

-- Christmas Bazaar 2023 (North Yankton)

------------------------------------------------------------------------------------------------------------------------------------------------------------
-- function registerConsumable(_item_id, item_name, weight, action_type, hunger_variance, thirst_variance, intoxication_variance, _callback, extra)
-- function registerItem(item_id, item_name, weight, choices, recipe, extra)
-- function registerFurnitureTokenItem(item_id, item_name, weight, choices, recipe, extra)
------------------------------------------------------------------------------------------------------------------------------------------------------------

-- Stall 1: Abuela's Kitchen
registerConsumable('xm23_s1_atole', "Abuela's Atole", 0.2, 'drink', 0, -25, false, function(character, item_id)
  if GetResourceState('blrp_yankton') == 'started' then
    tYankton.warmByPercent(character.source, { 25 })
  end
end, {
  category = 'food',
  duration = 13.5,
  bone = 28422,
  prop_name = 'p_amb_coffeecup_01',
  sequence = {
    { 'amb@world_human_drinking@coffee@male@idle_a', 'idle_c', 1 },
  },
  recipe = {
    ['food_raw_corn'] = 1,
    ['food_vanilla'] = 1,
    ['food_cinnamon'] = 1,
    ['milk'] = 1,
    ['water'] = 1,
  }
})

registerConsumable('xm23_s1_tamales', "Abuela's Tamales", 0.2, 'eat', -50, 0, false, false, {
  category = 'food',
  prop_name = 'bzzz_restaurant_food_e',
  zOff = 0.07,
  xRot = -30.0,
  propTwo = {
    prop_name = "alcaprop_fork",
    xOff = 0.14, yOff = 0.02, zOff = 0.01, xRot = -118.0, yRot = 192.0, zRot = 24.0,
  },
  duration = 10,
  sequence = {
    {"bzzz@restaurant@eat", "bzzz_restaurant_eat", 1},
    {"bzzz@restaurant@eat", "bzzz_restaurant_eat", 1},
    {"bzzz@restaurant@eat", "bzzz_restaurant_eat", 1},
    {"bzzz@restaurant@eat", "bzzz_restaurant_eat", 1},
  },
  recipe = {
    ['food_raw_corn'] = 1,
    ['food_flour_mix'] = 1,
    ['food_raw_pork'] = 1,
    ['food_spices'] = 1,
    ['food_butter'] = 1,
  }
})

registerConsumable('xm23_s1_conchas', "Abuela's Conchas", 0.2, 'eat', -25, 0, false, false, {
  category = 'food',
  prop_name = 'bzzz_food_bakery_muffin_a',
})

-- Stall 2: DKN
registerConsumable('xm23_s2_cookie', 'Gingerbro Cookie', 0.2, 'eat', -30, 0, false, function(character, item_id)
  local num = math.random(1, 10)
  if num < 7 then
    character.notify("That cookie hit different…")
    SetTimeout(5000, function()
      tItemInteractions.playScreenEffect(character.source, {'DMT_flight', 60})
    end)
    SetTimeout(60000, function()
      I.effectFicus(character, 10, false)
    end)
  else
    character.notify("That was yummy!")
  end
end, {
  category = 'food',
  prop_name = 'bzzz_food_bakery_muffin_a',
})

registerConsumable('xm23_s2_borg', 'Holiday Borg', 0.2, 'drink', 10, -65, { 20, 45 }, function(character, item_id)
  character.notify('Black out or back out, bitch! DKN! DKN!')
  character.animate({
    { 'anim@amb@nightclub_island@dancers@club@', 'hi_idle_a_f01', 1 },
    { 'random@drunk_driver_1', 'drunk_fall_over', 1 },
  }, false, false)
end, {
  prop_name = 'prop_ceramic_jug_01'
})

-- Third DKN item in lockpick item file (xm23_s2_pick)

-- Stall 3: Catgirl Curios
registerConsumable('xm23_s3_rum', 'Hot Buttered Rum', 0.2, 'drink', 0, -25, 15, function(character, item_id)
  if GetResourceState('blrp_yankton') == 'started' then
    tYankton.warmByPercent(character.source, { 25 })
  end
end, {
  duration = 13.5,
  bone = 28422,
  prop_name = 'prop_drink_whisky',
  sequence = {
    { 'amb@world_human_drinking@coffee@male@idle_a', 'idle_c', 1 },
  }
})

local catgirl_options = {
  {
    name = 'Use',
    callback = function(character)
      character.dpEmote('gangsign3')
      character.meText('thinks about catgirls')
    end
  }
}

registerItem('xm23_s3_cg1', 'Catgirl Surprise Ornament', 0.1, catgirl_options)
registerItem('xm23_s3_cg2', 'Catgirl Ornament', 0.1, catgirl_options)
registerItem('xm23_s3_cg3', 'Catgirl Star Ornament', 0.1, catgirl_options)

-- Stall 4: Maeple Tales
registerConsumable('xm23_s4_hc', 'Hot Chocolate', 0.2, 'drink', 0, -25, false, function(character, item_id)
  if GetResourceState('blrp_yankton') == 'started' then
    tYankton.warmByPercent(character.source, { 25 })
  end
end, {
  duration = 13.5,
  bone = 28422,
  prop_name = 'p_amb_coffeecup_01',
  sequence = {
    { 'amb@world_human_drinking@coffee@male@idle_a', 'idle_c', 1 },
  }
})

registerConsumable('xm23_s4_mac', 'Maple Macarons', 0.2, 'eat', -30, 0, false, false, {
  category = 'food',
  prop_name = 'bzzz_food_bakery_muffin_a',
})

registerItem('xm23_s4_book', 'A Yankton Tale', 0.8, {
  {
    name = 'Read',
    callback = function(character)
      character.client('blrp_inventory:hide')
      character.client("blrp_inventory:showEnlargedImage", 'xm23_s4_book_lg', 0.5, 0.5, 0.75, 0.75)
    end
  }
})

-- Stall 5: St Peter's Brewery
registerItem('xm23_s5_paper', 'Rolling Páipéirs', 0.02, false, false, { categories_secondary = { 'rolling_papers' }})

registerConsumable('xm23_s5_pint', '3-Headed Stout Pint', 0.2, 'drink', 10, -25, 10, nil, {recipe ={
  ['water'] = 2,
  ['yeast'] = 2,
  ['food_malt'] = 2,
},prop_name = 'prop_plastic_cup_02'})

registerConsumable('xm23_s5_bottle', '3-Headed Stout Bottle', 0.2, 'drink', 10, -25, 10, nil, {recipe ={
  ['water'] = 1,
  ['yeast'] = 1,
  ['food_malt'] = 1,
},prop_name = 'prop_beer_bottle'})

-- Stall 6: Yankton Nuts
registerConsumable('xm23_s6_nuts', 'Bourbon Nuts', 0.2, 'eat', -20, 0, false, function(character, item_id)
  if GetResourceState('blrp_yankton') == 'started' then
    tYankton.warmByPercent(character.source, { 5 })
  end
end, {
  category = 'food',
})

registerConsumable('xm23_s6_bis', 'Spiced Hazelnut Biscotti', 0.2, 'eat', -25, 0, false, false, {
  category = 'food',
})

-- Stall 7: Ludendorff Orphanage
registerItem('xm23_s7_kc_a', 'I ❤️ Daddy Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})
registerItem('xm23_s7_kc_b', 'I ❤️ Mommy Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})
registerFurnitureTokenItem('xm3_prop_xm3_toy_dog_01a', "Little Kyle's Toy Dog", 0.6)

-- Stall 8: Mystic Mistletoe
registerConsumable('xm23_s8_lp', 'Mistletoe Love Potion #9', 0.2, 'drink', 0, 0, false, function(character, item_id)
  Citizen.Wait(2000)
  character.dpEmote('blowkiss')
  character.client('dpemotes:WalkCommandStart', { false, 'maneater' })
end)

registerItem('xm23_s8_bk', 'Yule Book of Shadows', 0.8, {
  {
    name = 'Open',
    callback = function(character, item_id)
      local meta_id = item_id:gsub(':meta:', '')

      character.client('blrp_inventory:hide')

      SetTimeout(250, function()
        character.openChest(meta_id, 8.0, function() end, {
          whitelisted_items = {
            'quarts_crystal',
            'amethyst_crystal',
            'lapis_lazuli_crystal',
            'emerald_crystal',
            'tigers_eye_crystal',
            'carnelian_crystal',
            'garnet_crystal',
            'xm23_s8_crystal'
          }
        })
      end)
    end
  },
}, {}, {
  unstackable = true,
  categories_secondary = { 'res_table_allowed' },
})

registerItem('xm23_s8_crystal', 'Moldavite Crystal Tree', 0.2, {
  {
    name = 'Use',
    callback = function(character, item_id)
      if not character.take(item_id, 1, false) then
        return
      end

      character.notify('"You feel Jolly"')
    end
  }
},nil,{categories_secondary={"spellbound"}})

-- Stall 9: Ganja Grotto
registerItem('xm23_s9_bud_a', 'Yankton Stank Bud', 0.5, bud_choices, false, {
  weed_item = 'xm23_s9_joint_a',
})

registerItem('xm23_s9_bud_b', 'Tinsel Trainwreck Bud', 0.5, bud_choices, false, {
  weed_item = 'xm23_s9_joint_b',
})

registerItem('xm23_s9_bud_c', "Santa's Stash OG Bud", 0.5, bud_choices, false, {
  weed_item = 'xm23_s9_joint_c',
})

registerItem('xm23_s9_joint_a', 'Yankton Stank Joint', 0.5, joint_choices, false, {
  weed_callback = function(character)
    character.varyNeedOverTime('thirst', 10, 15)
    tSurvivalAlcohol.addIntoxication(character.source, { 5 })

    return true, true
  end,

  categories_secondary = {
    'drugs'
  },

  sub_color = '#6756AA',
  sub_name = 'Marijuana',
})

registerItem('xm23_s9_joint_b', 'Tinsel Trainwreck Joint', 0.5, joint_choices, false, {
  weed_callback = function(character)
    character.varyHealthOverTime(60, 15)

    SetTimeout(5000, function()
      tSurvival.ragdoll(character.source, { true })
      Citizen.Wait(math.random(3000, 5000))
      tSurvival.ragdoll(character.source, { false })

      tItemInteractions.setTimecycleModifier(character.source, { 'prologue_ending_fog', 300 })
      tItemInteractions.playScreenEffect(character.source, { 'ChopVision', 300 })
    end)

    return false, false
  end,

  categories_secondary = {
    'drugs'
  },

  sub_color = '#6756AA',
  sub_name = 'Marijuana',
})

registerItem('xm23_s9_joint_c', "Santa's Stash Joint", 0.5, joint_choices, false, {
  weed_callback = function(character)
    character.varyHealthOverTime(40, 15)

    SetTimeout(5000, function()
      tSurvival.ragdoll(character.source, { true })
      Citizen.Wait(math.random(3000, 5000))
      tSurvival.ragdoll(character.source, { false })

      tItemInteractions.setTimecycleModifier(character.source, { 'prologue_ending_fog', 300 })
      tItemInteractions.playScreenEffect(character.source, { 'ChopVision', 300 })
    end)

    return false, false
  end,

  categories_secondary = {
    'drugs'
  },

  sub_color = '#6756AA',
  sub_name = 'Marijuana',
})

-- Stall X: Banny's Snowglobe Emporium
local snowglobe_options = {
  {
    name = 'Use',
    callback = function(character, item_id)
      if item_id == 'xm23_sx_globe_a' then
        character.notify('You are reminded of police sirens and distant gunshots...')
      elseif item_id == 'xm23_sx_globe_b' then
        character.notify('You are reminded of sandy beaches and panther attacks...')
      elseif item_id == 'xm23_sx_globe_c' then
        character.notify('You are reminded of a vast and desolate snowy wasteland...')
      end
    end
  }
}

registerItem('xm23_sx_globe_a', 'Los Santos Snowglobe', 1.0, snowglobe_options)
registerItem('xm23_sx_globe_b', 'Cayo Perico Snowglobe', 1.0, snowglobe_options)
registerItem('xm23_sx_globe_c', 'Yankton Snowglobe', 1.0, snowglobe_options)

-----------------------------------------------------------------------------
-----------------------------------------------------------------------------
-------------------------------- AIR HERLER ---------------------------------
-----------------------------------------------------------------------------
-----------------------------------------------------------------------------

beginRegisterCategory('food')

registerConsumable('xm23_ah_nuts', 'Nut Free Nut Mix', 0.2, 'eat', -5, 0)
registerConsumable('xm23_ah_pep', 'Peppermint Choco-Stix', 0.2, 'eat', -5, 0)
registerConsumable('xm23_ah_spr', 'Sprinkle Choco-Stix', 0.2, 'eat', -5, 0)

-----------------------------------------------------------------------------
-----------------------------------------------------------------------------
------------------------------ GENERAL STORE --------------------------------
-----------------------------------------------------------------------------
-----------------------------------------------------------------------------

beginRegisterCategory('xm2023')

registerItem('xm23_kc_bf', 'Bigfoot Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})
registerItem('xm23_flintsteel', 'Flint and Steel', 0.2, false, false, { categories_secondary = { 'lighters' }})

beginRegisterCategory('food')

registerConsumable('xm23_jerk_a', 'Lemon Pepper Jerky', 0.2, 'eat', -20, 0)
registerConsumable('xm23_jerk_b', 'Salt & Soy Sauce Jerky', 0.2, 'eat', -20, 0)
registerConsumable('xm23_jerk_c', 'Sweet Teriyaki Jerky', 0.2, 'eat', -20, 0)

-----------------------------------------------------------------------------
-----------------------------------------------------------------------------
-------------------------- CHRISTMAS PRESENT ITEMS --------------------------
-----------------------------------------------------------------------------
-----------------------------------------------------------------------------

beginRegisterCategory('excluded')

-- Christmas tree items
local gift_choices_2023 = {
  {
    name = 'Use',
    callback = function(character, item_id)
      if tSurvival.getIsInAnyVehicle(character.source) then
        character.notify('You would leave too much wrapping paper in this vehicle. Get out first')
        return
      end

      character.hideInventory(true)

      if
      not character.progressPromise('Unwrapping Gift', 10, {
        animation = {
          animDict = 'missheistdockssetup1clipboard@idle_a',
          anim = 'idle_a',
          flags = 49,
        },
        prop = {
          model = 'v_ret_box',
          coords = {
            x = 0.0,
            y = 0.0,
            z = -0.1,
          },
        },
      }) or
        not character.take(item_id, 1, false)
      then
        return
      end

      -----------------------------------------
      -----------------------------------------
      -----------------------------------------

      local category = 'xmas2023_tier1'

      -- Box is guaranteed to be at least tier 1
      if math.random(1, 100) <= 10 then
        category = 'xmas2023_tier2'
      end

      -- LEO and Dealership employees not eligible for car gifts
      if
      category == 'xmas2023_tier3' and
        character.hasGroup({
          'LEO_OffDuty', 'The Boathouse', 'PDM Auto', 'Los Santos Customs',
        })
      then
        print('has bad group, defer to tier2')
        category = 'xmas2023_tier2'
      end

      local available_items = GItems.getItemsByCategory(category)

      if not available_items or #available_items == 0 then
        print('Error: no items found for category: ' .. category)
        return
      end

      local gift_item_id = available_items[math.random(1, #available_items)]

      local log_message = 'Rewarding item from xmas 2023 gift reward table'
      local log_context = {
        category = category,
        item_id = gift_item_id,
      }

      if gift_item_id == 'xm23_candycane' then
        local variations = {
          'comp_sk_bl_01_candy', -- Ho Ho Ho, bitch
          'comp_sk_bl_02_candy', -- Suck on this
          'comp_sk_bl_03_candy', -- Lick my cane
          'comp_sk_bl_04_candy', -- Rainbow
        }

        local variation = math.random(1, 5)

        variation = 5

        local meta = nil

        if variation ~= 5 then
          variation = variations[variation]
          meta = { components = { variation } }
        end

        log_context.item_variation = variation

        character.give('wbody|WEAPON_CANDYCANE', 1, meta)
      elseif gift_item_id == 'xm23_camera' then
        local variations = {
          'badpix_camera_snowflakes', -- Snowflakes
          'badpix_camera_hearts', -- Hearts
        }

        local variation = variations[math.random(1, #variations)]

        log_context.item_id = variation

        character.give(variation, 1)
      else
        character.give(gift_item_id, 1)
      end

      -- Applicable logging
      character.log('XMAS2023-GIFTS', log_message, {
        gift_info = log_context
      })

      if category == 'xmas2023_tier3' then
        DataStore.store('xm23g:t3_num_awarded', tier3_num_awarded + 1, false)
        DataStore.storeUser(character.get('identifier'), 'xm23g:t3_awarded', true, false)

        logDiscord('events', log_message, character.get('identifier'))
      end
    end
  }
}

registerItem('xm23_g_gift', 'Holiday Gift Box', 1.0, gift_choices_2023)

beginRegisterCategory('xmas2023_tier2')

-- Claimable items
for furniture_id, furniture_name in pairs({
  ['hei_prop_drug_statue_box_01'] = 'Impotent Rage Figure (Mint Condition)',
  ['p_jewel_pickup33_s'] = 'Disco Bones',
  ['prop_cs_lipstick'] = 'Berries in the snow lipstick',
  ['prop_cs_photoframe_01'] = "Someone's Uncle Photo Frame",
  ['prop_tool_box_01'] = 'Masc-Man Toolbox',
  ['prop_v_15_cars_clock'] = "'Murica Shop Clock",
  ['v_club_vumakeupbrsh'] = 'Craque Makeup Brush Roll',
  ['v_ilev_exball_blue'] = 'Blue Exercise Ball',
  ['v_ilev_exball_grey'] = 'Grey Exercise Ball',
  ['vw_prop_casino_art_bird_01a'] = 'Big Bird Statue',
  ['vw_prop_toy_sculpture_01a'] = 'Up-n-Atom Waiter',
  ['vw_prop_toy_sculpture_02a'] = 'Up-n-Atom Waitress',
  ['vw_prop_vw_card_case_01a'] = "Fool's Straight Card Display",
  ['xs_prop_trinket_mug_01a'] = 'Non-Dishwasher Safe Mug',
  ['xs_prop_x18_drill_01a'] = 'Cordless Power Drill',
}) do
  registerFurnitureTokenItem(furniture_id, furniture_name, 0.5)
end

-- Candy Cane weapon
registerItem('xm23_candycane', 'XM23 CANDYCANE PLACEHOLDER', 0)
-- no skin             = Base
-- comp_sk_bl_01_candy = Ho Ho Ho, bitch
-- comp_sk_bl_02_candy = Suck on this
-- comp_sk_bl_03_candy = Lick my cane
-- comp_sk_bl_04_candy = Rainbow

-- Badpix camera skin
registerItem('xm23_camera', 'XM23 CAMERA PLACEHOLDER', 0)
-- badpix_camera_snowflakes
-- badpix_camera_hearts

-- Dice / coins (RNG)
local function dice_options(...)
  local tbls = {}

  for _, tbl in pairs({...}) do
    table.insert(tbls, tbl)
  end

  return {
    {
      name = 'Use',
      callback = function(character, item_id)
        local response = ''

        for _, tbl in pairs(tbls) do
          local outcome = tbl[math.random(1, #tbl)]

          if response ~= '' then
            response = response .. ' '
          end

          response = response .. outcome
        end

        local verb = 'Rolling'

        if item_id == 'xm23_dice_a' or item_id == 'xm23_dice_g' then
          verb = 'Flipping'
        end

        character.notify(verb .. ' ' .. GItems.getItemName(item_id) .. '...')

        character.animate({
          {'anim@mp_player_intcelebrationmale@wank', 'wank', 1},
        })

        SetTimeout(1500, function()
          character.notify(response)
          character.meText(response)
        end)
      end
    }
  }
end

registerItem('xm23_dice_a', 'A Quarter', 0.2, dice_options({ 'Heads', 'Tails' }))
registerItem('xm23_dice_b', 'Dreidel', 0.2, dice_options({ 'Nun (Nothing)', 'Gimel (Everything)', 'Hey (Half)', 'Shin (Put In)' }))
registerItem('xm23_dice_c', 'Dirty Dice', 0.2, dice_options(
  { 'Cuddle', 'Hug', 'Kiss', 'High Five', 'Bite', 'Take foot pics' },
  { 'at the beach', 'at the arcade', 'in the bathtub', 'at the auto garage', 'at church', 'in an open grave' }
))
registerItem('xm23_dice_d', 'Drinking Game Dice', 0.2, dice_options({
  'Make up a rhyme',
  'Take a shot',
  'Take two shots',
  'Take three shots',
  'Drink a beer',
  'Drink a cocktail',
  'Sing a song',
  'Do a dance',
  'Hug someone',
  'Pick someone else to drink',
  'Run in circles',
  'Do a dare',
  'Answer a truth question',
  'Buy another round for everyone',
  'All the girls drink',
  'All the boys drink',
  'Take your shoes off',
  'Take your shirt off',
  'Take your pants off',
  'Smooch the person to your left',
}))
registerItem('xm23_dice_e', 'Ganja Game Dice', 0.2, dice_options(
  {
    'Last player to point up',
    'Everyone',
    'You',
    'Somebody',
    'Person of your choice',
    'Person to your right',
  },
  {
    'smoke a joint',
    'take a bong hit',
    'smoke a blunt',
    'take an edible',
    'roll a joint',
    'roll a blunt',
  },
  {
    'while walking in circles',
    'while humming a tune',
    'while saying the ABCs',
    'while making cat noises',
    'while being hugged',
    'while counting backward from 69',
  },
  {
    'then tell a joke',
    'twice',
    'then do a dance',
    'then tell a secret',
    '',
    'then share a life goal',
  }
))
registerItem('xm23_dice_f', "I'm bored, help! Dice", 0.2, dice_options({
  'Go bowling',
  'Go play an arcade game',
  'Go play PlasmaGame',
  'Go race PlasmaKarts',
  'Go get something to drink',
  'Go get something to eat',
  'Go golfing',
  'Go BMXing',
  'Go for a hike',
  'Go swimming in a lake',
  'Go for a boat ride',
  'Go shopping',
}))
registerItem('xm23_dice_g', 'Memento Mori Coin', 0.2, dice_options({ 'Life', 'Death' }))
registerItem('xm23_dice_h', 'Character Creation Dice', 0.2, dice_options(
  {
    'Evil Orc',
    'Magical Elf',
    'Friendly Human',
    'Unlawful Gnome',
    'Chaotic Human',
    'Chaotic Gnome',
    'Friendly Elf',
    'Lawful Orc',
    'Neutral Centaur',
    'Sexy Centaur',
  },
  {
    'Farmer',
    'Fisherman',
    'Dancer',
    'Bard',
    'Soldier',
    'Paladin',
    'Warrior',
    'Healer',
    'Marksman',
    'Archer',
  }
))

registerItem('xm24_dice_b1', 'Truth Dice', 0.2, dice_options(
  {
    'What is your relationship dealbreaker?',
    'What is a secret you have never told anyone?',
    'Who is the last person you texted?',
    'Who are the last 3 people you texted?',
    'Who would you leave your partner for?',
    'How often do you wash your sheets?',
    'What is your favorite gross food combination?',
    'Who would you like to smooch in this room?',
    'What were you last arrested for?',
    'What is the worst date you have been on?',
    'What is the best date you have been on?',
    'What is your toxic trait?',
    'What is your favorite pet name to be called',
    'Have you ever peed in the pool?',
    'What is the worst trend you have tried?',
  }
))

registerItem('xm24_dice_b2', 'Dare Dice', 0.2, dice_options(
  {
    'Ride a jet ski through the Vespucci canals',
    'Rob a bank in your undies',
    'Post a selfie on Life Invader with the caption “Single and ready to mingle”',
    'Let everyone look through your pockets',
    'Say an honest thing about each person in the group',
    'Do 50 jumping jacks',
    'Give a lap dance to someone of your choice',
    'Do your best sexy crawl',
    'Howl like a wolf',
    'Do an impression of someone else in the group',
    'Let the group help you pick out a new outfit',
    'Let the group help you pick out a new tattoo',
    'Let the group help you pick out a new hairstyle',
    'Remove an item of clothing',
    'Remove two items of clothing',
  }
))

beginRegisterCategory('xmas2023_tier1')

-- Perfumes
for _, perfume in pairs({
  { 'xm23_perfume_a', 'Amber Energy Cologne', 'Smells like amber, patchouli, and musk' },
  { 'xm23_perfume_b', 'Black Dahlia Perfume', 'Smells like dahlias and death' },
  { 'xm23_perfume_c', 'Cayo Kiss Perfume', 'Smells like warm sandy beaches and fresh fruit' },
  { 'xm23_perfume_d', 'Emerald Eroticism Cologne', 'Smells like body odor and green soap' },
  { 'xm23_perfume_e', 'Sea Treasures Cologne', 'Smells like salty air and scurvy' },
  { 'xm23_perfume_f', 'Vanilla Sunset Perfume', 'Smells like vanilla, but at night' },
}) do
  registerItem(perfume[1], perfume[2], 0.2, {
    {
      name = 'Use',
      callback = function(character)
        character.meText(perfume[3])
      end
    }
  })
end

-- Air Fresheners
for _, freshener in pairs({
  { 'xm23_freshener_a', 'Cherry Blossom Air Freshener', 'You smell a faint scent of shallow self esteem and cherry blossom' },
  { 'xm23_freshener_b', 'Dark Ice Air Freshener', 'You smell a faint scent of last place and a burnt out clutch' },
  { 'xm23_freshener_c', 'Hot Rod Air Freshener', 'You smell a faint scent of first place and baby oil' },
}) do
  registerItem(freshener[1], freshener[2], 0.2, {
    {
      name = 'Use',
      callback = function(character)
        character.notify(freshener[3])
      end
    }
  })
end

-- Hand sanitizer
local sani_options = {
  {
    name = 'Use',
    callback = function(character, item_id)
      character.dpEmote('cleanhands')
    end
  }
}

registerItem('xm23_sani_a', 'Extra Anti-Back Sanitizer', 0.2, sani_options)
registerItem('xm23_sani_b', 'Just Peachy Hand Sanitizer', 0.2, sani_options)
registerItem('xm23_sani_c', 'Squeaky Clean Hand Sanitizer', 0.2, sani_options)
registerItem('xm23_sani_d', 'Sunny Honey Hand Sanitizer', 0.2, sani_options)
registerItem('xm23_sani_e', 'Sweet Sugar Hand Sanitizer', 0.2, sani_options)

-- Keychains
registerItem('xm23_kc_a', 'Balloon Dog Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})
registerItem('xm23_kc_b', 'Kitty Cat Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})
registerItem('xm23_kc_c', 'Little Red Truck Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})
registerItem('xm23_kc_d', 'Piñata Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})
registerItem('xm23_kc_e', 'Pizza Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})
registerItem('xm23_kc_f', 'Rudolph Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})
registerItem('xm23_kc_g', 'Snowflake Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})
registerItem('xm23_kc_h', 'Snowman Keychain', 0.1, false, false, { categories_secondary = { 'keychains' }})

local bag_whitelist = {
  'badpix_pic',
  'printed_paper',
  'cash',
  'cash_peso',
  'id_citizen',
  'id_cdl',
  'id_hunting',
  'id_fishing',
  'id_bcso',
  'id_sasp',
  'id_doc',
  'id_doj',
  'id_lsfd',
  'id_lspd',
  'id_lsdwp',
  'id_gtf',
  'id_fib',
  'id_fib2',
  'id_noose',
  'id_zancudo',
  'id_mmh',
  'id_nysp',
  'id_casino_chairman',
  'id_casino_elite',
  'id_casino_premier',
  'id_casino_vip',
  'id_bestbuds',
  'manilla_folder',
  'm_document',
  'paper',
  'drawable_sss',
  'drawable_bcc',
  'scratcher_cb',
  'scratcher_hog',
  'scratcher_lc',
  'scratcher_rg',
  'scratcher_csf',
  'paper_chop',
}

-- Purses / Wallets
for wallet_id, wallet_data in pairs({
  ['xm23_wal_a'] = { name = 'Big Spender Wallet' },
  ['xm23_wal_b'] = { name = 'Blue Wallet' },
  ['xm23_wal_c'] = { name = 'Cherry Blossom Coin Purse' },
  ['xm23_wal_d'] = { name = 'Coral Fashion Wallet' },
  ['xm23_wal_e'] = { name = "Dad's in Debt Wallet" },
  ['xm23_wal_f'] = { name = 'Expensive Designer Handbag', whitelisted_items = bag_whitelist },
  ['xm23_wal_g'] = { name = 'Green Wallet' },
  ['xm23_wal_h'] = { name = "It's Giving Purple Handbag", whitelisted_items = bag_whitelist },
  ['xm23_wal_i'] = { name = 'Pink Wallet' },
  ['xm23_wal_j'] = { name = 'Practical Brown Handbag', whitelisted_items = bag_whitelist },
  ['xm23_wal_k'] = { name = 'Red Wallet' },
  ['xm23_wal_l'] = { name = 'Shopping Basket' },
  ['xm23_wal_m'] = { name = 'Turquoise Wallet' },
  ['xm23_wal_n'] = { name = 'VIP Briefcase', whitelisted_items = bag_whitelist },
}) do
  registerItem(wallet_id, wallet_data.name, 0.2, wallet_choices(wallet_data.whitelisted_items), false, { unstackable = true })
end

-- Books
for book_id, book in pairs({
  ['xm23_book_a'] = {
    name = 'Healing Your Inner Self',
    lines = {
      "Bro, balance your chakras, balance your brews, balance your babes, and balance your life.",
      "Chakras aligned = epic party vibes, brah!",
      "Dude, harmonize your inner energy, then dominate the beer pong table. You got this, bro!",
      "Chakra check before the pregame!",
      "Master your chakras, master the frat castle.",
      "Chakras blocked? Broski, let's get those vibes flowing! Cheers!",
      "Chakra balance: because even your energy needs a wingman.",
      "Unblock your energy centers and unleash the party animal, dudeskis!",
      "Chakras on point, life on point - that's the rule, brotato.",
    },
  },
  ['xm23_book_b'] = {
    name = 'How To Start a Boy Band',
    lines = {
      "Harmony in both vocals and friendships is the key to launching a successful boy band.",
      "Dress the part - if you don't own anything in leopard print or with sequins, it's time to go shopping.",
      "If your dance moves make people laugh, you're doing it right - it's called 'dance comedy,' and boy bands should embrace it!",
      "When in doubt, add more glitter.",
      "If your lyrics can make people laugh, cry, and question their life choices - congratulations, you've mastered the boy band trifecta.",
      "Pro tip: Practice your smoldering gazes in the mirror until even your reflection swoons - that's the secret to heartthrob status.",
      "The secret ingredient to boy band success? 20% talent, 80% shameless self-promotion, and 100% commitment to those synchronized hand gestures.",
      "When choreographing, always ask yourself: 'Can I do this move in heels?' If the answer is yes, add it to the routine immediately. If the answer is no, do it anyway.",
    },
  },
  ['xm23_book_c'] = {
    name = 'Love Bites Back',
    lines = {
      "... he reached toward her oozing mound ...",
      "... it wasn't just the undead rising up that night ...",
      "... is that a bone in your pocket or are you just happy to see me? ...",
      "... she smelled waterlogged and bloated... effervescent ...",
      "... the virus infected the pair with love... and bloodlust...for each other ...",
      "... he moaned... she moaned... their voice boxes had both been ripped out ...",
      "... she'd torn her clothes off in anticipation... or perhaps they'd gotten caught on a fence ...",
      "... they say \"til death do us part\"... but what happens if you reanimate? ...",
      "... I'd say he takes my breath away... but my lungs fell out years ago ...",
    },
  },
  ['xm23_book_d'] = {
    name = '50 Shades of Green - Salad Recipe Book',
    lines = {
      "Watermelon and Feta Fiesta: Cubed watermelon, crumbled feta cheese, fresh mint leaves, and a balsamic glaze. And lettuce.",
      "Roasted Beetroot and Goat Cheese Delight: Roasted beet slices, crumbled goat cheese, arugula, and a honey Dijon vinaigrette. And lettuce.",
      "Quinoa Mango Tango Salad: Cooked quinoa, diced mango, black beans, red onion, cilantro, and a lime-cumin dressing. And lettuce.",
      "Avocado Grapefruit Sensation: Sliced avocado, segmented grapefruit, spring mix, and a citrus vinaigrette. And lettuce.",
      "Cucumber, Radish, and Dill Dream: Sliced cucumber, radishes, fresh dill, chopped walnuts, and a lemon yogurt dressing. And lettuce.",
      "Pear, Blue Cheese, and Walnut Wonder: Sliced pears, crumbled blue cheese,spinach, candied walnuts, and a balsamic reduction. And lettuce.",
      "Spicy Mango-Lime Slaw: Shredded cabbage, julienned carrots, diced mango, cilantro, and a spicy mango-lime dressing. And lettuce.",
      "Caprese with a Twist: Cherry tomatoes, mozzarella balls, fresh basil, and a drizzle of pesto-infused balsamic glaze. And lettuce.",
      "Asian-Inspired Sesame Ginger Noodle Salad: Cooked soba noodles, julienned vegetables (carrots, bell peppers, and cucumbers), sesame seeds, and a ginger-soy dressing. And lettuce.",
      "Pomegranate and Pistachio Paradise: Arugula, pomegranate seeds, crumbled feta, and chopped pistachios, dressed with a pomegranate vinaigrette. And lettuce.",
    },
  },
  ['xm23_book_e'] = {
    name = 'Wizards of Wiggly Space',
    lines = {
      "In the cosmic haze of the Nebula Lounge, Zephyr the stoner wizard sparked his interstellar blunt, casting iridescent spells into the intergalactic void.",
      "Ganja crystals, pulsating with arcane energy, adorned the floating wizard hats of the celestial sorcerers aboard the Nebula Nimbus.",
      "As the spaceship soared through the star-studded nebulae, the stoner wizards debated the merits of teleportation versus time dilation for their cosmic adventures.",
      "Luna, the lunar-loving wizard, conjured moon rocks that glowed with psychedelic luminescence, turning the spacecraft into a celestial disco.",
      "\"Dude, pass me the space kaleidoscope; I need to recalibrate the astral frequencies,\" mumbled Orion, the laid-back astro-alchemist.",
      "Nebula nymphs, ethereal beings of pure energy, joined the wizards for a zero-gravity dance, leaving trails of stardust in their wake.",
      "Ganjalf the Green, the wise and ancient sage, puffed contemplatively on his space pipe, gazing into the swirling depths of the cosmic bong.",
      "The stoner wizards discovered a wormhole shortcut that led to the Munchie Galaxy, where planets were made of cheese and rivers flowed with liquid chocolate.",
      "In the vast expanse of the celestial desert, the wizards stumbled upon a psychedelic oasis where the cacti sang reggae tunes and the sand whispered ancient herbal secrets.",
      "\"By the blazing moons of Ganja Prime!\" exclaimed Stardust Steve, the spacefaring shaman, as he conjured a comet-shaped hookah for the ultimate cosmic communion.",
    },
  },
  ['xm23_book_f'] = {
    name = 'Healing Your Inner Self (Signed Edition)',
    lines = {
      "Bro, balance your chakras, balance your brews, balance your babes, and balance your life.",
      "Chakras aligned = epic party vibes, brah!",
      "Dude, harmonize your inner energy, then dominate the beer pong table. You got this, bro!",
      "Chakra check before the pregame!",
      "Master your chakras, master the frat castle.",
      "Chakras blocked? Broski, let's get those vibes flowing! Cheers!",
      "Chakra balance: because even your energy needs a wingman.",
      "Unblock your energy centers and unleash the party animal, dudeskis!",
      "Chakras on point, life on point - that's the rule, brotato.",
    },
    category = 'groups',
  },
  ['xm23_book_g'] = {
    name = 'Crime for Dummies',
    lines = {
      "When you shoot someone down, be sure to yell \"PACKED!\" directly in their face.",
      "When faced by your opps, don't use your words. That's silly! Bullets say more than \"please stop\" ever could.",
      "Be sure to pack nutrient dense and protein rich snacks. A rumbly tummy gets in the way of crime!",
      "Double check that your shoelaces are tied before trying to run from the police.",
      "Nothing says inconspicuous like a backpack with the barrel of a gun sticking out from it!",
      "If you see spike strips in the road, do your best to avoid them.",
      "Do a fit check with the crew before leaving. Police are known to be more forgiving with the drippiest of crims.",
      "Security stopping you from entering a bank with a full face mask? Keep trying. They'll let you in eventually.",
      "Pick a playlist BEFORE robbing a store. Most cops aren't willing to wait for you to pick the perfect track for your getaway.",
      "If you see a cop in public, make sure to loudly exclaim: \"Smells like bacon in here! Oink oink!\"",
      "While in a car chase, try going faster than the cops. Losing them is your best chance at getting away.",
    },
  },
  ['xm23_book_h'] = {
    name = 'How To Be a Great Cop',
    lines = {
      "Before starting your shift, look at yourself and say: \"You are smart, you are beautiful, and you make this city a better place.\"",
      "Remember: Traffic tickets and search warrants are just love notes from the law.",
      "Chase your dreams AND your suspects with equal determination.",
      "One of the biggest hurdles of LEO life is avoiding donut-related stereotypes. You can do it!",
      "Remember: Cops are superheroes with badges instead of capes.",
      "If you spill coffee on your uniform, a little bit of hairspray will get that stain right out!",
      "If you're having trouble staying awake while patrolling the highway, consider asking your partner to slap you across the face.",
      "Tired of your captain pushing you around? File an IA against them but blame it on another officer.",
      "Shoot first, ask questions later.",
      "When in doubt, raid 'em all.",
      "When criminals call you a pussy, don't worry, they're only right about 50% of the time.",
      "If you're worried about repercussions for your actions, provide the public with a different officer's badge number.",
      "No witnesses.",
    },
  },
}) do
  registerItem(book_id, book.name, 0.2, {
    {
      name = 'Use',
      callback = function(character)
        character.notify(book.lines[math.random(1, #book.lines)])
      end
    }
  }, false, {
    category = book.category
  })
end

local label_option = function(character, item_id)
  character.hideInventory(true)

  local label = character.prompt('Enter label', '', true, 30)

  if label == '' then
    label = nil
  end

  modifyItemMeta(character.source, item_id, 'storage_label', label)

  if not label then
    character.notify('Removed label')
  else
    character.notify('Labelled: ' .. label)
  end
end

registerItem('xm23_cassette', 'Casette Tape', 0.2, {
  {
    name = 'Label',
    callback = label_option,
  },
})

registerItem('xm23_floppy', 'Floppy Disk', 0.2, {
  {
    name = 'Use',
    callback = function(character)
      character.notify('What year do you think it is?')
    end,
  },
  {
    name = 'Label',
    callback = label_option,
  },
})

registerItem('xm23_cd', 'Mix CD', 0.2, {
  {
    name = 'Label',
    callback = label_option,
  },
})

-- Filler items with no use
registerItem('xm23_gc_a', '$1000 Ponsonbys Gift Card', 0.1)
registerItem('xm23_gc_b', '$10 Binco Gift Card', 0.1)
registerItem('xm23_gc_c', '$10 Laser Prints Gift Card', 0.1)
registerItem('xm23_gc_d', '$100 ProLaps Gift Card', 0.1)
registerItem('xm23_gc_e', '$150 Perseus Gift Card', 0.1)
registerItem('xm23_gc_f', '$500 Suburban Gift Card', 0.1)
registerItem('xm23_gc_g', '$69 Click Lovers Gift Card', 0.1)
registerItem('xm23_socks_a', '6-Pack of Socks', 0.5)
registerItem('xm23_socks_b', 'Cargo Socks', 0.5)
registerItem('xm23_ring_a', 'Claddagh Ring', 0.1)
registerItem('xm23_ring_b', 'Pink Diamond Ring', 0.1)
registerItem('xm23_ring_c', 'Purple Diamond Ring', 0.1)
registerItem('xm23_ring_d', 'Ruby Butterfly Ring', 0.1)
registerItem('xm23_ring_e', 'Sapphire Ring', 0.1)
registerItem('xm23_earring_a', 'Butterfly Earrings', 0.1)
registerItem('xm23_alligator', "Chubby Lil' Alligator", 0.5)
registerItem('xm23_kiwi', 'Golden Kiwi Bird', 0.5)
registerItem('xm23_earwax', 'Ear Wax Vacuum', 0.5)
registerItem('xm23_bf', 'Grow a boyfriend kit', 0.5)
registerItem('xm23_gf', 'Grow a girlfriend kit', 0.5)
registerItem('xm23_mold', 'Grow a mold farm kit', 0.5)
registerItem('xm23_laser', 'Laser Pointer', 0.2)
registerItem('xm23_matryoshka', 'Matryoshka Doll', 0.2)
registerItem('xm23_menorah', 'Menorah', 0.5)
registerItem('xm23_mopflop', 'Mop Flops', 0.5)
registerItem('xm23_troll', 'Norwegian Troll', 0.5)
registerItem('xm23_pens', 'Novelty Pen Set', 0.2)
registerItem('xm23_headphones', 'Red Wireless Headphones', 0.5)
registerItem('xm23_scrub_f', 'Scrub Mama', 0.2)
registerItem('xm23_scrub_m', 'Scrub Papa', 0.2)
registerItem('xm23_craft', 'Shitty Hand Made Craft', 0.2)
registerItem('xm23_shork', 'Shork Chain', 0.5)
registerItem('xm23_cactus', 'Singing Cactus', 0.5)
registerItem('xm23_phonebooth', 'Phone Booth', 0.5)
registerItem('xm23_funnel', 'Travel Urinal', 0.2)
registerItem('xm23_gaming', 'Vintage Gaming System', 0.5)
registerItem('xm23_earrings_a', 'Wine Mom Earrings', 0.2)
registerItem('xm23_stocking_a', 'Wine Stocking', 0.2)
registerItem('xm23_shoes_a', 'Wooden Shoes', 0.2)
registerItem('xm23_bracelet_a', 'BFF Bracelet Pearl', 0.2)
registerItem('xm23_bracelet_b', 'BFF Bracelet Obsidian', 0.2)
registerItem('xm23_orn_a', '"A" Ornament', 0.2)
registerItem('xm23_orn_b', '"B" Ornament', 0.2)
registerItem('xm23_orn_c', '"C" Ornament', 0.2)
registerItem('xm23_orn_d', '"D" Ornament', 0.2)
registerItem('xm23_orn_e', '"E" Ornament', 0.2)
registerItem('xm23_orn_f', '"F" Ornament', 0.2)
registerItem('xm23_orn_g', '"G" Ornament', 0.2)
registerItem('xm23_orn_h', '"H" Ornament', 0.2)
registerItem('xm23_orn_i', '"I" Ornament', 0.2)
registerItem('xm23_orn_j', '"J" Ornament', 0.2)
registerItem('xm23_orn_k', '"K" Ornament', 0.2)
registerItem('xm23_orn_l', '"L" Ornament', 0.2)
registerItem('xm23_orn_m', '"M" Ornament', 0.2)
registerItem('xm23_orn_n', '"N" Ornament', 0.2)
registerItem('xm23_orn_o', '"O" Ornament', 0.2)
registerItem('xm23_orn_p', '"P" Ornament', 0.2)
registerItem('xm23_orn_q', '"Q" Ornament', 0.2)
registerItem('xm23_orn_r', '"R" Ornament', 0.2)
registerItem('xm23_orn_s', '"S" Ornament', 0.2)
registerItem('xm23_orn_t', '"T" Ornament', 0.2)
registerItem('xm23_orn_u', '"U" Ornament', 0.2)
registerItem('xm23_orn_v', '"V" Ornament', 0.2)
registerItem('xm23_orn_w', '"W" Ornament', 0.2)
registerItem('xm23_orn_x', '"X" Ornament', 0.2)
registerItem('xm23_orn_y', '"Y" Ornament', 0.2)
registerItem('xm23_orn_z', '"Z" Ornament', 0.2)

--[[
local counts = {}

for _, cat in ipairs({ 'xmas2023_tier1', 'xmas2023_tier2'}) do
  if not counts[cat] then
    counts[cat] = 0
  end

  local its = GItems.getItemsByCategory(cat)

  for _, item_id in pairs(its) do
    counts[cat] = counts[cat] + 1
    print('[' .. cat .. '] ' .. GItems.getItemName(item_id))
  end
end

print_r(counts)
]]
