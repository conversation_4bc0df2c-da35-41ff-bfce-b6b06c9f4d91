beginRegisterCategory('general')

local folderbook_choices = {
    {
        name = 'Open',
        callback = function(character, item_id)

          local whitelisted_items = {'paper', 'm_document'}

          if not string.match(item_id, ':meta:') then
            character.take(item_id, 1, false, function(taken)
              if taken then
                local bag_id = math.random(1, 99999999)
                local chest_name = item_id .. bag_id
                local item_full_id = item_id .. ':meta:' .. bag_id

                character.give(item_full_id, 1, {}, false, false, function(given)
                  if given then
                    character.client('blrp_inventory:hide')

                    SetTimeout(250, function()
                        character.openChest(chest_name, 10.0, function() end, {
                          whitelisted_items = whitelisted_items
                        })
                    end)
                  end
                end)
              end
            end)
          else
            local meta_id = item_id:gsub(':meta:', '')
            local chest_name = meta_id

            character.client('blrp_inventory:hide')

            SetTimeout(250, function()
              character.openChest(chest_name, 10.0, function() end, {
                whitelisted_items = whitelisted_items
              })
            end)
          end
        end
      },
      {
        name = 'Label',
        callback = function(character, item_id)
          character.client('blrp_inventory:hide')

          SetTimeout(100, function()
            local label = character.prompt('Enter name for the folder', false, true)

            if not label or label == '' then
              character.notify('Invalid label')
              return
            end

            if not string.match(item_id, ':meta:') then
              local bag_id = math.random(1, 99999999)

              character.take(item_id, 1, false)
              character.give(item_id .. ':meta:' .. bag_id, 1, {
                storage_label = label
              }, false, false)
            else
              character.take(item_id, 1, false)
              character.give(item_id, 1, {
                storage_label = label
              }, false, false)
            end

            character.notify('You scribble the name on the folder')
          end)
        end
      }
    }


registerItem('book_selfhelp', 'Self Help Book', 1.0, {
    {
      name = 'Read',
      callback = function(character, item_id)
        local sayings = {
          "If it feels wrong, don't do it",
          "Don't be a people pleaser",
          "Never give up on your dreams",
          "Don't be afraid to say no",
          "Don't be afraid to say yes",
          "Stay away from drama and negativity",
          "Practice yoga",
          "Start a bucket list",
          "Go out to eat",
          "Read a book",
          "Start by doing what's necessary, then do what's possible; and suddenly you are doing the impossible",
          "Every situation in life is temporary",
          "For every minute you are angry you lose sixty seconds of happiness",
          "Every day may not be good, but there is something good in every day",
          "Once you start laughing you start healing",
          "Instead of worrying about what you cannot control, shift your energy to what you can create",
          "You can't go back and change the beginning, but you can start where you are and change the ending",
          "Make new friends",
          "Be more thankful",
          "Take frequent showers",
        }

        character.client('blrp_inventory:hide')
        character.notify('You turn to a random page and read the wisdom')

        SetTimeout(750, function()
          character.notify(sayings[math.random(1, #sayings)])
        end)
      end
    }
  })

registerItem('book_ghostridge', 'Ghost Ridge Book', 1.0, {
    {
      name = 'Read',
      callback = function(character, item_id)
        character.animate({
          { 'cellphone@', 'cellphone_text_read_base', 1 },
        }, true, true)

        tProps.addPlayerProp(character.source, { 'prop_novel_01', 6286, 0.15, 0.03, -0.065, 0.0, 180.0, 90.0 })
        character.client('blrp_inventory:hide')
        tQuests.runDialogue(character.source, {
          {
            { 'SELF', false, "You open the book and start to read, feeling the anticipation build." },
            { false, false, "In the sunbaked deserts, where the horizon stretched endlessly and the heat shimmered like a mirage, there roamed a band of outlaws like no other, The Ghost Ridge Siblings." },
            { false, false, "Together, they roamed the desert like a pack of wolves, striking swiftly and disappearing into the shadows before the law could catch their scent." },
            { false, false, "Their robberies were legendary. They held up stagecoaches with precision, looted banks with ease, and raided remote settlements with absolute authority." },
            { false, false, "As the years passed, word spread of the Ghost Ridge siblings, drawing the attention of lawmen and bounty hunters from every corner." },
            { false, false, "But the siblings were always one step ahead, disappearing into the desert like phantoms in the night." },
            { false, false, "Though every outlaw’s luck eventually runs out."},
            { false, false, "that day came in the form of a determined sheriff named Jack \"The Lawman\" Thompson"},
            { false, false, "Sheriff Thompson chased the siblings across the desert, tracking them through the scorching heat and the biting cold."},
            { false, false, "In a dramatic showdown beneath the blazing sun, the Ghost Ridge siblings met their match as bullets flew like rain."},
            { false, false, "One sibling remained. Nash “Iron heart” Ryder escaped into the dust storm. Leaving with the promise that the Ghost Ridge legacy would live on."},
            { 'SELF', false, "You finish the last page, close the book, and reflect on the saga of the Ghost Ridge Siblings, their legend burning brightly even in the quiet of your room."}
          }
        })
        character.client('core:client:wipeProps')
        character.client('vrp:client:stopAnimation', false)
      end
    }
  })

registerItem('book_yamrmask', 'The Curse of The Noshipa Yamar Mask Book', 1.0, {
    {
      name = 'Read',
      callback = function(character, item_id)
        character.animate({
          { 'cellphone@', 'cellphone_text_read_base', 1 },
        }, true, true)

        tProps.addPlayerProp(character.source, { 'prop_novel_01', 6286, 0.15, 0.03, -0.065, 0.0, 180.0, 90.0 })
        character.client('blrp_inventory:hide')
        tQuests.runDialogue(character.source, {
          {
            { 'SELF', false, "You open the book and start to read, bracing yourself for the journey ahead." },
            { false, false, "The year is 1896, Max Carter and his partner, Thomas Reed, arrived on a remote, fog-shrouded island, rumored to hold the cursed Cayo Noshipa Yamar Mask. The island's foreboding atmosphere hinted at dark secrets waiting to be uncovered." },
            { false, false, "Together, they battled through a nightmarish jungle, where carnivorous plants and eerie, shadowy creatures with glowing eyes seemed to close in on them." },
            { false, false, "Max and Thomas navigated treacherous, submerged caverns, contending with grotesque, serpentine monsters that lurked in the dark waters." },
            { false, false, "They discovered ancient ruins where malevolent hallucinations of twisted faces and ghostly whispers tormented their sanity." },
            { false, false, "Spectral guardians with hollow, anguished eyes attacked, their supernatural presence inflicting excruciating pain and fear." },
            { false, false, "Thomas betrayed Max, leading to a brutal ambush by shadowy figures that relentlessly hunted Max, leaving him injured and alone."},
            { false, false, "Max, now separated from Thomas, entered a nightmarish maze where shifting walls and disorienting whispers heightened his terror."},
            { false, false, "Max discovered a chamber where he performed a blood-soaked ritual, surrounded by grotesque, rotting figures clawed at him."},
            { false, false, "He faced horrifying entities that embodied the tortured souls of past seekers, each vision inflicted their agony and despair upon him."},
            { false, false, "Max put on the Cayo Noshipa Yamar Mask, and a dark force consumed him, stripping away his humanity. He become a soulless being, forever cursed, while Thomas's fate remained unknown."},
            { 'SELF', false, "You finish the last page and close the book, feeling the chill of the cursed mask lingering in your mind."}
          }
        })
        character.client('core:client:wipeProps')
        character.client('vrp:client:stopAnimation', false)
      end
    }
  })

registerItem('book_bloodink', "Blood-Stained Ink Book", 1.0, folderbook_choices)

registerItem('book_mrbones', 'Mr. Bones Goes Down Book', 1.0, {
  {
    name = 'Read',
    callback = function(character, item_id)
      character.animate({
        { 'cellphone@', 'cellphone_text_read_base', 1 },
      }, true, true)

      tProps.addPlayerProp(character.source, { 'prop_novel_01', 6286, 0.15, 0.03, -0.065, 0.0, 180.0, 90.0 })
      character.client('blrp_inventory:hide')
      tQuests.runDialogue(character.source, {
        {
          { 'SELF', false, 'You open "Mr. Bones Goes Down", a short story by Rory Bennet...' },
          { false, false, "The air was still, heavy with the scent of lavender, and filled with an impenetrable mist... She was leaning against a gravestone, her back arched in shock as it touched the cold, hard surface…" },
          { false, false, "Suddenly, she felt a rumbling from below. A pulse from a deep, dark place... The grass and soil began to rupture, exposing a skeletal hand… then another… then…" },
          { false, false, "A skull... sternum... ribcage, spine, and a… pelvis…" },
          { false, false, "The skeleton began to crawl toward her, a lustful gaze in his eye sockets. He raised his hand from the dirt toward her…" },
          { false, false, "With a gentle press of his boney index finger against the inside of each of her knees, he parted her legs as if pleasuring a woman too well is what sentenced him to death." },
          { false, false, "Looking down into the empty voids where a man’s eyes would usually be, she almost snapped back to reality… but… instead… lifted up her long woolen skirt to expose her wanting, desire filled body."},
          { false, false, "A knowing chuckle emerges from the skeleton, and he tells her “I’ll give you something worth living for”, and lowers himself to the ground… moonlight bouncing off his pearly white cranium."},
          { false, false, "She feels herself sink further back against the headstone, sliding her bare ass through the dirt and worms and bugs… gripping fistfuls of soil with one hand, and her other thumb laced through his left eye socket, holding onto his head like a bowling ball."},
          { false, false, "The skeleton’s exposed teeth chattered in anticipation as he neared her quivering mound, but suddenly, he froze. Time came almost to a stop, then he crawled backward, rising to his feet, grasping at his face…"},
          { false, false, "A low growl escaped his ribcage, which quickly turned to a whimper… he gazed longingly down at the woman, then dropped to his knees in anguish, crying to the sky… “I HAVE NO TONGUE!!!”"},
          { 'SELF', false, "You close the book, the provocative scenes echoing in your mind. You sit there, trying to figure out what you’re feeling—confusion, excitement, something else entirely—as the weight of the story lingers."}
        }
      })
      character.client('core:client:wipeProps')
      character.client('vrp:client:stopAnimation', false)
    end
  }
})

registerItem('book_likesnow', 'Falling Like Snow', 1.0, {
  {
    name = 'Read',
    callback = function(character, item_id)
      character.animate({
        { 'cellphone@', 'cellphone_text_read_base', 1 },
      }, true, true)

      tProps.addPlayerProp(character.source, { 'prop_novel_01', 6286, 0.15, 0.03, -0.065, 0.0, 180.0, 90.0 })
      character.client('blrp_inventory:hide')
      tQuests.runXmasDialogue(character.source, {
        {
          { 'SELF', false, 'You open "Falling Like Snow", a short story by Katherine White…' },
          { false, false, "On a crisp December afternoon, small, joyous laughter fills the air as schoolteachers usher children out of their yellowed buses towards the big city ice rink. Duke Caldwell, known for his meticulously swept-back hair and signature smile, shakily enters the rink and skates around the perimeter when… bzzt… Duke reaches into his pocket to retrieve the humming phone. Distracted now, his fingers tap hurriedly on the screen barely noticing the skater ahead when…" },
          { false, false, "WHAM! Duke collides with Clara Winters, a young woman whose warm laughter had previously echoed across the ice. Together they crash into the side railing, Duke flattening Clara against the wall. Duke smirks and leans toward Clara’s ear to whisper, “looks like I’m falling for you – literally.” Clara gracefully wiggles her way out from between Duke and the railing. Skating away, she casually remarks “Well, it’s a good thing the LSFD offers free medical care.” For the first time in years, Duke is at a loss for words, captivated by Clara’s quick wit and warm demeanor." },
          { false, false, "The next day, Duke heads to The Regent Bar and Residencies for Schöne Blume Toy Drive. As fate would have it, Clara is volunteering her time by sorting toys into different bins for cataloging. Duke approaches Clara, ice skates in hand, and smiles sheepishly as he hands them over for donation…" },
          { false, false, "“Don’t tell me you’re giving up already?” Clara asks with a pointed look on her face. Duke chuckles softly, “more like giving someone the gift of finding beauty in their life like these helped me.” A soft pink glow blooms across Clara’s cheeks and Duke is mesmerized by her beauty: the long braid of her auburn hair falling over her left shoulder, her hazel eyes warm and captivating." },
          { false, false, "Over the next week, the two run into each other around town… A holiday baking contest where Clara makes the best figgy pudding… the smell of cinnamon and nutmeg lingering in Duke’s brain for days… A snowball fight in the park… Duke captivated by the loose hair around Clara’s face, the strands catching the light like a sun’s kiss…" },
          { false, false, "One winter night, Duke drives out to Paleto to visit his grandparents at their small diner. He helps them decorate the diner, filling the dining area with tinsel and holly. Holiday tunes fill the air, matching the pace of the snow falling outside. As Duke’s grandparents decorate the tree, he hangs the mistletoe…"},
          { false, false, "After a night of full bellies warm from hot cocoa and hearts full of holiday cheer, Duke pushes through the door into the snowy parking lot. There, under the glow of the streetlamp, Clara twirls, catching snowflakes on her tongue. Duke watches, enchanted by her, his eyes following how her cheeks and nose turn rosy from the cold. Duke asks “so what exactly does a snowflake taste like?” Clara replies softly, “like a fleeting kiss from the sky, a cool sweetness delicate enough to suspend a memory.”"},
          { false, false, "On Christmas Eve, Los Santos holds its annual tree lighting ceremony in the heart of downtown. Legion Square is aglow with twinkling lights, carolers sing in harmony, and couples sit with hot cocoa as they wait for the tree to light up. Clara is there with her students, who excitedly point out Duke as he approaches."},
          { false, false, "“I want to give you this,” Duke says softly, handing Clara a small gift bag. Inside is a hand blown glass ornament shaped like a snowflake. Clara’s heart swells, “It’s perfect.” Duke looks at Clara, his voice soft. “You might just be a Christmas miracle, Clara Winters.”"},
          { false, false, "The countdown to the tree lighting begins, and as the crowd erupts in cheers, the massive Christmas tree comes to life, casting a magical glow. Clara lifting onto her tippy toes, places her arms around Duke’s neck, leaning in as their lips meet under the glow of the tree, the crowd fading into the background. For the first time in his life, Duke realizes he’s found something—someone—worth falling for."},
          { 'SELF', false, "You read the last words with a sense of warmth, as if the story had wrapped itself around you like a cozy holiday blanket. The small-town charm, the twinkle of Christmas lights, and the love that blossomed between two unlikely souls lingered like the scent of freshly baked cookies. As the snow fell softly outside their window, you couldn't help but believe that, sometimes, the magic of Christmas really does make dreams come true."}
        }
      })
      character.client('core:client:wipeProps')
      character.client('vrp:client:stopAnimation', false)
    end
  }
})

registerItem('book_mrbones_b', 'Mr. Bones Comes Alive', 1.0, {
  {
    name = 'Read',
    callback = function(character, item_id)
      character.animate({
        { 'cellphone@', 'cellphone_text_read_base', 1 },
      }, true, true)

      tProps.addPlayerProp(character.source, { 'prop_novel_01', 6286, 0.15, 0.03, -0.065, 0.0, 180.0, 90.0 })
      character.client('blrp_inventory:hide')
      tQuests.runDialogue(character.source, {
        {
          { 'SELF', false, 'You open the second book in the series, "Mr. Bones Comes Alive", a short story by Rory Bennet…' },
          { false, false, "As quickly as he could, the skeleton sprinted from the graveyard, his foot bones slamming against the pavement. Searching desperately for a place to hide, he slunk into a dark alley, crumpling into a heap of bones against a dumpster." },
          { false, false, "Suddenly, a door swung open with force. The skeleton froze in fear as a large, black platform boot stepped through the door frame… but relaxed as he admired the spindly, fishnet adorned legs filling the boots." },
          { false, false, "Further up her legs, black garters were pleasantly stretched around her healthy, flesh covered femurs. Her hips and waist suggested he was in for a treat were he ever so lucky as to also see it from the back." },
          { false, false, "Intently he watched as she fumbled through her mini-skirt pockets, her bracelets jingling as she searched. Some bracelets had locks like handcuffs, others looked as if they were just intricately tied knots from thin rope…" },
          { false, false, "She found what she was looking for. A lighter. She pulled a joint from behind her ear, and went to light it. The flame illuminated her face, and the skeleton’s jaw dropped open." },
          { false, false, "The way she held the joint between her long, bony fingers was intoxicating. Lipstick prints stained the joint where her mouth had just been, sending him into pleasant memories of the past…"},
          { false, false, "Having shifted uncomfortably to adjust a phantom limb, he accidentally bumped into the dumpster, bone against metal clanging. The woman yelped in surprise, dropping her lighter on the ground."},
          { false, false, "As she bent down to grab it, he couldn’t help but lean forward to catch a glimpse of her spilling from the pleather corset struggling to constrain her ample bosom…"},
          { false, false, "Caught in the act, the woman’s breath hitched at the sight of the skeleton. Fearing the worst, he turned his skull in shame… but then felt a hand against his mandible."},
          { false, false, "Turning his head to meet her eyes, he saw a devious curiosity he recognized all too well…"},
          { 'SELF', false, "You close the page, your mind swirling with the echoes of every word. What exactly was that? Desire, curiosity, something else? It doesn’t matter… what matters is that Mr. Bones has a way of making you wonder just how alive things can get."}
        }
      })
      character.client('core:client:wipeProps')
      character.client('vrp:client:stopAnimation', false)
    end
  }
})

registerItem('book_becomingher', 'Becoming Her', 1.0, {
  {
    name = 'Read',
    callback = function(character, item_id)
      character.animate({
        { 'cellphone@', 'cellphone_text_read_base', 1 },
      }, true, true)

      tProps.addPlayerProp(character.source, { 'prop_novel_01', 6286, 0.15, 0.03, -0.065, 0.0, 180.0, 90.0 })
      character.client('blrp_inventory:hide')
      tQuests.runDialogue(character.source, {
        {
          { 'SELF', false, 'You open “Becoming Her”. A psychological thriller by Katherine White' },
          { false, false, "Rain beats against the diner windows as I sip my coffee. It started 30 minutes ago and is only getting worse. Lucy, the only waitress, gives me a worried look. “Better get going, hun. Those back roads will wash out if you wait.” She knows I hate the highway. I tuck my chin and let my eyes drop, unsure what to do with being noticed. I leave a wad of cash on the table and head to the door." },
          { false, false, "Visibility is low. I drive slowly, hoping the roads hold. With the busted radio, I’m left alone with my thoughts. I start to wonder if I should’ve just slept in the car. Then: thump-thump-thump. A flat tire. I pull off under some trees, get out, and check the passenger side. I press my palms to my temples. Yep—flat. As I move to the trunk, headlights appear. Someone’s coming." },
          { false, false, "The car pulls over. A handsome young man steps out. The kind of guy who looks like he fixes fences by day and breaks hearts by night— sun-warmed skin and a smile that makes you forget what you were mad about. “Looks like you’ve got a flat,” he calls. “I can help.” He grabs my spare and gets to work. His clothes are pristine—a soft tee and jeans—soon become coated in mud. “All done,” he says, flashing a smile." },
          { false, false, "He asks if I have far to go. I nod, unsure if the spare will make it. He notices I’m shivering and soaked head to toe. My smile falters as a breeze chills me to my bones. “Why don’t you come back to my cabin? My wife can make you tea.” His eyes are soft, gentle even. Every instinct screams no, but I’m wet, tired, and he mentioned a wife. Surely, it’s safe? I agree to follow him." },
          { false, false, "The cabin looks ancient, with ivy creeping up the side and flowers blooming in front. A stone path leads to a shed off to the side. I stand in awe as I take in the scenery. The rain has stopped, but it’s colder now. He opens the door and beckons me in. There’s a fire, flowers on the table, a sandwich left out. It feels... lived in. The air is thick with cedar and calm, and for the briefest second, I let myself feel safe." },
          { false, false, "My fingers brush the back of a plaid couch as I inch towards the hearth. Then I hear it: the click of a lock. The hairs on my arm stand up as I turn to look at the door. No knob, no keyhole. Just a flat plate and a deadbolt. “Excuse me,” I ask, “what’s your wife’s name?” No answer. No photos. My stomach knots with dread—the silence sucking any remaining air out of my lungs. This is where I am going to die."},
          { false, false, "He places a tray—soup, bread, napkin—on the table. I flinch, pulling myself into a ball on the couch. “I made lentil,” he says, not meeting my eyes. “It should warm you up.” At this point, I’ve lost track of the days. I want to hate him. I should. But I nod and thank him. At first, I screamed and bled from pounding the door. “You’ll tire yourself out,” he said. And I did."},
          { false, false, "Days pass. Maybe weeks. My body aches, exhaustion breeding inside my bones. He reads books aloud. I ask about the flowers out front. “My wife planted them,” he says. His presence is becoming more familiar. I start caring, laughing at his jokes. He smiles—a soft, real smile. I can feel myself smiling too. I notice his voice, his hands, the warmth in his presence. I don’t feel trapped anymore. I feel seen."},
          { false, false, "I start memorizing his routine. I note the way he likes his coffee, his favorite breakfast, even how he ties his shoelaces—classic bunny ear, bunny ear style. I leave notes in his jacket pockets, little love letters only he will find. I live for the way his eyes light up when he reads them. I’m not just falling for him—I’m folding myself into his every breath."},
          { false, false, "One morning, I wake up alone. The fire’s out. No one is home. I pull his flannel tighter, make coffee. Then I see it: the key. Sitting on the kitchen table. The key to the deadbolt. After everything—fear, comfort, maybe even love—it’s here. Waiting. Now the choice is mine…"},
          { 'SELF', false, "You close the book, unsure what you’re feeling. Rain, silence, and something like love linger in your thoughts. The cabin. The key. His voice. Was it safety or surrender? The story doesn’t offer answers—only the lingering question: When you lose yourself… who do you become?"}
        }
      })
      character.client('core:client:wipeProps')
      character.client('vrp:client:stopAnimation', false)
    end
  }
})

registerItem('book_doublecrossed', 'Double Crossed', 1.0, {
  {
    name = 'Read',
    callback = function(character, item_id)
      character.animate({
        { 'cellphone@', 'cellphone_text_read_base', 1 },
      }, true, true)

      tProps.addPlayerProp(character.source, { 'prop_novel_01', 6286, 0.15, 0.03, -0.065, 0.0, 180.0, 90.0 })
      character.client('blrp_inventory:hide')
      tQuests.runDialogue(character.source, {
        {
          { 'SELF', false, 'You open Shadows of the Syndicate: Double Crossed. A gritty crime thriller by Maelynn Kowaldi-Brooks. The pages smell like whiskey, rain, and bad decisions.' },
          { false, false, "It was a rainy night—the kind that made the city look like it was crying. I came home soaked, tired, and ready to drink something regrettable. That’s when I saw it: a letter, taped to my door like a bad omen. No return address. Just a warning—a betrayal brewing deep within the Falcone family. A conspiracy big enough to crack the spine of the city’s criminal underworld. One thing was clear: this wasn’t gonna end with a simple arrest report." },
          { false, false, "The first tremor hit the city when a 16th-century Italian pocket watch vanished from the Liberty City Museum. It had ties to the Falcone family—and it wasn’t just some dusty antique. It was on loan from the Vatican, which meant someone upstairs was getting nervous. It wasn’t just a heist. It was a message. And like any good message in this town, it was wrapped in blood, secrets, and a whole lotta bad news." },
          { false, false, "I tried to track down Don Vincent Falcone—ghosted me like an ex on parole. No calls, no sightings. So I hit the streets the old-fashioned way: cigars, backrooms, and deals that smelled like betrayal. Every whisper led to one name: The Silent Hand. No face, no fingerprints—just rumors that the pocket watch unlocked something ancient. Something that made the Falcones what they were. And someone wanted that power for themselves." },
          { false, false, "The trail took me to a Catholic church with more secrets than sermons. A nun, stabbed to death behind the soup kitchen. Witness was a homeless woman, said she saw a priest arguing with a tall man—scar down his face, voice like gravel. My gut said Don Vincent. She also saw the nun listening in the courtyard shadows. Two hours later, the nun was dead, and the priest pled guilty without a word. Something stank, and it wasn’t the incense." },
          { false, false, "With no leads left, I did what no cop wants to admit—I started reading. Dove into old archives like a librarian with a vengeance. Turns out the Falcones had ties to Pope Alexander VI. Yeah, that Pope. The Borgia one. Crime ran in the bloodline. When the Borgias fell, some slipped through the cracks and fled west. New world, new name: Falcone. The sins of the fathers don’t die. They get meaner—and better at hiding." },
          { false, false, "Three months. Twelve interviews. Seven people vanished like smoke rings in a hurricane. Just when the whole thing felt colder than my ex-wife’s voicemail, a hit landed. Tony. Don Vincent’s cousin. Greasy, loud, and not half as clever as he thought. I bought him a drink, threatened to ruin his life, and he folded faster than a cheap suit. He spilled everything—mansion bunkers, secret tunnels, and a boss who wasn’t just hiding. He was planning."},
          { false, false, "I dragged Tony back to the station. He caught sight of my partner Marco—and went pale like he saw a ghost in uniform. Said it was nothing, but his poker face had holes. I pressed Marco later, and he got defensive. “What, you gonna interrogate me now?” he snapped. That’s when I knew I had a leak. I’d been playing checkers in a chess game—and someone had been moving pieces behind my back. I kept my eyes on Marco after that."},
          { false, false, "With Tony’s intel, we rolled out a SWAT raid on the mansion. Big lights, big guns, and a lotta angry yelling. But no Don Vincent. Just empty glasses and cold ashtrays. I cornered Tony again, this time without the charm. Told him exactly what prison was gonna look like—and who he’d be sharing it with. Tony squealed like a sax solo in a cheap club. The rat wasn’t just in the walls. He was wearing a badge. Marco"},
          { false, false, "I dragged Marco into the interrogation room—just him, me, and a truth that smelled like gasoline. He cracked after two threats and a long stare. Said Don Vincent Falcone was holed up near the old airstrip, getting ready to board a private jet to Italy. A one-way ticket out of the mess he helped make. We hit the strip hard. Caught the Don mid-escape, calm as ever, like he wanted to be found."},
          { false, false, "Vincent didn’t blink. Said the pocket watch was a key—to a map. And wouldn’t you know it, the old fox had it tucked in his coat. He handed it over like a handshake deal with the devil. Told me Lorenzo Accardi—the Silent Hand—was Borgia blood, hellbent on vengeance. If the Hand stayed standing, the Falcones would fall. This wasn’t surrender. It was a chess move. Don Vincent knew the Hand had to be stopped for the Falcones to survive."},
          { false, false, "Marco’s gig wasn’t over. I wired him up like a Christmas tree and tossed him back to the wolves. He slid into the act like butter on a hot skillet—cool, smooth, sold the performance. Handed over the map to The Silent Hand without flinching. The Hand shrugged him off, none the wiser. That was our cue. We tailed him through a maze of tunnels buried since the quake of ’82. He thought the secrets were his—until we caught him red-handed, map and pocket watch at the vault."},
          { false, false, "The vault was a museum of crime—paintings, sculptures, first editions, all gathering dust in the dark. But the real gold was inside a drawer of olivewood: five little black books dating back to the 1860s. Every page read like a death sentence—names, bribes, murders. Not just the Falcones. Politicians, CEOs, judges. The rot was deeper than we thought. Over the next few months, the arrests came like clockwork—and no one was safe."},
          { false, false, "The case was closed, but not quiet. Rossi nearly forgot how it all started—a letter on a rainy night, taped like a warning. He never found out who left it, and maybe that was the point. Don Vincent’s cooperation hadn’t been weakness. It was a chess move. Take down The Silent Hand, and clear the board for Falcone power to rise again. The city changed, but it didn’t clean up. Just got smarter. And Rossi? He stayed watching."},
          { false, false, "As for the Falcones, the old lion handed the reins to his son—Don Vincent Falcone II. Young, hungry, and polished like a funeral suit. The old man knew the game: survive by adapting. With The Silent Hand gone and rivals behind bars, the Falcone name rose again, cleaner on the surface, sharper underneath. The city was still a riddle wrapped in a trench coat—but now, Rossi had read the first chapter. And he wasn’t putting the book down just yet."},
          { 'SELF', false, "You close the book. Rossi got his answers—or did he? Something in you stirs, unsettled. Maybe it’s the silence. Maybe it’s the secrets. Or maybe it’s knowing the shadows never really leave."}
        }
      })
      character.client('core:client:wipeProps')
      character.client('vrp:client:stopAnimation', false)
    end
  }
})

registerItem('book_nightfallrising', 'Nightfall Rising', 1.0, {
  {
    name = 'Read',
    callback = function(character, item_id)
      character.animate({
        { 'cellphone@', 'cellphone_text_read_base', 1 },
      }, true, true)

      tProps.addPlayerProp(character.source, { 'prop_novel_01', 6286, 0.15, 0.03, -0.065, 0.0, 180.0, 90.0 })
      character.client('blrp_inventory:hide')
      tQuests.runDialogue(character.source, {
        {
          { 'SELF', false, 'You open Shadows of the Syndicate: Nightfall Rising. A cold-blooded spy thriller by Maelynn Kowaldi-Brooks. The air crackles with secrets, and every page bleeds with vengeance.' },
          { false, false, "Nightfall wasn’t just a name—it was a whisper behind assassinations, an echo in European conspiracies. Founded in the 1940s, they left no fingerprints, no trace. Now, the shadows of Europe had set their sights on San Andreas. And at the helm? Lenna Darkbloom—Russian, ruthless, cold as vodka in January. Her family breathed Nightfall since Stalin wore a mustache. But was she here for power… or revenge?" },
          { false, false, "San Andreas had mobs. Dirty money, dirtier hands. But Lenna? She made blood run like jazz on vinyl—smooth, dark, old-school. With a wink and a wallet, she had crime lords eating from her gloved hand. But her first move wasn’t a handshake—it was a bullet. A judge, ex-CIA, Cold War vet, and Supreme Court nominee. He held Nightfall’s darkest secrets. He had to go. Quietly." },
          { false, false, "Agent Caspian lit a cigarette on courthouse steps. Gray suit, smirk, silenced Walther PPK. Judge Whitaker sipped espresso, clueless. Caspian timed the shot between sirens. One to the heart, another to the past. Whitaker slumped, dead before sugar hit his lips. Nightfall had moved—and no one noticed. Lenna smiled from her bulletproof limo." },
          { false, false, "While Caspian played orchestra with a pistol, Agent Yelena slipped into the judge’s home. She moved like smoke—quiet, deadly, bad for your health. His private vault held files—FIB, CIA, Vatican archives—Cold War ghosts and Nightfall’s bloody fingerprints. She left behind only a cracked bourbon glass and an open safe. By dawn, the judge was dead, and his secrets stolen." },
          { false, false, "With the files in hand, Nightfall wasn’t just a ghost—it was a phantom kingmaker. Blackmail, leverage, secrets... they could topple governments in stilettos and silk gloves. But Lenna? She didn’t come for politics—not yet. Something personal simmered beneath her icy smile—an old wound in a tailored suit. Before rewriting the world order, she planned to rewrite one man’s obituary." },
          { false, false, "He was a ghost too—Maddox. Ex-MI6. Silver fox, steel nerves, secrets soaked in blood. In ’95, he took out Nikolai Darkbloom, Lenna’s brother, during a Prague op. She was 10 then. Now? She was the nightmare he never saw coming. Leading a covert San Andreas task force—untouchable, feared. She slipped into his life’s shadows like perfume in a coat collar—subtle, intoxicating, lethal."},
          { false, false, "She became Elena Morozova, a “consultant” with charm, a Russian accent soft as sin, eyes that knew too much. At an embassy gala, he poured her champagne. She poured mystery. Weeks passed, and she became the whisper he couldn’t ignore. She laughed at his jokes, let him think he led. But every smile hid a loaded gun."},
          { false, false, "Maddox thought he was chasing a new thrill. But he was dancing on a tripwire. Dinners turned to nights, nights into secrets. He whispered things only a haunted man would. She held him like she cared—but her heart was steel wrapped in silk. He never asked where she came from. He should have. Ghosts don’t expect revenge in red lipstick."},
          { false, false, "They escaped to a Vinewood Hills chateau—his paradise, her stage for justice. She kissed him under moonlight, rehearsing the ending. Not yet. The kill had to be perfect. Not just death, but poetry. She mixed the toxin herself—colorless, tasteless, a lover’s final gift. Just one more night. One last kiss."},
          { false, false, "He poured wine. She poured fate. They danced, slow, close. Lenna laughed—soft, aching, real enough to fool them both. Then the kiss. Sweet, slow, final. His breath caught. Pulse stumbled. Fingers trembled. She whispered in Russian, low and intimate: “Ty pomnish’ Praghu, devyanosto pyatyy?” He froze. A name unspoken lingered. Poison bloomed."},
          { false, false, "Maddox blinked. The words hit like a bullet—“Prague, ’95.” Memories crashed in. He pulled back, eyes searching hers. “Wait… Nikolai?” he gasped. She smiled—cold, sharp, no warmth. “You killed my brother, darling,” she purred, venom dripping. His legs gave out. The past was alive; this wasn’t love. This was cold-blooded revenge."},
          { false, false, "She stepped over him, heels clicking like death’s last beat. No panic. No regret. Just icy satisfaction. Maddox lay crumpled, breath shallow, eyes locked on her—the ghost from his past. Voice sharp as broken glass: “Cold enough for you, agent?” Then she left a silence louder than any gunshot. Nightfall had its crown. Lenna had her justice—poison and poetry."},
          { false, false, "The papers said natural causes—a quiet end. But somewhere in the city’s underbelly, whispers traced night air like perfume. Nightfall had arrived. And at its helm? A woman with blood on her hands and ice in her veins. Lenna never looked back. Revenge wasn’t a mission—it was art. And this? Her masterpiece. Now it was back to the shadows of business."},
          { false, false, "San Andreas was just the opening act—a prelude in a symphony of shadows. It was there Lenna settled her personal score. Now, the real business began. Liberty City… the East Coast’s dark heart, where power screamed instead of whispered. Nightfall’s reach would grow, ready to slip deep into the veins of America. Revenge was done; it was time to weave Nightfall into the shadows of Americona."},
          { 'SELF', false, "You close the book. The ending sits heavy, like perfume in still air. Her kiss, his eyes, the truth too late. You’re not sure what she took more—his life, or something deeper. Liberty City looms next—a chessboard of power, shadows, and ghosts. Could Lenna’s path cross with Rossi’s sharp eye? Two shadows dancing in the dark, each hunting secrets that could ignite the next war. The game’s just begun... and the night is far from over."}
        }
      })
      character.client('core:client:wipeProps')
      character.client('vrp:client:stopAnimation', false)
    end
  }
})

endRegisterCategory()