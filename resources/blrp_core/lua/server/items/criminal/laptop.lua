local laptop_targets = {}

function registerLaptopTarget(target_type, coords, distance, display_name, address, lifecycle)
  table.insert(laptop_targets, {
    type = target_type,
    coords = coords,
    distance = distance,
    display_name = display_name,
    address = address,
    lifecycle = lifecycle or {},
  })
end

beginRegisterCategory('criminal_laptop')

local function imprintLaptopMeta(character, item_id)
  local contents = core.chest(item_id).fetchContents()
  local usb_count = 0

  local existing_meta = character.hasGetItemMeta(item_id) or {}

  -- Wipe existing meta
  modifyItemMeta(character.source, item_id, false, false)

  local _weight = 0

  -- Add USBs present to meta
  for contents_item_id, contents_item_data in pairs(contents) do
    local definition, contents_id_base = GItems.getItemDefinition(contents_item_id)
    local contents_item_meta = contents_item_data.meta or {}
    local contents_item_weight = GItems.getItemWeight(contents_id_base, contents_item_meta)

    if definition then
      _weight = _weight + contents_item_weight

      local should_add = true

      if contents_id_base == 'usb_pink' and contents_item_meta.dur_cur <= 0 then
        should_add = false
      end

      if should_add then
        modifyItemMeta(character.source, item_id, contents_id_base, true)
      end
    end
  end

  -- Set dynamic weight
  if _weight > 0 then
    modifyItemMeta(character.source, item_id, '_w', _weight)
  end

  -- Set durability
  modifyItemMeta(character.source, item_id, 'dur_cur', existing_meta.dur_cur)
  modifyItemMeta(character.source, item_id, 'dur_start', existing_meta.dur_start)
end

local criminal_laptop_options = {
  [1] = {
    name = 'Modify Hardware',
    callback = function(character, item_id, item_id_base)
      character.hideInventory()

      if item_id == item_id_base then
        character.notifyError('You fumble with the laptop and fail to modify it. Try again.')
        return
      end

      SetTimeout(250, function()
        character.openChest(item_id, 0.5, function()
          imprintLaptopMeta(character, item_id)
        end, {
          max_slots = 3,
          whitelisted_items = exports.blrp_core:GetItemsByCategory('criminal_usbs')
        })
      end)
    end,
  },

  -- Brute force option (green USB)
  [2] = {
    name = 'Brute Force',
    callback = function(character, item_id, item_id_base, meta)
      local definition = GItems.getItemDefinition(item_id)
      local coords = character.getCoordinates()

      local targets = {}

      -- Build device list
      for k, v in pairs(laptop_targets) do
        if v.type == 'usb_green' and #(coords - v.coords) <= v.distance then
          table.insert(targets, v.display_name .. ' (' .. v.address .. ')')
        end
      end

      if #targets == 0 then
        character.notify('No devices found nearby')
        return
      end

      character.hideInventory(true)
      character.client('blrp_inventory:client:ForceDisarm', 'bl core laptop @L102')

      local dur_cur = meta and tonumber(meta.dur_cur) or 0

      if dur_cur < 1 then
        character.notify('Insufficient charge in laptop')
        return
      end

      local response = tUi.triggerFormWait(character.source, {
        {
          header = 'Brute Force Device',
          fields = {
            {
              id = 'keylength',
              txt = 'Key Length',
            },
            {
              id = 'keyspace',
              txt = 'Keyspace',
            },
            {
              id = 'target',
              txt = 'Nearby Devices',
              grid_column_start = 'span 3',
              options = targets,
            },
          },
        },
      })

      if not response then
        return
      end

      if not response.keylength or response.keylength == '' then
        character.notify('Invalid key length')
        return
      end

      if not response.keyspace or response.keyspace == '' then
        character.notify('Invalid keyspace')
        return
      end

      local target = string.match(response.target, "%((.+)%)")

      for _, v in pairs(laptop_targets) do
        if v.type == 'usb_green' and v.address == target then
          target = v
          break
        end
      end

      if type(target) ~= 'table' then
        character.notify('Error targeting device')
        return
      end

      -- Run external check function to see if this thing can be done
      -- If not, notify the player why
      if target.lifecycle.check then
        local check, check_message = target.lifecycle.check(character)

        if not check then
          if check_message then
            character.notifyError('[Do not read this message out loud] ' .. check_message, 15000)
          end

          return
        end
      end

      local keylength = tonumber(response.keylength)

      if keylength <= 0 then
        character.notify('Invalid key length')
        return
      end

      local keyspace = {}
      local keyspace_used = {}

      for i = 1, #response.keyspace do
        local char = string.sub(response.keyspace, i, i)

        if not keyspace_used[char] then
          table.insert(keyspace, char)
          keyspace_used[char] = true

          -- Handle alternating caps for ascii letters
          -- If user provides keyspace of abc123, consider it to be
          -- abcABC123
          local byte = string.byte(char, 1, 2)
          local other = nil

          -- Lowercase ascii letter, do upper too
          if byte >= 97 and byte <= 172 then
            other = string.char(byte - 32)
          end

          -- Uppercase ascii letter, do lower too
          if byte >= 65 and byte <= 90 then
            other = string.char(byte + 32)
          end

          if other then
            table.insert(keyspace, other)
            keyspace_used[other] = true
          end
        end
      end

      -- Compute permutations
      local permutations = (#keyspace) ^ keylength

      -- Figure out when (RNG) the correct code will be hit
      -- If the keyspace doesn't contain all the chars for the correct code, compute that too
      local complete_iteration = math.random(math.floor(permutations * (math.random(70, 80) / 100)), permutations)
      local correct_code = tostring(target.lifecycle.getter())

      for i = 1, #correct_code do
        if not keyspace_used[string.sub(correct_code, i, i)] then
          complete_iteration = permutations
          correct_code = false
          break
        end
      end

      modifyItemMeta(character.source, item_id, 'dur_cur', dur_cur - 1)

      -- Start laptop scene
      local scene_status = tCore.laptopSceneStart(character.source, {
        vector4(-629.3906, -230.4335, 38.38219 + 0.9, 28.702),
        {
          coords = vector3(-627.787, -230.834, 38.057),
          rot = vector3(0.0, 0.0, 105.379),
        },
        {
          coords = vector3(-627.787, -230.834, 38.057),
          rot = vector3(-15.0, 0.0, 101.379),
        },
        definition.texture_variation
      })

      -- Return if laptop scene cancelled up to now
      if not scene_status then
        return
      end

      -- Run progress bar, dynamic duration based on # of permutations provided
      -- To prevent just running all the permutations of a code (5 digit code, 10 digits = 100,000 permutations)
      -- cap the time at 120 seconds in the internals of mythic progress
      local duration = math.ceil((permutations / 1000) * 8)

      local progress = character.progressPromise('Brute forcing', duration, {
        controlDisables = {
          disableMovement = true,
          disableCarMovement = true,
          disableMouse = false,
          disableCombat = true,
        },
        extra = {
          iterations = permutations,
          complete_iteration = complete_iteration,
          label_format = 'Brute forcing %s / %s',
          max_time = (120 * 1000),
        }
      })

      -- Soft end the laptop scene
      tCore.laptopSceneEnd(character.source)

      -- Progress bar cancelled, return
      if not progress then
        return
      end

      -- Check if character still has laptop and laptop still has USB
      -- Take USB
      if not character.hasItemQuantity(item_id, 1) or not core.chest(item_id).take('usb_green', 1, true) then
        character.notifyError('Software media removed from laptop')
        return
      end

      -- The keyspace didn't contain all chars for the correct code, return
      if not correct_code then
        character.notifyError('Software unable to brute force code in provided keyspace')
        return
      end

      imprintLaptopMeta(character, item_id)
      character.notify('Code brute forced: ' .. tostring(target.lifecycle.getter()) ..'. USB was destroyed during use')

      -- Callback success
      if target.lifecycle.done then
        target.lifecycle.done(character)
      end
    end,
    filter = function(character, item_id, meta)
      return meta and meta.usb_green
    end,
  },
  -- /Brute force option (green USB)
  -- Breach firewall option (aqua USB)
  [3] = {
    name = 'Bypass Firewall',
    callback = function(character, item_id, item_id_base, meta)
      local coords = character.getCoordinates()

      local target_distance = nil
      local target = nil

      for k, v in pairs(laptop_targets) do
        if v.type == 'usb_aqua' then
          local distance = #(coords - v.coords)

          if distance <= v.distance and (not target_distance or distance < target_distance) then
            target_distance = distance
            target = v
          end
        end
      end

      if not target then
        character.notify('No devices found nearby')
        return
      end

      character.hideInventory(true)
      character.client('blrp_inventory:client:ForceDisarm', 'bl core laptop serv @L332')

      local dur_cur = meta and tonumber(meta.dur_cur) or 0

      if dur_cur < 1 then
        character.notify('Insufficient charge in laptop')
        return
      end

      -- Run external check function to see if this thing can be done
      -- If not, notify the player why
      if target.lifecycle.check then
        local check, check_message = target.lifecycle.check(character)

        if not check then
          if check_message then
            character.notifyError('[Do not read this message out loud] ' .. check_message, 15000)
          end

          return
        end
      end

      modifyItemMeta(character.source, item_id, 'dur_cur', dur_cur - 1)

      local success, consume_usb = target.lifecycle.hack(character)

      -- Hack failed, return
      if not success then
        if consume_usb then
          core.chest(item_id).take('usb_aqua', 1, true)
          imprintLaptopMeta(character, item_id)
          character.notifyError('Your USB fried while trying to gain access')
        end

        return
      end

      -- Check if character still has laptop and laptop still has USB
      -- Take USB
      if not character.hasItemQuantity(item_id, 1) or not core.chest(item_id).take('usb_aqua', 1, true) then
        character.notifyError('Software media removed from laptop')
        return
      end

      imprintLaptopMeta(character, item_id)

      -- Callback success
      if target.lifecycle.done then
        target.lifecycle.done(character)
      end
    end,
    filter = function(character, item_id, meta)
      return meta and meta.usb_aqua
    end,
  },
  -- /Breach firewall option (aqua USB)
  -- Capture stream option (pink USB)
  [4] = {
    name = 'Capture Stream',
    callback = function(character, item_id, item_id_base, meta)
      local coords = character.getCoordinates()

      local target_zone = nil
      local target = nil

      for k, v in pairs(laptop_targets) do
        if v.type == 'usb_pink' then
          local zone_name = v.coords

          if tZones.isInsideMatchedZone(character.source, { zone_name, true }) then
            target_zone = zone_name
            target = v
          end
        end
      end

      local chest = Chest:new(item_id)

      local usb_data, usb_item_id = chest:getItems():find(function(chest_item_data, chest_item_id)
        return (
          string.match(chest_item_id, 'usb_pink:meta:') and
          chest_item_data.meta and
          chest_item_data.meta.dur_cur and
          chest_item_data.meta.dur_cur >= 1
        )
      end)

      if not usb_data then
        return
      end

      if not target_zone or not target then
        character.notify('No devices found nearby')
        return
      end

      character.hideInventory(true)
      character.client('blrp_inventory:client:ForceDisarm', 'bl core laptop @L430')

      local dur_cur = meta and tonumber(meta.dur_cur) or 0

      if dur_cur < 0.25 then
        character.notify('Insufficient charge in laptop')
        return
      end

      local box_model, box_coords = tCore.getClosestModelOfType(character.source, { 1.5, {
        `tr_prop_tr_elecbox_01a`,
      }})

      if not box_model then
        character.notify('No devices found nearby')
        return
      end

      -- Pre-check, if defined
      if target.lifecycle.check then
        local check, check_message = target.lifecycle.check(character, box_model, box_coords)

        if not check then
          if check_message then
            character.notifyError('[Do not read this message out loud] ' .. check_message, 15000)
          end

          return
        end
      end

      modifyItemMeta(character.source, item_id, 'dur_cur', dur_cur - 0.25)

      local success, degrade_usb = target.lifecycle.hack(character, box_model, box_coords)

      if degrade_usb then
        local degraded, usb_new_dur = chest:decrementItemDurability(usb_item_id, 1)

        if not degraded then
          character.notifyError('Software media removed from laptop')
          return
        end

        if usb_new_dur == 0 then
          character.notify('Your USB degraded during use')
          chest:take(usb_item_id, 1)
        end
      end

      imprintLaptopMeta(character, item_id)

      -- Hack failed, return
      if not success then
        return
      end

      -- Check if character still has laptop
      if not character.hasItemQuantity(item_id, 1) then
        character.notifyError('Software media removed from laptop')
        return
      end

      -- Callback success
      if target.lifecycle.done then
        target.lifecycle.done(character, item_id, box_model, box_coords)
      end
    end,
    filter = function(character, item_id, meta)
      return meta and meta.usb_pink
    end,
  }
  -- /Capture stream option (pink USB)
}

registerItem('laptop_green', 'Laptop', 0.5, criminal_laptop_options, false, {
  unstackable = true,
  texture_variation = 0, -- Green
  item_durability = {
    dur_cur = 14,
    dur_initial = 14,
  },
})

registerItem('laptop_blue', 'Laptop', 0.5, criminal_laptop_options, false, {
  unstackable = true,
  texture_variation = 1, -- Blue
  item_durability = {
    dur_cur = 14,
    dur_initial = 14,
  },
})

registerItem('laptop_violet', 'Laptop', 0.5, criminal_laptop_options, false, {
  unstackable = true,
  texture_variation = 2, -- Violet
  item_durability = {
    dur_cur = 14,
    dur_initial = 14,
  },
})

registerItem('laptop_yellow', 'Laptop', 0.5, criminal_laptop_options, false, {
  unstackable = true,
  texture_variation = 3, -- Yellow
  item_durability = {
    dur_cur = 14,
    dur_initial = 14,
  },
})

registerItem('laptop_black', 'Laptop', 0.5, criminal_laptop_options, false, {
  unstackable = true,
  texture_variation = 4, -- Black
  item_durability = {
    dur_cur = 14,
    dur_initial = 14,
  },
})

registerItem('laptop_red', 'Laptop', 0.5, criminal_laptop_options, false, {
  unstackable = true,
  texture_variation = 5, -- Red
  item_durability = {
    dur_cur = 14,
    dur_initial = 14,
  },
})

beginRegisterCategory('criminal_usbs')

registerItem('usb_pkg', 'Facade USB Package', 0.2, {
  {
    name = 'Open',
    callback = function(character, item_id)
      if
        not character.progressPromise('Opening Package', 3, {
          animation = {
            animDict = 'missheistdockssetup1clipboard@idle_a',
            anim = 'idle_a',
            flags = 49,
          },
        }) or
        not character.take(item_id, 1)
      then
        return
      end

      character.give('usb_black', 1)
    end
  }
})

-- Base USB item - unprogrammed
registerItem('usb_black', 'Black USB', 0.1)

-- Brute Force USB - Replaces hacking phone
registerItem('usb_green', 'Green USB', 0.1, false, {
  ['usb_black'] = 1,
  ['aluminum'] = 4,
  ['gadget'] = 2,
  ['normal_phone'] = 2,
  ['cash'] = 4500,
}, {
  unstackable = true
})

-- Firewall breach USB - Replaces Vangelico programmed USB
registerItem('usb_aqua', 'Aqua USB', 0.1, false, {
  ['aluminum'] = 4,
  ['gadget'] = 2,
  ['usb_black'] = 1,
  ['cash'] = 6500,
}, {
  unstackable = true
})

-- Stream capture USB - used on electrical boxes at Bobcat
registerItem('usb_pink', 'Pink USB', 0.1, false, {
  ['aluminum'] = 35,
  ['gadget'] = 20,
  ['normal_phone'] = 20,
  ['cash'] = 4500,
}, {
  unstackable = true,
  item_durability = {
    dur_cur = 10,
    dur_initial = 10,
  },
})

-- USBs yet to be implemented
-- registerItem('usb_blue', 'Blue USB', 0.1)
-- registerItem('usb_violet', 'Violet USB', 0.1)
-- registerItem('usb_yellow', 'Yellow USB', 0.1)
-- registerItem('usb_orange', 'Orange USB', 0.1)
-- registerItem('usb_red', 'Red USB', 0.1)

registerItem('usb_magstripe', 'Magstripe reader/writer', 0.2)
