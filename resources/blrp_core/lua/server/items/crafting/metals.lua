beginRegisterCategory('metal_raw')

registerItem('raw_aluminum', 'Raw Aluminum', 0.5, false, false, {
  smelts_into = 'aluminum',
  power_consumption = 0.7
})

registerItem('raw_steel', 'Raw Steel', 0.5, false, false, {
  smelts_into = 'steel',
  power_consumption = 1.59,
})

registerItem('raw_iron', 'Raw Iron', 0.5, false, false, {
  smelts_into = 'iron',
  power_consumption = 1.28,
})

registerItem('raw_titanium', 'Raw Titanium', 0.5, false, false, {
  smelts_into = 'titanium',
  power_consumption = 1.77,
})

registerItem('raw_platinum', 'Raw Platinum', 0.5, false, false, {
  smelts_into = 'platinum',
  power_consumption = 1.88,
})

registerItem('gold_ore', 'Gold Nugget', 0.5, false, false, {
  smelts_into = 'gold_ingot',
  power_consumption = 1.77,
})

registerItem('craft_plank_wood', 'Wood Planks', 0.5, false, false, {
  smelts_into = 'craft_charcoal',
  power_consumption = 0.6,
  smelting_reagent_amount = 1,
  smelting_product_amount = 20,
})

registerItem('craft_copper_coil', 'Copper Coil', 1.0, false, false, {
  smelts_into = 'craft_copper',
  power_consumption = 1.65,
  smelting_reagent_amount = 1,
  smelting_product_amount = 6,
})

registerItem('craft_battery', 'AA Battery', 0.1, false, false, {
  smelts_into = 'craft_copper',
  power_consumption = 1.65,
  smelting_reagent_amount = 2,
  smelting_product_amount = 1,
})

registerItem('raw_cobalt', 'Raw Cobalt', 0.5)

beginRegisterCategory('metal_intermediate')

registerItem('steel', 'Steel', 0.5, false, false, {
  smelts_into = 'steel_carbon',
  power_consumption = 2.05,
  smelting_reagent_amount = 5,
  smelting_flux_id = 'flux_limestone',
  smelting_flux_dur = 1,
})

registerItem('aluminum', 'Aluminum', 0.5, false, false, {
  smelts_into = 'aluminum_alloy',
  power_consumption = 2.1,
  smelting_reagent_amount = 5,
  smelting_flux_id = 'flux_limestone',
  smelting_flux_dur = 1,
})

registerItem('titanium', 'Titanium', 0.5, false, false, {
  smelts_into = 'titanium_alloy',
  power_consumption = 2.1,
  smelting_reagent_amount = 10,
  smelting_flux_id = 'flux_limestone',
  smelting_flux_dur = 1,
})

beginRegisterCategory('metal_products')

registerItem('craft_copper', 'Copper', 0.2)
registerItem('iron', 'Iron', 0.5)
registerItem('steel_hardened', 'Hardened Steel', 1.0)
registerItem('platinum', 'Platinum', 0.5)
registerItem('iron_powdered', 'Powdered Iron', 0.1)
registerItem('aluminum_powdered', 'Powdered Aluminum', 0.1)
registerItem('gold_ingot', 'Gold Ingot', 0.5)
registerItem('steel_carbon', 'Carbon Steel', 0.5)
registerItem('aluminum_alloy', 'Aluminum Alloy', 0.5)
registerItem('titanium_alloy', 'Titanium Alloy', 0.5)

beginRegisterCategory('metal_fluxes')

registerItem('flux_limestone', 'Limestone Bag', 2.0, {}, {}, {
  unstackable = true,
  item_durability = {
    dur_cur = 25,
    dur_initial = 25,
    dur_weight_per = 0.3,
  },
})
