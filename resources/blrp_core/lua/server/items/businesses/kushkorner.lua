beginRegisterCategory('kushkorner_bl-jo')

registerItem('kk_ppj', 'Purple Panic Joint', 0.5, joint_choices, false, {
  categories_secondary = {
    'joints_blunts',
    'drugs',
    'res_table_allowed',
  },

  sub_color = '#6756AA',
  sub_name = 'Marijuana',
})

registerItem('kk_ppb', 'Purple Panic Blunt', 0.5, joint_choices, false, {
  categories_secondary = {
    'joints_blunts',
    'drugs',
    'res_table_allowed',
  },

  sub_color = '#6756AA',
  sub_name = 'Marijuana',
})

  registerItem('kk_grinder', '<PERSON>sh Korner Grinder', 0.3, false, false, { categories_secondary = { 'grinders', 'res_table_allowed' }})

  endRegisterCategory()

  beginRegisterCategory('kk_ing')

  registerItem('panic_bud', 'Purple Panic Bud', 0.5, bud_choices, false, {
    weed_item = 'kk_ppj',
    blunt_item = 'kk_ppb',
  })

  registerItem('kk_papers', 'Rose Petal Rolling papers',0.02, false, false, { categories_secondary = { 'rolling_papers', 'res_table_allowed' }})

  registerItem('kk_lighter2', '<PERSON><PERSON>rner Torch Lighter', 0.1, false, false, { categories_secondary = { 'lighters', 'res_table_allowed' }})

  endRegisterCategory()

  beginRegisterCategory('kk_edibles')

    registerConsumable('kk_thcdrink',      'Peaches & Kream THC Drink',        0.1, 'drink', 0, -40, nil, function(character, item_id)

        tItemInteractions.playScreenEffect(character.source, { 'DMT_flight', 60 })
        tSurvivalEdible.AddEdibleEffect(character.source,{60})
        character.varyHealthOverTime(20, 15)
        character.log('HEALTH', 'Started gaining health over time with item', {
          item_id = item_id,
          amount = 20,
          duration = 15,
        })

        SetTimeout(15 * 1000, function()
          character.log('HEALTH', 'Finished gaining health over time with item', {
            item_id = item_id,
            amount = 20,
            duration = 15,
          })
        end)
    end,{
        prop_name = 'prop_plastic_cup_02',
        sequence = {
          { "mp_player_intdrink", "loop", 3 },
          { "mp_player_intdrink", "outro", 1 }
        },
        duration = 9,
        recipe = {
          ['food_heavycream'] = 2,
          ['food_sugar'] = 1,
          ['peach'] = 2,
          ['panic_bud'] = 3,
        },
        recipe_allow_target = true,
        categories_secondary = { 'res_table_allowed' },
    })

    registerConsumable('kk_thc_bar',      'Kookies & Kream THC Bar',        0.1, 'eat', -40, 0, nil, function(character, item_id)
        tItemInteractions.playScreenEffect(character.source, { 'DMT_flight', 60 })
        tSurvivalEdible.AddEdibleEffect(character.source,{60})
        character.varyHealthOverTime(20, 15)
        character.log('HEALTH', 'Started gaining health over time with item', {
          item_id = item_id,
          amount = 20,
          duration = 15,
        })

        SetTimeout(15000, function()
          character.log('HEALTH', 'Finished gaining health over time with item', {
            item_id = item_id,
            amount = 20,
            duration = 15,
          })
        end)
    end,{
      recipe = {
        ['food_flour_mix'] = 2,
        ['food_chocolate'] = 1,
        ['food_sugar'] = 1,
        ['food_egg'] = 1,
        ['panic_bud'] = 3,
      },
      recipe_allow_target = true,
      categories_secondary = { 'res_table_allowed' },
    })

  endRegisterCategory()

  beginRegisterCategory('zoobies_packaging')


  registerItem('kk_prcrate','Kush Korner Pre-roll crate', 2.0, {
    {
      name = 'Use',
      callback = function(character, item_id, item_id_original, meta)
        if not meta or not meta.dur_cur then
          return
        end

        if meta.dur_cur <= 0 then
          character.notify('There are no more pre-rolls boxes in this crate')
          return
        end

        modifyItemMeta(character.source, item_id, 'dur_cur', meta.dur_cur - 1)

        character.give('kk_prerolls', 1)
      end
    }
  }, {}, {
    item_durability = {
      dur_initial = 100,
      dur_weight_per = 0.1,
    }
  })

  registerItem('kk_sscrate','Kush Korner Edibles Bag crate', 2.0, {
    {
      name = 'Use',
      callback = function(character, item_id, item_id_original, meta)
        if not meta or not meta.dur_cur then
          return
        end

        if meta.dur_cur <= 0 then
          character.notify('There are no more Kush Korner Edibles Bag\'s in this crate')
          return
        end

        modifyItemMeta(character.source, item_id, 'dur_cur', meta.dur_cur - 1)

        character.give('stor_kk', 1)
      end
    }
  }, {}, {
    item_durability = {
      dur_initial = 100,
      dur_weight_per = 0.1,
    }
  })


  endRegisterCategory()

