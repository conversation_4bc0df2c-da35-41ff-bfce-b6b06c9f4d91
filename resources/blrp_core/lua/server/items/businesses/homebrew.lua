beginRegisterCategory('drink')

registerConsumable('hb_sorrel', 'Sorrel', 0.2, 'drink', 0, -65, nil, nil, {
  recipe = {
    ['food_hibiscustealeafs'] = 1,
    ['food_cinnamon'] = 1,
    ['food_ice'] = 1,
  }
})

registerConsumable('hb_karib_kiss_ban', 'Banana Karibbean Kiss', 0.2, 'drink', 0, -55, nil, function(character, item_id)
  character.give('empty_bottle', 1, false)
end, {
  recipe = {
    ['milk'] = 1,
    ['food_banana'] = 2,
  }
})

registerConsumable('hb_karib_kiss_blu', 'Blueberry Karibbean Kiss', 0.2, 'drink', 0, -55, nil, function(character, item_id)
  character.give('empty_bottle', 1, false)
end, {
  recipe = {
    ['milk'] = 1,
    ['food_blueberry'] = 10,
  }
})

registerConsumable('hb_karib_kiss_str', 'Strawberry Karibbean Kiss', 0.2, 'drink', 0, -55, nil, function(character, item_id)
  character.give('empty_bottle', 1, false)
end, {
  recipe = {
    ['milk'] = 1,
    ['food_frozenstrawberries'] = 5,
  }
})

registerConsumable('hb_calypso_isl', 'Calypso Island Wave', 0.2, 'drink', 0, -45)
registerConsumable('hb_calypso_ob', 'Calypso Ocean Blue', 0.2, 'drink', 0, -45)
registerConsumable('hb_calypso_org', 'Calypso Original', 0.2, 'drink', 0, -45)
registerConsumable('hb_calypso_sth', 'Calypso Southern Peach', 0.2, 'drink', 0, -45)
registerConsumable('hb_calypso_str', 'Calypso Strawberry', 0.2, 'drink', 0, -45)

registerConsumable('drink_bwood_blast', 'Beechwood Blast', 0.2, 'drink', 0, -65)
registerConsumable('drink_vinny_cocoa', "Vinny Denny's Hot Cocoa", 0.2, 'drink', 0, -15)

-- Alcoholic

registerConsumable('hb_charles_angels', "Charles' Angels", 0.2, 'drink', 0, -65, 5)
registerConsumable('hb_magnum', 'Magnum', 0.2, 'drink', 0, -65, 5)

registerConsumable('hb_dirty_ban', 'Dirty BanANNA', 0.2, 'drink', 10, -45, 10, nil, {
  recipe = {
    ['food_banana'] = 1,
    ['food_coconutcream'] = 1,
    ['drink_raggarum'] = 1,
  }
})

registerConsumable('hb_raras_gt', 'Raras GnT', 0.2, 'drink', 0, -0, 5, nil, {
  recipe = {
    ['food_gin'] = 1,
    ['food_clubsoda'] = 1,
    ['cucumber'] = 1,
  }
})

registerConsumable('hb_rum_punch', 'Rum Punch', 0.2, 'drink', 0, -45, 5, nil, {
  recipe = {
    ['drink_raggarum'] = 1,
    ['hb_calypso_org'] = 1,
  }
})

beginRegisterCategory('food')

-- Mains

registerConsumable('hb_jerk_chx', 'Jerk Chicken', 0.2, 'eat', -15, 0, nil, nil, {
  recipe = {
    ['food_raw_chicken'] = 1,
    ['food_jerk_seasoning'] = 1,
    ['food_cooked_rice'] = 1,
    ['food_peas'] = 1,
  }
})
registerConsumable('hb_jerk_tofu', 'Jerk Tofu Chicken', 0.2, 'eat', -55, 0, nil, nil, {
  recipe = {
    ['food_tofu'] = 2,
    ['food_jerk_seasoning'] = 1,
    ['food_cooked_rice'] = 1,
    ['food_peas'] = 1,
  }
})

registerConsumable('hb_goat', 'Curry Goat', 0.2, 'eat', -60, 0, nil, nil, {
  recipe = {
    ['food_onions'] = 1,
    ['curry_mix'] = 1,
    ['food_raw_goat'] = 1,
  }
})

registerConsumable('hb_fish', 'Ackee & Salt Fish', 0.2, 'eat', -65, 0, nil, nil, {
  recipe = {
    ['food_raw_fish'] = 1,
    ['food_habpeppers'] = 1,
    ['food_paprika'] = 1,
    ['ackee'] = 1,
  }
})

registerConsumable('hb_oxtail', 'Oxtails', 0.2, 'eat', -60, 0, nil, nil, {
  recipe = {
    ['food_oxtail'] = 1,
    ['food_onions'] = 1,
    ['food_carrot'] = 1,
  }
})

registerConsumable('hb_pasta', 'Rasta Pasta', 0.2, 'eat', -60, 0, nil, nil, {
  recipe = {
    ['food_shrimp'] = 1,
    ['food_pasta'] = 1,
    ['food_jerk_seasoning'] = 1,
  }
})

-- Sides

registerConsumable('food_beef_patty', 'Beef Patty', 0.2, 'eat', -33, 0, nil, nil, {
  recipe = {
    ['food_raw_meat'] = 2,
    ['food_egg'] = 2,
    ['food_flour_mix'] = 2,
  }
})

registerConsumable('food_vegan_patty', 'Vegan Patty', 0.2, 'eat', -33, 0, nil, nil, {
  recipe = {
    ['food_chopped_potato'] = 2,
    ['food_egg'] = 2,
    ['food_flour_mix'] = 2,
  }
})

registerConsumable('hb_fries', 'Sweet Potato Fries', 0.2, 'eat', -33, 0, nil, nil, {
  recipe = {
    ['food_chopped_potato'] = 1,
    ['food_salt'] = 1,
  }
})

registerConsumable('hb_coco_bread', 'Coco Bread', 0.2, 'eat', -33, 0, nil, nil, {
  recipe = {
    ['food_coconutcream'] = 1,
    ['food_egg'] = 1,
    ['food_flour_mix'] = 1,
  }
})

registerConsumable('food_jerk_chickenwings',     'Jerk Chicken Wings',   0.2, 'eat', -33, 0, nil, nil, {
  recipe = {
    ['food_raw_chicken'] = 3,
    ['food_jerk_seasoning'] = 1
  }
})

-- Dessert

registerConsumable('hb_choc_kiss', 'Choc Kiss', 0.2, 'eat', -5, 0)

registerConsumable('hb_cookie', 'BD Cookie', 0.2, 'eat', -5, 0, nil, function(character, item_id)
  if math.random(1, 100) <= 10 then
    Citizen.Wait(math.random(5000, 10000))

    character.notify('You feel a sudden urge come over you...')
    I.effectPeyote(character, false, 30)
    character.dpEmote('dancemoves')
  end
end, {
  recipe = {
    ['food_chocolate'] = 1,
    ['food_flour_mix'] = 1,
    ['peyote_chunk'] = 1,
  }
})

registerConsumable('hb_balls', 'Tamarind Balls', 0.2, 'eat', -15, 0, nil, nil, {
  recipe = {
    ['tamarind'] = 1,
    ['food_sugar'] = 1,
  }
})

registerConsumable('food_fried_plantain', 'Fried Plantain', 0.2, 'eat', -15, 0, nil, nil, {
  recipe = {
    ['food_plantain'] = 1,
    ['food_sugar'] = 1,
  }
})

endRegisterCategory()
