---
--- Bad Help
--- This file was created by "/usr/bin/rizz#0867"
--- https://github.com/rizzdev/bad_help
---

local guiEnabled = false
local my_callbacks = { }
local current_menu = false

function focusNUI (shouldDisplay)
    guiEnabled = shouldDisplay
    SetNuiFocus(guiEnabled, false)
    SetNuiFocusKeepInput(guiEnabled)
    TriggerEvent('core:client:setBadMenuState', shouldDisplay)
end

RegisterNetEvent('badMenu:client:create')
AddEventHandler('badMenu:client:create', function(packet)
    current_menu = packet
    make(packet, true)
end)

function make(packet, should_reset)
    for _, option in pairs(packet.options) do
        if option.type == 'menu' then
            if #option.options >= 1 then
                if not option.options then
                    print('error with badMenu config in "options" sub-menu type')
                end
                local packet_copy = copy(packet)
                packet_copy.section_title = option.name
                packet_copy.options = option.options
                packet.options[_].callback = function()
                    make(packet_copy, false)
                end
            else
                print('error with badMenu config = type "menu" must have at least 1 option')
            end
        end

        if option.callback then
            local uuid = uuid()
            my_callbacks[uuid] = { cb = option.callback, after_actions = option.after_actions }
            packet.options[_].callback = nil
            packet.options[_].uuid = uuid
            packet.options[_].server = false
        end
    end

    TriggerEvent('badMenu:client:nuiShow', packet, should_reset)
end

RegisterNetEvent('badMenu:client:nuiShow')
AddEventHandler('badMenu:client:nuiShow', function(packet, should_reset)
    focusNUI(true)
    SendNUIMessage({
        type = 'menu:show',
        packet = packet,
        should_reset = should_reset
    })
end)

RegisterNetEvent('badMenu:client:resolve')
AddEventHandler('badMenu:client:resolve', function(uuid, new_value)
    SendNUIMessage({
        type = 'menu:resolve',
        packet = {
            uuid = uuid,
            new_value = new_value
        }
    })
end)

RegisterNetEvent('menu:forceCloseMenu')
AddEventHandler('menu:forceCloseMenu', function()
  TriggerEvent('badMenu:client:hideAll')
end)

RegisterNetEvent('badMenu:client:hideAll')
AddEventHandler('badMenu:client:hideAll', function()
    my_callbacks = { }
    TriggerEvent('core:client:tempLockEscape')
    TriggerServerEvent('badMenu:server:clearMyCache')
    focusNUI(false)
    SendNUIMessage({
        type = 'menu:hide',
    })
end)


RegisterNUICallback('escape', function(data, cb)
    TriggerEvent('badMenu:client:hideAll')
    cb('ok')
end)

RegisterNUICallback('lockControls', function(data, cb)
    --SetNuiFocus(true, true)
    SetNuiFocusKeepInput(false)
    cb('ok')
end)

RegisterNUICallback('unlockControls', function(data, cb)
    SetNuiFocus(true, false)
    SetNuiFocusKeepInput(true)
    cb('ok')
end)

RegisterNUICallback('confirmSelection', function(data, cb)
    cb('ok')

    if data.server then
        return TriggerServerEvent('badMenu:server:confirmSelection', data.uuid, data.value)
    end

    if my_callbacks[data.uuid] then
        my_callbacks[data.uuid].cb(data.value, function(new_value)
            if new_value == 'close' then
              TriggerEvent('badMenu:client:hideAll')
            end

            TriggerEvent('badMenu:client:resolve', data.uuid, new_value)
        end)
    else
        print('error while trying to do callback on menu selection - cb does not exist')
    end
end)

function doAfter(method)
    if method == 'close' then
        TriggerEvent('badMenu:client:hideAll')
    end

    if method == 'refresh' then
        TriggerEvent('badMenu:client:create', current_menu)
    end
end

-- Make sure the menu can't ever get stuck by proxying functions
--Citizen.CreateThread(function()
--    while true do
--        Citizen.Wait(1)
--        if IsControlJustReleased(1,  177) then
--            SendNUIMessage({ type = 'menu:key:press', packet = { key = 'backspace' } })
--        end
--
--        if IsControlJustReleased(1,  194) then
--            SendNUIMessage({ type = 'menu:key:press', packet = { key = 'backspace' } })
--        end
--
--        if IsControlJustReleased(1,  202) then
--            SendNUIMessage({ type = 'menu:key:press', packet = { key = 'backspace' } })
--        end
--    end
--end)


function uuid()
    local randomizer = math.random(1, 999999)
    math.randomseed(GetGameTimer() - randomizer)
    math.randomseed(GetGameTimer() - randomizer)
    math.randomseed(GetGameTimer() - randomizer)
    local template ='xxxxxxxx-xxxx-xxxx-yxxx-xxxxxxxxxxxx'
    return string.gsub(template, '[xy]', function (c)
        local v = (c == 'x') and math.random(0, 0xf) or math.random(8, 0xb)
        return string.format('%x', v)
    end)
end

function copy(obj)
    if type(obj) ~= 'table' then return obj end
    local res = {}
    for k, v in pairs(obj) do res[copy(k)] = copy(v) end
    return res
end
