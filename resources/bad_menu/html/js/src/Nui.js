const Nui = class Nui {
    call(method, data) {
        if(!data) data = {}
        fetch(`https://bad_menu/${method}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify(data)
        }).then(resp => resp.json()).then(resp => {
            return resp;
        });
    }
}

export default new Nui();