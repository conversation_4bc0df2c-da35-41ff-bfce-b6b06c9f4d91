
cfg = {}
-- define garage types with their associated vehicles
-- (vehicle list: https://wiki.fivem.net/wiki/Vehicles)

-- each garage type is an associated list of veh_name/veh_definition
-- they need a _config property to define the blip and the vehicle type for the garage (each vtype allow one vehicle to be spawned at a time, the default vtype is "default")
-- this is used to let the player spawn a boat AND a car at the same time for example, and only despawn it in the correct garage
-- _config: vtype, blipid, blipcolor, permission (optional, only users with the permission will have access to the shop)

cfg.garage_types = { }



-- {garage_type,x,y,z}
cfg.garages = {
	{"compacts",-356.146, -134.69, 39.0097},
	{"coupe",723.013, -1088.92, 22.1829},
	{"sports",233.69268798828, -788.97814941406, 30.605836868286},
	{"sportsclassics",1174.76, 2645.46, 37.7545},
	{"supercars",112.275, 6619.83, 31.8154},
	{"motorcycles",-205.789, -1308.02, 31.2916},
	{"taxi",-286.870056152344,-917.948181152344,31.080623626709},
	{"police",454.4,-1017.6,28.4},
	{"emergency",-492.08544921875,-336.749206542969,34.3731842041016},
	{"bicycles",-352.038482666016,-109.240043640137,38.6970825195313},
	--{"boats",-849.501281738281,-1367.69567871094,1.60516905784607},
	--{"boats",1299.11730957031,4215.66162109375,33.9086799621582},
	--{"boats",3867.17578125,4464.54248046875,2.72485375404358},
	--{"planes",1640, 3236, 40.4},
	--{"planes",2123, 4805, 41.19},
	--{"planes",-1348, -2230, 13.9},
	--{"helicopter",1750, 3260, 41.37},
	--{"helicopter",-1233, -2269, 13.9},
	--{"helicopter",-745, -1468, 5},
	--{"container",-978.674682617188,-2994.29028320313,13.945068359375},
	--{"transport",-962.553039550781,-2965.82470703125,13.9450702667236}
}

return cfg
