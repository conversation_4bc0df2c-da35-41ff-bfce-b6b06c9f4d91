AddTextEntry('ALEUT_LIV_11', 'The Vault')
AddTextEntry('ALEUT_LIV_12', 'AlphaMail')
AddTextEntry('ASBO_LIV11', 'Homebrew Cafe - Green')
AddTextEntry('ASBO_LIV12', 'Homebrew Cafe - Yellow')
AddTextEntry('BALL7_LIV_14', 'Omerta Advertising')
AddTextEntry('BALL8_LIV13', 'Specter & Associates')
AddTextEntry('BALL8_LIV14', 'Aces')
AddTextEntry('bati701_liv8', 'Green Nightmare')
AddTextEntry('bati701_liv9', 'Jackall Racing')
AddTextEntry('BEN2_LIV1', 'Orang-O-Tang')
AddTextEntry('BEN2_LIV2', 'Orang-O-Tang Monkey Juice')
AddTextEntry('BEN2_LIV3', 'Cok O Pops')
AddTextEntry('BEN2_LIV4', 'Flow')
AddTextEntry('BEN2_LIV5', 'Logger')
AddTextEntry('BEN2_LIV6', "Blarney's")
AddTextEntry('BEN2_LIV7', "Jakey's")
AddTextEntry('BEN2_LIV8', 'Stronzo')
AddTextEntry('BEN2_LIV9', 'Cherenkov')
AddTextEntry('BEN2_LIV10', 'Pißwasser')
AddTextEntry('BEN2_LIV11', 'Rainé')
AddTextEntry('BEN2_LIV12', 'Fridgit')
AddTextEntry('BEN2_LIV13', 'Phat Chips')
AddTextEntry('BEN2_LIV14', '24/7')
AddTextEntry('BEN2_LIV15', 'Burgershot')
AddTextEntry('BEN2_LIV16', 'Alco Holla')
AddTextEntry('BEN2_LIV17', 'Taqueria de Los Santos')
AddTextEntry('BEN2_LIV18', 'Burgershot')
AddTextEntry('BEN2_LIV19', 'Cool Beans')
AddTextEntry('BEN2_LIV20', "Boo's Catfe")
AddTextEntry('BEN2_LIV21', "TequiLaLa")
AddTextEntry('BEN2_LIV22', "Vanilla Unicorn")
AddTextEntry('BEN2_LIV23', "Chihuahua Hotdogs")
AddTextEntry('BEN2_LIV24', 'Pearls')
AddTextEntry('BEN2_LIV25', 'Nice Dreams')
AddTextEntry('BEN2_LIV26', "Chihuahua Hotdogs")
AddTextEntry('BLIMP3_LIVERY21', "Boo's Catfe")
AddTextEntry('BOOR_LIV15', 'Camp Morningwood Charcoal')
AddTextEntry('BOOR_LIV16', 'Camp Morningwood Cocoa')
AddTextEntry('BOOR_LIV17', 'Camp Morningwood Latte')
AddTextEntry('BOOR_LIV18', 'Camp Morningwood Clover')
AddTextEntry('BOOR_LIV19', 'Camp Morningwood Skyrealm')
AddTextEntry('BOOR_LIV20', 'Camp Morningwood Bubblegum')
AddTextEntry('BRS_LIV30', 'Chihuahua Hotdogs')
AddTextEntry('BRS_LIV31', 'Westwoods')
AddTextEntry('BUC2_LIV_11', 'Mad Wheels')
AddTextEntry('BUC2_LIV_12', 'Ballas')
AddTextEntry('BUFF4_LIV_13', 'Beechwood Disciples')
AddTextEntry('BUFF4_LIV_14', 'Mutiny Mob')
AddTextEntry('BUFF4_LIV_15', 'Reapers')
AddTextEntry('BUFF4_LIV_16', 'Rebels')
AddTextEntry('BUFF4_LIV_17', '305')
AddTextEntry('BUFF4_LIV_18', "Fratelli d'Schnazy")
AddTextEntry('BUFF4_LIV_19', '305')
AddTextEntry('BUFF4_LIV_20', 'Condemned MC')
AddTextEntry('BUFF4_LIV_21', 'The Chosen Few')
AddTextEntry('BUFF4_LIV_22', 'Sinner St. Crip')
AddTextEntry('BUFF4_LIV_23', 'Los Miradz')
AddTextEntry('BUFF4_LIV_24', 'FDF')
AddTextEntry('BUFF4_LIV_25', 'BSG')
AddTextEntry('BUFF4_LIV_26', 'Misfitz')
AddTextEntry('BUFF4_LIV_27', 'CTL')
AddTextEntry('BUFF4_LIV_28', 'The Unknown')
AddTextEntry('BUS2_LIV1', 'ONG/GRY Metro Local')
AddTextEntry('BUS2_LIV2', 'RED/WHI Metro Rapid')
AddTextEntry('BUS2_LIV3', 'RED/GRY Metro Rapid')
AddTextEntry('BUS2_LIV4', 'BLU/WHI Metro Express')
AddTextEntry('BUS2_LIV5', 'PNK/WHI Pillbox Hill Area Transit')
AddTextEntry('BUS2_LIV6', 'RED/ONG/YEL Stripe')
AddTextEntry('BUS2_LIV7', 'RED/ONG/YEL Stripe + Ads')
AddTextEntry('BUS2_LIV8', 'ONG City of Del Perro')
AddTextEntry('BUS2_LIV9', 'BLU/ONG/WHI Blaine County Transit')
AddTextEntry('BUS2_LIV10', 'BLU/ONG/WHI Blaine County Transit + Ads')
AddTextEntry('BUS2_LIV11', 'WHI/YEL/BLU LSIA Shuttle')
AddTextEntry('BUS2_LIV12', 'WHI Traveler Expedient')
AddTextEntry('BUS2_LIV13', 'BLU/WHI Del Perro Metro')
AddTextEntry('BUS2_LIV14', 'BLU/WHI Del Perro Metro + Ads')
AddTextEntry('BUS2_LIV15', 'GRY Vicefree Bus Line')
AddTextEntry('CALICO_LIVERY16', '305')
AddTextEntry('CALICO_LIVERY17', 'LSF')
AddTextEntry('BUFF5_LIV11', 'Beechwood Disciples')
AddTextEntry('CARA2_LIV_11', 'Alamo Sea Pirates')
AddTextEntry('CARA2_LIV_12', 'Blaine County Hawks')
AddTextEntry('CARA2_LIV_13', 'Trojan Armor')
AddTextEntry('CARA2_LIV_14', 'Scorpions')
AddTextEntry('CAV3_LIVERY11', 'Dynasty 8')
AddTextEntry('CAV3_LIVERY12', 'East Side Armor')
AddTextEntry('CAV3_LIVERY13', 'Vendetta')
AddTextEntry('CAV3_LIVERY14', 'Messina Crime Family')
AddTextEntry('CAV3_LIVERY15', 'The Vault')
AddTextEntry('CAV3_LIVERY16', 'Wayward Tattoos')
AddTextEntry('CAV3_LIVERY17', 'Gallahan & Co Attorneys at Law')
AddTextEntry('CHEB_LIV11', 'Yankton "Adibass"')
AddTextEntry('CHINO2_LIV_11', 'LSF')
AddTextEntry('CINQ_LIV_11', 'Blath Alainn Charity')
AddTextEntry('COQ2_LIV11', 'In memory of Frank Frost')
AddTextEntry('COQ4_LIV12', 'Stalker')
AddTextEntry('COQ4_LIV13', 'Vice Motorsports')
AddTextEntry('COMET6_LIV16', 'bulbasaur')
AddTextEntry('COMET7_LIV12', 'SC Design Black')
AddTextEntry('COMET7_LIV13', 'SC Design White')
AddTextEntry('CYPH_LIVERY16', 'Exiled')
AddTextEntry('CYPH_LIVERY17', 'Messina Crime Family')
AddTextEntry('CYPH_LIVERY18', 'LSF')
AddTextEntry('CYPH_LIVERY19', 'The Collective')
AddTextEntry('CYPH_LIVERY20', 'The Vault')
AddTextEntry('DOM7_LIVERY16', 'Los Santos Customs')
AddTextEntry('DOM8_LIVERY16', 'Blaine County Hawks')
AddTextEntry('DRFTR_LIV11', 'Vanilla Unicorn')
AddTextEntry('DRFTR_LIV12', 'Rascals')
AddTextEntry('DRFTR_LIV13', 'Beechwood Disciples')
AddTextEntry('DRFTR_LIV14', '305')
AddTextEntry('DRFTR_LIV15', 'Mutiny Mob')
AddTextEntry('DRFTR_LIV16', 'PBF')
AddTextEntry('DRFTR_LIV17', 'FDF')
AddTextEntry('DRFTR_LIV18', 'NOISEBOMB')
AddTextEntry('DYN_LIV11', 'Big Hawk Taxi')
AddTextEntry('ELEGY1_LIV10', 'Annis Racing')
AddTextEntry('ELEGY1_LIV11', 'Gensai Racing')
AddTextEntry('ELEGY1_LIV12', 'Nishigawa')
AddTextEntry('ELEGY1_LIV13', 'Access')
AddTextEntry('ELEGY1_LIV14', 'Blue X-Flow')
AddTextEntry('ELEGY1_LIV15', 'Alien Racing')
AddTextEntry('ELEGY1_LIV16', 'X-Flow')
AddTextEntry('ELEGY1_LIV17', 'A-Styling')
AddTextEntry('ELEGY1_LIV18', 'Alien Lab')
AddTextEntry('ELEGY1_LIV19', 'G.A.S')
AddTextEntry('ELEGY1_LIV20', 'Yellow Alien')
AddTextEntry('ELEGY1_LIV21', 'East Workz')
AddTextEntry('ELEGY1_LIV22', 'East Workz X-Flow')
AddTextEntry('ELEGY1_LIV23', 'Yellow Alien Racing')
AddTextEntry('ELEGY1_LIV24', 'P&Q')
AddTextEntry('ELEGY1_LIV25', 'Sweper')
AddTextEntry('ELEGY1_LIV26', 'Night Racing')
AddTextEntry('ELEGY1_LIV27', 'Test Dummy')
AddTextEntry('ELEGY1_LIV28', 'Vanilla Works')
AddTextEntry('ELEGY1_LIV29', 'SAHP')
AddTextEntry('ELEGY1_LIV30', 'Annim Racing')
AddTextEntry('ELEGY1_LIV31', 'Jet Shark')
AddTextEntry('ELEGY1_LIV32', 'Japanese Warrior')
AddTextEntry('ELEGY1_LIV33', 'Proto')
AddTextEntry('ELEGY1_LIV34', 'Annis Racing')
AddTextEntry('ELEGY1_LIV35', 'Red Tiger Racing')
AddTextEntry('ELEGY1_LIV36', 'Redwood Racing')
AddTextEntry('ELEGY1_LIV37', 'E-144')
AddTextEntry('ELEGY1_LIV38', 'Japanese dragon')
AddTextEntry('ELEGY1_LIV39', 'Samurai')
AddTextEntry('ELEGY1_LIV40', 'Nishigama')
AddTextEntry('ELEGY1_LIV41', '5 Racing')
AddTextEntry('ELEGYRHD_LIV1', 'RH5 Racing')
AddTextEntry('ELEGYRHD_LIV2', 'Elegy RH5')
AddTextEntry('ELEGYRHD_LIV3', 'RH5 Racing flag')
AddTextEntry('ELEGYRHD_LIV4', 'Yours')
AddTextEntry('ELEGYRHD_LIV5', 'Annim')
AddTextEntry('ELEGYRHD_LIV6', 'X-Flow')
AddTextEntry('ELEGYRHD_LIV7', 'Ripper Roo')
AddTextEntry('EUDORA_LIV12', 'Happy Holidays')
AddTextEntry('EUDORA_LIV13', 'Big Hawk Taxi Co.')
AddTextEntry('EVERO_LIV_11', 'Thomson Scrapyard')
AddTextEntry('EVERON2_LIV30', '6 Vice Motorsports')
AddTextEntry('FAC2_LIV_10', 'Ballas')
AddTextEntry('FAC2_LIV_11', 'Ballas')
AddTextEntry('FAC3_LIV_10', 'Go Truck Yourself')
AddTextEntry('FAC3_LIV_11', 'Ballas')
AddTextEntry('FAC3_LIV_12', 'Ballas')
AddTextEntry('FR36_lIV17', 'Ballas')
AddTextEntry('FR36_lIV18', 'Misfitz')
AddTextEntry('FCRWL_LIV_9', 'Mikes Sporting Goods')
AddTextEntry('GARG_LIV_3', 'Sinister Sons')
AddTextEntry('GARG_LIV_4', 'Lost MC')
AddTextEntry('GARG_LIV_5', 'Condemned MC')
AddTextEntry('GAUNT4_LIV13', 'Los Santos Customs')
AddTextEntry('GAUNT4_LIV14', 'Fort Zancudo')
AddTextEntry('GAUNT6_LIV31', '6 Vice Motorsports')
AddTextEntry('GAUC_LIV_13', 'Rascals')
AddTextEntry('GAUC_LIV_14', 'Vagos')
AddTextEntry('GEN_LIVERY19', 'TequiLaLa')
AddTextEntry('GRAN2_LIV_13', 'Thompson & Associates Law')
AddTextEntry('GRAN2_LIV_14', 'VU')
AddTextEntry('HELL_LIV11', 'Blaine County Hawks')
AddTextEntry('HERMES_LIV11', 'TequiLaLa')
AddTextEntry('HOTRING_LIV31', '41 eCola X Sprunk')
AddTextEntry('HOTRING_LIV32', '99 Blaine County Hawks')
AddTextEntry('HOTRING_LIV33', '31 Sandy Shores Carwash')
AddTextEntry('HOTRING_LIV34', '17 Mikes Sporting Goods')
AddTextEntry('HOTRING_LIV35', '125 Thomson Scrapyard')
AddTextEntry('HOTRING_LIV36', '247 24/7 Market')
AddTextEntry('HOTRING_LIV37', '6 Joes Corner')
AddTextEntry('HOTRING_LIV38', '7 Joes Corner')
AddTextEntry('HOTRING_LIV39', '3 305 Autobody')
AddTextEntry('HOTRING_LIV40', '2 Yellow Jack Inn')
AddTextEntry('HOTRING_LIV41', 'Los Santos Customs')
AddTextEntry('HOTRING_LIV42', '90 Los Santos Fire Department')
AddTextEntry('HOTRING_LIV43', '84 The Boat House')
AddTextEntry('HOTRING_LIV44', '365 24/7 Market')
AddTextEntry('HOTRING_LIV45', '68 Flywheels')
AddTextEntry('HOTRING_LIV46', '85 PDM')
AddTextEntry('HOTRING_LIV47', '9 PDM')
AddTextEntry('HOTRING_LIV48', '21 Dynasty 8')
AddTextEntry('HOTRING_LIV49', '37 Mikes Sporting Goods')
AddTextEntry('HOTRING_LIV50', '6 Vice Motorsports')
AddTextEntry('HOTRING_LIV51', '13 Rocky Road Towing')
AddTextEntry('HOTRING_LIV52', '14 Rocky Road Towing')
AddTextEntry('HOTRING_LIV53', '33 Go Truck Yourself')
AddTextEntry('HOTRING_LIV54', '82 Chihuahua Hotdogs')
AddTextEntry('HOTRING_LIV55', '12 Finders Keepers')
AddTextEntry('HOTRING_LIV56', '72 Thompson & Associates')
AddTextEntry('HOTRING_LIV57', '115 MK Textures')
AddTextEntry('HOTRING_LIV58', '420 The Vault')
AddTextEntry('HOTRING_LIV59', '666 Reapers')
AddTextEntry('HOTRING_LIV60', '63 Los Santos Customs')
AddTextEntry('HUSTL_LIV11', 'Rum Runners')
AddTextEntry('HUSTL_LIV12', 'TequiLaLa')
AddTextEntry('IMPERIAL_LIV13', 'Weazel News')
AddTextEntry('IMPERIAL_LIV14', 'Rocky Road Towing')
AddTextEntry('IMPERIAL_LIV15', 'Pearls')
AddTextEntry('IMPERIAL_LIV16', 'Strawberry Rails')
AddTextEntry('IMPERIAL_LIV17', 'Chihuahua Hotdogs')
AddTextEntry('IMPERIAL_LIV18', "Boo's Catfe")
AddTextEntry('IMPERIAL_LIV19', 'Spellbound Occult')
AddTextEntry('IMPERIAL_LIV20', 'Cool Beans')
AddTextEntry('IMPERIAL_LIV21', 'Urban Shootz')
AddTextEntry('IMPERIAL_LIV22', 'DOC')
AddTextEntry('IMPERIAL_LIV23', 'Dump and Pump Septic')
AddTextEntry('IMPERIAL_LIV24', 'Trojan Armor')
AddTextEntry('IMPERIAL_LIV25', 'Blaine County Hawks')
AddTextEntry('IMPERIAL_LIV26', 'Vynchenzo\'s Distillery')
AddTextEntry('IMPERIAL_LIV27', 'Weazel News')
AddTextEntry('IMPERIAL_LIV28', 'Big Hawk Taxi Co.')
AddTextEntry('IMPERIAL_LIV29', 'Bed Bugs & Beyond')
AddTextEntry('IMPERIAL_LIV30', 'Kush Korner')
AddTextEntry('IMPERIAL_LIV31', 'Go Truck Yourself')
AddTextEntry('IMPERIAL_LIV32', 'All The Shingle Ladies')
AddTextEntry('IMPALER5_LIV14', 'Ballas')
AddTextEntry('IMP6_LIV11', 'Ballas')
AddTextEntry('ISSI3_LIV11', 'Burgershot')
AddTextEntry('ISSI7_LIV11', 'Boos Catfe')
AddTextEntry('ISSI8_LIV11', 'Cool Beans')
AddTextEntry('ITALI_LIV_L', 'PDM Auto')
AddTextEntry('ITALI_LIV_M', 'Vendetta')
AddTextEntry('IWAG_LIV_11', 'Omerta Advertising')
AddTextEntry('JEST4_LIVERY16', 'PaintBall')
AddTextEntry('JEST4_LIVERY17', 'B & W Roses')
AddTextEntry('JEST4_LIVERY18', 'PDM Auto')
AddTextEntry('JESTGPR_LIV3', 'Rugrats')
AddTextEntry('JESTGPR_LIV4', 'Hayes Auto')
AddTextEntry('JESTGPR_LIV5', 'Lux')
AddTextEntry('JGP_LIV7', 'LSF')
AddTextEntry('JUG_LIV11', 'Little Seoul Family')
AddTextEntry('JUG_LIV12', 'Misfitz')
AddTextEntry('JUG_LIV13', 'Yakuza')
AddTextEntry('JUG_LIV14', 'Angels of Death')
AddTextEntry('JUG_LIV15', 'BSG Poptart')
AddTextEntry('JUG_LIV16', 'BSG')
AddTextEntry('KAMACH_LIV11', "Mikes' Sporting Goods")
AddTextEntry('KAMACH_LIV12', 'Condemned MC')
AddTextEntry('KAMACH2_LIV1', 'LSFD SAR')
AddTextEntry('KOMO_LIV_11', 'La Cosa Nostra')
AddTextEntry('KOMO_LIV_12', 'Burgershot')
AddTextEntry('KOMO_LIV_13', 'Chihuahua Hotdogs')
AddTextEntry('KOMO_LIV_14', 'Petrovich Cartel')
AddTextEntry('KOMO_LIV_15', '305')
AddTextEntry('KOMO_LIV_16', 'Los Miradz')
AddTextEntry('KOMO_LIV_17', 'Gearheads')
AddTextEntry('KOMO_LIV_18', 'The Continental')
AddTextEntry('KRIE_LIV11', 'Mutiny Mash')
AddTextEntry('KURUMA_LIV16', 'Rascals')
AddTextEntry('KURUMA_LIV17', 'Condemned MC')
AddTextEntry('KURUMA_LIV18', 'Misfitz')
AddTextEntry('KURUMAR_LIV1', 'THRX7')
AddTextEntry('KURUMAR_LIV2', 'Yellow Karin Rally')
AddTextEntry('KURUMAR_LIV3', 'Ron')
AddTextEntry('KURUMAR_LIV4', 'Red Atomic')
AddTextEntry('KURUMAR_LIV5', 'Jackal Racing 73')
AddTextEntry('KURUMAR_LIV6', 'Red and white Karin Racing')
AddTextEntry('KURUMAR_LIV7', 'Yellow and white Karin Racing')
AddTextEntry('KURUMAR_LIV8', 'cyan and white Karin Racing')
AddTextEntry('KURUMAR_LIV9', 'Blue and white Karin Racing')
AddTextEntry('KURUMAR_LIV10', 'KRAP Yellow')
AddTextEntry('KURUMAR_LIV11', 'KRAP Red')
AddTextEntry('KURUMAR_LIV12', 'Racing red')
AddTextEntry('KURUMAR_LIV13', 'Sprunk Extreme Racing Gray')
AddTextEntry('KURUMAR_LIV14', 'Sprunk Extreme Racing White')
AddTextEntry('KURUMAR_LIV15', 'Redwood Racing')
AddTextEntry('KURUMAR_LIV16', 'Sprunk Racing 6')
AddTextEntry('KURUMAR_LIV17', 'Junk Racing 17')
AddTextEntry('KURUMAR_LIV18', 'Fuego Racing')
AddTextEntry('LANDST2_LIV_11', 'Bahama Mamas')
AddTextEntry('lpbagger_liv_1', 'The Chosen Few Mc')
AddTextEntry('lpbagger_liv_2', 'Mongrels')
AddTextEntry('lpbagger_liv_3', 'Angels of Death')
AddTextEntry('lpbagger_liv_4', 'Lost MC')
AddTextEntry('LXX_LIV4', '305')
AddTextEntry('LYNX_LIV_3', 'Cold & Sons')
AddTextEntry('LYNX_LIV_4', 'Stickerbomb')
AddTextEntry('LYNX_LIV_5', 'Cherry Blossom')
AddTextEntry('MAN2_LIV13', 'Aztecas')
AddTextEntry('MAN2_LIV14', 'Vagos')
AddTextEntry('MESAR_LIV4', 'Finders Keepers')
AddTextEntry('MESAR_LIV5', 'Paleto Diner')
AddTextEntry('MONSTROC_LIV11', 'Cayo Perico Livery 1')
AddTextEntry('MONSTROC_LIV12', 'Cayo Perico Livery 2')
AddTextEntry('MONSTROC_LIV13', 'Cayo Perico Livery 3')
AddTextEntry('MONSTROC_LIV14', 'Cayo Perico Livery 4')
AddTextEntry('MONSTROC_LIV15', 'Cayo Perico Livery 5')
AddTextEntry('MONSTROC_LIV16', 'Blath Alainn Charity')
AddTextEntry('MONSTROC_LIV17', 'D&P 2025 new years')
AddTextEntry('MULE_LIV1', 'Junk Energy')
AddTextEntry('MULE_LIV2', 'Cherenkov Vodka')
AddTextEntry('MULE_LIV3', 'You Tool')
AddTextEntry('MULE_LIV4', 'Cluckin Bell')
AddTextEntry('MULE_LIV5', 'Big Goods')
AddTextEntry('MULE_LIV6', 'Pißwasser')
AddTextEntry('MULE_LIV7', 'Dusche Gold')
AddTextEntry('MULE_LIV8', "O'deas Pharmacy")
AddTextEntry('MULE_LIV9', 'Trojan Armor')
AddTextEntry('MULE_LIV10', 'East Side Armor')
AddTextEntry('MULE_LIV11', 'Burgershot')
AddTextEntry('MULE_LIV12', 'Thomson Scrapyard')
AddTextEntry('MULE_LIV13', 'Taqueria de Los Santos')
AddTextEntry('MULE_LIV14', 'Condemned Mining Company')
AddTextEntry('MULE_LIV15', 'Go Truck Yourself')
AddTextEntry('MULE_LIV16', 'Ace Logistics')
AddTextEntry('MULE_LIV17', 'Dump and Pump Septic')
AddTextEntry('MULE_LIV18', 'Mikes Sporting Goods')
AddTextEntry('MULE_LIV19', 'Nice Dreams')
AddTextEntry('MULE_LIV20', 'Rocky Road Towing')
AddTextEntry('MULE_LIV21', 'Nice Dreams')
AddTextEntry('MULE_LIV22', 'Reapers')
AddTextEntry('MULE_LIV23', 'Weazel News')
AddTextEntry('MULE_LIV24', 'Kush Korner')
AddTextEntry('MULE_LIV25', 'Happy Holidays')
AddTextEntry('MULE_LIV26', 'The Vault')
AddTextEntry('MULE_LIV27', 'Jenkins Farms')
AddTextEntry('MULER_LIV11', 'Go Truck Yourself')
AddTextEntry('MULER_LIV12', 'Weazel News')
AddTextEntry('MULER_LIV13', 'BCSO')
AddTextEntry('MULER_LIV14', 'Weazel News')
AddTextEntry('NBLADE_LIV13', 'Angels of Death')
AddTextEntry('NERO2_LIV_10', 'Scam Life')
AddTextEntry('NOVA_LIV11', 'SAHP Seized')
AddTextEntry('OPENW1_LIV11', 'BCSO Team')
AddTextEntry('OPENW1_LIV12', 'Burgershot Team')
AddTextEntry('OPENW1_LIV13', 'Obey Team')
AddTextEntry('OPENW1_LIV14', 'Epsilon Team')
AddTextEntry('OPENW1_LIV15', 'lampidatti Team')
AddTextEntry('OPENW1_LIV16', 'Annis Team')
AddTextEntry('OPENW1_LIV17', 'Vapid Team')
AddTextEntry('OPENW1_LIV18', 'Karin Team')
AddTextEntry('OPENW1_LIV19', 'Red Gull Team')
AddTextEntry('OPENW1_LIV20', 'Invertero Team')
AddTextEntry('OPENW1_LIV21', 'Dewbauchee Team')
AddTextEntry('OMEGT_LIV16', 'Huff & Associates')
AddTextEntry('OMEGT_LIV17', '305')
AddTextEntry('OMEGT_LIV18', 'Specter & Associates')
AddTextEntry('OMEGT_LIV19', 'Dynasty 8')
AddTextEntry('PARAG_LIV13', 'CTL')
AddTextEntry('PEN2_LIV13', 'Rebels')
AddTextEntry('PEN2_LIV14', 'Triads')
AddTextEntry('PEY3_LIVERY11', 'vagos')
AddTextEntry('PREV_LIV16', 'Westwoods')
AddTextEntry('R300_LIV11', 'Beechwood Disciples')
AddTextEntry('R300_LIV12', '305')
AddTextEntry('R300_LIV13', 'Rebels')
AddTextEntry('R300_LIV14', 'LSF')
AddTextEntry('R300_LIV15', 'Mutiny Mob')
AddTextEntry('R300_LIV16', 'Yokai')
AddTextEntry('R300_LIV17', 'LSC Halloween')
AddTextEntry('R300_LIV18', 'Swim Team')
AddTextEntry('R300_LIV19', 'Westwoods')
AddTextEntry('R300_LIV20', 'Misfitz')
AddTextEntry('R300_LIV21', 'Gearheads')
AddTextEntry('R300_LIV22', 'The Continental')
AddTextEntry('R300_LIV23', 'MK Textures')
AddTextEntry('REBLA_LIV11', 'The Belmont')
AddTextEntry('REBLA_LIV12', '305')
AddTextEntry('REBLA_LIV13', 'Misfitz')
AddTextEntry('REBLA_LIV14', 'Dynasty 8')
AddTextEntry('RH7_LIV_20', 'Cobras')
AddTextEntry('RH7_LIV_21', 'The Pit')
AddTextEntry('RH7_LIV_22', 'LSC NY 2025')
AddTextEntry('RHINE_LIV13', 'BSG')
AddTextEntry('RHINE_LIV14', 'The Unknown')
AddTextEntry('RHINE_LIV15', 'Joes Corner')
AddTextEntry('RUIN4_LIV11', 'Scorpions')
AddTextEntry('RT3000_LIV16', 'Angel')
AddTextEntry('SANDSTORM_LIVERY33', 'Blaine County Commissioner')
AddTextEntry('SANDSTORM_LIVERY34', 'Go Truck Yourself')
AddTextEntry('SANDSTORM_LIVERY35', 'Dump and Pump Septic')
AddTextEntry('SANDSTORM_LIVERY36', 'Mikes Sporting Goods')
AddTextEntry('SANDSTORM_LIVERY37', 'The Chosen Few')
AddTextEntry('SCHLA_LIV11', 'Pearls')
AddTextEntry('SCHLA_LIV12', 'Aces & Eights')
AddTextEntry('SCHLA_LIV13', 'Misfitz')
AddTextEntry('SEM2_LIVERY11', 'Dump & Pump Septic')
AddTextEntry('SENT_LIV11', 'Midnight Club')
AddTextEntry('SENT4_LIV15', 'Messina Crime Family')
AddTextEntry('SHINOBI_LIV_11', 'Cherry Blossom')
AddTextEntry('SHINOBI_LIV_12', 'Dragons')
AddTextEntry('SHINOBI_LIV_13', 'Yakuza')
AddTextEntry('SHINOBI_LIV_14', "Boo's Catfe")
AddTextEntry('SHINOBI_LIV_15', "Misfitz")
AddTextEntry('SLVAN3_LIV10', 'Yellow Jack')
AddTextEntry('STOCK4_LIV1', 'Diamond Casino')
AddTextEntry('STOCK4_LIV2', 'Gruppe 6 - Black Text')
AddTextEntry('STOCK4_LIV3', 'Gruppe 6 - White Text')
AddTextEntry('STOCK4_LIV4', 'Penris')
AddTextEntry('STOCK4_LIV5', 'Kayton')
AddTextEntry('STOCK4_LIV6', 'Humane Labs')
AddTextEntry('STOCK4_LIV7', 'Ammunation')
AddTextEntry('STOCK4_LIV8', 'Diamond Casino')
AddTextEntry('STOCK4_LIV9', 'Lock & Load Security')
AddTextEntry('STOCK4_LIV10', 'Unmarked')
AddTextEntry('STOCK4_LIV11', 'Bobcat')
AddTextEntry('STREAMER_LIV_11', 'Rum Runners')
AddTextEntry('STREAMER_LIV_12', 'BCSO')
AddTextEntry('SUG_LIV11', 'Vice Motorsports')
AddTextEntry('SULTAN_LIV9', 'Los Santos Customs')
AddTextEntry('SULTAN_LIV10', 'KTD')
AddTextEntry('SULTAN_LIV11', 'Nishigawa')
AddTextEntry('SULTAN_LIV12', 'GTK')
AddTextEntry('SULTAN_LIV13', 'Samurai')
AddTextEntry('SULTAN_LIV14', 'karin')
AddTextEntry('SULTAN_LIV15', 'Artisan')
AddTextEntry('SULTAN_LIV16', 'Blue Carbin Fiber')
AddTextEntry('SULTAN_LIV17', 'X-Flow')
AddTextEntry('SULTAN_LIV18', 'Underground Racing X-FLow')
AddTextEntry('SULTAN_LIV19', 'GTK Super Oil')
AddTextEntry('SULTAN_LIV20', 'Zenshin Motorsports')
AddTextEntry('SULTAN_LIV21', 'KDR')
AddTextEntry('SULTAN_LIV22', 'P&Q')
AddTextEntry('SULTAN_LIV23', 'Yours')
AddTextEntry('SULTAN_LIV24', 'KTD White')
AddTextEntry('SULTAN_LIV25', 'Princess Robot Bubblegum')
AddTextEntry('SULTAN_LIV26', 'X-Flow Anime')
AddTextEntry('SULTAN_LIV27', 'Palm Tree White')
AddTextEntry('SULTAN_LIV28', 'Palm Tree Black')
AddTextEntry('SULTAN_LIV29', 'Westricers')
AddTextEntry('SULTAN_LIV30', 'Junk Racer 137')
AddTextEntry('SULTAN_LIV31', 'Skull')
AddTextEntry('SULTAN2_LIV11', 'Messina Crime Family')
AddTextEntry('SULTAN2_LIV12', 'Mad Wheels')
AddTextEntry('SULTAN2_LIV13', 'Import Spec')
AddTextEntry('SULTAN2_LIV14', 'KRAP')
AddTextEntry('SULTAN2_LIV16', 'VX Trim White')
AddTextEntry('SULTAN2_LIV17', 'VX Trim Black')
AddTextEntry('SULTAN2_LIV18', 'VX Trim Beige')
AddTextEntry('SULTAN2_LIV19', 'Stanced Online')
AddTextEntry('SULTAN2_LIV20', 'Sakura Tree')
AddTextEntry('SULTAN2_LIV21', 'RS White')
AddTextEntry('SULTAN2_LIV22', 'RS Black')
AddTextEntry('SULTAN2_LIV23', 'Noodle Exchange')
AddTextEntry('SULTAN2_LIV24', 'Nation')
AddTextEntry('SULTAN2_LIV25', 'Logic Films White')
AddTextEntry('SULTAN2_LIV26', 'Logic Films Black')
AddTextEntry('SULTAN2_LIV27', 'Legend')
AddTextEntry('SULTAN2_LIV28', 'Inner Beast')
AddTextEntry('SULTAN2_LIV29', 'HFS')
AddTextEntry('SULTAN2_LIV30', 'GBXL')
AddTextEntry('SULTAN2_LIV31', 'Budget Racing')
AddTextEntry('SULTAN2_LIV32', 'Breadvan')
AddTextEntry('SULTAN2_LIV33', 'Yakuza')
AddTextEntry('SURF3_LIV14', 'Nice Dreams')
AddTextEntry('SWINGER_LIV11', 'In memory of Frank Frost')
AddTextEntry('TERMINUS_LIV11', 'Delta Kappa Nu')
AddTextEntry('TERMINUS_LIV12', 'Mikes Sporting Goods')
AddTextEntry('TERMINUS_LIV13', 'Westside Shootz')
AddTextEntry('TERMINUS_LIV14', 'Los Miradz')
AddTextEntry('TERMINUS_LIV15', 'BSG')
AddTextEntry('TERMINUS_LIV16', 'The Chosen Few')
AddTextEntry('TERMINUS_LIV17', 'The Lost MC')
AddTextEntry('TACO2_LIV1', 'Yellow Jack')
AddTextEntry('TACO2_LIV2', 'Chihuahua Hotdogs')
AddTextEntry('TACO2_LIV3', 'Vanilla Unicorn')
AddTextEntry('TACO2_LIV4', 'Burgershot')
AddTextEntry('TACO2_LIV5', 'Homebrew Cafe')
AddTextEntry('TACO2_LIV6', 'Bahama Mamas')
AddTextEntry('TACO2_LIV7', 'Pearls')
AddTextEntry('TACO2_LIV8', "Boo's Catfe")
AddTextEntry('TACO2_LIV9', 'The Belmont')
AddTextEntry('TENF_LIVERY11', 'Beechwood Disciples')
AddTextEntry('TENF_LIVERY12', 'Petrovich Cartel')
AddTextEntry('TENF_LIVERY13', 'Los Santos Tuners')
AddTextEntry('TENF_LIVERY14', 'Los Santos Tuners')
AddTextEntry('TENF_LIVERY15', 'CMC')
AddTextEntry('TENF_LIVERY16', 'Mutiny Mob')
AddTextEntry('TENF_LIVERY17', 'Rebels')
AddTextEntry('TENF_LIVERY18', 'LSF')
AddTextEntry('TENF2_LIVERY17', 'Beechwood Disciples')
AddTextEntry('TENF2_LIVERY18', 'Petrovich Cartel')
AddTextEntry('TENF2_LIVERY19', 'Los Santos Tuners')
AddTextEntry('TENF2_LIVERY20', 'Los Santos Tuners')
AddTextEntry('TENF2_LIVERY21', 'CMC')
AddTextEntry('TENF2_LIVERY22', 'Mutiny Mob')
AddTextEntry('TENF2_LIVERY23', 'Rebels')
AddTextEntry('TENF2_LIVERY24', 'LSF')
AddTextEntry('TENF2_LIVERY25', 'TCF')
AddTextEntry('TFDOM9_LIVERY1', 'Los Santos Customs') -- Domgtcoupe
AddTextEntry('TFDOM9_LIVERY2', 'Los Santos Customs w/ Glass') -- Domgtcoupe
AddTextEntry('TFDOM9_LIVERY3', 'Vapid Performance') -- Domgtcoupe
AddTextEntry('TFDOM9_LIVERY4', 'White Double Pinstripe')
AddTextEntry('TFDOM9_LIVERY5', 'Yellow Double Pinstripe')
AddTextEntry('TFDOM9_LIVERY6', 'Blue Double Pinstripe')
AddTextEntry('TFDOM9_LIVERY7', 'PoPo')
AddTextEntry('TGAIT2_LIV16', 'BSG')
AddTextEntry('TGAIT2_LIV17', 'D8')
AddTextEntry('TULA_LIVERY9', 'Blath Alainn Charity')
AddTextEntry('TULIP_LIV11', 'High Desert')
AddTextEntry('VDO2_LIV_10', 'TequiLaLa')
AddTextEntry('VTC_LIV16', 'Burgershot')
AddTextEntry('VIGR2_LIV_12', 'Forum Families')
AddTextEntry('VIGR2_LIV_13', 'Beechwood Disciples')
AddTextEntry('VIGR2_LIV_14', 'Vagos')
AddTextEntry('VIGR2_LIV_15', 'Rebels')
AddTextEntry('VIG3_LIV_19', 'Beechwood Disciples')
AddTextEntry('VIG3_LIV_20', 'Cinco De Mayo')
AddTextEntry('VIS_LIV9', 'Burgershot')
AddTextEntry('VIVA_LIV1', 'Forum Families')
AddTextEntry('VSTR_LIVERY11', 'Vendetta')
AddTextEntry('WARR2_LIV16', 'Pump and Dump Halloween')
AddTextEntry('WEEVIL_LIV16', 'Cool Beans')
AddTextEntry('WNK_LIV30', 'Blath Alainn Charity')
AddTextEntry('YOS3_LIV19', 'The Boat House') -- yosemite3
AddTextEntry('YOS3_LIV20', 'Angels Autocare') -- yosemite3
AddTextEntry('YOSEM_LIV11', 'Yellow Jack') -- yosemite
AddTextEntry('YOUGA2_LIV13', 'Galaxy Nightclub')
AddTextEntry('YOUGA2_LIV14', '24/7 Supermarket')
AddTextEntry('YOUGA2_LIV15', "Joe's Corner")
AddTextEntry('YOUGA2_LIV16', 'Zoobies')
AddTextEntry('YOUGA2_LIV17', 'Ballas')
AddTextEntry('YOUGA3_LIV17', 'Trojan Armor')
AddTextEntry('YOUGA3_LIV18', 'Crackistani Whiskey')
AddTextEntry('YOUGA3_LIV19', 'Best Buds')
AddTextEntry('YOUGA3_LIV20', 'Rogers Salvage and Scrap')
AddTextEntry('YOUGA3_LIV21', 'Odd Jobs')
AddTextEntry('YOUGA3_LIV22', 'Ballas')
AddTextEntry('YOUGA3_LIV23', 'TequiLaLa')
AddTextEntry('YOUGA3_LIV24', 'Thomson Scrapyard')
AddTextEntry('YOUGA3_LIV25', 'Big Hawk Candy Van')
AddTextEntry('YOUGA3_LIV26', 'Flywheels')
AddTextEntry('YOUGA3_LIV27', 'Rum Runners')
AddTextEntry('YOUGA3_LIV28', 'Wayward Tattoos')
AddTextEntry('ZR350_LIV16', 'Yakuza')
AddTextEntry('zr390_liv1', 'Gearheads')


AddTextEntry('nriata_livery33', 'BCSO Supporter')

local function hasLiveryFlag(flag)
  return livery_flags & flag ~= 0
end

custom_liveries = {
  [`aleutian`] = {
    [10] = 'The Vault',
    [11] = 'AlphaMail',
  },

  [`asbo`] = {
    [10] = 'Homebrew Cafe',
    [11] = 'Homebrew Cafe',
  },

  [`avarus`] = {
    [18] = 'TequiLaLa',
  },

  [`baller7`] = {
    [13] = 'Omerta Advertising',
  },

  [`baller8`] = {
    [12] = 'Specter & Associates',
    [13] = 'The Unknown',
  },

  [`benson2`] = {
    [13] = 'Yellers Market', -- 24/7
    [14] = 'Burgershot',
    [15] = 'Blacklisted', -- Ballas Alco Holla
    [16] = 'Taqueria De Los Santos',
    [17] = 'Burgershot',
    [18] = 'Cool Beans',
    [19] = "Boo's Catfe",
    [20] = 'TequiLaLa',
    [21] = 'Vanilla Unicorn',
    [22] = 'Chihuahua Hotdogs',
    [23] = 'Pearls',
    [24] = 'Nice Dreams Ice Cream',
    [25] = 'Chihuahua Hotdogs',
  },

  [`boor`] = {
    [8] = 'FIB',
    [14] = 'Camp Morningwood',
    [15] = 'Camp Morningwood',
    [16] = 'Camp Morningwood',
    [17] = 'Camp Morningwood',
    [18] = 'Camp Morningwood',
    [19] = 'Camp Morningwood',
  },

  [`brisket`] = {
    [26] = 'Chihuahua Hotdogs',
    [27] = 'Westwoods',
  },

  [`buccaneer2`] = {
    [10] = 'Ballas',
  },

  [`buffalo4a`] = {
    [12] = 'Beechwood Disciples',
    [13] = 'Mutiny Mob',
    [14] = 'Ikonz',
    [15] = 'Rebels',
    [16] = '305 Mafia',
    [17] = "Fratelli d'Schnazy",
    [18] = '305 Mafia',
    [19] = 'Blacklisted', -- Condemed MC
    [20] = 'The Chosen Few Mc',
    [21] = 'Sinner St. Crip',
    [22] = 'Los Miradz',
    [23] = 'Forum Family',
    [24] = 'BSG',
    [25] = 'Misfitz',
    [26] = 'The Continental',
    [27] = 'The Unknown',
  },

  [`buffalo5`] = {
    [10] = 'Beechwood Disciples',
  },

  [`calicoa`] = {
    [15] = '305 Mafia',
    [16] = 'OTF',
  },

  [`caracara2`] = {
    [10] = 'Alamo Sea Pirates',
    [11] = 'Blaine County Hawks',
    [12] = 'Trojan Armor',
    [13] = 'Scorpions',
  },

  [`cavalcade3`] = {
    [10] = 'Dynasty 8',
    [11] = 'East Side Armor',
    [12] = 'Vendetta',
    [13] = 'Messina Crime Family',
    [14] = 'Blacklist', -- The Vault black list
    [15] = 'Obsidian Tattoos',
    [16] = 'Gallahan & Co Attorneys at Law',
  },

  [`cheburek`] = {
    [10] = function()
      return hasLiveryFlag(LIVERY_YANKTON_2022)
    end,
  },

  [`chino2`] = {
    [9] = 'OTF',
  },

  [`cinquemilaa`] = {
    [10] = 'Blath Alainn Charity',
  },

  [`comet6`] = {
    [15] = 'Finders Keepers',
  },

  [`comet7`] = {
    [11] = 'SC Design',
    [12] = 'SC Design',
  },

  [`coquette4`] = {
    [11] = 'Vice Motorsports',
  },

  [`cypher`] = {
    [15] = 'Blacklisted', -- Former Exiled
    [16] = 'Messina Crime Family',
    [17] = 'OTF',
    [18] = 'El Rancho Ravens',
    [19] = 'The Vault',
  },

  [`cypherwb`] = {
    [15] = 'Blacklisted', -- Former Exiled
    [16] = 'Messina Crime Family',
    [17] = 'OTF',
    [18] = 'El Rancho Ravens',
    [19] = 'The Vault',
  },

  [`dominator7`] = {
    [15] = 'Los Santos Customs',
  },

  [`dominator8`] = {
    [15] = 'Blaine County Hawks',
  },

  [`domgtcoupe`] = {
    [0] = 'Los Santos Customs',
    [1] = 'Los Santos Customs', -- livery with glass overlay
   -- [2] = open Vapid Performance
  },

  [`drafter`] = {
    [10] = 'Vanilla Unicorn',
    [11] = 'Blacklisted-Rascals',
    [12] = 'Beechwood Disciples',
    [13] = '305 Mafia',
    [14] = 'Mutiny Mob',
    [15] = 'Pyrite Blood Family',
    [16] = 'Forum Family',
    [17] = 'NOISEBOMB',
  },

  [`draftgpr`] = {
    [14] = 'Vanilla Unicorn',
    [15] = 'Blacklisted-Rascals',
    [16] = 'Beechwood Disciples',
    [17] = '305 Mafia',
    [18] = 'Mutiny Mob',
    [19] = 'Pyrite Blood Family',
    [20] = 'Forum Family',
    [21] = 'NOISEBOMB',
  },

  [`dynasty`] = {
    [10] = 'Big Hawk Taxi',
  },

  [`eudora`] = {
    [12] = 'Big Hawk Taxi', 
  },

  [`elegy`] = {
    [28] = 'SAHP_Internal',
    --[9] - [40] Open liverys
  },

  [`elegyrh5`] = {
    [28] = 'SAHP_Internal',
  },

  [`elegyrh7`] = {
    [19] = 'Cobras',
    [20] = 'Midnight Club',
    [21] = 'admin', -- LSC NY 2025 1/1
  },

  [`everon`] = {
    [10] = 'Thomson Scrapyard',
  },

  [`everon2`] = {
    [29] = 'Vice Motorsports',
  },

  [`faction2`] = {
    [9] = 'Ballas',
    [10] = 'Ballas',
  },

  [`faction3`] = {
    [9] = 'Go Truck Yourself',
    [10] = 'Ballas',
    [11] = 'Ballas',
  },

  [`fr36`] = {
    [16] = 'Ballas',
    [17] = 'Misfitz',
  },

  [`freecrawler`] = {
    [8] = 'Mikes Sporting Goods',
  },

  [`gargoyle`] = {
    [2] = 'Blacklisted', -- Former Sinister Sons
    [3] = 'Lost MC',
    [4] = 'Blacklisted', -- Condemed MC
  },

  [`gauntlet4`] = {
    [12] = 'Los Santos Customs',
    [13] = 'Fort Zancudo',
  },

  [`gauntlet6`] = {
    [30] = 'Vice Motorsports',
  },

  [`gauntletc`] = {
    [12] = 'Blacklisted-Rascals',
    [13] = 'Vagos',
  },

  [`granger2`] = {
    [12] = 'Thompson & Associates Law',
    [13] = 'Vanilla Unicorn',
  },

  [`gbimpaler`] = {
    [16] = 'fib'
  },

  [`hellion`] = {
    [10] = 'Blaine County Hawks',
  },

  [`hermes`] = {
    [10] = 'TequiLaLa',
  },

  [`hotring`] = {
    [31] = 'Blaine County Hawks',
    [32] = 'Sandy Car Wash',
    [33] = 'Mikes Sporting Goods',
    [34] = 'Thomson Scrapyard',
    [35] = 'Yellers Market',
    [36] = 'Joes Corner',
    [37] = 'Joes Corner',
    [38] = '305 Autobody',
    [39] = 'Yellow Jack',
    [40] = 'Los Santos Customs',
    [41] = 'LSFD_OffDuty',
    [42] = 'The Boathouse',
    [43] = 'Yellers Market',
    [44] = 'Flywheels',
    [45] = 'PDM Auto',
    [46] = 'PDM Auto',
    [47] = 'Dynasty 8',
    [48] = 'Mikes Sporting Goods',
    [49] = 'Vice Motorsports',
    [50] = 'Rocky Road Towing',
    [51] = 'Rocky Road Towing',
    [52] = 'Go Truck Yourself',
    [53] = 'Chihuahua Hotdogs',
    [54] = 'Finders Keepers',
    [55] = 'Thompson & Associates Law',
    [56] = 'MK Textures',
    [57] = 'The Vault',
    [58] = 'Reapers Scrapyard',
    [59] = 'Los Santos Customs',
  },

  [`hustler`] = {
    [10] = 'Rum Runners',
    [11] = 'TequiLaLa',
  },

  [`imperial`] = {
    [6] = 'AlphaMail',
    [9] = 'Weazel News',
    [10] = 'Rocky Road Towing',
    [11] = 'Pearls',
    [12] = 'Forum Family',
    [13] = 'Chihuahua Hotdogs',
    [14] = "Boo's Catfe",
    [15] = 'Spellbound Occult',
    [16] = 'Cool Beans',
    [17] = 'Urban Shootz',
    [18] = 'Dump and Pump Septic',
    [19] = 'Trojan Armor',
    [20] = 'Blaine County Hawks',
    [21] = 'Vynchenzo\'s Distillery',
    [22] = 'Weazel News',
    [23] = 'Big Hawk Taxi',
    [25] = 'The Kush Korner',
    [26] = 'Go Truck Yourself',
  },

  [`imperialpas`] = {
    [5] = 'Big Hawk Taxi',
  },

  [`impaler5`] = {
    [12] = 'Ballas',
  },

  [`impaler6`] = {
    [10] = 'Ballas',
  },

  [`issi3`] = {
    [10] = 'Burgershot',
  },

  [`issi7`] = {
    [10] = "Boo's Catfe"
  },

  [`issi8`] = {
    [10] = 'Cool Beans'
  },

  [`italigto`] = {
    [11] = 'PDM Auto',
    [12] = 'Vendetta',
  },

  [`iwagen`] = {
    [10] = 'Omerta Advertising',
  },

  [`jackgpr`] = {
    [6] = 'OTF',
  },

  [`jester4`] = {
    [15] = 'Blacklisted', --paintball livery locked as its a 1 of 1
    --[16] = open livery B & W Roses
    [17] = 'PDM Auto',
  },

  [`jestgpr`] = {
    [17] = 'Rugrats',
    [18] = 'Hayes Auto',
    [19] = 'The Founders',
    [20] = 'Blacklisted', --paintball livery locked as its a 1 of 1
    --[21] = open livery B & W Roses
    [22] = 'PDM Auto',
  },

  [`jugulara`] = {
    [10] = 'Blacklisted', -- Old OTF livery
    [11] = 'Misfitz',
    [12] = 'Asian Market',
    [13] = 'Blacklisted', -- AOD Livery to be updated
    [14] = 'BSG',
    [15] = 'BSG',
  },

  [`kamacho`] = {
    [10] = 'Mikes Sporting Goods',
    [11] = 'Blacklisted', -- Condemed MC
  },

  [`komodaa`] = {
    [10] = 'Blacklisted', -- Former La Cosa Nostra
    [11] = 'Burgershot',
    [12] = 'Chihuahua Hotdogs',
    [13] = 'Petrovich Cartel',
    [14] = '305 Mafia',
    [15] = 'Los Miradz',
    [16] = 'Gearheads',
    [17] = 'The Continental',
  },

  [`krieger`] = {
    [10] = 'Mutiny Mob', -- mutiny mash 2 1 of 1
  },

  [`landstalker2`] = {
    [10] = 'Bahama Mamas',
  },

  [`lpbagger`] = {
    [0] = 'The Chosen Few Mc',
    [1] = 'Mongrels',
    [2] = 'Angels of Death',
    [3] = 'Lost MC',
  },

  [`lynx`] = {
    [2] = 'Joes Corner',
    -- 3 = Stickerbomb (public)
    -- 4 = Cherry Blossom (public)
  },

  [`lynxgpr`] = {
    [5] = '305 Mafia',
    [6] = 'Joes Corner',
    -- 7 = Stickerbomb (public)
    -- 8 = Cherry Blossom (public)
  },

  [`manana2`] = {
    [12] = 'Aztecas',
    [13] = 'Vagos',
  },

  [`mesar`] = {
    [3] = 'Finders Keepers',
  },

  [`mesaxl`] = {
    [5] = 'Paleto Diner',
  },

  [`monstrociti`] = {
    [10] = function()
      return hasLiveryFlag(LIVERY_CAYO_2023)
    end,
    [11] = function()
      return hasLiveryFlag(LIVERY_CAYO_2023)
    end,
    [12] = function()
      return hasLiveryFlag(LIVERY_CAYO_2023)
    end,
    [13] = function()
      return hasLiveryFlag(LIVERY_CAYO_2023)
    end,
    [14] = function()
      return hasLiveryFlag(LIVERY_CAYO_2023)
    end,
    [15] = 'Blath Alainn Charity',
    [16] = 'Blacklisted', -- Dump and Pump Septic 2025 livery
  },

  [`mule6`] = {
    [8] = 'Trojan Armor',
    [9] = 'Blacklisted', -- East Side Armor, mule7 only
    [10] = 'Blacklisted', -- Burgershot, mule7 only
    [11] = 'Thomson Scrapyard',
    [12] = 'Taqueria De Los Santos',
    [13] = 'Blacklisted', -- Condemed MC
    [14] = 'Go Truck Yourself',
    [15] = 'The Unknown',
    [16] = 'Dump and Pump Septic',
    [17] = 'Mikes Sporting Goods',
    [18] = 'Nice Dreams Ice Cream',
    [19] = 'Rocky Road Towing',
    [20] = 'Nice Dreams Ice Cream',
    [21] = 'Reapers Scrapyard',
    [22] = 'Weazel News',
    [23] = 'The Kush Korner',
    -- 24 = Happy Holidays (public)
    [25] = 'The Vault',
    [26] = 'Jenkins Farms',
  },

  [`mule7`] = {
    [8] = 'Trojan Armor',
    [9] = 'East Side Armor',
    [10] = 'Burgershot',
    [11] = 'Thomson Scrapyard',
    [12] = 'Taqueria De Los Santos',
    [13] = 'Blacklisted', -- Condemned Mining Company
    [14] = 'Go Truck Yourself',
    [15] = 'The Unknown',
    [16] = 'Dump and Pump Septic',
    [17] = 'Mikes Sporting Goods',
    [18] = 'Nice Dreams Ice Cream',
    [19] = 'Rocky Road Towing',
    [20] = 'Nice Dreams Ice Cream',
    [21] = 'Reapers Scrapyard',
    [22] = 'Weazel News',
    [23] = 'The Kush Korner',
    -- 24 = Happy Holidays (public)
    [25] = 'The Vault',
    [26] = 'Jenkins Farms',
  },

  [`muler`] = {
    [10] = 'Go Truck Yourself',
    [11] = 'Weazel News',
    [12] = 'Sheriff_Internal',
    [13] = 'Weazel News',
  },

  [`kurumaa`] = {
    [15] = 'Blacklisted-Rascals',
    [16] = 'Blacklisted', -- Condemed MC
    [17] = 'Misfitz',
  },

  [`nightblade`] = {
    [12] = 'Angels of Death',
  },

  [`omnisegt`] = {
    [15] = 'Huff & Associates',
    [16] = '305 Mafia',
    [17] = 'Specter & Associates',
    [18] = 'Dynasty 8',
  },

  [`paragon`] = {
    [12] = 'The Continental',
  },

  [`penumbra2`] = {
    [12] = 'Rebels',
    [13] = 'Triads',
  },

  [`peyote3`] = {
    [10] = 'Vagos',
  },

  [`previona`] = {
    [15] = 'Westwoods',
  },

  [`r300`] = {
    [10] = 'Beechwood Disciples',
    [11] = '305 Mafia',
    [12] = 'Rebels',
    [13] = 'OTF',
    [14] = 'Mutiny Mob',
    [15] = 'Yokai',
    [16] = 'Blacklist', -- One of One will need blacklisted after he puts it on
    [17] = 'Swim Team Racing',
    [18] = 'Westwoods',
    [19] = 'Misfitz',
    [20] = 'Gearheads',
    [21] = 'The Continental',
    [22] = 'MK Textures',
  },

  [`rebla`] = {
    [10] = 'The Belmont',
    [11] = '305 Mafia',
    [12] = 'Misfitz',
    [13] = 'Dynasty 8',
  },

  [`rhineharta`] = {
    [12] = 'BSG',
    [13] = 'The Unknown',
    [14] = 'Joes Corner',
  },

  [`ruiner4`] = {
    [10] = 'Scorpions',
  },

  [`rt3000`] = {
    [15] = function()
      return hasLiveryFlag(LIVERY_VDAY_2025)
    end,
  },

  [`sandstorm`] = {
    [29] = 'San Andreas Government',
    [30] = 'Go Truck Yourself',
    [31] = 'Dump and Pump Septic',
    [32] = 'Mikes Sporting Goods',
    [33] = 'The Chosen Few Mc',
  },

  [`schlagen`] = {
    [10] = 'Pearls',
    [11] = 'The Unknown',
    [12] = 'Misfitz',
  },

  [`seminole2`] = {
    [10] = 'Dump and Pump Septic',
  },

  [`sentinel`] = {
    [12] = 'Midnight Club',
  },

  [`sentinel4`]= {
    [14] = 'Messina Crime Family',
  },

  [`shinobi`] = {
    -- 10 = Cherry Blossom (public)
    -- 11 = Dragons (public)
    [12] = 'Asian Market',
    [13] = "Boo's Catfe",
    [14] = 'Misfitz',
  },

  [`slamvan3`] = {
    [9] = 'Yellow Jack',
  },

  [`streamer216x`] = {
    [10] = 'Rum Runners',
    [11] = 'LEO_OffDuty',
  },

  [`sugoia`] = {
    [10] = 'Vice Motorsports',
  },

  [`sultan2`] = {
    [10] = 'Messina Crime Family',
    --[11] -- [30] (public liverys)
    [31] = 'Asian Market',
  },

  [`sultanrs`] = {
    [8] = 'Los Santos Customs',
    --[9] -- [30] (public liverys)
  },

  [`surfer3`] = {
    [13] = 'Nice Dreams Ice Cream',
  },

  [`swinger`] = {
    -- public frank livery
  },

  [`taco2`] = {
    [0] = { 'Yellow Jack', 1 },
    [1] = { 'Chihuahua Hotdogs', 1 },
    [2] = { 'Vanilla Unicorn', 1 },
    [3] = { 'Burgershot', 1 },
    [4] = { 'Homebrew Cafe', 1 },
    [5] = { 'Bahama Mamas', 1 },
    [6] = { 'Pearls', 1 },
    [7] = { "Boo's Catfe", 1 },
    [8] = { 'The Belmont', 1 },
  },

  [`tailgater2a`] = {
    [15] = 'BSG',
    [16] = 'Dynasty 8',
  },

  [`tenf`] = {
    [10] = 'Beechwood Disciples',
    [11] = 'Petrovich Cartel',
    [12] = 'Los Santos Tuners',
    [13] = 'Los Santos Tuners',
    [14] = 'Blacklisted', -- Condemed MC
    [15] = 'Mutiny Mob',
    [16] = 'Rebels',
    [17] = 'OTF',
    [18] = 'The Chosen Few Mc',
  },

  [`tenf2`] = {
    [16] = 'Beechwood Disciples',
    [17] = 'Petrovich Cartel',
    [18] = 'Los Santos Tuners',
    [19] = 'Los Santos Tuners',
    [20] = 'Blacklisted', -- Condemed MC
    [21] = 'Mutiny Mob',
    [22] = 'Rebels',
    [23] = 'OTF',
    [24] = 'The Chosen Few Mc',
  },

  [`terminus`] = {
    [10] = 'Delta Kappa Nu',
    [11] = 'Mikes Sporting Goods',
    [12] = 'Westside Shootz',
    [13] = 'Los Miradz',
    [14] = 'BSG',
    [15] = 'The Chosen Few Mc',
    [16] = 'Lost MC',
  },

  [`tula`] = {
    [8] = 'Blath Alainn Charity',
  },

  [`tulip`] = {
    -- 10: High Desert (public)
  },

  [`vectrea`] = {
    [15] = 'Burgershot',
  },

  [`vigero2`] = {
    [11] = 'Forum Family',
    [12] = 'Beechwood Disciples',
    [13] = 'Vagos',
    [14] = 'Rebels'
  },

  [`vigero3`] = {
    [18] = 'Beechwood Disciples',
    [19] = function()
      return hasLiveryFlag(LIVERY_CINCO_2025)
    end,
  },

  [`visione`] = {
    [8] = 'Burgershot',
  },

  [`vivanite`] = {
    [0] = 'Forum Family',
  },

  [`voodoo`] = {
    [9] = 'TequiLaLa',
  },

  [`vstr`] = {
    [10] = 'Vendetta',
  },

  [`warrener2`] = {
    [15] = 'Blacklist' -- Dump and Pump Septic 1 of 1
  },

  [`weevil`] = {
    [15] = 'Cool Beans',
  },

  [`winky`] = {
    [29] = 'Blath Alainn Charity',
  },

  [`winky2`] = {
    [29] = 'Blath Alainn Charity',
  },

  [`yosemite`] = {
    [10] = 'Yellow Jack',
  },

  [`yosemite3`] = {
    [18] = 'The Boathouse',
    [19] = 'Angels Autocare',
  },

  [`youga2`] = {
    [6] = 'Chihuahua Hotdogs',
    [12] = 'Blacklisted',
    [13] = 'Yellers Market',
    [14] = 'Joes Corner',
    [15] = 'Zoobies',
    [16] = 'Ballas',
  },

  [`youga3`] = {
    [16] = 'Trojan Armor',
    [17] = 'Rufus Inc',
    [18] = 'Best Buds',
    [19] = 'Blacklisted', -- Former Rogers Salvage and Scrap
    [20] = 'Finders Keepers',
    [21] = 'Ballas',
    [22] = 'TequiLaLa',
    [23] = 'Thomson Scrapyard',
    [24] = 'Hawk Militia',
    [25] = 'Flywheels',
    [26] = 'Rum Runners',
    [27] = 'Obsidian Tattoos',
  },

  [`zr350`] = {
    [15] = 'Asian Market',
  },

  [`zrgpr`] = {
    [20] = 'Asian Market',
  },

  [`nriata`] = {
    [32] = 'Blaine County Sheriffs Office',
  },

  [`zr390`] = {
    [2] = 'Gearheads',
  },
}
