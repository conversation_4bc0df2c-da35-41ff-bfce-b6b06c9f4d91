local playerItemsCount = {}
local cachedDefaultMixers = Deepcopy(Config.MixerList)

local function deleteNetEntity(netId)
  if not netId then return false end
  local ent = NetworkGetEntityFromNetworkId(netId)
  if not ent or ent == 0 then return false end

  if DoesEntityExist(ent) then
    local ok, err = pcall(function()
      local st = Entity(ent).state
      if st then st:set('mixer_id', nil, true) end
    end)
    if not ok then
      print('[xdiskjockey_beta] state clear failed:', err)
    end

    DeleteEntity(ent)
    return true
  end

  return false
end

local function hasActiveMixerFor(identifier)
  local entry = Config.MixerList[identifier]
  if not entry then return false end

  if entry.net_id and entry.net_id ~= 0 then
    local ent = NetworkGetEntityFromNetworkId(entry.net_id)
    if ent and ent ~= 0 and DoesEntityExist(ent) then
      return true
    else
      Config.MixerList[identifier] = nil
      TriggerClientEvent("xdiskjockey_beta:removeMixerCache", -1, identifier)
      return false
    end
  end

  return true
end

--(item_id, item_name, weight, choices, recipe, extra)
exports.blrp_core:RegisterItem('dj_deck', Config.ItemName, 5, {
  {
    name = 'Use',
    callback = function(character, item_id)
      local source = character.source
      local player = SharedObject.GetPlayerFromId(source)
      local model = Config.MixerPlaceObject or 'h4_prop_battle_dj_deck_01a_a'

      if hasActiveMixerFor(player.identifier) then
        character.notify('You already have a mixer out.')
        return
      end

      local success, status, rd = exports.blrp_core:SyncedPropsPrespawn(source, model, {
        ground_properly = true,
        freeze = true,
      })
      if not success then
        character.notify('Placement cancelled or failed: ' .. tostring(status))
        return
      end

      player.removeInventoryItem(character, 'dj_deck', 1)

      local idKey = player.identifier
      local data = {
        mixer = { { pos = rd.coords, heading = rd.heading } },
        identifier = idKey,
        net_id = rd.network_id,
      }

      local ent = NetworkGetEntityFromNetworkId(rd.network_id)
      if ent and ent ~= 0 and DoesEntityExist(ent) then
        Entity(ent).state:set('mixer_id', idKey, true)
      end

      Config.MixerList[idKey] = data
      TriggerClientEvent("xdiskjockey_beta:playPickAnim", source)
      Wait(800)
      TriggerClientEvent("xdiskjockey_beta:addMixerToCache", -1, data, idKey)
    end
  }
}, false, { category = 'dj' })

-- exports.blrp_core:RegisterItem('dj_deck_new' -- Will add mixer with decay at another time

RegisterNetEvent("xdiskjockey_beta:fetchCachedPlayerMixers", function()
    local playerPlacedMixers = Deepcopy(Config.MixerList)

    for k, v in pairs(cachedDefaultMixers) do
        playerPlacedMixers[k] = nil
    end

    TriggerClientEvent("xdiskjockey_beta:fetchCachedPlayerMixers", source, playerPlacedMixers)
end)

RegisterNetEvent("xdiskjockey_beta:pickupMixer", function(mixerData)
  local source = source
  local player = SharedObject.GetPlayerFromId(source)
  local character = exports.blrp_core:character(source)

  if (character.get('phone')) ~= mixerData.identifier and not character.hasGroup('staff') then
    character.notify('This doesn\'t belong to you')
    return
  end

  if (character.get('phone')) ~= mixerData.identifier and character.hasGroup('staff') and not character.request('Use your staff powers to pick up this deck?', 30) then
    character.notify('This doesn\'t belong to you')
    return
  end

  local isNew = mixerData.is_new == true
  local idKey = mixerData.identifier .. (isNew and "_new" or "")

  if isNew then
    if not character.hasRoomFor('dj_deck_new', 1) then
      character.notify('You can\'t carry this right now.')
      return
    end
  else
    if not character.hasRoomFor('dj_deck', 1) then
      character.notify('You can\'t carry this right now.')
      return
    end
  end

  local netId = mixerData.net_id
  if not netId and Config.MixerList[idKey] then
    netId = Config.MixerList[idKey].net_id
  end
  deleteNetEntity(netId)

  Config.MixerList[idKey] = nil
  TriggerClientEvent("xdiskjockey_beta:removeMixerCache", -1, idKey)
  TriggerEvent("xdiskjockey_beta:stopAllMusic", mixerData)

  if isNew then
    local now = os.time()
    local remaining = math.max(0, (mixerData.expireAt or now) - now)

    local returned_id = player.addInventoryItem(character, 'dj_deck_new', 1)
    if returned_id then
      modifyItemMeta(source, returned_id, 'dur_cur', remaining)
    else
      local found_id = character.hasGetItemMetaWithProperty
        and select(2, character.hasGetItemMetaWithProperty('dj_deck_new', function() return true end))
      if found_id then
        modifyItemMeta(source, found_id, 'dur_cur', remaining)
      end
    end

    character.notify(('Packed up the DJ Mixer. %d seconds remaining.'):format(remaining))
  else
    player.addInventoryItem(character, 'dj_deck', 1)
  end
end)