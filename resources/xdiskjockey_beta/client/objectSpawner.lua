AddEventHandler("onResourceStop", function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then
        return
    end
    for k, v in pairs(Config.SpawnMixerTable) do
        if DoesEntityExist(v.entity) then
            DeleteEntity(v.entity)
        end
    end

    for identifier, v in pairs(Config.MixerList) do
        for key, val in pairs(v.mixer) do
            if val.entity then
                DeleteEntity(val.entity)
            end
        end
    end
end)
