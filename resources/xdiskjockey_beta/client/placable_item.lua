local playerItemsCount = {}
local cachedDefaultMixers = Deepcopy(Config.MixerList)

local function EditMixerData(data)
  if not data or not data.mixer or not data.mixer[1] then return data end

  local m = data.mixer[1]

  data.speaker = data.speaker or {
    {
      pos = m.pos,
      distance = Config.PersonalMixerDistance or 20.0,
    },
  }

  m.distance = m.distance or (Config.PersonalMixerObjectDistance or 30.0)
  m.isPlayerMixer = true

  data.defaultVolume = data.defaultVolume or 0.5

  return data
end

local function deleteNetEntity(netId)
  if not netId then return false end
  local ent = NetworkGetEntityFromNetworkId(netId)
  if not ent or ent == 0 then return false end

  if DoesEntityExist(ent) then
    DeleteEntity(ent)
    return true
  end

  return false
end

RegisterNetEvent("xdiskjockey_beta:playPickAnim", function()
  PlayPickupAnim()
end)

RegisterNetEvent("xdiskjockey_beta:addMixerToCache", function(data, identifier)
    data = EditMixerData(data)

    Config.MixerList[identifier] = data
end)

RegisterNetEvent("xdiskjockey_beta:removeMixerCache", function(idKey)
  Config.MixerList[idKey] = nil
end)

RegisterNetEvent("xdiskjockey_beta:fetchCachedPlayerMixers", function(data)
  for k, v in pairs(data or {}) do
    Config.MixerList[k] = v
  end
end)