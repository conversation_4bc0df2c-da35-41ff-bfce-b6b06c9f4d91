local closestMarkerIndex = {}
local lastPos = vector3(0, 0, 0)
local closeToIntreactWithMixer = 2.0

CreateThread(function()
    lastPos = GetEntityCoords(PlayerPedId())
    while true do
        Wait(200)
        lastPos = GetEntityCoords(PlayerPedId())
    end
end, "Updating player cached position")

CreateThread(function()
    while true do
        Wait(200)
        closestMarkerIndex = {}
        for identifier, v in pairs(Config.MixerList or {}) do
            for key, val in pairs(v.mixer or {}) do
                if key == "jobs" then
                    print(identifier, "has wrong placement of `jobs` it has to be somewhere else. Please look at the unedited config.lua where to put it!")
                else
                    if not val.isPlayerMixer then
                        local inJob = true
                        val.identifier = identifier

                        if #(val.pos - lastPos) < (val.distance or 30.0) then
                            if v.jobs then
                                inJob = false
                                if IsPlayerAtJob(v.jobs) then
                                    inJob = true
                                    table.insert(closestMarkerIndex, val)
                                end
                            else
                                inJob = true
                                table.insert(closestMarkerIndex, val)
                            end
                        end

                        if inJob then
                            if #(val.pos - lastPos) < closeToIntreactWithMixer then
                                if not val.entered then
                                    val.entered = true
                                    ShowHelpNotification(_U("mixer_enter"))
                                end
                            else
                                if val.entered then
                                    if ActiveMixer then
                                        HideDiskjockeyUI()
                                    end
                                    val.entered = false
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end, "Checking nearby static/job mixers")

CreateThread(function()
    while true do
        Wait(0)
        if #closestMarkerIndex == 0 then
            Wait(1000)
        end

        for _, v in pairs(closestMarkerIndex) do
            if not v.isPlayerMixer then
                DrawMarker(25, v.pos.x, v.pos.y, v.pos.z - 0.9,
                    0.0, 0.0, 0.0, 0, 0.0, 0.0,
                    1.0, 1.0, 1.0, 255, 255, 255, 100,
                    false, true, 2, false, false, false, false)
            end
        end
    end
end, "Rendering markers for static/job mixers")

RegisterKey(function()
    if #closestMarkerIndex == 0 then return end
    local data = closestMarkerIndex[1]
    if data and #(data.pos - lastPos) < closeToIntreactWithMixer then
        ActiveMixer = data
        OpenDiskjockeyUI()
    end
end, "opendiskjockeyui", "Will open xdj UI", "E")

local function DrawText3D(coords, text)
    SetDrawOrigin(coords.x, coords.y, coords.z, 0)
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextCentre(true)
    BeginTextCommandDisplayText("STRING")
    AddTextComponentSubstringPlayerName(text)
    EndTextCommandDisplayText(0.0, 0.0)
    ClearDrawOrigin()
end

-- small spam guard for G pickup
local gBlock = false
local function blockG()
    if gBlock then return true end
    gBlock = true
    CreateThread(function()
        Wait(1500)
        gBlock = false
    end)
    return false
end

CreateThread(function()
    while true do
        Wait(0)

        local ped = PlayerPedId()
        local ppos = GetEntityCoords(ped)

        for idKey, v in pairs(Config.MixerList or {}) do
            local mix = v.mixer and v.mixer[1]
            if mix and mix.isPlayerMixer then
                local pos = mix.pos
                local ent
                if v.net_id and v.net_id ~= 0 then
                    ent = NetworkGetEntityFromNetworkId(v.net_id)
                    if ent and ent ~= 0 and DoesEntityExist(ent) then
                        pos = GetEntityCoords(ent)
                    end
                end

                --if v.__pendingRemoval then
                --  goto CONTINUE
                --end

                if not pos then goto CONTINUE end

                local dist = #(ppos - pos)
                if dist < 15.0 then

                    if dist < 2.0 then
                        -- prompt
                        DrawText3D(pos + vector3(0,0,1.0), "[E] Play / [G] Pick up")

                        -- E: open UI
                        if IsControlJustPressed(0, 38) then
                            local teleport
                            if ent and ent ~= 0 and DoesEntityExist(ent) then
                                local tpPos = GetOffsetFromEntityInWorldCoords(ent, Config.MixerObjectOffsetTeleport or vec3(0.0, -0.65, 0.0))
                                teleport = {
                                    pos = tpPos,
                                    heading = (mix.heading or 0.0) - (Config.OffsetHeadingForObjectTeleport or 0.0),
                                    animDict = Config.AnimDictMixerIdle,
                                    animClip = Config.AnimClipMixerIdle,
                                }
                            end

                            ActiveMixer = {
                                identifier = idKey,
                                isPlayerMixer = true,
                                teleportPlayer = teleport,
                            }
                            OpenDiskjockeyUI()
                        end

                        -- G: pick up
                        if IsControlJustPressed(0, 47) and not blockG() then
                            PlayPickupAnim()
                            Wait(800)
                            TriggerServerEvent("xdiskjockey_beta:pickupMixer", {
                                identifier = idKey,
                                is_new = v.is_new,
                                expireAt = v.expireAt,
                                net_id = v.net_id,
                            })
                            --v.__pendingRemoval = true
                        end
                    end
                end
            end
            ::CONTINUE::
        end
    end
end, "Portable mixers marker + interaction (E/G)")