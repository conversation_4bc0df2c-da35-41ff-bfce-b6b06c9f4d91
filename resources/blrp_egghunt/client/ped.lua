override_rabbit_visibility = false

exports.blrp_core:CreateStaticPed({
  id = 'Easter2025WaterTower',
  model = `a_m_y_acult_02`,
  coords = vector4(-1084.090, 4938.336, 229.221, 93.274),
  dict = 'amb@prop_human_bum_shopping_cart@male@idle_a',
  clip = 'idle_c',

  outfit = {
    [2] = {1, 0},
    [3] = {1, 1},
    [4] = {1, 1},
    [5] = {0, 0},
    [6] = {0, 0},
    [7] = {0, 0},
    [8] = {0, 0},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {0, 0},
    ['p0'] = {-1, 0},
    ['p1'] = {1, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  },
})

exports.blrp_core:CreateStaticPed({
  id = 'Easter2025Dancer01',
  model = `a_m_y_acult_01`,
  coords = vector4(-1137.985, 4936.641, 222.269, 198.992),
  dict = 'anim@amb@nightclub@mini@dance@dance_solo@male@var_b@',
  clip = 'high_center_down',

  outfit = {
    [2] = {0, 0},
    [3] = {0, 1},
    [4] = {1, 0},
    [5] = {0, 0},
    [6] = {0, 0},
    [7] = {0, 0},
    [8] = {1, 0},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {0, 0},
    ['p0'] = {-1, 0},
    ['p1'] = {-1, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  },
})

rabbit_ped = exports.blrp_core:CreateStaticPed({
  id = 'EasterRabbit01',
  quest = 'EasterRabbit01',
  model = `a_c_rabbit_02`,
  coords = vector4(-1170.751, 4926.867, 224.315, 262.273),
  outfit = {
    [0] = {0, 4},
  },

  visibility_check = function(entity)
    local flags = exports.blrp_core:me().get('flags_easter2025') or 0

    return override_rabbit_visibility or (flags & EGG_QUEST_2025_STAGES.STAGE_2 ~= 0)
  end
})

cultist_ped = exports.blrp_core:CreateStaticPed({
  id = 'EasterPerson01',
  quest = 'EasterPerson01',
  model = `A_M_O_ACult_01`,
  coords = vector4(-1144.647, 4908.683, 220.969, 30.199),
  dict = 'anim@mp_corona_idles@male_c@idle_a',
  clip = 'idle_a',
  outfit = {
    [0] = {0, 2},
    [1] = {0, 0},
    [2] = {0, 0},
    [3] = {1, 1},
    [4] = {1, 0},
    [5] = {0, 0},
    [6] = {0, 0},
    [7] = {0, 0},
    [8] = {0, 0},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {0, 0},
    ['p0'] = {-1, 0},
    ['p1'] = {-1, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
  },
})
