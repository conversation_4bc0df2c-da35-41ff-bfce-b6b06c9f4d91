fx_version 'cerulean'
games { 'gta5' }
author 'https://github.com/MOXHARTZ'
repository 'https://github.com/MOXHARTZ/mx-audioplayer'
discord 'https://discord.gg/crbtDw9hT7'
version '2.8.5'
lua54 'yes'

shared_scripts {
    'shared/*.lua'
}

client_scripts {
    -- copy of ox_lib's callback
    '@blrp_rpc/tunnel/client.lua',
    '@blrp_rpc/proxy/client.lua',

    '@mx-surround/client/callback.lua',
    'client/framework/*.lua',
    'client/*.lua',
    'client/addons/*.lua'
}
server_scripts {
    -- copy of ox_lib's callback
    '@blrp_rpc/tunnel/server.lua',
    '@blrp_rpc/proxy/server.lua',

    '@mx-surround/server/callback.lua',
    'server/framework/*.lua',
    'server/*.lua'
}

ui_page 'ui/build/index.html'
-- ui_page 'http://localhost:5173/' -- dev

files({
    'locales/*.json',
    'ui/build/index.html',
    'ui/build/**/*',
})

dependencies {
    '/onesync',
    'mx-surround'
}
