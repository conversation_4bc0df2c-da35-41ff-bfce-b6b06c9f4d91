{"name": "ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:game": "tsc && vite build --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@heroui/react": "^2.7.2", "@reduxjs/toolkit": "^2.6.0", "array-move": "^4.0.0", "classnames": "^2.5.1", "fast-memoize": "^2.5.2", "framer-motion": "^11.18.2", "i18next": "^24.2.2", "million": "^3.1.11", "motion": "^11.18.2", "path": "^0.12.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-easy-sort": "^1.6.0", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "react-toastify": "^10.0.6"}, "devDependencies": {"@types/node": "^22.13.5", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "vite": "^5.4.14"}}