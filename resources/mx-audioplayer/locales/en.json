{"header.title": "Title", "header.artist": "Artist", "header.playlist": "Playlist", "playlist.empty": "No songs in playlist", "playlist.not_selected": "No song selected", "playlist.deleted_songs": "Songs are deleted successfully", "playlist.cleared": "Playlist cleared successfully", "playlist.no_more_songs": "No more songs to play", "playlist.song_already_playing": "<PERSON> is already playing", "playlist.song_not_exist": "Song does not exist", "playlist.search": "Search…", "playlist.select_playlist": "First, select a playlist. You can create a playlist by right clicking the left side", "playlist.dialog.created": "Playlist created successfully", "playlist.dialog.name": "Name", "playlist.dialog.description": "Description", "playlist.dialog.image_url": "Image Url", "playlist.dialog.create": "Create a playlist", "playlist.dialog.edit": "Edit details", "playlist.context.edit": "Edit Details", "playlist.context.delete": "Delete Playlist", "playlist.context.create": "Create Playlist", "playlist.context.share": "Share Playlist To A Player", "playlist.context.actions": "Actions", "playlist.context.danger": "Danger Zone", "edit.clear": "Delete all", "edit.delete": "Delete Selected Songs", "edit.cancel": "Cancel", "search_track.content": "You can add a song to the playlist by entering a track name or you can pass here by a spotify, youtube or soundcloud url. For now, soundcloud playlists are not supported.", "search_track.title": "Add a song to the playlist", "search_track.placeholder": "Track name or url (supports playlist and album)", "search_track.invalid_url": "Invalid url", "search_track.knowledge": "You can't play songs with a song duration of more than 15 minutes (for performance reasons)", "general.unknown": "Unknown", "general.something_went_wrong": "Something went wrong", "general.ui.disabled": "At the moment you can't open the radio cause its using by another player.", "general.edit.enabled": "Edit mode is enabled. Now you can sort the playlist and songs by dragging", "general.cancel": "Cancel", "general.done": "Done", "general.delete.confirm": "Please click again delete to confirm.", "shared.title": "Nearby Players", "shared.no_players": "No one was found to share the playlist", "shared.success": "Playlist shared successfully", "playlist.already_exist": "You received a playlist from %s but you already have that playlist. So we don't add it to your playlists. Playlist Name: %s", "playlist.received": "You received a playlist from %s. Playlist Name: %s", "boombox.target.open": "Open", "boombox.target.destroy": "Destroy Boombox", "boombox.target.pickup": "Pickup Boombox", "boombox.drop.text": "Press [H] to drop the boombox", "dj.open.menu": "Press [E] to open the DJ menu", "dj.target.open": "Open UI", "radio.command": "Open Car Radio", "search_track.add_all": "Add all to playlist", "general.currently_playing": "Currently Playing", "general.play": "Play", "playlist.confirm.delete_all.title": "Are you sure you want to delete all songs?", "playlist.confirm.delete_all.content": "This action can't be undone", "modal.confirm": "Confirm", "modal.cancel": "Cancel", "settings.title": "Settings", "settings.minimal_hud": "Minimal HUD", "settings.minimal_hud.description": "When you activate it. You can manage the songs in your playlist with the shortcuts even when you close the menu. To learn the shortcuts, move your mouse to the play, forward and back buttons.", "settings.minimal_hud.position.title": "Minimal Hud Position", "settings.minimal_hud.position.top_left": "Top Left", "settings.minimal_hud.position.top_left_description": "The minimal hud will be placed on the top left corner of your screen", "settings.minimal_hud.position.top_right": "Top Right", "settings.minimal_hud.position.top_right_description": "The minimal hud will be placed on the top right corner of your screen", "settings.minimal_hud.position.bottom_left": "Bottom Left", "settings.minimal_hud.position.bottom_left_description": "The minimal hud will be placed on the bottom left corner of your screen", "settings.minimal_hud.position.bottom_right": "Bottom Right", "settings.minimal_hud.position.bottom_right_description": "The minimal hud will be placed on the bottom right corner of your screen", "keyboard.shortcut.toggle": "Play/Pause", "keyboard.shortcut.volume": "Volume Up/Down", "keyboard.shortcut.forward": "Previous/Next"}