{"header.title": "Titel", "header.artist": "<PERSON><PERSON><PERSON><PERSON>", "playlist.empty": "<PERSON><PERSON> in der Playlist", "playlist.not_selected": "keine Lieder ausgewählt", "playlist.deleted_songs": "Lieder wurden erfolgreich <PERSON>", "playlist.cleared": "Playlist <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playlist.no_more_songs": "<PERSON><PERSON> weiteren Lieder zum Abspielen", "playlist.song_already_playing": "Lied wird bereits gespielt", "playlist.song_not_exist": "Lied existiert nicht", "playlist.search": "<PERSON>e…", "playlist.select_playlist": "<PERSON><PERSON><PERSON><PERSON> Sie zu<PERSON>t eine Playlist. Sie können eine Playlist erstellen, indem Sie mit der rechten Maustaste auf die linke Seite klicken", "playlist.dialog.created": "Playlist er<PERSON><PERSON><PERSON><PERSON><PERSON> er<PERSON>", "playlist.dialog.name": "Name", "playlist.dialog.description": "Beschreibung", "playlist.dialog.image_url": "Bild-URL", "playlist.dialog.create": "<PERSON><PERSON><PERSON> eine Playlist", "playlist.dialog.edit": "Details bearbeiten", "playlist.context.edit": "Details bearbeiten", "playlist.context.delete": "Playlist <PERSON><PERSON><PERSON>", "playlist.context.create": "Playlist er<PERSON><PERSON>", "playlist.context.share": "Playlist e<PERSON><PERSON> teilen", "edit.clear": "Alles löschen", "edit.delete": "Ausgewählte Lieder löschen", "edit.cancel": "Abbrechen", "search_track.content": "Sie können ein Lied zur Playlist hinzufügen, indem Sie einen Liednamen eingeben, oder Sie können hier mit einer Spotify-, YouTube- oder SoundCloud-URL vorbeikommen", "search_track.title": "Füge der Playlist ein Lied hinzu", "search_track.placeholder": "Liedname oder URL", "search_track.invalid_url": "Ungültige URL", "search_track.knowledge": "<PERSON>e können keine Lieder mit einer Länge von mehr als 15 Minuten abspielen (aus Leistungsgründen)", "general.unknown": "Unbekannt", "general.something_went_wrong": "Etwas ist schiefgelaufen", "general.ui.disabled": "<PERSON><PERSON> können Sie das Radio nicht öffnen, da es von einem anderen Spieler verwendet wird.", "general.edit.enabled": "Bearbeitungsmodus ist aktiviert. Jetzt können Sie die Playlist und die Lieder durch Ziehen sortieren", "general.cancel": "Abbrechen", "general.done": "<PERSON><PERSON><PERSON>", "general.delete.confirm": "Bitte klicken Si<PERSON> erneut auf Löschen, um zu bestätigen.", "shared.title": "<PERSON><PERSON>", "shared.no_players": "<PERSON><PERSON><PERSON> wurde gefunden, um die Playlist zu teilen", "shared.success": "Playlist <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playlist.already_exist": "Sie haben eine Playlist von %s erhalten, aber Sie haben diese Playlist bereits. Deshalb fügen wir sie nicht zu Ihren Playlists hinzu. Playlist-Name: %s", "playlist.received": "<PERSON>e haben eine Playlist von %s erhalten. Playlist-Name: %s", "boombox.target.open": "<PERSON><PERSON><PERSON>", "boombox.target.destroy": "Boombox zerstören", "boombox.target.pickup": "Boombox aufheben", "boombox.drop.text": "<PERSON><PERSON><PERSON> [H], um die Boombox fallen zu lassen", "dj.open.menu": "<PERSON><PERSON><PERSON> [E], um das DJ-Menü zu öffnen", "dj.target.open": "Radio öffnen", "radio.command": "Auto-Radio öffnen", "header.playlist": "Wiedergabeliste", "playlist.confirm.delete_all.content": "Möchten Si<PERSON> wirklich alle Elemente in der Wiedergabeliste löschen?", "general.currently_playing": "Aktuell spielt", "playlist.confirm.delete_all.title": "Alle löschen bestätigen", "modal.cancel": "Abbrechen", "modal.confirm": "Bestätigen", "playlist.context.danger": "<PERSON><PERSON><PERSON><PERSON>", "search_track.add_all": "Alle hinzufügen", "playlist.context.actions": "Aktionen", "general.play": "Abspielen", "settings.title": "Einstellungen", "settings.minimal_hud": "Minimales HUD", "settings.minimal_hud.description": "<PERSON>n Sie es aktivieren, können Sie die Songs in Ihrer Playlist mit den Tastenkombinationen verwalten, sel<PERSON>t wenn Si<PERSON> das Menü schließen. Um die Tastenkombinationen zu lernen, bewegen Sie Ihre Maus über die Wiedergabe-, Vorwärts- und Zurück-Tasten.", "settings.minimal_hud.position.title": "Minimale HUD-Position", "settings.minimal_hud.position.top_left": "<PERSON><PERSON>s", "settings.minimal_hud.position.top_left_description": "Das minimale HUD wird in der oberen linken Ecke deines Bildschirms platziert", "settings.minimal_hud.position.top_right": "<PERSON><PERSON>", "settings.minimal_hud.position.top_right_description": "Das minimale HUD wird in der oberen rechten Ecke deines Bildschirms platziert", "settings.minimal_hud.position.bottom_left": "Unten Links", "settings.minimal_hud.position.bottom_left_description": "Das minimale HUD wird in der unteren linken Ecke deines Bildschirms platziert", "settings.minimal_hud.position.bottom_right": "Unten Rechts", "settings.minimal_hud.position.bottom_right_description": "Das minimale HUD wird in der unteren rechten Ecke deines Bildschirms platziert", "keyboard.shortcut.toggle": "Wiedergabe/Pause", "keyboard.shortcut.volume": "Lautstärke Lauter/Leiser", "keyboard.shortcut.forward": "Vorheriger/Nächster Titel"}