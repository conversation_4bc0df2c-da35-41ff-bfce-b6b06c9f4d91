

local player_inside = false
local last_vehicle = nil
local function MonitorBehavior()
  local checked_vehicle = false
  Citizen.CreateThread(function()
    while player_inside do
      if last_vehicle and DoesEntityExist(last_vehicle) then
        if tZones.isInsideMatchedZone('TongvaOutsidePoly',nil,nil,GetEntityCoords(last_vehicle)) then
          TriggerServerEvent('DrugLab:React:MoveCar')
          player_inside = false
        end
        checked_vehicle = true
      end

      if IsPedShooting(PlayerPedId()) or IsPlayerFreeAiming(PlayerPedId()) or IsPedArmed(PlayerPedId(),4) then
        if tZones.isInsideMatchedZone('TongvaInterior',nil,nil,GetEntityCoords(PlayerPedId())) then
          TriggerServerEvent('DrugLab:React:IsShooting')
          player_inside = false
        end
      end
      Citizen.Wait(1000)
    end
  end)
end

addPolyZone('TongvaInterior', {
  vector2(-1930.678, 1785.812), vector2(-1919.135, 1790.306), vector2(-1916.193, 1782.372), vector2(-1927.711, 1778.067),
}, 171.1, 174.2, {
  onPlayerInOut = function(inside)
    if inside then
      local banned = exports.blrp_core:LabBanCheck('Tongva')

      if banned then
        TriggerServerEvent('DrugLab:React:Banned')
        return
      end

      player_inside = true
      MonitorBehavior()
      return
    end

    if not inside then
      player_inside = false
    end
  end
})


addPolyZone('TongvaOutsidePoly', {
  vector2(-1941.800, 1790.363), vector2(-1912.083, 1799.912),vector2(-1899.534, 1780.655), vector2(-1933.990,1765.814)
}, 168.576, 175.060, {
  onPlayerInOut = function(inside)
    if inside then
     last_vehicle =  GetVehiclePedIsIn(PlayerPedId(), true)
    end
  end
})

