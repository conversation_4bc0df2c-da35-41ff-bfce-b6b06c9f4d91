pRadio = P.getInstance('pma-voice', 'radio')
pCore = P.getInstance('blrp_core', 'core')
local was_in_channel = false

addPolyZone('BurgerShotDriveThru', {
  vector2(-1203.554, -900.0757), vector2(-1207.551, -902.2229), vector2(-1212.191, -895.9799), vector2(-1207.694, -892.9394)
}, 12.0, 18.0, {
  onPlayerInOut = function(inside, zone_name)
    local me = exports.blrp_core:me()
    local clock_in_data = pCore.isClockedIntoTabletBusiness({ 'Burgershot' })

    if not me.hasGroup('Burgershot') or (me.hasGroup('Burgershot') and clock_in_data and not clock_in_data.clocked_in) then
      if inside then
        was_in_channel = LocalPlayer.state.radioChannel
        TriggerServerEvent('pma-voice:addPlayerToRadio', 10086)
      else
        TriggerServerEvent('pma-voice:removePlayerFromRadio', 10086)
        if was_in_channel then
          pRadio.forceTalkOnChannel({ was_in_channel })
        end
      end
    end
  end
})

addPolyZone('BurgerShotAutomaticRadio', {
  vector2(-1217.74, -889.6065), vector2(-1171.631, -859.2174), vector2(-1153.681, -893.1483), vector2(-1197.955, -918.702)
}, 12.0, 18.0, {
  onPlayerInOut = function(inside, zone_name)
    local me = exports.blrp_core:me()
    local clock_in_data = pCore.isClockedIntoTabletBusiness({ 'Burgershot' })

    if me.hasGroup('Burgershot') and clock_in_data and clock_in_data.clocked_in then
      if inside then
        TriggerServerEvent('pma-voice:addPlayerToRadio', 10086)
        me.notify('Added to Burger Shot Drive Thru Radio')
      else
        TriggerServerEvent('pma-voice:removePlayerFromRadio', 10086)
        me.notify('Removed from Burger Shot Drive Thru Radio')
      end
    end
  end
})