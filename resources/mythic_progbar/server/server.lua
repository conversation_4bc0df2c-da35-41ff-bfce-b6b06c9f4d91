storage = {}
tProgress = T.getInstance('mythic_progbar', 'progress')

-- server side usage of mythic_progbar with server callback
-- this is used as an export, just as the client version is used
function serverProgress(source, action, finish)
    local alreadyDoingSomething = tProgress.isDoingAction(source, {})

    if alreadyDoingSomething then
        return TriggerClientEvent('vrp:client:notify', source, 'You are already doing an action')
    end

    local randomizedId = math.random(999999)
    storage[randomizedId] = finish -- Assign callback to ID
    TriggerClientEvent('mythic_progbar:client:serverProcess', source, action, finish, randomizedId)
end

-- Hit the callback once the progress bar is done
RegisterServerEvent('mythic_progbar:server:ProgressFinished')
AddEventHandler("mythic_progbar:server:ProgressFinished", function(identifier, result)
    if storage[identifier] ~= nil then
        local callback = storage[identifier]
        callback(result)
        storage[identifier] = nil
    end
end)

