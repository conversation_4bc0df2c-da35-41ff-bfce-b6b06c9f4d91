resource_manifest_version '44febabe-d386-4d18-afbe-5e627f4af937'
resource_manifest_version '44febabe-d386-4d18-afbe-5e627f4af937'

name 'Mythic Framework Progress Bar'
author '<PERSON><PERSON> - https://github.com/Alzar'
version 'v1.0.1'

ui_page('html/index.html') 

client_scripts {
    '@blrp_rpc/proxy/client.lua',
    '@blrp_rpc/tunnel/client.lua',
    'client/functions.lua',
    'client/events.lua',
    'client/game.lua',
}

server_scripts {
    '@blrp_rpc/proxy/server.lua',
    '@blrp_rpc/tunnel/server.lua',
    'server/server.lua',
}

files {
    'html/index.html',
    'html/css/style.css',
    'html/js/script.js',

    'html/css/bootstrap.min.css',
    'html/js/jquery.min.js',
    'html/sounds/win.mp3',
}

exports {
    'Progress',
    'ProgressWithStartEvent',
    'ProgressWithTickEvent',
    'ProgressWithStartAndTick'
}

server_exports {
    'serverProgress',
}