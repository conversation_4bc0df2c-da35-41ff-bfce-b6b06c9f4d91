* {
    box-sizing: border-box;
	border-radius: 10px;
}

p {
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
}

.no-select {
    user-select: none;
}

body {
    font-family: 'Roboto', sans-serif;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-height: 80vh;


}

.zoomed-out {
    zoom: 0.6;
    transform: scale(0.6);
    -moz-transform: scale(0.6);
    transform-origin: 0 0;
    -moz-transform-origin: 0 0;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type=number] {
  appearance: textfield;
  -moz-appearance: textfield;
}

/* width */
::-webkit-scrollbar {
    width: 4px;

}

/* Track */
::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.0);
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;

}

.column {
    display: flex;
    flex-direction: column;
    /*flex-basis: 100%;*/
    /*flex: 1;*/
}

.slot-position {
    position: absolute;
    margin-left: 5px;
    padding: 10px;
    font-size: 25px;
    opacity: 80%;
    color: #10111d;


    /*display: flex;*/
    /*justify-content: center;*/
    /*align-items: center;*/
    /*align-content: center;*/
    /*align-self: center;*/
}

.column-spacer {
    width: 100px;
}

.help-area {
    position: absolute;
    top: 450px;
    margin-left: 14px;
}


.main-inventory {
    margin-top: 15px;

}

/*
.page1 {
    webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    border-radius: 15px;
}
*/

.page2 {
    padding-top: 19px;
    width: 1080px;
    position: relative;
    height: 660px;
    background-color: rgba(36, 38, 60, 0.8);
    color: whitesmoke;
    text-shadow: 0px 0px 4px black;
}

.return-button {
    width: 150px;
    float: right;
    padding: 5px 15px;
    font-size: 18px;
    background-color: rgba(36, 38, 60, 1);
    position: absolute;
    bottom: 25px;
    right: 25px;
    border-radius: 3px;
    border: deepskyblue 1px solid !important;
    box-shadow: 0px 0px 3px deepskyblue;
}

.interaction-area {
    -webkit-user-select: none;
    user-select: none;
    display: none;
    position: relative;
    max-height: 680px;
    border-radius: 15px;


}

.items {
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
}

.grid {
    padding-left: 8px;
    padding-bottom: 9px;
    padding-right: 1px;
    padding-top: 15px;
    border-radius: 5px;

}



.grid::before {
    content: "";
    position: absolute;
    background: inherit;
    z-index: -1;
    filter: blur(10px);

}

.grid-container {
    z-index: 2;
    position: relative;
    display: grid;
    grid-column-gap: 4px;
    grid-row-gap: 4px;
    grid-template-rows: repeat(1, 120px);
    grid-template-columns: repeat(5, 140px);/* 700 */

}

.grid-item {
    background-color: rgba(36, 38, 60, 0.8);
    height: 120px;
    width: 140px;
    box-shadow: 0px 0px 4px;
    border: 1px #252f4c solid;

}

.grid-item-being-dragged {
    background-color: rgba(0, 0, 0, 0.0) !important;
    box-shadow: 0px 0px 0px;
    border: 0px
}

.grid-item:hover {
    /*background-color: #1d1d1d;*/
    background-color: rgba(36, 38, 60, 0.3);
    border: deepskyblue 2px solid !important;
    box-shadow: 1px 1px 8px deepskyblue;
}

.grid-item-real:hover {
    background-color: rgba(36, 38, 60, 0.3);
    border: deepskyblue 2px solid !important;
    box-shadow: 1px 1px 8px deepskyblue;
}

.selected {
    background-color: rgba(36, 38, 60, 0.3);
    border: deepskyblue 2px solid !important;
}


.side-container {
    /*background-color: rgba(0, 0, 0, 0.3);*/
    border-radius: 5px;
}

.grid-container-hotbar {
    position: relative;
    display: grid;
    grid-column-gap: 40px;
    grid-row-gap: 10px;
    grid-template-columns: repeat(5, 120px);
    grid-template-rows: repeat(20, 140px);
}

.grid-container-inventory {
    box-shadow: 10px 10px;
}


/*#grid-container-inventory > .grid-first-five:nth-child(1) {*/
/*    background-color: rgba(16, 16, 16, 0.4);*/
/*}*/

/*#grid-container-inventory > .grid-first-five:nth-child(2) {*/
/*    background-color: rgba(16, 16, 16, 0.4);*/
/*}*/

/*#grid-container-inventory > .grid-first-five:nth-child(3) {*/
/*    background-color: rgba(16, 16, 16, 0.4);*/
/*}*/

/*#grid-container-inventory > .grid-first-five:nth-child(4) {*/
/*    background-color: rgba(16, 16, 16, 0.4);*/
/*}*/

/*#grid-container-inventory > .grid-first-five:nth-child(5) {*/
/*    background-color: rgba(16, 16, 16, 0.4);*/
/*}*/

.grid-item.item-blur .item-weight,
.grid-item.item-blur .item-image,
.grid-item.item-blur .item-name {
  filter: blur(10px);
}

.item-name {
    text-align: center;
    font-size: 13px;
    color: white;
    padding-bottom: 5px;
    padding-left: 3px;
    margin-top: 13px;
    text-shadow: 0px 0px 5px black;
}

.item-name-long {
    margin-top: 0 !important;
}

.item-name-durability {
  margin: auto !important;
  box-sizing: border-box;
}

.type-icon {
  height: 20px !important;
  width: 20px !important;
  top: -10049px !important;
  right: -10108px !important;
  filter: brightness(0.6) !important;
}

.item-image {
    /*margin-top: 16px;*/
    overflow: hidden;
    position: relative;
    height: 68px;
}

.item-image-long {
    height: 68px;
}



.item-image img {
    position: absolute;
    top: -9999px;
    bottom: -9999px;
    left: -9999px;
    right: -9999px;
    margin: auto;
    height: 68px;
}

img {
    background-color: transparent;
    vertical-align: center;
    text-align: center;
    align-content: center;
}


.actions {
    flex-direction: column;
    padding-left: 10px;
    padding-right: 10px;
    margin-top: 55px;
    color: whitesmoke;
    width: 200px;
    /*border-left: 1px #292929 solid;*/
    z-index: 1;
    background-image: none;
    display: block;
    justify-content: space-between;

}

.option-item {
    /*z-index: 5 !important;*/
    padding: 25px;
    border: 1px #252f4c solid;
    margin-top: 20px;
    background-color: rgba(36, 38, 60, 0.8);
    border-radius: 15px;
    box-shadow: 0px 0px 5px black;
    text-shadow: 0px 0px 4px black;

}

.option-item:hover {
    border: deepskyblue 1px solid !important;
    box-shadow: 1px 1px 8px deepskyblue;
    text-shadow: 0px 0px 4px black;
}

.give-cash-button {
    /*background-color: rgba(0, 0, 0, 0.78);*/
    z-index: 600 !important;
    padding: 5px;
    background-color: rgba(36, 38, 60, 0.8);
    font-size: 15px;
    width: 100%;
    height: 100%;
    border-radius: 3px;
    border: 1px #252f4c solid;
    box-shadow: 0px 0px 5px black;
    margin-bottom: 10%;
}

.give-cash-button:hover {
    border: deepskyblue 1px solid !important;
    box-shadow: 1px 1px 8px deepskyblue;
    text-shadow: 0px 0px 4px black;
}

.actions-area-logo {
    padding: 8px;
}

/*.option-properties:hover {*/
/*    background-color: #282828 !important;*/
/*}*/

.hover {
    background-color: #282828 !important;
}

.item-weight {
    display: flex;
    justify-content: space-between;
}

.span-name {
    font-size: 10px;
    text-transform: uppercase;
    text-align: center;
}

.span-weight {
    /*position: absolute;*/
    color: gray;
    font-size: 13px;
    padding-top: 3px;
    padding-right: 6px;
    text-shadow: 0px 0px 1px black;
}

.span-amount {
    position: relative;
    color: #c3c3c3;
    float: left;
    left: 7px;
    font-size: 15px;
    padding-top: 3px;
    padding-right: 4px;
    text-shadow: 0px 0px 1px black;
}

.span-price {
  position: relative;
  /* color: lawngreen; */
  float: left;
  left: 7px;
  font-size: 15px;
  padding-top: 3px;
  padding-right: 4px;
  text-shadow: 0px 0px 1px black;
}

.span-cash_on_hand {
    color: gray;
    float: right;
    font-size: 11px;
    padding-top: 3px;
    padding-left: 6px;
    padding-right: 4px;
}

.inventory-info {
    margin-top: 10px;
    font-size: 10px;
}

.item-progress-bar-outer {
  background-color: #131313;
  height: 4px;
  width: 100%;
}

.item-progress-bar {
  background-color: #ff0000;
  height: 100%;
  width: 0;

  -webkit-transition: width 200ms ease-in-out;
  -moz-transition: width 200ms ease-in-out;
  -o-transition: width 200ms ease-in-out;
  transition: width 200ms ease-in-out;
  border-radius: unset;
}

.br-l {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.br-r {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}

.container-progress-bar-outer {
    background-color: #131313;
    height: 4px;
    width: 100%;
    margin-bottom: 3px;
    /*border-radius: 5px;*/
}

.container-progress-bar {
    background-color: #ff0000;
    height: 100%;
    width: 0;
    box-shadow: 0px 0px 1px black;
    -webkit-transition: width 200ms ease-in-out;
    -moz-transition: width 200ms ease-in-out;
    -o-transition: width 200ms ease-in-out;
    transition: width 200ms ease-in-out;
}

.container-progress-text {
    color: whitesmoke;
    font-size: 15px;
    text-shadow: 0px 0px 3px black;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-left: 10px;
    margin-top: 4px;
    margin-bottom: 8px;
}

.quantity-label {
    color: gray;
    display: block;
    padding-bottom: 5px;
}

.quantity-input:hover {
    border: deepskyblue 2px solid !important;
    box-shadow: 1px 1px 4px deepskyblue;
    border-radius: 2px;
}

.quantity-input {
    height: 40px;
    color: white;
    text-align: center;
    background: #1d1d1d;
    width: 98.6%;
    border: 1px #252f4c solid;
    background-color: rgba(36, 38, 60, 0.8);
    outline: #1d1d1d 0px solid;
    border-radius: 3px;
    box-shadow: 0px 0px 4px black;
    text-shadow: 0px 0px 4px black;

}

.progress-area {
    margin-top: 5px;
    background-color: #323232;
    margin-bottom: 10px;
}

.progress-filled {
    background-color: #045f00;
}

.progress-text{
    text-shadow: 10px;
}

.auto-close-timer {
    margin-bottom: 9px;
    color: gray;
    font-size: 10px;
}



.ghosted {
    display: none;
    -webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
    filter: grayscale(100%);
}

.cash-area {
    font-size: 18px;
    text-shadow: 0px 0px 4px black;
}

.weight-area {
    margin-top: 5px;
    margin-bottom: 5px;
    padding: 5px;
    border-bottom: 1px solid #404040;
}

.cash-image {
    height: 16px;
}

.label-area {
    margin: 6px;
    /*text-align: right;*/
}

.label-area-storage {
    text-align: left !important;
}

.error-area {
    margin-top: 20px;
    font-size: 12px;
    color: orangered;
}

.inventory-meta-content {
    display: none;
    -webkit-user-select: all;
    user-select: all;
    margin-top: 15px;
    background-color: rgba(36, 38, 60, 0.8);
    border: 1px #252f4c solid;
    text-align: left;
    color: white;
    width: 920px;
    position: absolute;
    right: 500px;
    box-shadow: 0px 0px 4px black;
    text-shadow: 0px 0px 4px black;
}

/*.inventory-meta-wrapper {*/
/*    padding: 10px;*/
/*}*/

.meta-pair {
    padding: 10px;
    /*display: block;*/
}

.meta-value {
    color: #b5b5b5;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

.hotbar {
    margin-bottom: 10px;
    position:absolute;
    height:130px;
    bottom:0px;
    right:15%;
    left:50%;
    margin-left:-350px;
    overflow: hidden;
    /*border: 1px black solid;*/
}


/*.grid-item-hotbar {*/
/*    opacity: 0;*/
/*    !*background-color: rgba(0, 0, 0, 0.2);*!*/
/*    !*border-bottom-right-radius: 12px;*!*/
/*    !*border-bottom-left-radius: 12px;*!*/
/*    margin: 10px;*/
/*    width: 100px;*/
/*    height: 90px;*/
/*    -webkit-transition : opacity 1000ms ease-out;*/
/*    -moz-transition : opacity 1000ms ease-out;*/
/*    -o-transition : opacity 1000ms ease-out;*/
/*    transition : opacity 1000ms ease-out;*/
/*}*/

.grid-item-hotbar:hover {
    background-color: #282828 !important;
}

.selected-hotbar {
    opacity: 100%;
    /*border-bottom: 2px solid #0054b0;*/
}

.inventory-guide {
    display: none;
}

.drug-color {
  display: inline-block;
  height: 20px;
  width: 20px;
  border-radius: unset;
  border: 1px solid black;
}
