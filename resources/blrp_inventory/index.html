<!DOCTYPE html>
<html lang="en">
<head>
    <title>BLRP Inventory</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script src="js/vendor/jquery-3.5.1.min.js"></script>
    <script src="js/vendor/jquery-ui.min.js"></script>
    <script src="js/vendor/w3color.js"></script>
    <link href="css/roboto.css" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">

    <!-- Modules -->
<!--    <script type="module" src="js/components/Grid.mjs"></script>-->
<!--    <script type="module" src="js/components/Inventory.mjs"></script>-->
<!--    <script type="module" src="js/components/Item.mjs"></script>-->

<!--    <script type="module" src="js/config/images.js"></script>-->

<!--    <script type="module" src="js/services/AudioService.mjs"></script>-->
<!--    <script type="module" src="js/services/FiveMService.mjs"></script>-->
<!--    <script type="module" src="js/services/InterfaceService.mjs"></script>-->
<!--    <script type="module" src="js/services/OrderService.mjs"></script>-->
<!--    <script type="module" src="js/services/ParseService.mjs"></script>-->

<!--    <script type="module" src="js/helpers.mjs"></script>-->
</head>
<body>

<div class="main-inventory">
    <div class="page1">
        <div class="interaction-area no-select" id="interaction-area" style="overflow: visible" >
            <div class="inventory row" id="inventory">

                <div class="items grid column" id="inventory-scroll">
                    <!-- Progress bar -->
                    <div class="container-progress-text" >
                        <div class="left" id="inventory-weight-progress-text-something">

                        </div>
                        <div class="right" id="inventory-weight-progress-text">

                        </div>
                    </div>
                    <div id="inventory-info" style="margin-bottom: 2px;">
                        <div class="container-progress-bar-outer" id="inventory-weight-progress-bar-outer">
                            <div class="container-progress-bar" id="inventory-weight-progress-bar"></div>
                        </div>
                    </div>

                    <div class="items" id="inventory-items">
                        <!-- Inventory Grid/Container -->
                        <div class="grid-container" id="grid-container-inventory">
                            <!-- Inventory Items will be injected here -->
                        </div>
                    </div>

                </div>

                <div class="actions column2" id="action-panel">
                    <div class="side-container">
                        <div class="cash-area">
                            <div class="give-cash-button" id="inventory-guide-open-action">
                                Inventory Guide
                            </div>
                        </div>
                    </div>
                    <div class="side-container" style="height: 440px">
                        <div>
                            <input style="font-size: 25px; font-weight: bold" id="quantity" type="number" name="quantity" class="quantity-input" value="1"/>
                        </div>

                        <div id="action-options"  style="width: 100%; text-align: center">
                            <!-- Action options will be automatically injected here -->
                        </div>

                        <div class="error-area">

                        </div>

                    </div>


                </div>

                <div class="items column grid storage-items" id="storage-scroll">
                    <!-- Progress bar -->
                    <div id="storage-progress" class="container-progress-text" >
                        <div class="left" id="storage-weight-progress-text-something">

                        </div>
                        <div class="right" id="storage-weight-progress-text">

                        </div>
                    </div>
                    <div id="storage-info" style="margin-bottom: 2px;">
                        <div class="container-progress-bar-outer" id="storage-weight-progress-bar-outer">
                            <div class="container-progress-bar" id="storage-weight-progress-bar"></div>
                        </div>
                    </div>

                    <div class="items" id="storage-items">
                        <!-- Container info -->
                        <div class="grid-container" id="grid-container-storage">
                            <!-- Inventory Items will be injected here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="inventory-meta-wrapper">
            <div class="inventory-meta-content"></div>
        </div>

    </div>

    <div class="page2" style="text-align: left">
        <div class="" style="margin: 15px">
            <h3 style="color: #cdcdcd">Inventory</h3>
            <ul style="color: #ececec">
                <li>
                    Holding <kbd>CTRL</kbd> down while clicking a stack will move the stack to the other container
                </li>
                <li>
                    When quantity is <kbd>0</kbd>, the entire stack of an item will be moved
                </li>
                <li>
                    <kbd>ALT</kbd> + <kbd>CLICKING</kbd> an item will set the quantity to the stack size of the selected item
                </li>
                <li>
                    <kbd>ESCAPE</kbd> will open and close the inventory
                </li>
            </ul>
            <h3 style="color: #cdcdcd">Hotbar</h3>
            <ul style="color: #ececec">
                <li>
                    Drag items to the hotbar to assign them
                </li>
                <li>
                    Keys <kbd>1 - 5</kbd> are bound to the numbers at the bottom
                </li>
            </ul>
        </div>
        <div class="return-button" id="inventory-guide-close-action">
            <img src="images/_left_arrow.webp" height="14px"> &nbsp;
            Back
        </div>
    </div>

</div>

<div class="hotbar no-select">
    <div id="inventory-hotbar">
        <div class="grid-container-hotbar" id="grid-container-hotbar">
            <!-- Inventory Items will be injected here -->
        </div>
    </div>
</div>

<script type="module">
    import Inventory from './js/components/Inventory.mjs'
    import Item from './js/components/Item.mjs'
    import { compareOptions } from './js/helpers.mjs'
    import AudioService from './js/services/AudioService.mjs'
    import FiveMService from './js/services/FiveMService.mjs'
    import InterfaceService from './js/services/InterfaceService.mjs'
    import ParseService from './js/services/ParseService.mjs'

    let mode = 'inventory';
    let interfaceShowing = false;
    let items = [];
    let selectedAutomaticItem = null;
    let tryAutomaticAction = false;
    let automaticCooldownLock = false;

    window.addEventListener('message', event => {
        if(!event.data) return false;

        if(event.data.type === 'inventory:init')
        {
            Inventory.setMode('inventory')
            Inventory.setup(event.data.packet, true)
            InterfaceService.hideAllHotbar()
            InterfaceService.initializeAliases(event.data.packet.component_aliases)
        }
        else if(event.data.type === 'inventory:toggle')
        {
            if (event.data.enable === true && event.data.packet)
            {
                Inventory.setup(event.data.packet, false);
                InterfaceService.showInterface();

                if(event.data.packet.decaying_food_items) {
                  Object.defineProperty(globalThis, 'decayingFoodItems', {
                    value: event.data.packet.decaying_food_items,
                    enumerable: true,
                    configurable: true,
                    writable: true
                  })
                }

                if(event.data.packet.server_time) {
                  Object.defineProperty(globalThis, 'serverTime', {
                    value: Number(event.data.packet.server_time),
                    enumerable: false,
                    configurable: true,
                    writable: true
                  })
                }

                if(event.data.packet.gun_decay_default) {
                  Object.defineProperty(globalThis, 'gunDecayDefault', {
                    value: Number(event.data.packet.gun_decay_default),
                    enumerable: false,
                    configurable: true,
                    writable: true
                  })
                }
            }
            else if (event.data.enable === false)
            {
                InterfaceService.hideInterface();
            }
        }
        else if (event.data.type === 'inventory:send:setItemSelected')
        {
            InterfaceService.setHotbarSelected(event.data)
        }
        else if (event.data.type === 'inventory:sync:storeQuantitySync')
        {
          if (Inventory.mode != 'store' || 'store:' + Inventory.storageGrid.realName != event.data.store_name)
          {
            return;
          }

          let existing_item = Inventory.storageGrid.findItemById(event.data.item_id);

          if (!existing_item)
          {
            return;
          }

          let position = existing_item.position;
          let new_item = new Item({ ghost: true, position: position });

          // Update quantity
          if (event.data.amount > 0)
          {
            new_item = existing_item.cloneWithPositionAndAmount(position, event.data.amount, true);
          }

          Inventory.storageGrid.addItem(new_item, true, false, true)

          InterfaceService.registerMoveHandler();
        }
        else if (event.data.type === 'inventory:sync:chestItemOut')
        {
          if (Inventory.storageGrid.realName != event.data.chest_name) {
            return;
          }

          let position = event.data.position;

          let existing_item = Inventory.storageGrid.findItemByPosition(position);

          if(existing_item.isGhostItem)
          {
            return;
          }

          ParseService.parseItems([event.data.item], '', [], [], 1).then((items) => {
            let item_data = items[0];

            if(existing_item.properties.amount > event.data.item.amount)
            {
              // Subtract and replace

              item_data.amount = existing_item.properties.amount - event.data.item.amount

              let item = new Item({ properties: item_data, position: position })
              Inventory.storageGrid.addItem(item, true, false, true)
            }
            else
            {
              // Replace entirely with ghost item

              const item = new Item({ ghost: true, })
              item.position = position;
              Inventory.storageGrid.addItem(item, true, false, true)
            }

            InterfaceService.registerMoveHandler();
          })


        }
        else if (event.data.type === 'inventory:sync:chestItemIn')
        {
          if (Inventory.storageGrid.realName != event.data.chest_name) {
            return;
          }

          let items_in = [
            event.data.item
          ]

          let position = event.data.position

          ParseService.parseItems(items_in, '', [], [], 1).then((items) => {
            let item_data = items[0];

            let existing_item = Inventory.storageGrid.findItemByPosition(position);

            if(!existing_item.isGhostItem)
            {
              item_data.amount = existing_item.properties.amount + event.data.item.amount;
            }

            let item = new Item({ properties: item_data, position: position })
            Inventory.storageGrid.addItem(item, true, false, true)
            InterfaceService.registerMoveHandler();
          })
        }
        else if(event.data.type === 'inventory:sync:chestItemMove')
        {
          if (Inventory.storageGrid.realName != event.data.chest_name) {
            return;
          }

          let from_item = Inventory.storageGrid.findItemByPosition(event.data.from_position);
          let to_item = Inventory.storageGrid.findItemByPosition(event.data.to_position);
          from_item.replaceWithSameGridItem(Inventory.storageGrid, Inventory.storageGrid, to_item, event.data.amount, true)
        }
    });

    document.onkeydown = evt => {
        if(!InterfaceService.interfaceShowing) return false;
        evt = evt || window.event;
        const kc = evt.which || evt.keyCode;

        // if [escape, o, w, i] pressed then hide interface
        if (evt.keyCode === 27 ||
            evt.keyCode === 87
            // kc === 9 ||
        ) {
            InterfaceService.hideInterface();
        }

        // holding alt down
        if(evt.keyCode === 18) {
            evt = evt || window.event;
            Inventory.altBeingPressed = true;
        }

        switch (evt.keyCode) {
            case 38:
                if(Inventory.lastHoveredGrid) Inventory.lastHoveredGrid.scroll('up')
                break;
            case 40:
                if(Inventory.lastHoveredGrid) Inventory.lastHoveredGrid.scroll('down')
                break;
        }
    };

    // document.onkeyup = evt => {
    //     // holding alt down
    //     if (evt.keyCode === 18) {
    //         evt = evt || window.event;
    //         Inventory.altBeingPressed = false;
    //     }
    // }

    // When an item is clicked
    $('body').delegate('.grid-item-real', 'click', function (event) {
        const hash = $(this).attr('data-hash');
        const item = Inventory.findItemByHash(hash);

        if(!item) return;

        // When holding CTRL down, move item to other container
        if (event.ctrlKey && Inventory.mode === 'transfer') {
            item.replaceWithOtherGridItemMagically(null, item.properties.amount);
            return;
        }

        if (event.altKey) {
            $("#quantity").val(item.properties.amount);
        }

        // Set selected when clicking on it
        Inventory.setSelectedItem(item, this);
        $(this).addClass('selected')
    });

    $('body').delegate('.grid-item-real', 'hover', function (event) {
        const hash = $(this).attr('data-hash');
        const item = Inventory.findItemByHash(hash);

        if (event.type === 'mouseenter') {
            // console.log('entering')
            return;
        }

        alert(item.properties.name)

    });

    // When an option is clicked
    $('body').delegate('.option-item', 'click', function () {
        if(mode !== 'inventory') return;

        const actionName = $(this).attr('data-actionName')

        if (Inventory.selectedItem) {
            Inventory.selectedItem.doAction(actionName, InterfaceService.targetMoveQuantity)
        }
    });

    $('.page1').show();
    $('.page2').hide();

    $('body').delegate('#inventory-guide-open-action', 'click', function () {
        $('.page1').hide();
        $('.page2').show();
    });

    $('body').delegate('#inventory-guide-close-action', 'click', function () {
        $('.page2').hide();
        $('.page1').show();
    });

    // $('.grid-item-real').bind('mousewheel', function(e) {
    //     let el = document.elementFromPoint(e.pageX, e.pageY);
    //     const inventoryItemGrid = $(el).parents('#inventory-items')[0];
    //     const storageItemGrid = $(el).parents('#storage-items')[0];
    //
    //
    //     if (inventoryItemGrid) {
    //         if (e.originalEvent.wheelDelta /120 > 0) {
    //             inventoryItemGrid.animate({
    //                 scrollBottom: 15
    //             },'slow');
    //         } else{
    //             console.log('scrolling down in inventory !');
    //         }
    //     } else if(storageItemGrid) {
    //         if (e.originalEvent.wheelDelta /120 > 0) {
    //             console.log('scrolling up in storage !');
    //         } else{
    //             console.log('scrolling down in storage !');
    //         }
    //     }
    //
    //
    // });

    // $('body').mousemove(function (event) {
    //     let el = document.elementFromPoint(event.pageX, event.pageY);
    //     const inventoryItemGrid = $(el).parents('#inventory-items')[0];
    //     const storageItemGrid = $(el).parents('#storage-items')[0];
    //
    //     if (inventoryItemGrid) {
    //         Inventory.lastHoveredGrid = Inventory.inventoryGrid
    //     } else if (storageItemGrid) {
    //         Inventory.lastHoveredGrid = Inventory.storageGrid
    //     }
    // })
    //
    // document.addEventListener('wheel', function (event) {
    //     if (Inventory.lastHoveredGrid) {
    //         if (checkScrollDirectionIsUp(event)) {
    //             Inventory.lastHoveredGrid.scroll('up')
    //         } else {
    //             Inventory.lastHoveredGrid.scroll('down')
    //         }
    //
    //     }
    // }, true /*Capture event*/);
    //
    // function checkScrollDirectionIsUp(event) {
    //     if (event.wheelDelta) {
    //         return event.wheelDelta > 0;
    //     }
    //
    //     return event.deltaY < 0;
    // }





</script>
</body>
</html>
