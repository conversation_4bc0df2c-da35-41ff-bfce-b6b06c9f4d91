import FiveMService from '../services/FiveMService.mjs'
import InterfaceService from '../services/InterfaceService.mjs'
import ParseService from '../services/ParseService.mjs'
import Grid from './Grid.mjs'
import { generateEmptyItem } from '../helpers.mjs'

class Inventory {
    inventoryGrid = new Grid('inventory')
    storageGrid = new Grid('storage')
    lastHoveredGrid;

    selectedItem = null;
    allowTransferFromStorage = false
    mode = 'inventory'
    currencySymbol = '$'
    characterNumber = null;
    hotbarEntities = []
    hotbarElement = $('#grid-container-hotbar');
    cooldownLock = false;
    chestPacket = null;
    altBeingPressed = false;
    openLocked = false;
    openLockInterval = null;
    smallMode = false;

    async setup(packet = null, isInit = false)
    {
        if (packet.inv_zoom) {
            document.body.style.zoom = packet.inv_zoom;
        }

        $('.hotbar').hide();

        if (screen.width < 1400) {
            const modifier = 0.72;
            this.smallMode = true;

            $('body').css('zoom', modifier)
            $('body').css('-moz-transform', `scale(${modifier})`)
            $('body').css('-moz-transform-origin', `0 0`)
        }

        setTimeout(() => {
            $("#quantity").focus();
        }, 300);

        if(!InterfaceService.interfaceShowing) {
            $("#quantity").val(0);
        }

        if (this.openLocked) {
            InterfaceService.hideInterface()
        }

        // The the inventory mode
        const mode = !packet.mode ? 'inventory' : packet.mode;
        this.setMode(mode)

        // Set the character number on first init
        if (packet.char_id)
        {
            // if (!this.characterNumber) {
                this.setCharacterNumber(packet.char_id)
            // }
        }

        // $('#grid-container').scrollTo()

        // Handle inventory
        if (packet.inventory)
        {
            this.inventoryGrid.setMaxWeight(packet.inventory.max_weight);
            this.inventoryGrid.setRealName(`inventory:${this.characterNumber}`);

            const rawItems = await ParseService.parseItems(packet.inventory.items, this.inventoryGrid.realName, [
                0, 1, 2, 3, 4
            ], packet.inventory.order ?? '[]', packet.inventory.max_slots);


            this.inventoryGrid.setInitialItems(rawItems)

            // let percentWeight = getRealPercentage(packet.inventory.max_weight, packet.inventory.weight);
            // animateColorPercent('#progress-bar', percentWeight, 750);
        }

        $('#storage-progress').css('opacity', 'unset');
        $('#storage-info').css('opacity', 'unset');

        if (packet.chest)
        {
            if(!isInit) {
                $('#storage-weight').show();
                $('#storage-items').show();
            }

            // $('.inventory').css('width', '1620');
            $('#storage_weight').html();

            const chestMaxWeight = Math.round(packet.chest.max_weight * 10) / 10
            this.storageGrid.setMaxWeight(chestMaxWeight);
            this.inventoryGrid.setMaxWeight(packet.inventory.max_weight);


            this.storageGrid.setRealName(packet.chest.chest_name)

            this.setAllowTransferFromStorage(packet.chest.allow_transfer_from_storage)

            const storageRawItems = await ParseService.parseItems(packet.chest.items, packet.chest.chest_name,
                [], !InterfaceService.interfaceShowing ? packet.chest.order : null, packet.chest.max_slots);

            this.chestPacket = packet.chest;

            this.storageGrid.setInitialItems(storageRawItems)

            this.storageGrid.updateWeight();

            $('#storage-scroll').show();
            this.inventoryGrid.setAllowBypassWeight(packet.chest.ignore_inventory_weight);
        }
        else if(packet.store) {
          this.currencySymbol = packet.store.currency_symbol ?? '$'

          $("#quantity").val(1);

          if(!isInit) {
            $('#storage-weight').show();
            $('#storage-items').show();
          }

          $('#storage-progress').css('opacity', 0);
          $('#storage-info').css('opacity', 0);

          // $('.inventory').css('width', '1620');
          $('#storage_weight').html();

          this.storageGrid.setMaxWeight(10000000);
          this.inventoryGrid.setMaxWeight(packet.inventory.max_weight);

          this.storageGrid.setRealName(packet.store.chest_name)

          this.setAllowTransferFromStorage(packet.store.allow_transfer_from_storage)

          const storageRawItems = await ParseService.parseItems(packet.store.items, packet.store.chest_name,
              [], !InterfaceService.interfaceShowing ? packet.store.order : null, packet.store.max_slots);

          this.chestPacket = packet.store;

          this.storageGrid.setInitialItems(storageRawItems)

          this.storageGrid.updateWeight();

          $('#storage-scroll').show();
          this.inventoryGrid.setAllowBypassWeight(packet.store.ignore_inventory_weight);
        } else if(!packet.should_update_only_if_open)
        {
            $('#storage-items').hide();
            $('#storage-weight').hide();
            $('#storage-scroll').hide();
            // $('.inventory').css('width', '1080');
        }

        if (isInit) {
            $("#quantity").val(0);
            InterfaceService.updateHotkeySlots()
        }

        if (!isInit) {
            // $('.hotbar').show();
            // InterfaceService.showAllHotbar();
            InterfaceService.setStartAutoClose()
            InterfaceService.interfaceShowing = true;
            $('#inventory').fadeIn(5);
            $(".interaction-area").css("display", "flex");
        }

        InterfaceService.updateHotkeySlots();
        InterfaceService.registerMoveHandler();
    }

    checkLockActionsForMilliseconds(milliseconds)
    {
        if (this.cooldownLock) {
            return false;
        }

        this.cooldownLock = true;
        setTimeout(() => {
            this.cooldownLock = false;
        }, milliseconds);

        return true;
    }

    setAllowTransferFromStorage(value)
    {
        if(value === undefined || value === null) {
          value = true;
        }

        this.allowTransferFromStorage = value;
    }

    setCharacterNumber(value)
    {
        this.characterNumber = value;
    }

    setMode(value)
    {
        this.mode = value;
    }

    clearHotbar()
    {
        this.hotbarElement.empty();
    }

    addItemToHotbar(entity)
    {
        this.hotbarEntities.push(entity)
        this.hotbarElement.append(entity.parseItemHtml(true));
    }

    findItemByHash(hash)
    {
        const inInventory = this.inventoryGrid.items.find(i => i.hash === hash)

        if(inInventory) return inInventory;

        const inStorage = this.storageGrid.items.find(i => i.hash === hash)

        if(inStorage) return inStorage;

        return false;
    }

    setSelectedItem(item, element)
    {
        this.element = element;
        this.selectedItem = item;
        item.setSelected();

        FiveMService.performRequestItemActions(item)

        InterfaceService.hideMetaInformation();

        if(item.blurred) {
          return;
        }

        item.showMetaInformation();
    }

    clearSelectedItem()
    {
        this.selectedItem = null;
        InterfaceService.hideMetaInformation(200);
    }

    moveItems(fromItem, toItem, toStorage)
    {
        if((fromItem && fromItem.blurred) || (toItem && toItem.blurred)) {
          return;
        }

        if (this.mode == 'store' && toStorage)
        {
            if (fromItem.container == 'storage')
            {
                return;
            }

            let amount = InterfaceService.targetMoveQuantity;

            FiveMService.performNuiMove('performSellToStore', fromItem.properties.idname, amount, this.storageGrid.realName).then(response => {
                if(!response.success) {
                    InterfaceService.showError(response.message);
                    return;
                }

                if (amount >= fromItem.properties.amount)
                {
                    // Sold the entire stack - replace with blank
                    let ghostItem = generateEmptyItem(fromItem.properties.position);
                    this.inventoryGrid.addItem(ghostItem);
                    ghostItem.renderOnGridPosition(this.inventoryGrid);
                    this.inventoryGrid.removeItem(fromItem.hash);
                }
                else
                {
                    // Sold partial stack, decrease quantity
                    fromItem.properties.amount -= amount;
                    fromItem.renderOnGridPosition(this.inventoryGrid);
                }

                this.inventoryGrid.saveInventoryOrder();

                InterfaceService.registerMoveHandler();
            });

            return;
        }

        if (!toItem) {
            console.error('no target item/slot found');
            return false;
        }

        // Moving to a different inventory
        if (toItem.container !== fromItem.container)
        {
            // Moving from storage to the inventory
            if (toItem.container === 'inventory')
            {
                if(!this.allowTransferFromStorage)
                {
                    return InterfaceService.showError('Hand Slipped')
                }

                fromItem.replaceWithOtherGridItemMagically(toItem);
            }
            // Moving from inventory to storage
            else if (toItem.container !== 'inventory')
            {
                if(!fromItem.canBeMovedToStorage()) {
                    return InterfaceService.showError('Can not put that in here')
                }

                fromItem.replaceWithOtherGridItem(this.inventoryGrid, this.storageGrid, toItem)
            }
        }

        // Moving to the same inventory
        if (toItem.container === fromItem.container) {
            fromItem.replaceWithOtherGridItemMagically(toItem);
        }

        InterfaceService.registerMoveHandler();
    }
}

export default new Inventory();
