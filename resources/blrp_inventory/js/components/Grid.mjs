import { animateColorPercent } from '../helpers.mjs'
import OrderService from '../services/OrderService.mjs'
import Item from './Item.mjs'
import { roundToTwo } from '../helpers.mjs'

class Grid {
    name;
    realName;
    items = [];
    weight = 0;
    maxWeight = 0;
    ignoreWeightMax = false
    element = null;
    weightBarElement = null;
    weightBarTextElement = null;

    constructor(name, realName)
    {
        this.name = name
        this.realname = realName

        if (this.name === 'inventory') {
            this.element = $('#grid-container-inventory');
        } else {
            this.element = $('#grid-container-storage');
        }

        this.weightBarElement = $(`#${this.name}-weight-progress-bar`)
        this.weightBarTextElement = $(`#${this.name}-weight-progress-text`)
    }

    // set weight(value)
    // {
    //     this.compiledWeight = value;
    //
    //     this.weightBarElement.width = `${this.percentFull}%`
    //
    //     console.log(`weight set to ${this.percentFull}`)
    // }
    //
    // get weight()
    // {
    //     return this.compiledWeight;
    // }

    get percentFull()
    {
        return roundToTwo((this.weight / this.maxWeight) * 100).toFixed(2);
    }

    get inventoryOrder()
    {
        const savingItems = []
        const items = this.items.map(i => {
            if (i.properties && i.properties.amount > 0) {
                return {
                    idname: i.properties.idname,
                    position: i.position,
                    amount: i.properties.amount,
                }
            }
        })

        for (const item of items) {
            if (item) savingItems.push(item);
        }

        return savingItems;
    }

    setMaxWeight(value)
    {
        // console.log('max weight set', value)
        this.maxWeight = value;

        if (this.name === 'inventory') {
            $('#max_weight').html(value);
        } else {
            $('#storage_max_weight').html(value);
        }
    }

    setRealName(value)
    {
        this.realName = value;
    }

    setInitialItems(rawItems)
    {
        this.clear();

        for (const rawItem of rawItems) {
            const entity = new Item({ properties: rawItem, position: rawItem.position });
            this.addItem(entity, true, true);
        }
    }

    setAllowBypassWeight(value)
    {
        this.ignoreWeightMax = value;
    }

    canHoldItem(item, amount)
    {
        if(this.ignoreWeightMax) return true;

        const newWeight = item.getTotalWeightOf(amount) + this.weight;

        if (newWeight === undefined) {
            return false;
        }

        return roundToTwo(newWeight) <= roundToTwo(this.maxWeight);
    }

    getHowManyOfItemCanHold(item)
    {
        let canHold = false;
        let amount = item.properties.amount

        while (!canHold)
        {
            if (this.canHoldItem(item, amount)) {
                return amount;
            }

            if (amount < 1) return false

            amount -= 1;
        }

        return false;
    }

    canHoldWeight(additionalWeightToTest)
    {
        if(this.ignoreWeightMax) return true;

        return (additionalWeightToTest + this.weight) <= this.maxWeight;
    }

    clear()
    {
        this.items = [];
        this.element.empty();
    }

    addItem(item, renderOnGrid = false, asNew = false, fromUpdate = false)
    {
        if(this.name === 'inventory' && item.position < 6)
        {
            item.isHotbarSlot = true;
        }

        if(fromUpdate)
        {
          this.items = this.items.filter(x => x.position != item.position)
        }

        item.container = this.name;
        item.grid = this;
        this.items.push(item);
        this.calculateWeight();

        if(renderOnGrid)
        {
            if(asNew)
            {
                item.element = $(item.parseItemHtml())

                this.element.append(item.parseItemHtml())
            }
            else
            {
                item.renderOnGridPosition(this)
            }

            // if (this.name === 'inventory')
            // {
            //     if (item.position > 4)
            //     {
            //         if(asNew)
            //         {
            //             item.element = $(item.parseItemHtml())
            //
            //             this.element.append(item.parseItemHtml())
            //         }
            //         else
            //         {
            //             item.renderOnGridPosition(this)
            //         }
            //     }
            // }
            // else
            // {
            //     if(asNew)
            //     {
            //         item.element = $(item.parseItemHtml())
            //
            //         this.element.append(item.parseItemHtml())
            //     }
            //     else
            //     {
            //         item.renderOnGridPosition(this)
            //     }
            // }


        }
    }

    findItemByPosition(position)
    {
        const number = parseInt(position)

        const item = this.items.find(e => e.position === number);

        if(!item) console.error(`Could not find item in ${this.name}:${position}`);

        return item;
    }

    calculateWeight()
    {
        this.weight = 0;

        for (const item of this.items) {
            if (item.properties && item.properties.amount && item.properties.amount !== 0) {
                this.weight = roundToTwo(this.weight + (item.properties.amount * item.properties.weight));
            }
        }

        // this.weightBarElement.css(`width`, `${this.percentFull}%`)
        // this.weightBarElement.animate({ width: `${this.percentFull}%` }, 50)


        if (this.name === 'inventory') {
            $('#weight').html(roundToTwo(this.weight).toFixed(2));

        } else {
            $('#storage_weight').html(roundToTwo(this.weight).toFixed(2));
        }

        this.updateWeight();
    }

    updateWeight()
    {
        // console.log(`weight ${this.name} set to ${this.percentFull}`, this.weightBarTextElement)

        animateColorPercent(this.weightBarElement, this.percentFull, 50)

        if (this.weightBarTextElement) {
            this.weightBarTextElement.html(`
            <span style="font-weight: bold">${roundToTwo(this.weight).toFixed(2)}</span>
            <span>&nbsp;/&nbsp;</span>
            <span style="font-weight: bold">${roundToTwo(this.maxWeight).toFixed(2)}</span>
            <span>&nbsp;(Kg)</span>

            `)
        }
    }

    removeItem(hash)
    {
        const itemIndex = this.items.findIndex(i => i.hash === hash)

        this.items.splice(itemIndex, 1)
    }

    saveInventoryOrder()
    {
        // console.log(`saving ordering for ${this.realName}`, this.inventoryOrder)
        OrderService.saveInventoryOrder(this.realName, this.inventoryOrder)
    }

    scroll(direction)
    {
        const ele = this.name === 'inventory'
            ? $('#grid-container-inventory')
            : $('#grid-container-storage');

        // const inv = document.getElementById('inventory-scroll')
        // const inv = document.getElementById('grid-container-inventory')

        // console.log(ele)
        ele.scrollTop(ele.height());

        // inv.scrollBy(0, 55)

        if (direction === 'up')
        {
            // let testOffset = ele.offset(), scrollOffset = ele.offset();
            //
            // ele.scrollTop(testOffset.top - scrollOffset.top);


            // ele.stop().animate({
            //     marginTop: ele.scrollTop() + 'px',
            // }, 'fast');
        }
    }

    findItemById(item_id)
    {
      const item = this.items.find(e => e.properties.idname == item_id);

      if(!item)
        return false;

      return item;
    }

    getPositionByItemId(item_id)
    {
      const item = this.items.find(e => e.properties.idname == item_id);

      if(!item)
        return false;

      return item.position;
    }
}

export default Grid;
