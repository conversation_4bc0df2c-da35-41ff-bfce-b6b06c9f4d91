import { generateEmptyItem, randomString, sortItemsByPosition } from '../helpers.mjs'
import AudioService from '../services/AudioService.mjs'
import FiveMService from '../services/FiveMService.mjs'
import InterfaceService from '../services/InterfaceService.mjs'
import Inventory from './Inventory.mjs'

class Item {
    hash = ''
    properties = {}
    type = 'inventory';
    container;
    position = null
    hotbarPosition = null
    element = null
    isHotbarSlot = false;
    store_purchase = false;
    blurred = false;

    constructor({ ghost = false, properties, position, type = 'inventory' })
    {
        this.type = type;
        this.position = position;

        if (ghost) {
            this.properties.idname = 'empty'
            this.properties.action = 'auto'
            this.hash = randomString(20)

            return;
        }

        if (!this.properties.idname) {
            this.properties.idname = properties.name
        }

        this.properties = properties
        this.hash = randomString(20)
    }

    cloneWithPositionAndAmount(position, amount, keep_price = false)
    {
        let props_copy = { ... this.properties };

        if(!keep_price)
          props_copy.price = null;

        const clone = new Item({
            properties: props_copy,
            type: this.type,
        });

        clone.properties.amount = amount;
        clone.renderedPosition = position;

        return clone;
    }

    set renderedPosition(value)
    {
        this.properties.position = value;
        this.position = value;
    }

    get totalWeight() {
        if (this.properties.amount && this.properties.weight) {
            return this.properties.amount * this.properties.weight
        }

        return 0;
    }

    getTotalWeightOf(amount) {
        if (amount && this.properties.weight) {
            return amount * this.properties.weight
        }

        return 0;
    }

    get isGhostItem()
    {
        if (this.properties.idname === 'empty' || this.properties.name === 'None') {
            return true
        }

        return false;
    }

    setNewAmountAndRender(value)
    {
        this.properties.amount = value;
    }

    addSetHotbarPosition(position)
    {
        this.hotbarPosition = position
        Inventory.addItemToHotbar(this)
    }

    canHoldAmount(amount)
    {

    }

    replaceWithOtherGridItemMagically(targetItem = null, amount = null)
    {
        // Prevent sorting items in a store
        if(targetItem && this.container == 'storage' && Inventory.mode == 'store' && targetItem.container == this.container) {
          return;
        }

        let toGrid;
        let fromGrid;

        if (!targetItem ||targetItem.container !== this.container)
        {
            if (this.container === 'inventory') {
                fromGrid = Inventory.inventoryGrid;
                toGrid = Inventory.storageGrid;
            } else {
                fromGrid = Inventory.storageGrid;
                toGrid = Inventory.inventoryGrid;
            }

            if (!targetItem) {
                sortItemsByPosition(toGrid.items)
                targetItem = toGrid.items.find(i => !i.isGhostItem && i.properties.idname === this.properties.idname)

                // find first empty item in grid
                if (!targetItem) {
                    targetItem = toGrid.items.find(i => i.isGhostItem && !i.isHotbarSlot)
                }
            }
        }
        else
        {
            if (this.container === 'inventory') {
                fromGrid = Inventory.inventoryGrid;
                toGrid = Inventory.inventoryGrid;
            } else {
                fromGrid = Inventory.storageGrid;
                toGrid = Inventory.storageGrid;
            }
        }

        if(toGrid.name != 'inventory' && !this.canBeMovedToStorage())
        {
          return InterfaceService.showError('Can not put that in here')
        }

        if (targetItem.container !== this.container)
        {
            this.replaceWithOtherGridItem(fromGrid, toGrid, targetItem, amount)
        }
        else
        {
            this.replaceWithSameGridItem(toGrid, fromGrid, targetItem)
        }
    }

    replaceWithSameGridItem(toGrid, fromGrid, targetItem, moveAmount = null, fromSync = false) {
        const currentPosition = this.position
        const newPosition = targetItem.position
        let isHotbarItem = false

        if (this.position < 5 || targetItem.position < 5) {
            isHotbarItem = true
        }

        // console.log(`moving ${fromGrid.name}:${currentPosition}:${this.properties.idname}:${this.properties.amount}:${'max'} to ${toGrid.name}: ${newPosition}:${targetItem.properties.idname}:${targetItem.properties.amount}`)

        if (moveAmount == null) {
          moveAmount = InterfaceService.targetMoveQuantity;
        }

        if (moveAmount === 0) {
            moveAmount = this.properties.amount;
        }

        if (moveAmount > this.properties.amount) {
            moveAmount = this.properties.amount;
        }

        // Combining items
        if (targetItem.properties.idname === this.properties.idname)
        {
            targetItem.setNewAmountAndRender(targetItem.properties.amount + moveAmount)

            this.properties.amount = this.properties.amount - moveAmount;

            // If combing a full stack into another stack of the same item
            if(this.properties.amount < 1)
            {
                // console.log('Moving a full stack to the same item spot in same inventory')
                toGrid.removeItem(this.hash)
                const ghostItem = generateEmptyItem(this.position)
                toGrid.addItem(ghostItem, true)

                targetItem.amount = targetItem.amount + moveAmount
                targetItem.renderOnGridPosition(toGrid)
            }
            // If not moving an entire stack, but still combing items
            else
            {
                // console.log('Moving part of a stack to the same item spot in same inventory')
                targetItem.amount = targetItem.amount + moveAmount

                this.renderOnGridPosition(toGrid)
                targetItem.renderOnGridPosition(toGrid)
            }
        }
        // Not combining items (moving to blank spot)
        else
        {
            // Moving entire stack to a blank spot
            if (moveAmount >= this.properties.amount)
            {
                // console.log('Moving entire stack to a blank spot in same inventory')
                this.position = newPosition
                targetItem.position = currentPosition

                this.renderOnGridPosition(toGrid)
                targetItem.renderOnGridPosition(toGrid)
            }
            // Moving part of a stack to a blank spot
            else
            {
                if(!targetItem.isGhostItem)
                {
                    return InterfaceService.showError(`Can not replace spots when amount being moved is less than stack size`)
                }

                if(this.container == 'storage' && toGrid.realName.includes('store:')) {
                  return InterfaceService.showError('Cannot split item stacks in store inventory');
                }

                this.properties.amount = this.properties.amount - moveAmount;

                // console.log('Moving part of a stack to a blank spot in same inventory')
                // Remove old blank spot, must do this after adding
                toGrid.removeItem(targetItem.hash)

                // Add new item with amount being moved and position
                const clonedItem = this.cloneWithPositionAndAmount(targetItem.position, moveAmount)
                toGrid.addItem(clonedItem, true)

                if(targetItem.position < 6) isHotbarItem = true

                // Update item being moved
                this.renderOnGridPosition(toGrid)
            }
        }

        fromGrid.saveInventoryOrder();

        // Synced storage stuff
        if (!fromSync && toGrid.name != 'inventory' && fromGrid.name != 'inventory')
        {
          let data = {
            chest: toGrid.realName,
            fromPosition: currentPosition,
            toPosition: newPosition,
            amount: moveAmount,
            order: JSON.stringify(fromGrid.inventoryOrder),
          }

          FiveMService.itemMovedSync(data);
        }
        InterfaceService.updateHotkeySlots();
        InterfaceService.registerMoveHandler();

        if (fromGrid.name === 'inventory') {
            if(isHotbarItem) FiveMService.itemMoved();
        }
    }

    replaceWithOtherGridItem(fromGrid, toGrid, targetItem, amount = null)
    {
        const currentPosition = parseInt(this.position)
        const newPosition = parseInt(targetItem.position)

        let moveAmount = amount ?? InterfaceService.targetMoveQuantity;

        if(Inventory.mode != 'store') {
          if (moveAmount === 0) {
            moveAmount = this.properties.amount
          }

          if (moveAmount > this.properties.amount) {
              moveAmount = this.properties.amount
          }
        }

        if (!toGrid.canHoldItem(this, moveAmount)) {
            moveAmount = toGrid.getHowManyOfItemCanHold(this)

            if (!moveAmount) {
                return InterfaceService.showError('Not enough room')
            }

            InterfaceService.showError('Not enough room for entire stack')
        }

        const isMovingEntireStack = moveAmount >= this.properties.amount

        if (this.properties.idname === 'empty' || this.properties.idname === 'None') {
            return alert('ERROR Item moved is an empty item. Something went wrong.')
        }

        // Prevent replacing item across storage
        if (!targetItem.isGhostItem && Inventory.mode != 'store') {
            // Allow placing the same item on itself to transfer
            console.log(targetItem.properties.idname)
            if (targetItem.properties.idname !== this.properties.idname) {
                return InterfaceService.showError('Can not replace items')
            }
        }

        let existing_item_position = toGrid.getPositionByItemId(this.properties.idname)

        if(toGrid.name == fromGrid.name && existing_item_position && existing_item_position != newPosition) {
          return InterfaceService.showError('Cannot split item stacks in store inventory');
        }

        // Wait for success/failure from server before visually moving item
        let handlerAction = null
        let handlerGrid = null
        let handlerPosition = null

        if(Inventory.mode == 'store') {
          if (fromGrid.name === 'inventory') {
              handlerAction = `performSellToStore`;
              handlerGrid = toGrid;
              handlerPosition = newPosition;
          } else {
              handlerAction = `performBuyFromStore`;
              handlerGrid = fromGrid;
              handlerPosition = currentPosition;
          }
        } else {
          if (fromGrid.name === 'inventory') {
              handlerAction = `performMoveToStorage`;
              handlerGrid = toGrid;
              handlerPosition = newPosition;
          } else {
              handlerAction = `performMoveToInventory`;
              handlerGrid = fromGrid;
              handlerPosition = currentPosition;
          }
        }

        FiveMService.performNuiMove(handlerAction, this.properties.idname, moveAmount, handlerGrid.realName, this.properties.meta, handlerPosition).then(response => {
            if(!response.success) {
              InterfaceService.showError(response.message);
              return;
            }

            // TODO: server needs to tell us if the stack should still be there

            // Handle fromGrid
            if(Inventory.mode == 'store') {
              // console.log('not moving entire stack')
              //this.properties.amount = 1;
              // this.renderOnGridPosition(fromGrid)
            } else {
              if (isMovingEntireStack) {
                // console.log('moving entire stack')
                // Replace item with empty item, the "target item"
                // console.log(`rendering ghost item in ${fromGrid.name}:${currentPosition} spot item was moved from`)
                const ghostItem = generateEmptyItem(currentPosition)
                fromGrid.addItem(ghostItem)
                ghostItem.renderOnGridPosition(fromGrid)
                fromGrid.removeItem(this.hash)
              } else {
                // console.log('not moving entire stack')
                this.properties.amount = this.properties.amount - moveAmount;
                this.renderOnGridPosition(fromGrid)
              }
            }

            fromGrid.calculateWeight()

            // Handle toGrid
            if (!targetItem.isGhostItem)
            {
              // Item is being transferred to the same in another container
              if (targetItem.properties.idname === this.properties.idname)
              {
                  // console.log('Item is being transferred to the same in another container')
                  targetItem.properties.amount += moveAmount
                  targetItem.renderOnGridPosition(toGrid)
                  toGrid.calculateWeight()
              }
              else
              {
                  // Item is being transferred to an existing spot in another container
                  // console.log('Item is being transferred to an existing spot in another container')
                  return InterfaceService.showError('Can not replace a partial stack of an item across inventories')
              }
            }
            else
            {
                // Item is being transferred to a blank spot
                // console.log('Item is being transferred to a blank spot')
                const newItem = this.cloneWithPositionAndAmount(newPosition, moveAmount)
                // Remove old item, and place new item
                toGrid.removeItem(targetItem.hash)
                toGrid.addItem(newItem)
                newItem.renderOnGridPosition(toGrid)
            }

            InterfaceService.updateHotkeySlots()

            // const placedItemCheck = toGrid.findItemByPosition(newPosition)
            // console.log('placed item', placedItemCheck)
            // if (placedItemCheck.isGhostItem) {
            //     const test = true;
            //     return alert('VALIDATE ERR Item moved is an empty item. Something went wrong.')
            // }

            toGrid.saveInventoryOrder();
            fromGrid.saveInventoryOrder();

            InterfaceService.registerMoveHandler();

            if(Inventory.mode == 'store') {
              $("#quantity").val(1);
            }
        })
    }

    renderOnGridPosition(grid)
    {
        this.element = grid.element.find(`[data-position='${this.position}']`);

        this.element.replaceWith(this.parseItemHtml(false))

    }

    setSelected()
    {
        $('.grid-item-real').removeClass('selected');
    }

    showMetaInformation()
    {
        InterfaceService.showMetaInformation(this.properties)
    }

    get humanPosition()
    {
        return this.position + 1;
    }

    percent2Color(percent) {
      percent = 100 - percent;

    	let red = Math.round(Math.min((percent / 100) * 255 * 2, 255));
      let green = Math.round(Math.min((255 - ((percent / 100) * 255)) * 2, 255));

      return 'rgba(' + red + ',' + green + ', 0, 0.75)';
    }

    parseItemHtml(onHotbar = false)
    {
        let itemNameClass = 'item-name'
        let itemImageClass = 'item-image'
        let itemName = this.properties.name

        let item_id = this.properties.idname
        let item_id_nometa = this.properties.idname
        let has_meta = false

        if(item_id_nometa && item_id_nometa.includes(':meta:'))
        {
          item_id_nometa = item_id_nometa.substr(0, item_id_nometa.indexOf(':meta:'))
          has_meta = true
        }

        if (itemName && itemName.toLowerCase().includes('printed paper'))
        {
            if (this.properties.meta && this.properties.meta['document_title']) {
                itemName = this.properties.meta['document_title']
            }
        }

        if (itemName && itemName.toLowerCase().includes('signed'))
        {
            if (this.properties.meta && this.properties.meta['document_title']) {
                itemName = this.properties.meta['document_title']
            }
        }

        let noSpacedName = this.properties?.name?.replace(' ', '')

        if(this.properties && this.properties.name && noSpacedName.length > 22)
        {
            itemNameClass = 'item-name item-name-long'
            itemImageClass = 'item-image item-image-long';
        }


        let has_durability = false;
        let durability = -1;
        let durability_color = '';

        /* Gun durability - new (time based) */
        if(this.properties && this.properties.meta && this.properties.meta.gun_exp_time)
        {
          let expiry_time = Number(this.properties.meta.gun_exp_time);
          let dur_adjusted = gunDecayDefault;

          if(this.properties.meta.gun_exp_life)
          {
            dur_adjusted = this.properties.meta.gun_exp_life;
          }

          has_durability = true;

          durability = (((expiry_time - serverTime) / dur_adjusted) * 100);
        }

        /* Gun durability - legacy (bullet based) */
        if(this.properties && this.properties.meta && (this.properties.meta['durability'] || (this.properties.meta['dur_cur'] >= 0 && this.properties.meta['dur_start'] >= 0)))
        {
          has_durability = true;

          if(!this.properties.meta['durability'])
          {
            let dur_adjusted = this.properties.meta['dur_start'];
            let dur_repair = this.properties.meta['dur_repair'];

            if(dur_repair) {
              dur_adjusted = Math.floor(dur_adjusted * (dur_repair / 100))
            }

            durability = (this.properties.meta['dur_cur'] / dur_adjusted) * 100
          }
          else
          {
            durability = this.properties.meta['durability']
          }
        }

        /* Used spray paint durability */
        if(this.properties && this.properties.meta && this.properties.meta['spray_quantity'])
        {
          has_durability = true;
          durability = this.properties.meta['spray_quantity'];
        }

        /* Unused spray paint durability */
        if(!has_durability && itemName && itemName.toLowerCase().includes('spray paint'))
        {
          has_durability = true
          durability = 100;
        }

        /* Food durability */
        if(item_id && item_id_nometa && decayingFoodItems && decayingFoodItems.includes(item_id_nometa))
        {
          let expiry_time = Number(item_id.substr(item_id.indexOf(':meta:') + 6))

          has_durability = true
          durability = (((expiry_time - serverTime) / 172800) * 100) // 172800 = 48 * 60 * 60 - see blrp_core/modules/food-decay/config.lua @ food_decay_config.decay_time

          // Store durability force to 100%
          if (Inventory.mode == 'store' && this.container == 'storage')
          {
            durability = 100
          }
        }

        /* Expiry general durability */
        if(this.properties && this.properties.meta && this.properties.meta['dur_exp_time'])
        {
          let expiry_time = Number(this.properties.meta['dur_exp_time'])
          let expiry_dur = Number(this.properties.meta['dur_exp_ttl'])

          has_durability = true
          durability = (((expiry_time - serverTime) / expiry_dur) * 100)
        }

        if(this.properties && this.properties.meta && this.properties.meta.fpd)
        {
          this.properties.meta.dur_repair = null;
          has_durability = true;
          durability = 0;
        }

        /* OBD laptop durability */
        if (item_id && item_id == 'laptop_obd' && !has_meta)
        {
          has_durability = true
          durability = 100
        }

        if(has_durability)
        {
          itemNameClass = itemNameClass + ' item-name-durability';
          durability_color = this.percent2Color(durability)

          if(durability <= 0) {
            durability_color = 'rgba(255, 0, 0, 0.75)'
          }
        }

        if(durability == -1)
        {
          durability = 0
        }

        let image = this.properties.image
        let has_cover_image = false

        if ( this.properties && this.properties.meta && this.properties.meta['cover_image'] ) {
            image = this.properties.meta['cover_image'].replace(/(<([^>]+)>)/gi, "")
            itemName = `${ itemName} (Paper)`
            has_cover_image = true
        }

        if ( this.properties && this.properties.meta && this.properties.meta['storage_label'] ) {
            itemName = this.properties.meta['storage_label']
        }

        if ( this.properties && this.properties.meta && this.properties.meta['_l'] ) {
            itemName = this.properties.meta['_l']
        }

        if ( this.properties && this.properties.meta && this.properties.meta['dynamic_image'] ) {
            image = this.properties.meta['dynamic_image'].replace(/(<([^>]+)>)/gi, "")
            has_cover_image = true
        }

        /* Render */
        if (this.properties.name === 'None' || this.properties.idname === 'empty' || this.properties.amount === 0) {
            return `
            <div data-hash="${this.hash}" data-position="${this.position}" data-isNone="true" data-slotArea="true" class=" grid-first-five grid-item grid-item-fake" id="item-${this.position}" data-identifier="${this.position}">
                ${this.htmlItemShouldShowPosition()}
                <div class="item-image">
                    <img alt="ghosted" class="ghosted" src="${this.properties.image}"/>
                </div>
                <div class="${itemNameClass}" id="item-name">

                </div>
            </div>`;
        } else if(this.properties.name) {
            if(this.properties.name.includes('body')) this.properties.name = this.properties.name.replace(' body', '');
            if(this.properties.name.includes('_')) this.properties.name = this.properties.name.replace('_', ' ');

            /* Durability bar, if applicable */
            let durability_str = ``;
            let durability_width = 100;
            let repair_bar = ``;
            let repair_width = 0;
            let br_class_right = `br-r`;

            if(has_durability) {
              if(this.properties && this.properties.meta && this.properties.meta['dur_repair']) {
                br_class_right = ``;
                durability_width = this.properties.meta['dur_repair'];
                repair_width = 100 - this.properties.meta['dur_repair'];

                repair_bar = `
                  <div class="item-progress-bar-outer" style="width:${repair_width}%" id="inventory-weight-progress-bar-outer">
                    <div class="item-progress-bar br-r" id="inventory-weight-progress-bar" style="background-color: red; width: 100%;"></div>
                  </div>
                `;
              }

              durability_str = `
              <div style="display: flex">
                <div class="item-progress-bar-outer" style="width:${durability_width}%" id="inventory-weight-progress-bar-outer">
                    <div class="item-progress-bar ${br_class_right}" id="inventory-weight-progress-bar" style="background-color: ${durability_color}; width: ${durability <= 0 ? 100 : durability}%;"></div>
                </div>
                ${repair_bar}
              </div>
                `
            }

            /* Compute amount field - price if store */
            let amount_str = ``;

            if(this.properties.price) {
              let limit_str = ``;

              if(!this.properties.unlimited) {
                limit_str = ` <span style="color:#c3c3c3">(${this.properties.amount})</span>`
              }

              amount_str = `<span class="span-price"><span style="color: lawngreen">${Inventory.currencySymbol}${this.properties.price}</span>${limit_str}</span>`;
            } else {
              amount_str = `<span class="span-amount">${this.properties.amount}</span>`;
            }

            let classes = `grid-first-five grid-item grid-item-real`;

            if(
              this.container === 'storage' &&
              Inventory.mode === 'transfer' &&
              Inventory.chestPacket?.properties?.blur_except &&
              !Inventory.chestPacket?.properties?.blur_except.includes(this.properties.idname)
            ) {
              classes += ' item-blur';
              console.log(Inventory.chestPacket?.properties?.blur_except)
              durability_str = '';
              this.blurred = true;
            }

            let type_icon = ``;

            if(this.properties?.meta?.dc)
            {
                type_icon = `<img class="type-icon" src="images/dc_certified.webp" />`
            }

            if(this.properties?.tebex)
            {
                type_icon = `<img class="type-icon" src="images/tebex.webp" />`
            }

            if(this.properties?.claimable)
            {
                type_icon = `<img class="type-icon" src="images/claimable.webp" />`
            }

            return `
            <div data-hash="${this.hash}" data-position="${this.position}" data-slotArea="true" class="${classes}" data-identifier="${this.position}" data-itemCode="${this.properties.idname}" data-slotCode="${this.properties.idname}">
                ${this.htmlItemShouldShowPosition()}
                <div class="item-weight">
                    ${amount_str}
                    <span class="span-weight">${this.properties.weight}</span>
                </div>
                <div class="${itemImageClass}">
                    <img class="slot-image" onerror="if (this.src != 'images/item_unknown.webp') this.src = 'images/item_unknown.webp';" alt="slot" src="${ image }"/>
                    ${type_icon}
                </div>
                <div class="${itemNameClass}">
                    <span class="span-name">${itemName}</span>
                </div>
                ${durability_str}
            </div>`;
        }

        return null;
    }

    htmlItemShouldShowPosition()
    {
        return this.humanPosition < 6 && this.container === 'inventory' ? `<div class="slot-position">${(this.humanPosition)}</div>` : '';
    }

    htmlItemImage()
    {

    }

    doAction(actionName, doAmount, hideActions = false) {
        if (doAmount === 0) {
            // If amount is = 0, then use all items
            doAmount = 1
        }

        if (!Inventory.checkLockActionsForMilliseconds(400))
        {
            return false;
        }

        if (hideActions) {
            if (!Inventory.selectedItem) {
                $('#action-options').html('');
                $('.grid-item-real').removeClass('selected');
            }
        }

        FiveMService.performItemAction(actionName, this.properties.idname, doAmount, this.properties.meta)

        if (actionName.includes(' & Close')) {
            InterfaceService.hideInterface();
        }
    }


    canBeMovedToStorage()
    {
        if(!Inventory.chestPacket?.whitelisted_items && !Inventory.chestPacket?.blacklisted_items) {
            return true;
        }

        let allow_movement = false;
        if(Inventory.chestPacket?.whitelisted_items){
            for(const check_item_name of Inventory.chestPacket.whitelisted_items) {
                if(check_item_name === "printed_paper" && this.properties.idname.includes("printed_paper")) {
                    allow_movement = true
                } else if(check_item_name === this.properties.idname) {
                    allow_movement = true
                } else if(this.properties.idname.includes(check_item_name)) {
                    allow_movement = true
                }
            }
        }
        if (!Inventory.chestPacket?.whitelisted_items){ allow_movement = true}

        if (Inventory.chestPacket?.blacklisted_items) {
            for (const check_item_name of Inventory.chestPacket.blacklisted_items) {
                if ( check_item_name === this.properties.idname ) {
                    allow_movement = false
                } else if ( this.properties.idname.includes(check_item_name) ) {
                    allow_movement = false
                }
            }
        }


        return allow_movement;
    }
}

export default Item;
