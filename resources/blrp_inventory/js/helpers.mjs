import Item from './components/Item.mjs'

export const generateEmptyItemHtml = (position) => {
    const item = new Item({ ghost: true, })
    item.position = position;

    return item.parseItemHtml();
}

export const generateEmptyItem = (position) => {
    return new Item({ ghost: true, position: position });
}

export const compareOptions = (a, b) => {
    return a.order - b.order;
}

export const randomString = (length) => {
    let result           = '';
    let characters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let charactersLength = characters.length;
    for ( let i = 0; i < length; i++ ) {
        result += characters.charAt(Math.floor(Math.random() *
            charactersLength));
    }
    return result;
}


export const sortItemsByName = (items) => {
    items.sort(function(a, b) {
        if(a.name < b.name) { return -1; }
        if(a.name > b.name) { return 1; }
        return 0;
    })

    return items
}

export const sortItemsByPosition = (items) => {
    return items.sort(function (a, b) {
        if (a.position < b.position) return -1;
        if (a.position > b.position) return 1;
        return 0;
    });
}

export const getRealPercentage = (current_weight, total_weight) => {
    let decreaseValue = current_weight - total_weight;
    let percentWeight = (decreaseValue / current_weight) * 100;
    percentWeight = Math.round(percentWeight);
    percentWeight = 100 - percentWeight;
    return percentWeight;
}

export const roundToTwo = (num) => {
    return Math.round((num + Number.EPSILON) * 100) / 100;
};


export const animateColorPercent = (selector, percent, time) => {
    let percentColors = [
        { pct: 0, color: '#00FF00' },	{ pct: 3, color: '#12FF00' },	{ pct: 6, color: '#24FF00' },
        { pct: 10, color: '#47FF00' },	{ pct: 13, color: '#58FF00' },	{ pct: 16, color: '#6AFF00' },
        { pct: 20, color: '#7CFF00' },	{ pct: 23, color: '#8DFF00' },	{ pct: 26, color: '#9FFF00' },
        { pct: 30, color: '#B0FF00' },	{ pct: 33, color: '#C2FF00' },	{ pct: 36, color: '#D4FF00' },
        { pct: 40, color: '#E5FF00' },	{ pct: 43, color: '#F7FF00' },	{ pct: 46, color: '#FFF600' },
        { pct: 50, color: '#FFE400' },	{ pct: 53, color: '#FFD300' },	{ pct: 56, color: '#FFC100' },
        { pct: 60, color: '#FFAF00' },	{ pct: 63, color: '#FF9E00' },	{ pct: 66, color: '#FF8C00' },
        { pct: 70, color: '#FF7B00' },	{ pct: 73, color: '#FF6900' },	{ pct: 76, color: '#FF5700' },
        { pct: 80, color: '#FF4600' },	{ pct: 83, color: '#FF3400' },	{ pct: 86, color: '#FF2300' },
        { pct: 90, color: '#FF1100' },	{ pct: 93, color: '#FF0000' },	{ pct: 96, color: '#FF0000' },
        { pct: 100, color: '#FF0000' }
    ];

    const closest = percentColors.reduce((a, b) => {
        return Math.abs(b.pct - percent) < Math.abs(a.pct - percent) ? b : a;
    });

    $(selector).css('background-color', closest.color);

    $(selector).css('width', `${percent}%`)
    //
    // $(selector).animate({
    //     width: `${percent}%`
    // }, 50);
}

const color_convert = function() {
    var pub = {}, canvas, context;
    canvas = document.createElement('canvas');
    canvas.height = 1;
    canvas.width = 1;
    context = canvas.getContext('2d');

    function byte_to_hex(byte) {
        // Turns a number (0-255) into a 2-character hex number (00-ff)
        return ('0'+byte.toString(16)).slice(-2);
    }

    pub.to_rgba_array = function(color) {
        /**
         * Turns any valid canvas fillStyle into a 4-element Uint8ClampedArray with bytes
         * for R, G, B, and A. Invalid styles will return [0, 0, 0, 0]. Examples:
         * color_convert.to_rgb_array('red')  # [255, 0, 0, 255]
         * color_convert.to_rgb_array('#ff0000')  # [255, 0, 0, 255]
         * color_convert.to_rgb_array('garbagey')  # [0, 0, 0, 0]
         */
        // Setting an invalid fillStyle leaves this unchanged
        context.fillStyle = 'rgba(0, 0, 0, 0)';
        // We're reusing the canvas, so fill it with something predictable
        context.clearRect(0, 0, 1, 1);
        context.fillStyle = color;
        context.fillRect(0, 0, 1, 1);
        return context.getImageData(0, 0, 1, 1).data;
    }

    pub.to_rgba = function(color) {
        /**
         * Turns any valid canvas fill style into an rgba() string. Returns
         * 'rgba(0,0,0,0)' for invalid colors. Examples:
         * color_convert.to_rgba('red')  # 'rgba(255,0,0,1)'
         * color_convert.to_rgba('#f00')  # 'rgba(255,0,0,1)'
         * color_convert.to_rgba('garbagey')  # 'rgba(0,0,0,0)'
         * color_convert.to_rgba(some_pattern)  # Depends on the pattern
         *
         * @param color  A string, pattern, or gradient
         * @return  A valid rgba CSS color string
         */
        var a = pub.to_rgba_array(color);
        return 'rgba('+a[0]+','+a[1]+','+a[2]+','+(a[3]/255)+')';
    }

    pub.to_hex = function(color) {
        /**
         * Turns any valid canvas fill style into a hex triple. Returns
         * '#000000' for invalid colors. Examples:
         * color_convert.to_hex('red')  # '#ff0000'
         * color_convert.to_hex('rgba(255,0,0,1)')  # '#ff0000'
         * color_convert.to_hex('garbagey')  # '#000000'
         * color_convert.to_hex(some_pattern)  # Depends on the pattern
         *
         * @param color  A string, pattern, or gradient
         * @return  A valid rgba CSS color string
         */
        var a = pub.to_rgba_array(color);
        // Sigh, you can't map() typed arrays
        var hex = [0,1,2].map(function(i) { return byte_to_hex(a[i]) }).join('');
        return '#'+hex;
    }

    return pub;
}();

const textToClipboard = (text) => {
    let copyText = document.getElementById("myInput");

    copyText.select();
    copyText.setSelectionRange(0, 99999);

    document.execCommand("copy");
}
