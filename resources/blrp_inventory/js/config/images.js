const images = {
    'hween_skull_22': 'images/item_skull_head.webp',
    'hween_skull_23': 'images/item_skull_head.webp',
    'prop_mikes_chair': 'images/prop_skid_chair_01.webp',
    'pager_racing': 'images/pager_generic.webp',
    'flyus_ticket_aug3': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug4': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug5': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug6': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug7': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug8': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug9': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug10': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug11': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug12': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug13': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug14': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug15': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug16': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug17': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug18': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug19': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug20': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug21': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug22': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug23': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug24': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug25': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug26': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug27': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug28': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug29': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug30': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_aug31': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec1': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec2': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec3': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec4': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec5': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec6': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec7': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec8': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec9': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec10': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec11': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec12': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec13': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec14': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec15': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec16': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec17': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec18': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec19': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec20': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec21': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec22': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec23': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec24': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec25': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec26': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec27': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec28': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec29': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec30': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_dec31': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan1': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan2': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan3': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan4': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan5': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan6': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan7': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan8': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan9': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan10': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan11': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan12': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan13': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan14': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan15': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan16': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan17': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan18': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan19': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan20': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan21': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan22': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan23': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan24': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan25': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan26': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan27': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan28': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan29': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan30': 'images/flyus_ticket_aug3.webp',
    'flyus_ticket_jan31': 'images/flyus_ticket_aug3.webp',
    'ticket_turner': 'images/ticket_turner.webp',
    'food_comp_hotdog': 'images/food_chi_dog.webp',
    'prison_clothes_dirty': 'images/prison_clothes_pile.webp',
    'prison_clothes_washed': 'images/prison_clothes_pile.webp',
    'prison_clothes_dry': 'images/prison_clothes_pile.webp',
    'prison_clothes_clean': 'images/prison_clothes_pile.webp',
    'food_prison_mushedpeas': 'images/prison_fulltray.webp',
    'food_prison_pizzasoup': 'images/prison_fulltray.webp',
    'food_prison_chunkymalk': 'images/prison_fulltray.webp',
    'food_prison_blandnoodles': 'images/prison_fulltray.webp',
    'food_prison_soggysalad': 'images/prison_fulltray.webp',
    // PHARMACY CRAFTING ITEMS
    'u_bupropion': 'images/medical_powder.webp',
    'u_naloxone': 'images/medical_powder.webp',
    'naloxone': 'images/nasal_inj.webp',
    'ing_benzalkonium': 'images/atc_bottle.webp',
    'ing_chloro': 'images/atc_bottle.webp',
    'ing_hydroacid': 'images/atc_bottle.webp',
    //--
    'food_prison_coldmixedveggies': 'images/prison_fulltray.webp',
    'food_prison_unsaltedrice': 'images/prison_fulltray.webp',
    'prison_frozen_meal': 'images/prison_fulltray.webp',
    'prison_microwaved_meal': 'images/prison_fulltray.webp',
    'blood_vial': 'images/blood_vial.webp',
    'empty_vial': 'images/empty_vial.webp',
    'hair_strand': 'images/hair_strand.webp',
    'jewl_silver_bracelet': 'images/jewl_silver_bracelet.webp',
    'jewl_gold_bracelet': 'images/jewl_gold_bracelet.webp',
    'jewl_diamond_bracelet': 'images/jewl_diamond_bracelet.webp',
    'jewl_gold_rolex': 'images/jewl_gold_rolex.webp',
    'jewl_diamond_rolex': 'images/jewl_diamond_rolex.webp',
    'jewl_silver_necklace': 'images/jewl_silver_necklace.webp',
    'jewl_gold_necklace': 'images/jewl_gold_necklace.webp',
    'jewl_diamond_necklace': 'images/jewl_diamond_necklace.webp',
    'veh_scrap': 'images/veh_scrap.webp',
    'bank_phone': 'images/bank_phone.webp',
    'usb_drive_a': 'images/usb_drive_a.webp',
    'usb_drive_corrupt': 'images/usb_drive.webp',
    'p_cone': 'images/p_cone.webp',
    'm_document': 'images/m_document.webp',
    'bullet_casing': 'images/bullet_casing.webp',
    'pink_slip': 'images/pink_slip.webp',
    'pcp': 'images/pcp.webp',
    'mescaline': 'images/pcp.webp',
    'heroin': 'images/heroin.webp',
    'mdma': 'images/mdma.webp',
    'lsd': 'images/lsd.webp',
    'hydrochloride': 'images/hydrochloride.webp',
    'ether': 'images/ether.webp',
    'formaldehyde': 'images/formaldehyde.webp',
    'safrole': 'images/safrole.webp',
    'anhydride': 'images/anhydride.webp',
    'opium': 'images/opium.webp',
    'ergot': 'images/ergot.webp',
    'lysergic': 'images/lysergic.webp',
    'paper': 'images/paper.webp',
    'xmas2021_q_note1': 'images/paper.webp',
    'xmas2021_q_note2': 'images/paper.webp',
    'xmas2021_q_note3': 'images/paper.webp',
    'note_vangelico': 'images/paper.webp',
    'veh_compreport': 'images/paper.webp',
    'shank_blueprint': 'images/shank_bp.webp',
    'vial': 'images/vial.webp',
    'pill_binder': 'images/pill_binder.webp',
    'pill_capsule': 'images/pill_capsule.webp',
    'plastic bag': 'images/ziploc-bag.webp',
    'steak': 'images/steak.webp',
    'food_steak_cheap': 'images/steak.webp',
    'scooby snacks': 'images/scooby.webp',
    'scooby_snacks': 'images/scooby.webp',
    'milk': 'images/milk.webp',
    'hotdog': 'images/hotdog.webp',
    'bread': 'images/bread.webp',
    'redgull': 'images/redbull.webp',
    'salt': 'images/saltpeter.webp',
    'cannabis_seed': 'images/seed.webp',
    'cannabis_seed_cayo': 'images/seed.webp',
    'seed_kingston': 'images/seed.webp',
    'seed_panic': 'images/seed.webp',
    'jute_seed': 'images/seed.webp',
    'seed_weed_a': 'images/seed.webp',
    'c24_kush_seed': 'images/seed.webp',
    'rare_artifact': 'images/artifact.webp',
    'common_artifact': 'images/common_artifact.webp',
    'kk_sscrate': 'images/kk_prcrate.webp',
    'gold_coin': 'images/coin.webp',
    'diamond_box': 'images/diamond-box.webp',
    'gold_brick': 'images/gold-brick.webp',
    'laptop_h': 'images/laptop.webp',
    'waybill_printer': 'images/waybill_printer.webp',
    'thermal_charge': 'images/thermal.webp',
    'scuba_kit': 'images/scuba.webp',
    'blood_diamond': 'images/blood_diamond.webp',
    'diamond_ring': 'images/ring.webp',
    'key_neverland': 'images/key.webp',
    'key_unknown': 'images/key.webp',
    'key_pdm': 'images/key.webp',
    'key_aod': 'images/key.webp',
    'key_aoda': 'images/key.webp',
    'key_aodb': 'images/key.webp',
    'key_unicorn': 'images/key.webp',
    'key_vagos': 'images/key.webp',
    'key_lost': 'images/key.webp',
    'key_spartan': 'images/key.webp',
    'key_casino_truck': 'images/key.webp',
    'key_eswarehouse': 'images/key.webp',
    'medkit': 'images/medical-kit.webp',
    'dressing': 'images/dressing.webp',
    'high_quality_fish': 'images/fish.webp',
    'regular_quality_fish': 'images/fish.webp',
    'fish': 'images/fish.webp',
    'gadget': 'images/gadget.webp',
    'cement': 'images/cement.webp',
    'nocrack': 'images/cement.webp',
    'ppizza': 'images/pizza.webp',
    'yeast': 'images/yeast.webp',
    'grape': 'images/grape.webp',
    'bitter_wine': 'images/wine.webp',
    'wine': 'images/wine.webp',
    'gocagola': 'images/cola.webp',
    'starlatte': 'images/latte.webp',
    'gsr_kit': 'images/gsr.webp',
    'gsr_kit_used': 'images/gsr.webp',
    'diamond': 'images/diamond.webp',
    'ruby': 'images/ruby.webp',
    'old_boot': 'images/old-boot.webp',
    'sapphire': 'images/saphire.webp',
    'mlion_hide': 'images/hide.webp',
    'deer_hide': 'images/hide.webp',
    'boar_hide': 'images/hide.webp',
    'rabbit_hide': 'images/hide.webp',
    'coyote_hide': 'images/hide.webp',
    'rat_hide': 'images/hide.webp',
    'taco_ingredient': 'images/meat.webp',
    'mlion_meat': 'images/meat.webp',
    'deer_meat': 'images/meat.webp',
    'boar_meat': 'images/meat.webp',
    'rabbit_meat': 'images/meat.webp',
    'rat_meat':'images/meat.webp',
    'coyote_meat': 'images/meat.webp',
    'meat': 'images/meat.webp',
    'dynamite': 'images/dynamite.webp',
    'hemp_cloth': 'images/hemp_cloth.webp',
    'aluminium': 'images/aluminium.webp',
    'raw_aluminium': 'images/raw-aluminium.webp',
    'gold': 'images/gold.webp',
    'raw_gold': 'images/raw-gold.webp',
    'processed_gold': 'images/raw-gold.webp',
    'ephedrine': 'images/ephedrine.webp',
    'weapon_disable_kit': 'images/disable.webp',
    'weapon_enable_kit': 'images/disable.webp',
    'safe_kit': 'images/cracking-kit.webp',
    'speedbomb': 'images/bomb.webp',
    'weapon_kit': 'images/pliers.webp',
    'donut': 'images/donut.webp',
    'pdonut': 'images/donut.webp',
    'fishing_rod': 'images/fishingrod.webp',
    'key_chain': 'images/keychain.webp',
    'tea': 'images/iced-tea.webp',
    'icetea': 'images/iced-tea.webp',
    'repairkit': 'images/industry.webp',
    'sandwich': 'images/sandwich.webp',
    'kebab': 'images/kebab.webp',
    'binoculars': 'images/jumelles.webp',
    'taco_kit': 'images/taco_kit.webp',
    'repair_parts': 'images/repair-parts.webp',
    'lockpick': 'images/lockpick-set.webp',
    'carrepairkit': 'images/fixkit.webp',
    'heavy_armor': 'images/armor.webp',
    'medium_armor': 'images/armor.webp',
    'light_armor': 'images/armor.webp',
    'police_armor': 'images/police_armor.webp',
    'tidalpod': 'images/tide-pod.webp',
    'lotto_ticket': 'images/lottery.webp',
    'peach': 'images/peach.webp',
    'gauze': 'images/guaze.webp',
    'inv_pack_mini_2': 'images/food_pack.webp',
    'inv_pack_mini_1': 'images/drink_pack.webp',
    'cookie': 'images/cookie.webp',
    'coffee': 'images/coffee.webp',
    'cigarette': 'images/cigarette-icon.webp',
    'personal_phone': 'images/mobile-phone.webp',
    'personal_phone_red': 'images/mobile-phone-red.webp',
    'personal_phone_orange': 'images/mobile-phone-orange.webp',
    'personal_phone_yellow': 'images/mobile-phone-yellow.webp',
    'personal_phone_green': 'images/mobile-phone-green.webp',
    'personal_phone_darkgreen': 'images/mobile-phone-darkgreen.webp',
    'personal_phone_teal': 'images/mobile-phone-teal.webp',
    'personal_phone_blue': 'images/mobile-phone-blue.webp',
    'personal_phone_purple': 'images/mobile-phone-purple.webp',
    'personal_phone_pink': 'images/mobile-phone-pink.webp',
    'personal_phone_brown': 'images/mobile-phone-brown.webp',
    'bandage': 'images/bandage.webp',
    'don_pereon': 'images/don.webp',
    'Vodka': 'images/vodka.webp',
    'beer': 'images/beer.webp',
    'firstaid': 'images/first-aid-kit.webp',
    'radio': 'images/radio.webp',
    'hand_radio': 'images/radio.webp',
    'duty_radio': 'images/radio.webp',
    'hand_gps': 'images/gps.webp',
    'duty_gps': 'images/gps.webp',
    'pills': 'images/pills.webp',
    'hydrocodone': 'images/hydrocodone.webp',
    'vicodin': 'images/vicodin.webp',
    'vicodin_prison': 'images/vicodin.webp',
    'morphine': 'images/morphine.webp',
    'water': 'images/water.webp',
    'water_prison': 'images/water.webp',
    'tacos': 'images/taco.webp',
    'lemonlimonad': 'images/lemonade.webp',
    'bobburger': 'images/burger.webp',
    'burger': 'images/burger.webp',
    'meth': 'images/meth.webp',
    'levacetylmethadol': 'images/lofexidine.webp',
    'crack cocaine': 'images/cocaine-baggy.webp',
    'cocaine2': 'images/cocaine-baggy.webp',
    'weed2': 'images/joint.webp',
    'cayo_kush_joint': 'images/joint.webp',
    'ld_joint_a': 'images/joint.webp',
    'ld_joint_b': 'images/joint.webp',
    'ld_joint_c': 'images/joint.webp',
    'ld_joint_d': 'images/joint.webp',
    'xm23_s9_joint_a': 'images/joint.webp',
    'xm23_s9_joint_b': 'images/joint.webp',
    'xm23_s9_joint_c': 'images/joint.webp',
    'll_joint_a': 'images/joint.webp',
    'll_joint_b': 'images/joint.webp',
    'marijuana2': 'images/weed.webp',
    'marijuana1': 'images/weed.webp',
    'cayo_kush_bud': 'images/weed.webp',
    'kifflom kush buds': 'images/weed.webp',
    'kifflom berry buds': 'images/weed.webp',
    'kingston_bud': 'images/ld_bud_a.webp',
    'weed': 'images/weed.webp',
    'ziptie': 'images/zip-ties_800x.webp',
    'suture': 'images/suture.webp',
    'lighter': 'images/lighter.webp',
    'wire_cutters': 'images/wire_cutters.webp',
    'craft_plastic': 'images/craft_plastic.webp',
    'evidence_bin': 'images/evidence_bin.webp',
    'pd_radio_tool': 'images/pd_radio_tool.webp',
    'cru_radio_tool': 'images/pd_radio_tool.webp',
    'beer_patriot_free': 'images/beer_patriot.webp',
    'lawyer_ticket': 'images/bank_draft.webp',
    'blunt1': 'images/backhoods_blunt.webp',
    'blunt2': 'images/backhoods_blunt.webp',
    'blunt4': 'images/backhoods_blunt.webp',
    'ld_blunt_a': 'images/backhoods_blunt.webp',
    'ld_blunt_b': 'images/backhoods_blunt.webp',
    'ld_blunt_c': 'images/backhoods_blunt.webp',
    'backhoods_cigar_cherry': 'images/backhoods_cigar.webp',
    'backhoods_cigar_honey': 'images/backhoods_cigar.webp',
    'backhoods_cigar_mojito': 'images/backhoods_cigar.webp',
    'backhoods_cigar_whiskey': 'images/backhoods_cigar.webp',
    'spellbound-bagdispenser': 'images/spellbound_box.webp',
    'thankyou-bagdispenser': 'images/bag_box.webp',
    'chbox_dispenser': 'images/bag_box.webp',
    'sweethearts_dispenser': 'images/bag_box.webp',
    'smiley-bagdispenser': 'images/bag_box.webp',
    'moviebag-dispenser': 'images/bag_box.webp',
    'alphamail_boxdispenser': 'images/alphamail_boxes.webp',
    'hunting_bait_7': 'images/animal_feed.webp',
    'hunting_bait_8': 'images/animal_feed.webp',
    'hunting_bait_9': 'images/animal_feed.webp',
    'cash': 'images/cash_item.webp',
    'steel_hardened': 'images/steel_hardened.webp',
    '0plytoiletpaper': 'images/12plytoiletpaper.webp',
    'xmas_gift_2021': 'images/xmas_gift.webp',
    'money_bag2': 'images/money_bag.webp',
    'money_bag_bcsb': 'images/money_bag.webp',
    'money_bag_bcsb2': 'images/money_bag.webp',
    'bank_moneybag': 'images/money_bag.webp',
    'money_bag_cb': 'images/money_bag.webp',
    'food_beef_patty': 'images/food_patties.webp',
    'food_vegan_patty': 'images/food_patties.webp',
    'drink_sprunkl': 'images/food_spec_sprunk.webp',
    'doll_blowup_2': 'images/doll_blowup.webp',
    'food_popcorn': 'images/food_holidaypopcorn.webp',
    'evidence_bin_general': 'images/evidence_bin.webp',
    'comp_sk_patr': 'images/comp_sk_generic.webp',
    'comp_sk_camo_bs': 'images/comp_sk_generic.webp',
    'comp_sk_camo_wl': 'images/comp_sk_generic.webp',
    'comp_sk_gold_ak': 'images/comp_sk_generic.webp',
    'comp_sk_dc': 'images/comp_sk_generic.webp',
    'comp_sk_camo_g': 'images/comp_sk_generic.webp',
    'comp_sk_gold_luxe': 'images/comp_sk_generic.webp',
    'comp_sk_patr_r': 'images/comp_sk_generic.webp',
    'comp_sk_camo_wl_r': 'images/comp_sk_generic.webp',
    'comp_sk_camo_bs_r': 'images/comp_sk_generic.webp',
    'wtint_sp_0': 'images/wtint_n_0.webp',
    'wtint_sp_2': 'images/wtint_mk2_11.webp',
    'wtint_sp_3': 'images/wtint_mk2_13.webp',
    'wtint_sp_4': 'images/wtint_mk2_4.webp',
    'wtint_sp_5': 'images/wtint_n_5.webp',
    'wtint_sp_7': 'images/wtint_n_7.webp',
    'wtint_sp_9': 'images/wtint_mk2_8.webp',
    'wtint_sp_10': 'images/wtint_n_1.webp',
    'wtint_sp_11': 'images/wtint_mk2_5.webp',
    'wtint_sp_12': 'images/wtint_mk2_2.webp',
    'wtint_sp_14': 'images/wtint_mk2_22.webp',
    'wtint_sp_16': 'images/wtint_mk2_3.webp',
    'wtint_sp_17': 'images/wtint_mk2_19.webp',
    'wtint_sp_18': 'images/wtint_mk2_18.webp',
    'wtint_sp_19': 'images/wtint_mk2_17.webp',
    'used_keycard': 'images/cb_keycard.webp',
    'final_keycard': 'images/cb_keycard.webp',
    'cb_keycard_u': 'images/cb_keycard.webp',
    'bestbuds_ficusjoint': 'images/weed3.webp',
    'blunt4': 'images/blunt3.webp',
    'ny23q4_s6_a': 'images/manilla_folder.webp',
    'food_lucky_charms': 'images/rotten_food.webp',
    'lockbox_fleeca_key': 'images/safety_deposit_key.webp',
    //Camp Morningwood Blueprints
    'camp_blueprint_a': 'images/crumple_blueprint.webp',
    'camp_blueprint_b': 'images/crumple_blueprint.webp',
    'camp_blueprint_c': 'images/crumple_blueprint.webp',
    'camp_blueprint_d': 'images/crumple_blueprint.webp',
    'camp_blueprint_e': 'images/crumple_blueprint.webp',
    'camp_blueprint_f': 'images/crumple_blueprint.webp',
    'camp_blueprint_g': 'images/crumple_blueprint.webp',
    'camp_blueprint_h': 'images/crumple_blueprint.webp',
    'camp_blueprint_i': 'images/crumple_blueprint.webp',
    'camp_blueprint_j': 'images/crumple_blueprint.webp',
    'camp_blueprint_k': 'images/crumple_blueprint.webp',
    'camp_blueprint_l': 'images/crumple_blueprint.webp',
    'camp_blueprint_m': 'images/crumple_blueprint.webp',
    'camp_blueprint_n': 'images/crumple_blueprint.webp',
    'camp_blueprint_o': 'images/crumple_blueprint.webp',
    'camp_blueprint_p': 'images/crumple_blueprint.webp',
    'camp_blueprint_q': 'images/crumple_blueprint.webp',
    'camp_blueprint_r': 'images/crumple_blueprint.webp',
    'camp_blueprint_s': 'images/crumple_blueprint.webp',
    'camp_blueprint_t': 'images/crumple_blueprint.webp',
    //Valentines shit
    'candyheart_1' : 'images/blue_candy.webp',
    'candyheart_2' : 'images/purple_candy.webp',
    'candyheart_3' : 'images/yellow_candy.webp',
    'candyheart_4' : 'images/green_candy.webp',
    'candyheart_5' : 'images/blue_candy.webp',
    'candyheart_6' : 'images/purple_candy.webp',
    'candyheart_7' : 'images/yellow_candy.webp',
    'candyheart_8' : 'images/green_candy.webp',
    'candyheart_9' : 'images/blue_candy.webp',
    'candyheart_10' : 'images/purple_candy.webp',
    'candyheart_11' : 'images/yellow_candy.webp',
    'candyheart_12' : 'images/green_candy.webp',
    'candyheart_13' : 'images/blue_candy.webp',
    'candyheart_14' : 'images/purple_candy.webp',
    'candyheart_15' : 'images/yellow_candy.webp',
    'candyheart_16' : 'images/green_candy.webp',
    'candyheart_17' : 'images/blue_candy.webp',
    'candyheart_18' : 'images/purple_candy.webp',
    'candyheart_19' : 'images/yellow_candy.webp',
    'candyheart_20' : 'images/green_candy.webp',
    'candyheart_21' : 'images/blue_candy.webp',
    'candyheart_22' : 'images/purple_candy.webp',
    'candyheart_23' : 'images/yellow_candy.webp',
    'candyheart_24' : 'images/green_candy.webp',
    'candyheart_25' : 'images/blue_candy.webp',
    'candyheart_26' : 'images/purple_candy.webp',
    'candyheart_27' : 'images/yellow_candy.webp',
    'candyheart_28' : 'images/green_candy.webp',
    'candyheart_29' : 'images/blue_candy.webp',
    'prop_bc_loot_b_a': 'images/prop_bc_loot_b.webp',
    // guns / weapons / hand helds
    'wbody|weapon_candybomb': 'images/wep_candybomb.webp',
    'wbody|weapon_vp897': 'images/wep_vp897.webp',
    'wbody|weapon_sp45': 'images/weapon_sp45.webp',
    'wbody|weapon_candycane': 'images/wep_candycane.webp',
    'wbody|weapon_stone_hatchet': 'images/wep_stonehatchet.webp',
    'wbody|weapon_hotdog': 'images/WEAPON_HOTDOG.webp',
    'wbody|weapon_knuckle': 'images/WEAPON_BRASSKNUCKLES.webp',
    'wbody|weapon_machete': 'images/WEAPON_MACHETE.webp',
    'wbody|weapon_firework': 'images/WEAPON_FIREWORK.webp',
    'wbody|weapon_pistol': 'images/WEAPON_PISTOL.webp',
    'wbody|weapon_pistol_mk2': 'images/WEAPON_PISTOL_MK2.webp',
    'wbody|weapon_combatpistol': 'images/WEAPON_COMBATPISTOL.webp',
    'wbody|weapon_glock17': 'images/WEAPON_GLOCK17.webp',
    'wbody|weapon_snspistol': 'images/WEAPON_SNSPISTOL.webp', // done
    'wbody|weapon_snspistol_mk2': 'images/WEAPON_SNSPISTOL_MK2.webp',
    'wbody|weapon_heavypistol': 'images/WEAPON_HEAVYPISTOL.webp',
    'wbody|weapon_pistol50': 'images/WEAPON_PISTOL50.webp',
    'wbody|weapon_vintagepistol': 'images/WEAPON_VINTAGEPISTOL.webp',
    'wbody|weapon_pumpshotgun': 'images/WEAPON_PUMPSHOTGUN.webp',
    'wbody|weapon_pumpshotgun2': 'images/WEAPON_PUMPSHOTGUN2.webp',
    'wbody|weapon_revolver': 'images/WEAPON_REVOLVER.webp',
    'wbody|weapon_revolver_mk2': 'images/weapon_revolver_mk2.webp',
    'wbody|weapon_doubleaction': 'images/WEAPON_DOUBLEACTION.webp',
    'wbody|weapon_assaultrifle': 'images/WEAPON_ASSAULTRIFLE.webp',
    'wbody|weapon_assaultrifle2': 'images/WEAPON_ASSAULTRIFLE2.webp',
    'wbody|weapon_assaultrifle_mk2': 'images/WEAPON_ASSAULTRIFLE_MK2.webp',
    'wbody|weapon_petrolcan': 'images/jerry_can_full.webp',
    'empty_petrolcan': 'images/jerry_can_empty.webp',
    'wbody|weapon_stungun': 'images/tazer.webp',
    'wbody|weapon_smg': 'images/WEAPON_SMG.webp',
    'wbody|weapon_ump45': 'images/WEAPON_UMP45.webp',
    'wbody|weapon_knife': 'images/WEAPON_KNIFE.webp',
    'wbody|weapon_switchblade': 'images/switch_blade_body.webp',
    'wbody|weapon_milspecknife': 'images/milspecknife.webp',
    'wbody|weapon_tomahawk': 'images/tomahawk.webp',
    'wbody|weapon_battleaxe': 'images/battle_axe_body.webp',
    'wbody|weapon_hammer': 'images/hammer.webp',
    'wbody|weapon_flashlight': 'images/wep_flashlight.webp',
    'wbody|weapon_flashlight_uv': 'images/wep_flashlight_uv.webp',
    'wbody|weapon_golfclub': 'images/golfclub_body.webp',
    'wbody|weapon_wrench': 'images/wrench_body.webp',
    'wbody|weapon_crowbar': 'images/crowbar.webp',
    'wbody|weapon_bat': 'images/wep_bat.webp',
    'wbody|weapon_lucille': 'images/Lucille.webp',
    'wbody|weapon_hobbyhorse': 'images/wep_hobbyhorse.webp',
    'wbody|weapon_nightstick': 'images/nightstick.webp',
    'wbody|weapon_flare': 'images/flare.webp',
    'wbody|weapon_snowball': 'images/WEAPON_SNOWBALL.webp',
    'wbody|weapon_poolcue': 'images/pool-cue.webp',
    'wbody|weapon_flashbang': 'images/flashbang.webp',
    'wbody|weapon_fireextinguisher': 'images/fire-extinguisher.webp',
    'wbody|weapon_bottle': 'images/broken_bottle.webp',
    'wbody|weapon_dagger': 'images/WEAPON_DAGGER.webp',
    'wbody|weapon_shank': 'images/shank.webp',
    'wbody|weapon_heavyshotgun': 'images/hunting_rifle.webp',
    'wbody|weapon_lightsaber': 'images/lightsaber_green.webp',
    'wbody|weapon_lightsaber_blue': 'images/lightsaber_blue.webp',
    'wbody|weapon_lightsaber_purple': 'images/lightsaber_purple.webp',
    'wbody|weapon_lightsaber_red': 'images/lightsaber_red.webp',
    'wbody|weapon_lightsaber_yellow': 'images/lightsaber_yellow.webp',
    'wbody|weapon_specialcarbine': 'images/WEAPON_SPECIALCARBINE.webp',
    'wbody|weapon_specialcarbine_mk2': 'images/WEAPON_SPECIALCARBINE_MK2.webp',
    'wbody|weapon_bullpuprifle_mk2': 'images/WEAPON_BULLPUPRIFLE_MK2.webp',
    'wbody|weapon_militaryrifle': 'images/WEAPON_MILITARYRIFLE.webp',
    'wbody|weapon_heavyrifle': 'images/WEAPON_HEAVYRIFLE.webp',
    'wbody|weapon_tacticalrifle': 'images/WEAPON_TACTICALRIFLE.webp',
    'wbody|weapon_flaregun': 'images/wep_flaregun.webp',
    'wbody|weapon_metaldetector': 'images/weapon_metaldetector.webp',
    'wbody|weapon_gadgetpistol': 'images/WEAPON_GADGETPISTOL.webp',
    'wep_flaregun_disabled': 'images/wep_flaregun.webp',
    'wbody|weapon_speedgun': 'images/wep_speedgun.webp',
    'wbody|weapon_servicepistol_45': 'images/Service_Pistol.webp',
    'wbody|weapon_servicepistol_9mm': 'images/Service_Pistol.webp',
    'wbody|weapon_servicepistol_auto': 'images/Service_Pistol.webp',

    'wbody|weapon_carbinerifle': 'images/WEAPON_CARBINE.webp',
    'wbody|weapon_carbinerifle_mk2': 'images/WEAPON_CARBINE_MK2.webp',
    'wbody|weapon_carbineriflet': 'images/WEAPON_CARBINET.webp',
    'wbody|weapon_carbineriflemk2t': 'images/WEAPON_CARBINEMK2T.webp',
    'wbody|weapon_fm5_spearlt': 'images/WEAPON_FM5_SPEARLT.webp',
    'wbody|weapon_dbshotgun': 'images/WEAPON_DBSHOTGUN.webp',
    'wbody|weapon_gusenberg': 'images/WEAPON_GUSENBERG.webp',
    'wbody|weapon_microsmg': 'images/WEAPON_MICROSMG.webp',
    'wbody|weapon_machinepistol': 'images/WEAPON_MACHINEPISTOL.webp',
    'wbody|weapon_compactrifle': 'images/WEAPON_COMPACTRIFLE.webp',
    'wbody|weapon_musket': 'images/WEAPON_MUSKET.webp',
    'wbody|weapon_molotov': 'images/WEAPON_MOLOTOV.webp',
    'wbody|weapon_mg': 'images/WEAPON_MG.webp',
    'wbody|weapon_smg_mk2': 'images/WEAPON_SMG_MK2.webp',
    'wbody|weapon_sawnoffshotgun': 'images/WEAPON_SAWNOFFSHOTGUN.webp',
    'wbody|weapon_appistol': 'images/WEAPON_APPISTOL.webp',
    'wbody|weapon_beanbag': 'images/beanbag.webp',
    'wbody|weapon_katana': 'images/weapon_katana.webp',
    'wbody|weapon_dragon_katana_blue': 'images/clth_chain_triads.webp',
    'wbody|weapon_katana_2': 'images/weapon_katana_2.webp',
    'wbody|weapon_minismg': 'images/WEAPON_MINISMG.webp',
    'wbody|weapon_grenade': 'images/WEAPON_GRENADE.webp',
    'wbody|weapon_railgun': 'images/WEAPON_EMPGUN.webp',
    'wbody|weapon_rpg': 'images/WEAPON_RPG.webp',
    'wbody|weapon_stickybomb': 'images/weapon_stickybomb.webp',
    'wbody|weapon_huntingrifle': 'images/weapon_huntingrifle.webp',
    'wbody|weapon_pistolxm3': 'images/WEAPON_PISTOLXM3.webp',
    'wbody|weapon_tecpistol': 'images/WEAPON_TECPISTOL.webp',
    'wbody|weapon_paintball': 'images/weapon_paintball.webp',

    'wbody|weapon_ceramicpistol': 'images/ceramicpistol.webp',
    'wbody|weapon_assaultsmg': 'images/WEAPON_ASSAULTSMG.webp',
    'wbody|weapon_combatpdw': 'images/WEAPON_COMBATPDW.webp',
    'wbody|weapon_assaultshotgun': 'images/assaultshotgun.webp',
    'wbody|weapon_advancedrifle': 'images/WEAPON_ADVANCEDRIFLE.webp',
    'wbody|weapon_bullpuprifle': 'images/WEAPON_BULLPUPRIFLE.webp',

    'wbody|weapon_combatmg': 'images/WEAPON_COMBATMG.webp',
    'wbody|weapon_combatmg_mk2': 'images/WEAPON_COMBATMG_MK2.webp',
    'wbody|weapon_pumpshotgun_mk2': 'images/WEAPON_PUMPSHOTGUN_MK2.webp',
    'wbody|weapon_bullpupshotgun': 'images/bullpupshotgun.webp',
    'wbody|weapon_autoshotgun': 'images/WEAPON_AUTOSHOTGUN.webp',
    'wbody|weapon_combatshotgun': 'images/combatshotgun.webp',

    'wbody|weapon_grenadelauncher': 'images/grenadelauncher.webp',
    'wbody|weapon_minigun': 'images/WEAPON_MINIGUN.webp',
    'wbody|weapon_sniperrifle': 'images/WEAPON_SNIPER.webp',
    'wbody|weapon_heavysniper': 'images/WEAPON_HEAVYSNIPER.webp',
    'wbody|weapon_heavysniper_mk2': 'images/WEAPON_HEAVYSNIPER_MK2.webp',
    'wbody|weapon_precisionrifle': 'images/precisionrifle.webp',
    'wbody|weapon_marksmanrifle': 'images/WEAPON_MARKSMANRIFLE.webp',
    'wbody|weapon_marksmanrifle_mk2': 'images/WEAPON_MARKSMANRIFLE_MK2.webp',
    'wbody|weapon_compactlauncher': 'images/WEAPON_COMPACTLAUNCHER.webp',

    'wbody|weapon_hominglauncher': 'images/WEAPON_HOMINGLAUNCHER.webp',
    'wbody|weapon_marksmanpistol': 'images/marksmanpistol.webp',
    'wbody|weapon_navyrevolver': 'images/WEAPON_NAVYREVOLVER.webp',
    'wbody|weapon_raypistol': 'images/WEAPON_RAYPISTOL.webp',
    'wbody|weapon_rayminigun': 'images/WEAPON_RAYMINIGUN.webp',
    'wbody|weapon_raycarbine': 'images/WEAPON_RAYCARBINE.webp',

    'wbody|weapon_grenadelauncher_smoke': 'images/weapon_grenadelauncher_smoke.webp',
    'wbody|weapon_lesslauncher': 'images/WEAPON_LESSLAUNCHER.webp',

    // ammo
    'wammo|weapon_firework': 'images/ammo_firework.webp',
    'wammo|weapon_beanbag': 'images/ammo_beanbag.webp',
    'wammo|weapon_stungun': 'images/ammo_stuncartridge.webp',
    'wammo|weapon_flaregun': 'images/ammo_flaregun.webp',
    'wammo|weapon_railgun': 'images/ammo_emp.webp',
    'ammo_762nato': 'images/ammo_762x51.webp',
    'ammo_9mm_nato': 'images/ammo_9mm.webp',

    'component_flashlight': 'images/flashlight_mod.webp',
    'component_supp_pi': 'images/component_supp.webp',
    'component_supp_smg': 'images/component_supp.webp',
    'component_supp_ar': 'images/component_supp.webp',

    'guitar(green)': 'images/guitar.webp',
    'guitar(white)': 'images/guitar.webp',
    'guitar(gibson)': 'images/guitar.webp',
    'guitar(acoustic)': 'images/guitar.webp',

    //GoPostal

    'gopostal_letter': 'images/gopostal_letter.webp',
    'gopostal_parcel': 'images/gopostal_parcel.webp',
    'gopostal_freight': 'images/gopostal_freight.webp',

};

for(let i = 1; i <= 27; i++) {
    images['prison_key_' + i] = 'images/key.webp'
}

export default images;
