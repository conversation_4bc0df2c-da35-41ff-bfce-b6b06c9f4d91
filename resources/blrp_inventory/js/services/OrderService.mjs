import Inventory from '../components/Inventory.mjs'

class OrderService {
    saveInventoryOrder(name, items) {
        // console.log('saving', name, items)
        if(name === 'market') return

        if(items.length < 1) return

        localStorage.setItem(name, JSON.stringify(items));
    }

    getInventoryOrder(containerName) {
        if(containerName === 'market') return []
        // if(!containerName) return console.error('Must include a container name');
        if(!containerName === undefined) return console.error('Must include a container name');
        if(containerName === 'inventory') containerName = `${Inventory.characterNumber}_inventory`
        const data = localStorage.getItem(containerName);
        if (!data) {
            return [];
        }
        return JSON.parse(data);
    }
}

export default new OrderService();
