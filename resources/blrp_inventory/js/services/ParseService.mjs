import images from '../config/images.js'
import { sortItemsByName, sortItemsByPosition } from '../helpers.mjs'
import OrderService from './OrderService.mjs'

class ParseService {

    async parseItems(items, containerName, ignoreSlotsAutofill = [], serverInventoryOrder, maxSlots = 200)
    {
        // Error correction
        const appendAfterwards = []

        const parsedItems = []

        if(serverInventoryOrder === undefined || serverInventoryOrder == null)
        {
            serverInventoryOrder = []
        }

        const storedPositions = serverInventoryOrder // ?? OrderService.getInventoryOrder(containerName)

        let storedPositionsMap = [];

        if(storedPositions.map) {
          storedPositionsMap = storedPositions.map(p => parseInt(p.position));
        }

        const usedPositions = [
            ...ignoreSlotsAutofill,
            ... storedPositionsMap
        ]

        const takenPositions = [];

        for (const item of items) {
            // console.log('[Parsing] Item:', item.name, 'Amount:', item.amount)

            const storedPositionSets = storedPositions.filter(p => p.idname === item.idname)

            // If there are not multiple stacks of the same item or item does not exist
            if(storedPositionSets.length === 1 || storedPositionSets.length === 0)
            {
                let position = storedPositionSets[0] ? parseInt(storedPositionSets[0].position)
                    : ParseService.#getNextFreePosition(usedPositions)

                if (takenPositions.includes(position)) {
                    usedPositions.push(position);
                    position = ParseService.#getNextFreePosition(usedPositions)
                }

                const parsedItem =  {
                    position: position,
                    name: item.name,
                    amount: item.amount ?? null,
                    description: item.description ?? null,
                    weight: parseFloat(item.weight) ?? 0,
                    price: item.price ?? null,
                    unlimited: item.unlimited ?? false,
                    idname: item.idname,
                    image: item.override_image ?? await ParseService.#mapImage(item.idname),
                    // container: 'inventory',
                    meta: item.meta,
                    flags: item.flags,
                    tebex: item.tebex ?? false,
                    claimable: item.claimable ?? false,
                }

                usedPositions.push(position)
                takenPositions.push(position)
                parsedItems.push(parsedItem)

                if(parsedItems.length === items.length) continue;
            }
            else
                // If there are multiple stacks of the same item
            {
                const totalAmount = parseInt(item.amount)
                let filledAmount = 0;
                let realAmountLeft = parseInt(item.amount);

                for(const positionSet of storedPositionSets)
                {
                    let position = parseInt(positionSet.position);
                    let cachedAmountInPosition = parseInt(positionSet.amount);

                    if (cachedAmountInPosition > realAmountLeft) {
                        continue;
                    }

                    realAmountLeft = realAmountLeft - cachedAmountInPosition;
                    filledAmount = filledAmount + cachedAmountInPosition

                    if (takenPositions.includes(position)) {
                        usedPositions.push(position);
                        position = ParseService.#getNextFreePosition(usedPositions)
                    }

                    const parsedItem =  {
                        position: position,
                        name: item.name,
                        amount: cachedAmountInPosition,
                        description: item.description ?? null,
                        weight: parseFloat(item.weight) ?? 0,
                        price: item.price ?? null,
                        unlimited: item.unlimited ?? false,
                        idname: item.idname,
                        image: item.override_image ?? await ParseService.#mapImage(item.idname),
                        // container: 'inventory',
                        meta: item.meta,
                        tebex: item.tebex ?? false,
                        claimable: item.claimable ?? false,
                    }

                    usedPositions.push(position)
                    takenPositions.push(position)
                    parsedItems.push(parsedItem)

                    if(parsedItems.length === items.length) continue;
                }

                if ( filledAmount - totalAmount ) {
                    appendAfterwards.push({
                        position: null,
                        name: item.name,
                        amount: realAmountLeft,
                        description: item.description ?? null,
                        weight: parseFloat(item.weight) ?? 0,
                        price: item.price ?? null,
                        unlimited: item.unlimited ?? false,
                        idname: item.idname,
                        image: item.override_image ?? await ParseService.#mapImage(item.idname),
                        // container: 'inventory',
                        meta: item.meta,
                        tebex: item.tebex ?? false,
                        claimable: item.claimable ?? false,
                    })
                }
            }
        }

        for (const item of appendAfterwards) {
            const positionToInsertAt = ParseService.#getNextFreePosition(usedPositions)
            console.log(`Autocorrecting ${ item.name } x ${item.amount} at position ${positionToInsertAt}`)

            item.position = positionToInsertAt

            usedPositions.push(positionToInsertAt)
            takenPositions.push(positionToInsertAt)
            parsedItems.push(item)
        }

        const finalizedItems = ParseService.#fillEmptyPositionsAndSort(parsedItems, maxSlots)

        // console.log('pared items', finalizedItems)

        return finalizedItems;
    }

    static #getNextFreePosition(usedPositions)
    {
        let counter = 0

        // console.log('getting next free position', usedPositions)

        while (usedPositions.includes(counter)) {
            counter++;
        }

        // console.log('next free position is ', counter)
        return counter;
    }

    static #fillEmptyPositionsAndSort(items, maxSlots = 300)
    {
        const fullItems = []

        for (let i = 0; i < maxSlots; i++) {
            const itemInPosition = items.find(n => n.position === i);
            if (!itemInPosition) {
                fullItems.push(ParseService.#makeEmptyItem(i))
            } else {
                fullItems.push(itemInPosition)
            }
        }

        return sortItemsByPosition(fullItems)
    }

    static #makeEmptyItem(slot)
    {
        return {position: parseInt(slot), id: slot, name: 'None', amount: 0, image: ''};
    }

    static async #mapImage(idname)
    {
        if (idname) {
            let parsedName = idname.toLowerCase();

            if (idname.includes(':meta')) {
                parsedName = parsedName.substring(0, parsedName.indexOf(':'));
            }

            // Image is defined or cached already
            if (images[parsedName]) {
                return images[parsedName];
            }

            let ref = `images/${parsedName}.webp`;

            const res = await fetch(ref, { method: 'HEAD' })

            if (res.ok) {
                images[parsedName] = ref // Cache it
                return ref
            } else {
                ref = `images/${parsedName}.png`
                images[parsedName] = ref // Cache it
                return ref
            }
        }

        return 'images/empty.webp';
    }
}

export default new ParseService()
