import Inventory from '../components/Inventory.mjs'
import InterfaceService from './InterfaceService.mjs'
import { compareOptions } from '../helpers.mjs'

class FiveMService {
    setItemSlots(slots)
    {
        fetch(`https://blrp_inventory/setItemSlots`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify(slots)
        }).then(resp => resp.json()).then(resp => {
            return resp;
        });
    }

    performNuiMove(action, item_code, amount, chest_name, meta, position)
    {
      return fetch(`https://blrp_inventory/${action}`, {
          method: 'POST',
          headers: {
              'Content-Type': 'application/json; charset=UTF-8',
          },
          body: JSON.stringify({
              item_code: item_code,
              item_amount: amount,
              chest_name: chest_name,
              meta: meta,
              position: position,
          })
      }).then(resp => resp.json());
    }

    performRequestItemActions(item)
    {
      /* if(Inventory.mode !== 'inventory') return false; */
      if(item.container !== 'inventory') {
        $('#action-options').html('');
         return false;
      }

      // Return with buy/sell options without touching server if properties has a price assigned
      if(item.properties.price) {
        InterfaceService.showActions(['Buy', 'Sell'])
        return false;
      }

      fetch(`https://blrp_inventory/requestItemActions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
          item_code: item.properties.idname,
        })
      }).then(resp => resp.json()).then(actions => {
        actions.sort(compareOptions);
        InterfaceService.showActions(actions);
      });
    }

    itemMovedSync(data)
    {
      fetch(`https://blrp_inventory/itemMovedSync`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify(data)
        }).then(resp => resp.json()).then(resp => {
            return resp;
        });
    }

    itemMoved()
    {
        fetch(`https://blrp_inventory/itemBeingMoved`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({})
        }).then(resp => resp.json()).then(resp => {
            return resp;
        });
    }

    performItemAction(action_name, item_code, amount, meta)
    {
        fetch(`https://blrp_inventory/performItemAction`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({
                action_name: action_name.replace(' & Close', ''),
                item_code: item_code,
                item_amount: amount,
                meta: meta,
            })
        }).then(resp => resp.json()).then(resp => {
            return resp;
        });
    }

    escapeInterface(mode, storage_name)
    {
        fetch(`https://blrp_inventory/escape`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({
                mode: mode,
                inventory_name: Inventory.inventoryGrid.realName,
                chest_name: storage_name,
                inventory_order: JSON.stringify(Inventory.inventoryGrid.inventoryOrder),
                storage_order: JSON.stringify(Inventory.storageGrid.inventoryOrder),
            })
        }).then(resp => resp.json()).then(resp => {
            return resp;
        });
    }
}

export default new FiveMService();
