import Inventory from '../components/Inventory.mjs'
import Item from '../components/Item.mjs'
import AudioService from './AudioService.mjs'
import FiveMService from './FiveMService.mjs'

class InterfaceService {
    currentTime = 0;
    timerObject = null;
    interfaceShowing = false
    tryAutomaticAction = false

    componentAliases = {};

    initializeAliases(aliases) {
      this.componentAliases = aliases;
    }

    get targetMoveQuantity() {
        const amount = parseInt($('#quantity').val());

        if (isNaN(amount)) {
            return 1;
        }

        if (amount < 0) {
            return 1;
        }

        return amount
    }

    setStartAutoClose()
    {
        this.currentTime = 180; // how long we should keep the inventory open for (in seconds)
        if(this.timerObject) return false;
        this.timerObject = setInterval(() => {
            this.currentTime = this.currentTime - 1;
            $('#timer-seconds').html(this.currentTime);
            if (this.currentTime === 0) this.hideInterface();
        }, 1000)
    }

    showInterface()
    {
        $(".main-inventory").css('display', "flex");
        this.setStartAutoClose()
    }

    hideInterface()
    {
        $(".main-inventory").css('display', "none");

        if (this.interfaceShowing) {
            FiveMService.escapeInterface(Inventory.mode, Inventory.storageGrid.realName)
        }

        Inventory.selectedItem = null;
        clearInterval(this.timerObject);
        this.timerObject = null;
        this.interfaceShowing = false;
        this.tryAutomaticAction = false;
        Inventory.inventoryGrid.items = []
        Inventory.storageGrid.items = []
        Inventory.inventoryGrid.realName = null;
        Inventory.storageGrid.realName = null;
        Inventory.storageGrid.maxWeight = null;
        Inventory.storageGrid.weight = null;
        // this.hideAllHotbar()

        $('.inventory-meta-content').fadeOut(200);
        $('#inventory').fadeOut(200);
        $(".interaction-area").css("display", "flex");
        $('.grid-container').empty();
        $('.main-inventory .grid-item-real').remove();
        $('#action-options').html('');
        // $('.hotbar').hide();


        // Inventory.openLocked = true;
        // Inventory.openLockInterval = setTimeout(() => {
        //     Inventory.openLocked = false;
        // }, 1500)

    }

    showAllHotbar()
    {
        let allSlots = $('#grid-container-hotbar').children()
        for (let slot of allSlots) {
            $(slot).addClass('selected-hotbar')
        }
    }

    hideAllHotbar()
    {
        let allSlots = $('#grid-container-hotbar').children()
        for (let slot of allSlots) {
            $(slot).removeClass('selected-hotbar')
        }
    }

    setHotbarSelected({ hotbar_number, should_flash, direction })
    {
        if(!should_flash) return

        hotbar_number = hotbar_number - 1

        let slot = $('#grid-container-hotbar').find(`[data-position="${hotbar_number}"]`)[0]

        if (slot) {
            $('#grid-container-hotbar').children().css('opacity', '0');
            $('.hotbar').show();

            let allSlots = $('#grid-container-hotbar').children().css('opacity', '0')

            for (let slot of allSlots) {
                const amount = $(slot).find($("span[class='span-amount']"))
                amount.html('1')

                $(slot).css('opacity', '0')
                $(slot).css('transition', 'all 0.5s')

                if (direction === 'out') {
                    $(slot).css('border', '1px solid green')
                } else if (direction === 'in') {
                    $(slot).css('border', '1px solid red')
                }
            }

            $(slot).css('opacity', '1').after(() => {
                setTimeout(() => {
                    $(slot).css('opacity', '0')
                    $(slot).css('border', 'none')
                }, 2000)
            })
        }
    }

    registerMoveHandler()
    {
        const self = this;
        $('.grid-item-real').draggable({
            // zIndex: 2,
            scroll: false,
            scrollSensitivity: 35,
            scrollSpeed: 28,
            helper: 'clone',
            appendTo: '#inventory',
            // appendTo: 'interaction-area',
            // containment: 'interaction-area',
            revert: true,
            revertDuration: 1,
            start: function (event, ui) {
                // ui.helper.width($(this).width());
                // $(this).css('visibility', 'hidden')
                // $(ui.helper).css('z-index', 2)

                const hash = $(event.target).attr('data-hash');
                const item = Inventory.findItemByHash(hash)
                $('.grid-item-real').removeClass('selected');
                $(this).addClass('selected');
                FiveMService.performRequestItemActions(item)

                // $(this).css('visibility', 'hidden')
                // $(this).find($("span[class='span-amount']"))[0].html(23)

                let quantity = self.targetMoveQuantity
                const amountElement = $(ui.helper).find($("span[class='span-amount']"))[0]
                const weightElement = $(ui.helper).find($("span[class='span-weight']"))[0]
                const nameElement = $(ui.helper).find($("span[class='span-name']"))[0]

                if (quantity === 0) {
                    quantity = item.properties.amount
                }

                if(quantity > item.properties.amount) {
                    quantity = `<span style="text-decoration: line-through">${quantity}</span> ${item.properties.amount}`
                }

                $(ui.helper).css('background-color', 'rgba(0, 0, 0, 0.0)')
                $(ui.helper).addClass('grid-item-being-dragged')
                $(weightElement).css('display', 'none')
                $(nameElement).css('display', 'none')
                $(amountElement).html(quantity);

                if (Inventory.smallMode) {
                    // Figure this out
                } else {
                    $(this).draggable('instance').offset.click = {
                        left: Math.floor(ui.helper.width() / 2),
                        top: Math.floor(ui.helper.height() / 2) + 4
                    };
                }
            },
            stop: function(event) {
                const targetElement = self.getItemOrActionAtMousePosition(event.pageX, event.pageY);
                const actionName = $(targetElement).attr('data-actionName')

                if (actionName) {
                    const hash = $(event.target).attr('data-hash');
                    const item = Inventory.findItemByHash(hash)
                    item.doAction(actionName, self.targetMoveQuantity)
                } else {
                    const toJQ = $(targetElement);
                    const fromItem = Inventory.findItemByHash($(event.target).attr('data-hash'))
                    let toItem = Inventory.findItemByHash(toJQ.attr('data-hash'))

                    /* Clear item actions if moving out of inventory */
                    if(toItem.container !== 'inventory') {
                      $('#action-options').html('');
                    }

                    // Get the hash from the div with .grid-item if the targeted element is any child of that div (image, text, etc)
                    if(!toJQ.hasClass('grid-item')) {
                      toItem = Inventory.findItemByHash(toJQ.parent().closest('div.grid-item').attr('data-hash'))
                    }

                    // toItem = Inventory.findItemByHash(toJQ.parent().closest('div.grid-item').attr('data-hash'))
                    // .grid

                    let toStorage = toJQ.parent().closest('div.grid').hasClass('storage-items');

                    if(!fromItem)
                      return; //self.hideInterface() // alert(`could not find item at cursor by hash: ${$(event.target).attr('data-hash')}`)

                    Inventory.moveItems(fromItem, toItem, toStorage)
                }
            },
        });

        /* This "feature" is inconsistent at best. Needs work
        $('.grid-item').mouseenter(function (event) {
            const element = $(event.target);
            const hash = element.attr('data-hash');
            const item = Inventory.findItemByHash(hash)

            if (item && !item.isGhostItem) {
                element.find($(`span[class='span-weight']`))
                    .text(item.totalWeight)
            }
        })

        $('.grid-item').mouseleave(function (event) {
            const element = $(event.target);
            const hash = element.attr('data-hash');
            const item = Inventory.findItemByHash(hash)

            if (item && !item.isGhostItem) {
                element.find($(`span[class='span-weight']`))
                    .text(item.properties.weight)
            }
        })
        */
    }

    showError(message)
    {
        $('.error-area').show(200);
        $('.error-area').text(message);
        setTimeout(() => {
            $('.error-area').hide(200);
        }, 2000);
    }

    updateHotkeySlots()
    {
        let hotkeys = [];
        Inventory.clearHotbar();
        for (let i = 0; i < 5; i++) {
            const entity = Inventory.inventoryGrid.findItemByPosition(i);
            if(entity) {
                entity.addSetHotbarPosition(i + 1);
                hotkeys[i] = { idname: entity.properties.idname, action_name: 'auto' }
            } else {
                const entity = new Item({ ghost: true, })
                entity.addSetHotbarPosition(i + 1)
                hotkeys[i] = {idname: 'empty', action_name: 'auto'}
            }
        }

        this.showAllHotbar()
        FiveMService.setItemSlots(hotkeys)
    }

    showActions(actions)
    {
        let actionOptionsEntity = $('#action-options');
        let addAutoClose = ['Eat', 'Drink', 'Turn On', 'Use', 'Take', 'Smoke', 'Equip'];
        actionOptionsEntity.html('');

        for (const action_data of actions) {
            let action_name = action_data.name

            if (action_name !== 'Give' && action_name !== 'Drop') {
                actionOptionsEntity.append(this.parseOptionHtml(action_name));
                if (addAutoClose.includes(action_name)) {
                    actionOptionsEntity.append(this.parseOptionHtml(action_name + ' & Close'));
                }
            }

            // console.log('fillling', action_name, actionOptionsEntity)
        }


        if (Inventory.mode === 'inventory') {
            actionOptionsEntity.append(this.parseOptionHtml('Give'));
            actionOptionsEntity.append(this.parseOptionHtml('Drop'));
        }
    }

    parseOptionHtml(name)
    {
        return `
            <div class="option-item" id="option-${name}" data-actionName="${name}" data-actionId="${name}">
                ${name}
            </div>`;
    }

    showMetaInformation(item)
    {
        let html = ``
        let style = ``
        for (const key in item.meta) {
            let value = item.meta[key]
            let title = null
            if(key === 'scr_name' || key == 'drb_name') title = 'Signed By'
            if(key === 'drb_date') title = 'Draw Date'
            if(key === 'gender') title = 'Gender'
            if(key === 'bid_casing') title = 'Ballistic Identifier'
            if(key === 'bid_fragment') title = 'Ballistic Identifier'
            if(key === 'bid_rifling') title = 'Rifling Ballistic Identifier'
            if(key === 'bid_firingpin') title = 'Firing Mechanism Ballistic Identifier'
            if(key === 'weapon_type') title = 'Weapon Caliber'
            if(key === 'evidence_id') {
                title = 'Evidence Identifier'
                style = `color: white; font-weight: bolder;`
            }
            if(key === 'found_at') title = 'Found At'
            if(key === 'dna_sequence') {
              title = 'DNA Sequence';
              style = `font-family: monospace, monospace;`;
            }
            if(key === 'dna_segment') {
              title = 'DNA Sequence (Partial)';
              style = `font-family: monospace, monospace;`;
            }
            if(key === 'blood_alcohol') title = 'Blood Alcohol Content'
            if(key === 'registration') title = 'Serial Number'
            if(key === 'analyzed_match') title = 'Analyzed Fingerprint Match'
            if(key === 'components') title = 'Components / Attachments'
            if(key === 'document_title') title = 'Document Type / Title'
            if(key === 'signed_by') title = 'Document Signed By'
            if(key === 'display_name') title = 'Name'
            if(key === 'primary_color') {
                title = 'Identified (P) Color'
                let color = item.meta[key]
                let color_name = `rgba(${color.r}, ${color.g}, ${color.b}, 100)`;
                style = `color: ${color_name}; font-weight: bolder;`
                value = 'Color Representation'
            }
            if(key === 'secondary_color') {
                title = 'Identified (S) Color'
                let color = item.meta[key]
                let color_name = `rgba(${color.r}, ${color.g}, ${color.b}, 100)`;
                style = `color: ${color_name}; font-weight: bolder;`
                value = 'Color Representation'
            }
            if(key === 'mail_address') title = "Address"
            if(key === 'mail_to') title = "To"
            if(key === 'mail_from') title = "From"
            if(key === 'evidence_bin_label') title = "Label"
            if(key === 'evidence_bin_id') title = "Evidence Bin ID"
            if(key === 'ticket_character_name') title = "Issued to"
            if(key === 'ticket_number') title = "Ticket Number"
            if(key === 'togo_bag_label') title = "Label"
            if(key === 'duffle_bag_label') title = "Label"
            if(key === 'label') title = "Label"
            if(key === 'name_display') title = "Furniture Name"
            if(key === 'bank_draft_recipient_name' || key === 'lawyer_ticker_recipient_name') title = "Payable To"
            if(key === 'bank_draft_amount_formatted') title = "Pay to the order of"
            if(key === 'lawyer_ticket_client_count') title = "Number of Clients"
            if(key === 'membership_type') title = 'Membership Type'
            if(key === 'last_modified_date') title = 'Last Modified Date'
            if(key === 'imprinted_text') title = 'Imprinted Text'
            if(key === 'cvoucher_recipient_name') title = 'Recipient'
            if(key === 'cvoucher_amount') title = 'Voucher Amount'
            if(key === 'matches_left') title = 'Matches Remaining'
            if(key === 'matchbook_message') title = 'Message'
            if(key === 'card_message') title = 'Message'
            if(key === 'drug_scan_result') title = 'Test Result'
            if(key === 'card_signature') title = 'Signed By'
            if(key === 'channel') title = 'Channel'
            if(key === 'acct_num') title = 'Routing Account Number'
            if(key === 'acct_nom') title = 'Routing Account Name'
            if(key === 'plate_owner') title = 'Owner DL #'
            if(key === 'rx_name') title = 'Name'
            if(key === 'rx_qty') title = 'Quantity'
            if(key === 'rx_drug') title = 'Substance'
            if(key === 'account_ped_name') title = 'Cardholder Name'
            if(key === 'fake_name') {
                title = 'Name'
                style = `color: #ffcc00; font-weight: bold;`
            }
            if(key === 'fake_phone') {
                title = 'Phone Number'
                style = `color: #ffcc00; font-weight: bold;`
            }
            if(key === 'fake_dlnumber') {
                title = 'Driver License #'
                style = `color: #ffcc00; font-weight: bold;`
            }
            if(key === '_l') {
              continue;
            }
            if(key === 'fpd') {
                title = 'Weapon Disabled';
                value = '';
            }

            if (key === 'rodSetup') {
                title = "Attached Tackle";
                let rodSetupItems = ``;

                // Filter and loop through the rodSetup object for specific properties
                const keysToDisplay = ['line', 'reel', 'bait', 'hook'];
                keysToDisplay.forEach(displayKey => {
                    let displayValue = value[displayKey] !== undefined ? value[displayKey] : 'None';
                    if (displayValue === false) {
                        displayValue = 'None';
                    }
                    rodSetupItems += `<li>${displayKey.charAt(0).toUpperCase() + displayKey.slice(1)}: ${displayValue}</li>`;
                });

                value = `<ul>${rodSetupItems}</ul>`;
            }

            if(key === 'sub_color') {
              let sub_name = item.meta['sub_name'];

              if(!sub_name || sub_name == '') {
                continue;
              }

              html += `
                      <div class="meta-pair">
                          <span class="meta-key">Drug Detected</span>
                          <span class="meta-value" style="${style}">
                            <div class="drug-color" style="background-color: ${value}"></div>
                            <span>${sub_name}</span>
                          </span>
                      </div>
                  `
              continue;
            }

            if(key === 'durability') {
              title = 'Durability'
              value = Math.round(item.meta['durability']) + '%'
            }

            if(title === null) continue;

            if(key === 'components') {
                let components = value.toString().split(',');

                let components_final = ''

                components.forEach(el => {
                    if(this.componentAliases[el]) {
                        el = this.componentAliases[el]
                    }

                    if(el.toLowerCase().includes('skin')) {
                      return;
                    }

                    components_final = components_final + el + '<br/>'
                })

                if(components_final == '') {
                  continue;
                }

                html += `
                      <div class="meta-pair">
                          <span class="meta-key">${title}</span>
                          <span class="meta-value" style="${style}">${components_final}</span>
                      </div>
                  `
            } else {
                html += `
                      <div class="meta-pair">
                          <span class="meta-key">${title}</span>
                          <span class="meta-value" style="${style}">${value}</span>
                      </div>
                  `
            }
        }

        if(html !== ``) {
            $('.inventory-meta-content').show(200);
            $('.inventory-meta-content').html(html)
            // $('.hotbar').hide()
        }
    }

    hideMetaInformation(delay = 200)
    {
        $('.inventory-meta-content').hide(delay)
        // $('.hotbar').show()
    }

    getItemOrActionAtMousePosition(x, y)
    {
        let el = document.elementFromPoint(x, y);

        if ($(el).hasClass('grid-item')) {
            return el;
        } else if ($(el).hasClass('item-image')) {
            return el.parentElement;
        } else if ($(el).hasClass('ghosted')) {
            return el.parentElement;
        } else if ($(el).hasClass('span-name')) {
            return el.parentElement.parentElement;
        } else if ($(el).hasClass('item-name')) {
            return el.parentElement;
        } else if ($(el).hasClass('slot-image')) {
            return el.parentElement.parentElement;
        } else if ($(el).hasClass('slot-position')) {
            return el.parentElement.parentElement;
        }

        return el;
    }
}

export default new InterfaceService();
