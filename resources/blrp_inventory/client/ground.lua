local ground_chests_render = {}

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(500)

    local player_coords = GetEntityCoords(PlayerPedId())
    local player_bucket = LocalPlayer.state.bucket_name
    local _ground_chests_render = {}

    for _, chest_data in pairs(GlobalState.ground_chests) do
      -- Only render chests from the same bucket
      if chest_data.bucket_name == player_bucket and #(player_coords - chest_data.coords) < 25 then
        table.insert(_ground_chests_render, chest_data.coords)
      end
    end

    ground_chests_render = _ground_chests_render
  end
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    for _, coords in pairs(ground_chests_render) do
      DrawMarker(2,
        coords.x, coords.y, coords.z, -- pos
        0, 0, 0, -- dir
        1.0, 0.0, 0.0, -- rot
        0.25, 0.25, 0.25, -- scale
        255, 255, 255, 100, -- rgba
        false, -- bobUpAndDown
        true, -- faceCamera
        2, false, false, false, false)
    end
  end
end)
