-- Author <PERSON> / <PERSON><PERSON>Goat

local holstered = true
local DisableStuff = false
local equipedWeapon = nil

fast_animation = {
  "WEAPON_FLASHLIGHT",
  "WEAPON_FLASHLIGHT_UV",
  "WEAPON_NIGHTSTICK",
  "WEAPON_SHANK",
  "WEAPON_MILSPECKNIFE",
}

medium_animation = {
  "WEAPON_STUNGUN",
  "WEAPON_BATTLEAXE",
  "WEAPON_CROWBAR",
  "WEAPON_WRENCH",
  "WEAPON_BAT",
  "WEAPON_LUCILLE",
  "WEAPON_HOBBYHORSE",
  "WEAPON_KNIFE",
  "WEAPON_SWITCHBLADE",
  "WEAPON_DAGGER",
  "WEAPON_BOTTLE",
  "WEAPON_HAMMER",
  "WEAPON_PISTOL",
  "WEAPON_SNSPISTOL",
  "WEAPON_SNSPISTOL_MK2",
  "WEAPON_COMBATPISTOL",
  "WEAPON_GLOCK17",
  "WEAPON_HEAVYPISTOL",
  "WEAPON_PISTOL50",
  "WEAPON_VINTAGEPISTOL",
  "WEAPON_REVOLVER",
  "WEAPON_REVOLVER_MK2",
  "WEAPON_PISTOL_MK2",
  "WEAPON_PISTOLXM3",
  "WEAPON_CERAMICPISTOL",
  "WEAPON_VP897",
  "WEAPON_SP45",
  "WEAPON_SERVICEPISTOL_45",
  "WEAPON_SERVICEPISTOL_9MM",
  "WEAPON_SERVICEPISTOL_AUTO",
  "WEAPON_GADGETPISTOL",
  "WEAPON_MACHINEPISTOL",
  "WEAPON_TECPISTOL",
  "WEAPON_BAT",
  "WEAPON_GOLFCLUB",
  "WEAPON_CROWBAR",
  "WEAPON_SMG",
  "WEAPON_UMP45",
  "WEAPON_PUMPSHOTGUN",
  "WEAPON_PUMPSHOTGUN2",
  "WEAPON_BEANBAG",
  "WEAPON_ASSAULTRIFLE",
  "WEAPON_ASSAULTRIFLE2",
  "WEAPON_ASSAULTRIFLE_MK2",
  "WEAPON_COMBATPDW",
  "WEAPON_CARBINERIFLE_MK2",
  "WEAPON_FM5_SPEARLT",
  "WEAPON_CARBINERIFLEMK2T",
  "WEAPON_GUSENBERG",
  "WEAPON_DBSHOTGUN",
  "WEAPON_MUSKET",
  "WEAPON_MICROSMG",
  "WEAPON_CARBINERIFLE",
  "WEAPON_CARBINERIFLET",
  "WEAPON_MG",
  "WEAPON_COMBATMG",
  "WEAPON_COMBATMG_MK2",
  "WEAPON_ASSAULTSMG",
  "WEAPON_MINISMG",
  "WEAPON_ADVANCEDRIFLE",
  "WEAPON_SPECIALCARBINE",
  "WEAPON_SPECIALCARBINE_MK2",
  "WEAPON_BULLPUPRIFLE",
  "WEAPON_COMPACTRIFLE",
  "WEAPON_SAWNOFFSHOTGUN",
  "WEAPON_BULLPUPSHOTGUN",
  "WEAPON_ASSAULTSHOTGUN",
  "WEAPON_MILITARYRIFLE",
  "WEAPON_HEAVYRIFLE",
  "WEAPON_TACTICALRIFLE",
  "WEAPON_BATTLERIFLE",
  "WEAPON_HEAVYSHOTGUN",
  "WEAPON_SNIPERRIFLE",
  "WEAPON_HEAVYSNIPER",
  "WEAPON_MARKSMANRIFLE",
  "WEAPON_AUTOSHOTGUN",
  "WEAPON_GRENADELAUNCHER",
  'WEAPON_GRENADELAUNCHER_SMOKE',
  "WEAPON_RPG",
  "WEAPON_MINIGUN",
  "WEAPON_FIREWORK",
  "WEAPON_RAILGUN",
  "WEAPON_HOMINGLAUNCHER",
  "WEAPON_COMPACTLAUNCHER",
  "WEAPON_POOLCUE",
  "WEAPON_CARBINERIFLE_MK2",
  "WEAPON_APPISTOL",
  "WEAPON_SMG_MK2",
  "WEAPON_MACHETE",
  "WEAPON_DOUBLEACTION",
  'WEAPON_KATANA',
  'WEAPON_KATANA_2',
  'WEAPON_DRAGON_KATANA_BLUE',
  'WEAPON_BULLPUPRIFLE_MK2',
  'WEAPON_FLAREGUN',
  'WEAPON_HUNTINGRIFLE',
  'WEAPON_SPEEDGUN',
  'WEAPON_PAINTBALL',
  'WEAPON_CANDYCANE',
  'WEAPON_STONE_HATCHET',
  'WEAPON_TOMAHAWK',
  'WEAPON_MILITARYRIFLE',
  'WEAPON_PUMPSHOTGUN_MK2',
  'WEAPON_LESSLAUNCHER',
}

local leo_draw = {
  ['WEAPON_STUNGUN'] = true,
  ['WEAPON_PISTOL'] = true,
  ['WEAPON_SNSPISTOL'] = true,
  ['WEAPON_SNSPISTOL_MK2'] = true,
  ['WEAPON_COMBATPISTOL'] = true,
  ['WEAPON_GLOCK17'] = true,
  ['WEAPON_HEAVYPISTOL'] = true,
  ['WEAPON_VINTAGEPISTOL'] = true,
  ['WEAPON_REVOLVER'] = true,
  ['WEAPON_REVOLVER_MK2'] = true,
  ['WEAPON_PISTOL_MK2'] = true,
  ['WEAPON_PISTOLXM3'] = true,
  ['WEAPON_SERVICEPISTOL_45'] = true,
  ['WEAPON_SERVICEPISTOL_9MM'] = true,
  ['WEAPON_VP897'] = true,
  ['WEAPON_SP45'] = true,
  ['WEAPON_GADGETPISTOL'] = true,
  ['WEAPON_SMG'] = true,
  ['WEAPON_UMP45'] = true,
  ['WEAPON_PUMPSHOTGUN'] = true,
  ['WEAPON_PUMPSHOTGUN2'] = true,
  ['WEAPON_CARBINERIFLE'] = true,
  ['WEAPON_CARBINERIFLE_MK2'] = true,
  ['WEAPON_FM5_SPEARLT'] = true,
  ['WEAPON_CARBINERIFLET'] = true,
  ['WEAPON_CARBINERIFLEMK2T'] = true,
  ['WEAPON_TACTICALRIFLE'] = true,
  ['WEAPON_GRENADELAUNCHER_SMOKE'] = true,
  ['WEAPON_HEAVYRIFLE'] = true,
  ['WEAPON_SPEEDGUN'] = true,
  ['WEAPON_MILITARYRIFLE'] = true,
  ['WEAPON_PUMPSHOTGUN_MK2'] = true,
  ['WEAPON_LESSLAUNCHER'] = true,
}

local always_leo_draw = {
  ['WEAPON_PISTOLXM3'] = true,
}

function copDraw(weapon_name)
  local me = exports.blrp_core:me()

  if always_leo_draw[weapon_name] then
    return true
  end

  if leo_draw[weapon_name] and me.hasGroup('LEO') then
    return true
  end

  if leo_offduty[weapon_name] and me.hasGroup('LEO_OffDuty') then
    return true
  end

  return false
end

-- Firing block should be handled by weapon switching
---- TODO check out firing block
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    if DisableStuff then
      DisablePlayerFiring(PlayerPedId(), true) -- Disable weapon firing
      DisableControlAction(0, 24, true) -- disable attack
      DisableControlAction(0, 47, true) -- disable weapon
      DisableControlAction(0, 58, true) -- disable weapon
      DisableControlAction(0, 263, true) -- disable melee
      DisableControlAction(0, 264, true) -- disable melee
      DisableControlAction(0, 257, true) -- disable melee
      DisableControlAction(0, 140, true) -- disable melee
      DisableControlAction(0, 141, true) -- disable melee
      DisableControlAction(0, 142, true) -- disable melee
      DisableControlAction(0, 143, true) -- disable melee
      DisableControlAction(0, 47, true) -- disable weapon
      DisableControlAction(0, 58, true) -- disable weapon
      DisableControlAction(0, 257, true) -- disable melee
      DisableControlAction(0, 106, true) -- VehicleMouseControlOverride
    end
  end
end)
-- end player quickfire

RegisterNetEvent('gundraw:client:drawAnimate')
AddEventHandler('gundraw:client:drawAnimate', function(weapon_name, force_medium_animation)
    local ped = PlayerPedId()

    -- Specific switchblade animations
    if weapon_name == "WEAPON_SWITCHBLADE" and not force_medium_animation then
        loadAnimDict("anim@melee@switchblade@holster")
        -- Play the unholster animation
        TaskPlayAnim(ped, "anim@melee@switchblade@holster", "unholster", 8.0, 3.0, -1, 48, 0, 0, 0, 0)
        SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)
        DisableStuff = true
        Citizen.Wait(400)
        -- Play weapon-specific unholster animation
        TaskPlayAnim(ped, "anim@melee@switchblade@holster", "w_unholster", 8.0, 3.0, -1, 48, 0, 0, 0, 0)
        Citizen.Wait(400)
        SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
        Citizen.Wait(500)
        ClearPedTasks(ped)
        holstered = false
        DisableStuff = false
        equipedWeapon = weapon_name
        return
    end

    if has_value(medium_animation, weapon_name) or force_medium_animation then
        if copDraw(weapon_name) then --If I'm a cop, play this exclusively
          loadAnimDict( "rcmjosh4" )
          loadAnimDict( "reaction@intimidation@cop@unarmed" )
          TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "intro", 3.0, 3.0, -1, 48, 0, 0, 0, 0 )
          SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)
          DisableStuff = true
          Citizen.Wait(700)
          TaskPlayAnim(ped, "rcmjosh4", "josh_leadout_cop2", 5.0, 2.0, 400, 48, 10, 0, 0, 0 )
          Citizen.Wait(300)
          SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
          Citizen.Wait(300)
          ClearPedTasks(ped)
          holstered = false
          DisableStuff = false
        else
          loadAnimDict( "reaction@intimidation@1h" )
          TaskPlayAnim(ped, "reaction@intimidation@1h", "intro", 8.0, 3.0, -1, 48, 00, 0, 0, 0 )
          SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)
          DisableStuff = true
          Citizen.Wait(1300)
          SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
          Citizen.Wait(700)
          ClearPedTasks(ped)
          holstered = false
          DisableStuff = false
        end
        equipedWeapon = weapon_name
    end
    if has_value(fast_animation, weapon_name) and not force_medium_animation then
        loadAnimDict( "reaction@intimidation@cop@unarmed" )
        TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "intro", 3.0, 3.0, -1, 48, 0, 0, 0, 0 ) -- COP ANIM
        --SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)
        DisableStuff = true
        --Citizen.Wait(1300)
        --SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
        Citizen.Wait(700)
        ClearPedTasks(ped)
        holstered = false
        DisableStuff = false
    end
    equipedWeapon = weapon_name
end)

RegisterNetEvent('gundraw:client:holsterAnimate')
AddEventHandler('gundraw:client:holsterAnimate', function(weapon_name, callback)
    local ped = PlayerPedId()

    -- Specific switchblade animations
    if weapon_name == "WEAPON_SWITCHBLADE" then
        if not holstered then
            loadAnimDict("anim@melee@switchblade@holster")
            DisableStuff = true
            SetCurrentPedWeapon(ped, GetHashKey(weapon_name), true)
            -- Play weapon-specific holster animation
            TaskPlayAnim(ped, "anim@melee@switchblade@holster", "w_holster", 8.0, 3.0, -1, 48, 0, 0, 0, 0)
            Citizen.Wait(400)
            -- Play the holster animation
            TaskPlayAnim(ped, "anim@melee@switchblade@holster", "holster", 8.0, 3.0, -1, 48, 0, 0, 0, 0)
            Citizen.Wait(400)
            callback()
            SetCurrentPedWeapon(ped, GetHashKey("WEAPON_UNARMED"), true)
            Citizen.Wait(500)
            ClearPedTasks(ped)
            holstered = true
            DisableStuff = false
            equipedWeapon = nil
        end
        return
    end

    if has_value(medium_animation, weapon_name) then
        if not holstered then
            if copDraw(weapon_name) then
              loadAnimDict( "reaction@intimidation@cop@unarmed" )
              TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "intro", 3.0, 3.0, -1, 48, 0, 0, 0, 0 ) -- COP ANIM
            else
              loadAnimDict( "reaction@intimidation@1h" )
              TaskPlayAnim(ped, "reaction@intimidation@1h", "outro", 8.0, 3.0, -1, 48, 0, 0, 0, 0 )
            end
            DisableStuff = true
            SetCurrentPedWeapon(ped, GetHashKey(weapon_name), true) -- This needs to recognize current weapon set it so it appears when holstering. Hex stuff /shrug
            Citizen.Wait(1300)
            callback()
            SetCurrentPedWeapon(ped, GetHashKey("WEAPON_UNARMED"), true)
            --Citizen.Wait(700)
            ClearPedTasks(ped)
            holstered = true
            DisableStuff = false
            equipedWeapon = nil
        end
    end
    callback()
end)

RegisterNetEvent('gundraw:client:draw')
AddEventHandler('gundraw:client:draw', function()
    local ped = PlayerPedId()
    if not IsPedInAnyVehicle(ped, true) then
        -- moved
        if canPlayAnimation(ped) then
            if holstered then
                -- Specific switchblade draw animation
                if CurrentWeapon(ped) == "WEAPON_SWITCHBLADE" then
                    loadAnimDict("anim@melee@switchblade@holster")
                    TaskPlayAnim(ped, "anim@melee@switchblade@holster", "unholster", 8.0, 3.0, -1, 48, 0, 0, 0, 0)
                    SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)
                    DisableStuff = true
                    Citizen.Wait(400)
                    TaskPlayAnim(ped, "anim@melee@switchblade@holster", "w_unholster", 8.0, 3.0, -1, 48, 0, 0, 0, 0)
                    Citizen.Wait(400)
                    SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
                    Citizen.Wait(500)
                    ClearPedTasks(ped)
                    holstered = false
                    DisableStuff = false
                    equipedWeapon = CurrentWeapon(ped)
                elseif copDraw(weapon_name) then --If I'm a cop, play this exclusively
                  loadAnimDict( "rcmjosh4" )
                  loadAnimDict( "reaction@intimidation@cop@unarmed" )
                  TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "intro", 3.0, 3.0, -1, 48, 0, 0, 0, 0 )
                  SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)
                  DisableStuff = true
                  Citizen.Wait(1400)
                  TaskPlayAnim(ped, "rcmjosh4", "josh_leadout_cop2", 3.0, 3.0, -1, 48, 0, 0, 0, 0 )
                  Citizen.Wait(300)
                  SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
                  Citizen.Wait(300)
                  ClearPedTasks(ped)
                  holstered = false
                  DisableStuff = false
                else
                  loadAnimDict( "reaction@intimidation@1h" )
                  TaskPlayAnim(ped, "reaction@intimidation@1h", "intro", 8.0, 2.0, 1700, 48, 10, 0, 0, 0 )
                  SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)
                  DisableStuff = true
                  Citizen.Wait(1300)
                  SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
                  Citizen.Wait(700)
                  ClearPedTasks(ped)
                  holstered = false
                  DisableStuff = false
                end
                equipedWeapon = CurrentWeapon(ped)
            end
        elseif not canPlayAnimation(ped) then
            if not holstered then
                -- Specific switchblade holster animation
                if equipedWeapon == "WEAPON_SWITCHBLADE" then
                    loadAnimDict("anim@melee@switchblade@holster")
                    TaskPlayAnim(ped, "anim@melee@switchblade@holster", "holster", 8.0, 3.0, -1, 48, 0, 0, 0, 0)
                    DisableStuff = true
                    SetCurrentPedWeapon(ped, GetHashKey(equipedWeapon), true)
                    Citizen.Wait(800)
                    SetCurrentPedWeapon(ped, GetHashKey("WEAPON_UNARMED"), true)
                    Citizen.Wait(500)
                    ClearPedTasks(ped)
                    holstered = true
                    DisableStuff = false
                    equipedWeapon = nil
                elseif copDraw(weapon_name) then
                  loadAnimDict( "reaction@intimidation@cop@unarmed" )
                  TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "intro", 3.0, 3.0, -1, 48, 0, 0, 0, 0 ) -- COP ANIM
                else
                  loadAnimDict( "reaction@intimidation@1h" )
                  TaskPlayAnim(ped, "reaction@intimidation@1h", "outro", 8.0, 3.0, -1, 48, 0, 0, 0, 0 )
                end
                DisableStuff = true
                SetCurrentPedWeapon(ped, GetHashKey(equipedWeapon), true) -- This needs to recognize current weapon set it so it appears when holstering. Hex stuff /shrug
                Citizen.Wait(1300)
                SetCurrentPedWeapon(ped, GetHashKey("WEAPON_UNARMED"), true)

                Citizen.Wait(700)
                ClearPedTasks(ped)
                holstered = true
                DisableStuff = false
                equipedWeapon = nil
            end
        end
    end
end)

RegisterNetEvent('gundraw:client:holsterWeapon')
AddEventHandler('gundraw:client:holsterWeapon', function()
    holstered = true
end)

AddEventHandler('gundraw:client:isHolstered', function(callback)
  callback(holstered)
end)

exports('IsHolstered', function()
  return holstered
end)

function canPlayAnimation(ped)
    currentWeapon = GetSelectedPedWeapon(ped)
    for k,v in pairs(weaponlist) do
        if GetHashKey(v) == currentWeapon then
            return true
        end
    end
    return false
end

function CurrentWeapon(ped)
    currentWeapon = GetSelectedPedWeapon(ped)
    for k,v in pairs(medium_animation) do
        if GetHashKey(v) == currentWeapon then
            return v
        end
    end
    return nil
end

function loadAnimDict( dict )
    while ( not HasAnimDictLoaded( dict ) ) do
        RequestAnimDict( dict )
        Citizen.Wait( 0 )
    end
end

local maxTaserCarts = 1 -- The amount of taser cartridges a person can have.
local taserCartsLeft = maxTaserCarts

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local pos = GetEntityCoords(ped, nil)
        local ped = GetPlayerPed(-1)
        local taserModel = GetHashKey("WEAPON_STUNGUN")

        if GetSelectedPedWeapon(ped) == taserModel then

            DisableControlAction(0, 45, true)
            if IsPedShooting(ped) then
                taserCartsLeft = taserCartsLeft - 1
            end
            if taserCartsLeft <= 0 or not has_ammo then
                if GetSelectedPedWeapon(ped) == taserModel then
                    SetPlayerCanDoDriveBy(ped, false)
                    DisablePlayerFiring(ped, true)
                end
            end

            if (IsDisabledControlJustReleased(1, 45)) then
                loadAnimDict("weapons@pistol@pistol_str" )
                TaskPlayAnim(ped, "weapons@pistol@pistol_str", "reload_aim_empty", 8.0, 2.0, 1700, 48, 10, 0, 0, 0 )

                SetTimeout(1550, function()
                  taserCartsLeft = maxTaserCarts
                end)
            end
        end
    end
end)

function has_value(tab, val)
    for index, value in ipairs(tab) do
        if value == val then
            return true
        end
    end

    return false
end
