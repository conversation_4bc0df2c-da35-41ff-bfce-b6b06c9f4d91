tInventory = {}
T.bindInstance('inventory', tInventory)

tInventory.hide = function()
  TriggerEvent('blrp_inventory:hide')

  Citizen.Wait(100)

  return true
end

pInventory = P.getInstance('blrp_core', 'inventory')

local sort_cache = {}
local guiEnabled = false
local last_hotbar_number = false
local currentWeapon = nil
local lastPressedHotkey = nil
local canUseHotkeys = true -- Prevent phone and other things triggering it

-- Prevent server spam
local coolDownLock = false

RegisterNetEvent('core:client:removeUI:inventory')
AddEventHandler('core:client:removeUI:inventory', function()
    TriggerEvent('blrp_inventory:hide')
end)

exports('IsInventoryOpen', function()
  return guiEnabled
end)

AddEventHandler('blrp_inventory:client:isInventoryOpen', function(callback)
  callback(guiEnabled)
end)

function focusNUI(shouldDisplay)
    guiEnabled = shouldDisplay
    SetNuiFocus(shouldDisplay, shouldDisplay)
    SetNuiFocusKeepInput(shouldDisplay)
    TriggerEvent('core:client:enableInterface', 'inventory', shouldDisplay)

    if shouldDisplay then
      TriggerEvent('badMenu:client:hideAll')
    end

    if(shouldDisplay) then
        TriggerScreenblurFadeIn(200)
    else
        TriggerScreenblurFadeOut(200)
    end
end

local Keys = {
    ["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
    ["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
    ["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
    ["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
    ["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
    ["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
    ["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
    ["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
    ["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}

local weapon_types = {
    "WEAPON_SHANK",
    "WEAPON_KNIFE",
    "WEAPON_DAGGER",
    "WEAPON_BOTTLE",
    "WEAPON_STUNGUN",
    "WEAPON_FLASHLIGHT",
    "WEAPON_FLASHLIGHT_UV",
    "WEAPON_NIGHTSTICK",
    "WEAPON_HAMMER",
    "WEAPON_BAT",
    "WEAPON_LUCILLE",
    "WEAPON_HOBBYHORSE",
    "WEAPON_DRAGON_KATANA_BLUE",
    "WEAPON_KATANA_2",
    "WEAPON_GOLFCLUB",
    "WEAPON_CROWBAR",
    "WEAPON_PISTOL",
    "WEAPON_SNSPISTOL",
    "WEAPON_SNSPISTOL_MK2",
    "WEAPON_COMBATPISTOL",
    "WEAPON_GLOCK17",
    "WEAPON_HEAVYPISTOL",
    "WEAPON_PISTOL50",
    "WEAPON_VINTAGEPISTOL",
    "WEAPON_PISTOL_MK2",
    "WEAPON_PISTOLXM3",
    "WEAPON_CERAMICPISTOL",
    "WEAPON_VP897",
    "WEAPON_SP45",
    "WEAPON_SERVICEPISTOL_45",
    "WEAPON_SERVICEPISTOL_9MM",
    "WEAPON_SERVICEPISTOL_AUTO",
    "WEAPON_GADGETPISTOL",
    --"WEAPON_MACHINEPISTOL",
    --"WEAPON_MICROSMG",
    "WEAPON_SMG",
    "WEAPON_UMP45",
    "WEAPON_CARBINERIFLE",
    "WEAPON_CARBINERIFLET",
    "WEAPON_CARBINERIFLEMK2T",
    "WEAPON_SPECIALCARBINE",
    "WEAPON_PUMPSHOTGUN",
    "WEAPON_PUMPSHOTGUN2",
    "WEAPON_PUMPSHOTGUN_MK2",
    "WEAPON_COMBATSHOTGUN",
    "WEAPON_FIREEXTINGUISHER",
    --"WEAPON_PETROLCAN",
    "WEAPON_FLARE",
    "WEAPON_REVOLVER",
    "WEAPON_REVOLVER_MK2",
    "WEAPON_SWITCHBLADE",
    "WEAPON_MILSPECKNIFE",
    "WEAPON_BATTLEAXE",
    "WEAPON_POOLCUE",
    "WEAPON_WRENCH",
    "WEAPON_DOUBLEACTION",

    --Da new weapons
    "WEAPON_ASSAULTRIFLE",
    "WEAPON_ASSAULTRIFLE2",
    "WEAPON_COMPACTRIFLE",
    "WEAPON_MACHINEPISTOL",
    "WEAPON_TECPISTOL",
    "WEAPON_BULLPUPRIFLE_MK2",
    'WEAPON_BEANBAG',
    "WEAPON_SPECIALCARBINE_MK2",
    "WEAPON_GRENADELAUNCHER_SMOKE",
    "WEAPON_MILITARYRIFLE",
    "WEAPON_HEAVYRIFLE",
    "WEAPON_TACTICALRIFLE",
    "WEAPON_COMBATPDW",
    "WEAPON_STONE_HATCHET",
    "WEAPON_TOMAHAWK",
    "WEAPON_CANDYCANE",
    "WEAPON_BATTLERIFLE",
    "WEAPON_SNOWBALL",
    "WEAPON_LESSLAUNCHER",
}

local weapon_hashes = {
    {"WEAPON_KNIFE",-1716189206},
    {"WEAPON_DAGGER",-1834847097},
    {"WEAPON_BOTTLE",-102323637},
    {"WEAPON_FLASHLIGHT",2343591895},
    {"WEAPON_FLASHLIGHT_UV", `WEAPON_FLASHLIGHT_UV`},
    {"WEAPON_NIGHTSTICK",1737195953},
    {"WEAPON_HAMMER",1317494643},
    {"WEAPON_BAT",-1786099057},
    {"WEAPON_LUCILLE",362387693},
    {"WEAPON_HOBBYHORSE",`WEAPON_HOBBYHORSE`},
    {"WEAPON_DRAGON_KATANA_BLUE", GetHashKey('WEAPON_DRAGON_KATANA_BLUE')},
    {"WEAPON_KATANA_2", GetHashKey('WEAPON_KATANA_2')},
    {"WEAPON_GOLFCLUB",1141786504},
    {"WEAPON_CROWBAR",-2067956739},
    {"WEAPON_PISTOL",453432689},
    {"WEAPON_SNSPISTOL",-1076751822},
    {"WEAPON_COMBATPISTOL",1593441988},
    {"WEAPON_HEAVYPISTOL",3523564046},
    {"WEAPON_PISTOL50",-1716589765},
    {"WEAPON_VINTAGEPISTOL",137902532},
    {"WEAPON_PISTOLXM3", GetHashKey('WEAPON_PISTOLXM3')},
    {"WEAPON_CERAMICPISTOL", GetHashKey('WEAPON_CERAMICPISTOL')},
    {"WEAPON_SERVICEPISTOL_45", GetHashKey('WEAPON_SERVICEPISTOL_45')},
    {"WEAPON_SERVICEPISTOL_9MM", GetHashKey('WEAPON_SERVICEPISTOL_9MM')},
    {"WEAPON_SERVICEPISTOL_AUTO", GetHashKey('WEAPON_SERVICEPISTOL_AUTO')},
    {"WEAPON_VP897", GetHashKey('WEAPON_VP897')},
    {"WEAPON_SP45", GetHashKey('WEAPON_SP45')},
    {"WEAPON_GADGETPISTOL",1470379660},
    --{"WEAPON_MACHINEPISTOL",222222222},
    --{"WEAPON_MICROSMG",222222222},
    {"WEAPON_SMG",736523883},
    {"WEAPON_UMP45", GetHashKey('WEAPON_UMP45')},
    {"WEAPON_CARBINERIFLE",2210333304},
    {"WEAPON_CARBINERIFLET", GetHashKey('WEAPON_CARBINERIFLET')},
    {"WEAPON_CARBINERIFLEMK2T", GetHashKey('WEAPON_CARBINERIFLEMK2T')},
    {"WEAPON_SPECIALCARBINE",-1063057011},
    {"WEAPON_PUMPSHOTGUN",487013001},
    {"WEAPON_PUMPSHOTGUN2", GetHashKey('WEAPON_PUMPSHOTGUN2')},
    {"WEAPON_COMBATSHOTGUN", GetHashKey('WEAPON_COMBATSHOTGUN')},
    {"WEAPON_STUNGUN",911657153},
    {"WEAPON_PAINTBALL",959234284},
    {"WEAPON_FIREEXTINGUISHER",101631238},
    --{"WEAPON_PETROLCAN",222222222},
    {"WEAPON_FLARE",1233104067},
    {"WEAPON_REVOLVER",-1045183535},
    {"WEAPON_REVOLVER_MK2",-879347409},
    {"WEAPON_SWITCHBLADE",-538741184},
    {"WEAPON_MILSPECKNIFE", GetHashKey('WEAPON_MILSPECKNIFE')},
    {"WEAPON_BATTLEAXE",-853065399},
    {"WEAPON_POOLCUE",-1810795771},
    {"WEAPON_WRENCH",419712736},
    {"WEAPON_DOUBLEACTION",-1746263880},

    --Da new weapons
    {"WEAPON_ASSAULTRIFLE",-1074790547},
    {"WEAPON_ASSAULTRIFLE2", GetHashKey('WEAPON_ASSAULTRIFLE2')},
    {"WEAPON_COMPACTRIFLE",1649403952},
    {"WEAPON_MACHINEPISTOL",-619010992},
    {"WEAPON_TECPISTOL",`WEAPON_TECPISTOL`},
    {"WEAPON_MG",-1660422300},
    {'WEAPON_BEANBAG', -2017701774},
    {"WEAPON_SNSPISTOL_MK2",-2009644972},
    {"WEAPON_SPECIALCARBINE_MK2",-1768145561},
    {"WEAPON_ASSAULTRIFLE_MK2",961495388},
    {"WEAPON_GLOCK17",-1882382516},
    {"WEAPON_MILITARYRIFLE",2636060646},
    {"WEAPON_HEAVYRIFLE",3347935668},
    {"WEAPON_TACTICALRIFLE",3520460075},
    {"WEAPON_GRENADELAUNCHER_SMOKE", GetHashKey('WEAPON_GRENADELAUNCHER_SMOKE')},
    {"WEAPON_CANDYCANE", GetHashKey('WEAPON_CANDYCANE')},
    {"WEAPON_STONE_HATCHET", GetHashKey('WEAPON_STONE_HATCHET')},
    {"WEAPON_TOMAHAWK", GetHashKey('WEAPON_TOMAHAWK')},
    {"WEAPON_BATTLERIFLE", GetHashKey('WEAPON_BATTLERIFLE')},
    {"WEAPON_SNOWBALL", GetHashKey('WEAPON_SNOWBALL')},
    {"WEAPON_PUMPSHOTGUN_MK2", GetHashKey('WEAPON_PUMPSHOTGUN_MK2')},
    {"WEAPON_LESSLAUNCHER", GetHashKey('WEAPON_LESSLAUNCHER')},
}

local isLoading = false

Citizen.CreateThread(function()
  while true do
      Citizen.Wait(0)
      DisableControlAction(0, Keys["TAB"], true)
      DisableControlAction(0, Keys["1"], true)
      DisableControlAction(0, Keys["2"], true)
      DisableControlAction(0, Keys["3"], true)
      DisableControlAction(0, Keys["4"], true)
      DisableControlAction(0, Keys["5"], true)
      DisableControlAction(0, 14, true) -- INPUT_WEAPON_WHEEL_NEXT
      DisableControlAction(0, 15, true) -- INPUT_WEAPON_WHEEL_PREV
      DisableControlAction(0, 16, true) -- INPUT_SELECT_NEXT_WEAPON
      DisableControlAction(0, 17, true) -- INPUT_SELECT_PREV_WEAPON
      DisableControlAction(0, 99, true) -- INPUT_VEH_SELECT_NEXT_WEAPON
      DisableControlAction(0, 100, true) -- INPUT_VEH_SELECT_PREV_WEAPON

      if coolDownLock then
        DisableControlAction(1, 25, true) -- INPUT_AIM
        DisableControlAction(1, 141, true) -- INPUT_AIM
        DisableControlAction(1, 68, true) -- INPUT_VEH_AIM
        DisableControlAction(1, 91, true) -- INPUT_VEH_PASSENGER_AIM
      end

      if IsDisabledControlJustReleased(0, Keys["TAB"]) then
          -- Tab was pressed
      end

      if IsDisabledControlJustReleased(0, Keys["1"]) then
          triggerHotbarSlot(1)
      end

      if IsDisabledControlJustReleased(0, Keys["2"]) then
          triggerHotbarSlot(2)
      end

      if IsDisabledControlJustReleased(0, Keys["3"]) then
          triggerHotbarSlot(3)
      end

      if IsDisabledControlJustReleased(0, Keys["4"]) then
          triggerHotbarSlot(4)
      end

      if IsDisabledControlJustReleased(0, Keys["5"]) then
          triggerHotbarSlot(5)
      end

      if guiEnabled then
          DisableControlAction(1, 18, true)
          DisableControlAction(1, 24, true)
          DisableControlAction(1, 69, true)
          DisableControlAction(1, 92, true)
          DisableControlAction(1, 106, true)
          DisableControlAction(1, 122, true)
          DisableControlAction(1, 135, true)
          DisableControlAction(1, 142, true)
          DisableControlAction(1, 144, true)
          DisableControlAction(1, 176, true)
          DisableControlAction(1, 223, true)
          DisableControlAction(1, 229, true)
          DisableControlAction(1, 237, true)
          DisableControlAction(1, 257, true)
          DisableControlAction(1, 329, true)

          DisableControlAction(1, 14, true)
          DisableControlAction(1, 16, true)
          DisableControlAction(1, 41, true)
          DisableControlAction(1, 43, true)
          DisableControlAction(1, 81, true)
          DisableControlAction(1, 97, true)
          DisableControlAction(1, 180, true)
          DisableControlAction(1, 198, true)
          DisableControlAction(1, 39, true)
          DisableControlAction(1, 50, true)

          DisableControlAction(1, 22, true)
          DisableControlAction(1, 55, true)
          DisableControlAction(1, 76, true)
          DisableControlAction(1, 102, true)
          DisableControlAction(1, 114, true)
          DisableControlAction(1, 143, true)
          DisableControlAction(1, 179, true)
          DisableControlAction(1, 193, true)
          DisableControlAction(1, 203, true)
          DisableControlAction(1, 216, true)
          DisableControlAction(1, 255, true)
          DisableControlAction(1, 298, true)
          DisableControlAction(1, 321, true)
          DisableControlAction(1, 328, true)
          DisableControlAction(1, 331, true)
      end
  end
end)

function triggerHotbarSlot(number)
  if
    coolDownLock or
    not canUseHotkeys or -- State check to prevent use when using hotkeys without intending to use hotbar item
    GetPlayerTargetEntity(PlayerPedId()) or -- Prevent switching while melee aiming or targeting in cars
    exports.blrp_core:IsInterfaceOpen() or
    GetPedConfigFlag(PlayerPedId(), 78) or -- (Exploit) Make sure ped is not aiming when switching
    exports.blrp_poker:PlayingGame() or
    exports.blrp_roulette:PlayingGame() or
    exports.blrp_vehicles:IsVehicleInteractActive() or
    exports.blrp_core:me().isBeingCarried() or
    exports.blrp_core:me().isCarryingSomeone() or
    exports.DiamondBlackjack:PlayingGame()
  then
    return
  end

  if exports.blrp_core:me().isDisabledDeadOrRestrained() then
    return
  end

  TriggerEvent("gcphone:getPhoneState", function(phone_in_use)
    if phone_in_use then
      return
    end

    lastPressedHotkey = number

    TriggerServerEvent('blrp_inventory:activeItemSlot', number)

    coolDownLock = true

    SetTimeout(300, function()
      coolDownLock = false
    end)
  end)
end

exports('OpenInventoryFromKeybind', function()
    if exports.blrp_vehicles:IsVehicleInteractActive()
            or exports.blrp_core:me().isBeingCarried()
            or exports.customscripts:IsNotepadOpen()
            or exports.blrp_core:me().hasInterfaceOpen()
            or exports.blrp_core:me().isCarryingSomeone()
            or exports.blrp_ui:istriggerFormWaitOpen() then
        return
    end

  if guiEnabled then
    return
  end

  if exports.blrp_core:me().isDisabledDeadOrRestrained() then
    return
  end

  TriggerEvent('gcphone:getPhoneState', function(using_phone)
    if using_phone then
      return
    end

    TriggerEvent('blrp_tablet:getTabletState', function(tablet_open)
      if tablet_open then
        return
      end

      if exports.customscripts:IsNotepadOpen() then
        return
      end

      TriggerServerEvent('blrp_inventory:openInventory')
    end)
  end)
end)

local decaying_food_items = nil

RegisterNetEvent('blrp_inventory:show', function(inventory_data)
  if not decaying_food_items then
    decaying_food_items = pInventory.getDecayingFoodItems()
  end

  inventory_data.inv_zoom = exports.blrp_core:me().getSetting('inv_zoom')
  inventory_data.char_id = tonumber(exports.blrp_core:me().get('id'))
  inventory_data.server_time = GlobalState.server_time
  inventory_data.gun_decay_default = GlobalState.GItemsGunDecayDuration
  inventory_data.decaying_food_items = decaying_food_items

  if inventory_data.chest then
    if guiEnabled then -- stops chest order fucking up when reopening an open storage
      return
    end
    TriggerEvent('blrp_inventory:client:ForceDisarm', 'bl_inv cl @ L392')

    if inventory_data.chest.chest_name and inventory_data.chest.order then
      sort_cache[inventory_data.chest.chest_name] = inventory_data.chest.order
    end
  end

  if inventory_data.inventory.order then
    sort_cache['inventory:' .. inventory_data.char_id] = inventory_data.inventory.order
  end

  -- If type is update and gui is not enabled then dont send this
  if inventory_data.should_update_only_if_open and not guiEnabled then
    return
  end

  TriggerEvent('menu:forceCloseRadialMenu')
  focusNUI(true)

  SendNUIMessage({
    type = "inventory:toggle",
    enable = true,
    packet = inventory_data,
  })
end)

RegisterNetEvent('blrp_inventory:client:setCanHotKeysState', function(state)
  canUseHotkeys = state
end)

RegisterNetEvent('blrp_inventory:hide', function()
  focusNUI(false)
  SendNUIMessage({
    type = "inventory:toggle",
    enable = false,
  })
end)

RegisterNetEvent('menu:forceCloseMenu', function()
  focusNUI(false)
  SendNUIMessage({
    type = "inventory:toggle",
    enable = false,
  })
end)

local available_shots = 0
local fired_shots = 0
local fired_taser_shots = 0

local available_ammo = 0
local cached_shots = 0
local cache_time = 0

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(50)

    if cached_shots > 0 and GetGameTimer() - cache_time > 250 then
      local _shots = cached_shots

      cached_shots = 0

      if GetHashKey(currentWeapon) ~= `WEAPON_CANDYBOMB` then
        TriggerServerEvent('blrp_inventory:registerFiredBullet', currentWeapon, _shots)
      end

      cache_time = GetGameTimer()
    end
  end
end)

local throwableDisarm = {
  ['WEAPON_SNOWBALL'] = true,
}

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    if IsPedShooting(PlayerPedId()) then
      cache_time = GetGameTimer()

      fired_shots = fired_shots + 1
      cached_shots = cached_shots + 1

      if currentWeapon ~= 'WEAPON_FIREEXTINGUISHER' then
        if fired_shots >= available_ammo then
          cache_time = 0
          local _currentWeapon = currentWeapon

          if (fired_shots > available_ammo) or
             (throwableDisarm[currentWeapon] and (fired_shots >= available_ammo)) then
            Citizen.Wait(100)
            TriggerEvent('blrp_inventory:client:ForceDisarm', 'bl_inv cl @ L480')
          end

        end
      end

      if currentWeapon == 'WEAPON_STUNGUN' then
        fired_taser_shots = fired_taser_shots + 1

        if fired_taser_shots >= available_shots then
          TriggerEvent('blrp_inventory:client:ForceDisarm', 'bl_inv cl @ L488')
        end
      end

      if currentWeapon == 'WEAPON_FLASHBANG' or currentWeapon == 'WEAPON_MOLOTOV' or currentWeapon == 'WEAPON_GRENADE' then
        Citizen.Wait(100)
        TriggerEvent('blrp_inventory:client:ForceDisarm', 'bl_inv cl @ L493')
      end

      if currentWeapon == 'WEAPON_FLARE' then
        TriggerEvent('core:client:toggleNpcAgroState', true)
        TriggerEvent('core:client:resetPedMeleeState')

        Citizen.CreateThread(function()
          Citizen.Wait(7000)
          TriggerEvent('core:client:renewNpcAgroState')
          TriggerEvent('core:client:toggleNpcAgroState', false)
        end)
        Citizen.Wait(100)
        TriggerEvent('blrp_inventory:client:ForceDisarm', 'bl_inv cl @ L505')
      end
    end
  end
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)

    pcall(function()
      if currentWeapon and currentWeapon ~= 'WEAPON_UNARMED' and exports.blrp_core:me().isDisabledDeadOrRestrained() then
        TriggerEvent('blrp_inventory:client:ForceDisarm', 'bl_inv cl @ L519')
      end
    end)
  end
end)

RegisterNetEvent("blrp_inventory:client:ForceDisarm", function(called_from)
  if called_from then
    print('forceDisarm called from', called_from)
  end

  if currentWeapon then
    last_hotbar_number = nil
    TriggerServerEvent('blrp_inventory:saveUsedBullets', currentWeapon)
    SetCurrentPedWeapon(PlayerPedId(), GetHashKey("WEAPON_UNARMED"), true)

    local playerPed = PlayerPedId()
    local hasParachute = HasPedGotWeapon(playerPed, GetHashKey('GADGET_PARACHUTE'), false)

    RemoveAllPedWeapons(playerPed, true)

    if hasParachute then
      GiveWeaponToPed(playerPed, GetHashKey('GADGET_PARACHUTE'), 1, false, false)
    end

    currentWeapon = nil
    fired_shots = 0
    fired_taser_shots = 0
    available_ammo = 0

    Citizen.CreateThread(function()
      local endTime = GetGameTimer() + 500
      while GetGameTimer() < endTime do
        DisablePlayerFiring(PlayerPedId(), false)
        Citizen.Wait(0)
      end
    end)


    Player(GetPlayerServerId(PlayerId()))
              .state:set('is_armed', false, true)
  end

  currentWeapon = nil
end)

has_ammo = false

local police_only = {
  ['WEAPON_BEANBAG'] = true,
  ['WEAPON_SMG'] = true,
  ['WEAPON_UMP45'] = true,
  ['WEAPON_CARBINERIFLE'] = true,
  ['WEAPON_CARBINERIFLET'] = true,
  ['WEAPON_SPECIALCARBINE'] = true,
  ['WEAPON_CARBINERIFLE_MK2'] = true,
  ['WEAPON_FM5_SPEARLT'] = true,
  ['WEAPON_CARBINERIFLEMK2T'] = true,
  ['WEAPON_GRENADELAUNCHER_SMOKE'] = true,
  ['WEAPON_TACTICALRIFLE'] = true,
  ['WEAPON_SPEEDGUN'] = true,
  ['WEAPON_LESSLAUNCHER'] = true,
}

leo_offduty = {
  ['WEAPON_COMBATPISTOL'] = true,
  ['WEAPON_GLOCK17'] = true,
  ['WEAPON_SERVICEPISTOL_9MM'] = true,
  ['WEAPON_HEAVYPISTOL'] = true,
  ['WEAPON_PUMPSHOTGUN'] = true,
  ['WEAPON_PUMPSHOTGUN2'] = true,
}

RegisterNetEvent("blrp_inventory:client:UseWeapon")
AddEventHandler("blrp_inventory:client:UseWeapon", function(idname, ammo_amount, meta)
    Player(GetPlayerServerId(PlayerId())) -- dont before anything else toggle give time for other scripts to know
            .state:set('is_armed', true, false)

    local me = exports.blrp_core:me()
    local weaponName = idname
    if not ammo_amount then ammo_amount = 0 end
    if currentWeapon then
        -- Update bullets when weapon is put away
        TriggerServerEvent('blrp_inventory:saveUsedBullets', currentWeapon, idname)
        fired_shots = 0
        fired_taser_shots = 0
    end
    if idname == 'WEAPON_STUNGUN' then
      available_shots = ammo_amount
    end

    local weapon_hash = GetHashKey(weaponName)
    TriggerEvent('core:client:animations:wipePlayer')

    -- Anti cheese
    if
      currentWeapon ~= weaponName and
      has_value(medium_animation, currentWeapon) and
      (
        weaponName == 'WEAPON_SWITCHBLADE' or
        weaponName == 'WEAPON_MILSPECKNIFE' or
        weaponName == 'WEAPON_SHANK' or
        weaponName == 'WEAPON_FLASHLIGHT' or
        weaponName == 'WEAPON_FLASHLIGHT_UV' or
        weaponName == 'WEAPON_KNIFE' or
        weaponName == 'WEAPON_BOTTLE' or
        weaponName == 'WEAPON_BATTLEAXE' or
        weaponName == 'WEAPON_CROWBAR' or
        weaponName == 'WEAPON_WRENCH' or
        weaponName == 'WEAPON_BAT' or
        weaponName == 'WEAPON_DAGGER' or
        weaponName == 'WEAPON_HAMMER' or
        weaponName == 'WEAPON_POOLCUE' or
        weaponName == 'WEAPON_KATANA' or
        weaponName == 'WEAPON_DRAGON_KATANA_BLUE' or
        weaponName == 'WEAPON_KATANA_2' or
        weaponName == 'WEAPON_GOLFCLUB'
      )
    then
      local is_police = (me.hasGroup('LEO') or me.hasGroup('Security'))

      if
        (police_only[weaponName] and not is_police) or
        (leo_offduty[weaponName] and not is_police and not me.hasGroup('LEO_OffDuty')) or
        (weaponName == 'WEAPON_STUNGUN' and not is_police and not me.isEMS()) or
        (weaponName == 'WEAPON_NIGHTSTICK' and not is_police and not me.isEMS())
      then
        return
      end

      TriggerEvent('gundraw:client:holsterAnimate', currentWeapon, function()
        currentWeapon = weaponName

        if weaponName == 'WEAPON_PETROLCAN' then
            TriggerEvent('essence:buyCan')
            ammo_amount = 1
        end
        if weaponName == 'WEAPON_FIREEXTINGUISHER' then ammo_amount = 40000000 end
        if weaponName == 'WEAPON_FLASHBANG' then ammo_amount = 1 end
        if weaponName == 'WEAPON_GARBAGEBAG' then ammo_amount = 1 end
        if weaponName == 'WEAPON_FLARE' then ammo_amount = 1 end
        if weaponName == 'WEAPON_MOLOTOV' then ammo_amount = 1 end
        if weaponName == 'WEAPON_GRENADE' then ammo_amount = 1 end

        GiveWeaponToPed(PlayerPedId(), weapon_hash, ammo_amount, false, false)
        SetPedAmmo(PlayerPedId(), weapon_hash, ammo_amount)
        available_ammo = ammo_amount
        SetCurrentPedWeapon(PlayerPedId(), weapon_hash, true)

        Citizen.CreateThread(function()
          local blocking = false

          while currentWeapon == weaponName do
            local _, current_ped_weapon_hash = GetCurrentPedWeapon(PlayerPedId())

            if not blocking and current_ped_weapon_hash ~= weapon_hash then
              blocking = true
              Citizen.Wait(500)
              blocking = false

              if currentWeapon == weaponName then
                local new_ammo_amount = ammo_amount - fired_shots

                GiveWeaponToPed(PlayerPedId(), weapon_hash, new_ammo_amount, false, false)
                SetPedAmmo(PlayerPedId(), weapon_hash, new_ammo_amount)
                available_ammo = new_ammo_amount
                fired_shots = 0
                ammo_amount = available_ammo
                SetCurrentPedWeapon(PlayerPedId(), weapon_hash, true)
              end
            end

            Citizen.Wait(100)
          end
        end)

        TriggerEvent('gundraw:client:drawAnimate', weaponName, true)

        if ammo_amount < 1 then
          has_ammo = false
            if IsPedInAnyVehicle(ped, true) then
                TriggerEvent('vrp:client:notify', 'You have no ammo')
            end
            if weaponName == 'WEAPON_STUNGUN' then
              SetPlayerCanDoDriveBy(ped, false)
              DisablePlayerFiring(ped, true)
            end
        else
          has_ammo = true
        end

        if meta and meta.components then
          TriggerEvent('core:client:weapon-components:addEquippedMods', weaponName, meta.components)
        end

        if
          meta and
          (
            meta.fpd or
            (
              meta.gun_exp_time and
              GlobalState.server_time > meta.gun_exp_time
            )
          )
        then
          Citizen.CreateThread(function()
            while currentWeapon == weaponName do
              Citizen.Wait(0)
              DisableControlAction(0, 24, true)
              DisableControlAction(0, 69, true)
              DisableControlAction(0, 92, true)
              DisableControlAction(0, 257, true)
            end
          end)
        end
      end)
    elseif currentWeapon == weaponName then -- Put Weapon Away
        SetPedAmmo(PlayerPedId(), GetHashKey(currentWeapon), 0)
        available_ammo = 0

        local playerPed = PlayerPedId()
        local hasParachute = HasPedGotWeapon(playerPed, GetHashKey('GADGET_PARACHUTE'), false)

        TriggerEvent('gundraw:client:holsterAnimate', currentWeapon, function()
            SetCurrentPedWeapon(playerPed, GetHashKey('WEAPON_UNARMED'), true)
            RemoveAllPedWeapons(playerPed, true)

            if hasParachute then
                GiveWeaponToPed(playerPed, GetHashKey('GADGET_PARACHUTE'), 1, false, false)
            end
        end)

        currentWeapon = nil
    else
      local is_police = (me.hasGroup('LEO') or me.hasGroup('Security'))

      if
        (police_only[weaponName] and not is_police) or
        weaponName == 'WEAPON_STUNGUN' and not is_police and not me.isEMS()
      then
        return
      end

      currentWeapon = weaponName

        if weaponName == 'WEAPON_PETROLCAN' then
            TriggerEvent('essence:buyCan')
            ammo_amount = 1
        end
        if weaponName == 'WEAPON_FIREEXTINGUISHER' then ammo_amount = 40000000 end
        if weaponName == 'WEAPON_FLASHBANG' then ammo_amount = 1 end
        if weaponName == 'WEAPON_GARBAGEBAG' then ammo_amount = 1 end
        if weaponName == 'WEAPON_FLARE' then ammo_amount = 1 end
        if weaponName == 'WEAPON_MOLOTOV' then ammo_amount = 1 end
        if weaponName == 'WEAPON_GRENADE' then ammo_amount = 1 end

        GiveWeaponToPed(PlayerPedId(), weapon_hash, ammo_amount, false, false)
        SetPedAmmo(PlayerPedId(), weapon_hash, ammo_amount)
        available_ammo = ammo_amount
        SetCurrentPedWeapon(PlayerPedId(), weapon_hash, true)

        Citizen.CreateThread(function()
          local blocking = false

          while currentWeapon == weaponName do
            local _, current_ped_weapon_hash = GetCurrentPedWeapon(PlayerPedId())

            if not blocking and current_ped_weapon_hash ~= weapon_hash then
              blocking = true
              Citizen.Wait(500)
              blocking = false

              if currentWeapon == weaponName then
                local new_ammo_amount = ammo_amount - fired_shots

                GiveWeaponToPed(PlayerPedId(), weapon_hash, new_ammo_amount, false, false)
                SetPedAmmo(PlayerPedId(), weapon_hash, new_ammo_amount)
                available_ammo = new_ammo_amount
                fired_shots = 0
                ammo_amount = available_ammo
                SetCurrentPedWeapon(PlayerPedId(), weapon_hash, true)
              end
            end

            Citizen.Wait(100)
          end
        end)

        TriggerEvent('gundraw:client:drawAnimate', weaponName)

        if ammo_amount < 1 then
          has_ammo = false
            if IsPedInAnyVehicle(ped, true) then
                TriggerEvent('vrp:client:notify', 'You have no ammo')
            end
            if weaponName == 'WEAPON_STUNGUN' then
              SetPlayerCanDoDriveBy(ped, false)
              DisablePlayerFiring(ped, true)
            end
        else
          has_ammo = true
        end

        if meta and meta.components then
          TriggerEvent('core:client:weapon-components:addEquippedMods', weaponName, meta.components)
        end

        if
          meta and
          (
            meta.fpd or
            (
              meta.gun_exp_time and
              GlobalState.server_time > meta.gun_exp_time
            )
          )
        then
          Citizen.CreateThread(function()
            while currentWeapon == weaponName do
              Citizen.Wait(0)
              DisableControlAction(0, 24, true)
              DisableControlAction(0, 69, true)
              DisableControlAction(0, 92, true)
              DisableControlAction(0, 257, true)
            end
          end)
        end
    end
end)

-- This is just in case the resources restarted whilst the NUI is focused
Citizen.CreateThread(function()
    TriggerEvent('blrp_inventory:hide')
end)

----------------------------
-- Callbacks
----------------------------

RegisterNUICallback('escape', function(data, cb)
    TriggerEvent('blrp_inventory:hide')

    if data.mode == 'store' then
      pInventory.storeClosed({ data.chest_name })
    end

    if data.mode == 'transfer' then
      pInventory.chestClosed({ data.chest_name, data.storage_order })
    end

    if sort_cache[data.inventory_name] ~= data.inventory_order then
      pInventory.saveInventoryOrder({ data.inventory_order })
    end

    cb('ok')
end)

-- Item Being Moved
RegisterNUICallback('itemBeingMoved', function(data, cb)
    cb('ok')
    TriggerEvent('core:client:animations:queueAnimationSmart', 'pickup_object', 'putdown_low', 2, true, {
        forward = 0.1, wipeAfter = 300, instantClear = true,
    })
end)

-- Request item actions when a player starts dragging an item
RegisterNUICallback('requestItemActions', function(data, callback)
  callback(pInventory.getItemActions({ data.item_code }))
end)

RegisterNUICallback('itemMovedSync', function(data, cb)
  cb('ok')

  pInventory.itemMovedSync({ data })
end)

-- Init inventory
RegisterNetEvent('blrp_inventory:init')
AddEventHandler('blrp_inventory:init', function(data)
    SetTimeout(2000, function() -- Wait for local storage to load to sort inventory
        SendNUIMessage({
            type = "inventory:init",
            packet = data
        })
    end)
end)

-- Send item actions back to panel
RegisterNetEvent('blrp_inventory:setItemSelected')
AddEventHandler('blrp_inventory:setItemSelected', function(action_name, hotbar_number)
    local direction = 'none'
    local should_flash = false
    local inv_flash_hotbar = exports.blrp_core:me().getSetting('inv_flash_hotbar')
    local inv_flash_hotbar_driving = exports.blrp_core:me().getSetting('inv_flash_hotbar_driving')

    if inv_flash_hotbar == '1' then
        should_flash = true
    end

    if inv_flash_hotbar_driving == '1' and GetVehiclePedIsUsing(PlayerPedId()) > 0 then
        should_flash = true
    end

    if action_name == 'Equip' then
        if last_hotbar_number == hotbar_number then
            direction = 'in'
            last_hotbar_number = nil
        else
            last_hotbar_number = hotbar_number
            direction = 'out'
        end
    end

    SendNUIMessage({
        type = "inventory:send:setItemSelected",
        hotbar_number = hotbar_number,
        should_flash = should_flash,
        direction = direction,
    })
end)


-- Request item actions when a player starts dragging an item
RegisterNUICallback('performItemAction', function(data, cb)
  cb('ok')

  if not data then
    return
  end

  TriggerServerEvent('blrp_inventory:performItemAction', {
    item_code = data.item_code,
    action_name = data.action_name,
    item_amount = data.item_amount,
    meta = data.meta,
  })
end)

RegisterNUICallback('performMoveToStorage', function(data, cb)
  local success, message = pInventory.moveToStorage({ data.chest_name, data.item_code, data.item_amount, data.position })

  cb({
    success = success,
    message = message or 'Failed to move item',
  })
end)

RegisterNUICallback('performMoveToInventory', function(data, cb)
  local success, message = pInventory.moveToInventory({ data.chest_name, data.item_code, data.item_amount, data.position })

  cb({
    success = success,
    message = message or 'Failed to move item',
  })
end)

RegisterNUICallback('performSellToStore', function(data, cb)
  local success, message = pInventory.sellToStore({ data })

  cb({
    success = success,
    message = message or 'Failed to sell item',
  })
end)

RegisterNUICallback('performBuyFromStore', function(data, cb)
  local success, message = pInventory.buyFromStore({ data })

  cb({
    success = success,
    message = message or 'Failed to buy item',
  })
end)

RegisterNUICallback('setItemSlots', function(data, cb)
    TriggerServerEvent('blrp_inventory:setItemSlots', data)
    cb('setItemSlots ok')
end)

local displayEnlargedImageRunning = false
local baseColor = vector4(255, 255, 255, 255)

RegisterNetEvent('blrp_inventory:showEnlargedImage')
AddEventHandler('blrp_inventory:showEnlargedImage', function(fileName, posx, posy, sizex, sizey)
  if not displayEnlargedImageRunning then
    displayEnlargedImageRunning = true
    Citizen.CreateThread(function()
      exports['mythic_notify']:PersistentAlert('start', closIdNotifId, 'inform', 'Press E to close card view')
      local pos = vector2(posx, posy)
      local cardimg = CreateRuntimeTxd("card_img")
      CreateRuntimeTextureFromImage(cardimg, "card_img", "images/"..fileName..".webp")
      while displayEnlargedImageRunning do
        DrawSprite("card_img", "card_img", pos, sizex, sizey, 0.0, baseColor)
        if IsControlPressed(0, 38) then
					displayEnlargedImageRunning = false
				end
        Citizen.Wait(0)
      end
      exports['mythic_notify']:PersistentAlert('end', closIdNotifId)
    end)
  end
end)

RegisterNetEvent('core:client:inventory:chestItemIn', function(chest_name, item, position)
  SendNUIMessage({
    type = 'inventory:sync:chestItemIn',
    chest_name = chest_name,
    item = item,
    position = position,
  })
end)

RegisterNetEvent('core:client:inventory:storeQuantitySync', function(store_name, item_id, amount)
  SendNUIMessage({
    type = 'inventory:sync:storeQuantitySync',
    store_name = store_name,
    item_id = item_id,
    amount = amount,
  })
end)

RegisterNetEvent('core:client:inventory:chestItemOut', function(chest_name, item, position)
  SendNUIMessage({
    type = 'inventory:sync:chestItemOut',
    chest_name = chest_name,
    item = item,
    position = position,
  })
end)

RegisterNetEvent('core:client:inventory:chestItemSync', function(chest_name, from_position, to_position, amount)
  SendNUIMessage({
    type = 'inventory:sync:chestItemMove',
    chest_name = chest_name,
    from_position = from_position,
    to_position = to_position,
    amount = amount,
  })
end)
