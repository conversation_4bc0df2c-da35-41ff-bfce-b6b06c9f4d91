local hotkeys = {}
local w_ammo = {}
local last_weapon_idname = {}
local last_idname = {}
local last_active_slot = nil
local legacy_ammo_map = nil

RegisterServerEvent('blrp_inventory:lastItemTouched')
AddEventHandler('blrp_inventory:lastItemTouched', function(player, callback)
    if last_idname[player] then
        return callback(last_idname[player])
    end
end)

RegisterNetEvent('blrp_inventory:openInventory', function(target, should_update_only_if_open)
  local player = target
  local from_client = false

  if not player then
    player = source
    from_client = true
  end

  local character = exports.blrp_core:character(player)

  if exports.blrp_core:IsChestOpen('inventory:' .. character.get('id')) then
    exports.blrp_core:kickPeopleFromChest('inventory:' .. character.get('id'))
  end

  if from_client then
    local vehicle = GetVehiclePedIsIn(GetPlayerPed(character.source))

    if not vehicle or vehicle <= 0 then
      -- Try to open the ground nearby
      local player_bucket = Player(character.source).state.bucket_name
      local ground_chest = getClosestChest(character.getCoordinates(), false, player_bucket)

      if ground_chest then
        openGroundChest(character.source, ground_chest)
        return
      end
    end
  end

  if not should_update_only_if_open then
    local items = character.getAllItems()

    if not should_update_only_if_open then
      -----------------------------------
      -------- REGISTER WEAPONS ---------
      ------- ADD GUN DURABILITY --------
      -----------------------------------

      for item_id, item_data in pairs(items) do
        local item_id_base = string.gsub(item_id, ':' .. (string.match(item_id, ":(.*)") or ''), '')
        local unique = (item_id ~= item_id_base)
        local amount = item_data.amount
        local meta = item_data.meta
        local meta_touched = false
        local item_id_new = nil

        -- Retrieve the item definition for each item
        local item_definition = exports.blrp_core:GetItemDefinition(item_id_base)

        if item_definition then
          -- Process `wbody` items specifically if applicable
          if string.match(item_id, 'wbody') then
            if not unique then
              if item_definition.gun then
                -- Generate serial if gun and register if legal
                local sequence = exports.blrp_core:DataGet('core:weapon_serial_sequence:' .. item_definition.gun_code) or 0
                sequence = sequence + 1
                exports.blrp_core:DataStore('core:weapon_serial_sequence:' .. item_definition.gun_code, sequence, true)

                local serial = (item_definition.gun_code .. string.format('%07d', sequence))
                item_id_new = item_id_base .. ':meta:' .. serial

                if not item_definition.illegal then
                  if not meta then
                    meta = {}
                  end

                  meta.registration = serial
                  meta_touched = true

                  MySQL.Sync.insert('INSERT INTO evidence_registrations (character_number, weapon_name, registration) VALUES (@character_id, @weapon_name, @registration)', {
                    character_id = character.get('id'),
                    weapon_name = item_definition.id,
                    registration = serial
                  })
                end
              elseif not item_definition.serial_exempt then
                item_id_new = item_id_base .. ':meta:' .. os.date('%y%m%d') .. GetGameTimer()
              end
            end

            if item_definition.gun and (not meta or not meta.gun_exp_time) then
              if not meta then
                meta = {}
              end

              local gun_lifetime = item_definition.gun_durability

              if gun_lifetime and not meta.fpd then
                gun_lifetime = gun_lifetime.time

                local percent = 1.0
                local dur_start = meta.dur_start

                if dur_start and meta.dur_cur then
                  if meta.dur_repair then
                    dur_start = dur_start * (meta.dur_repair / 100)
                  end

                  percent = meta.dur_cur / dur_start
                end

                if not gun_lifetime then
                  gun_lifetime = GlobalState.GItemsGunDecayDuration
                end

                if meta.dur_repair then
                  gun_lifetime = gun_lifetime * (meta.dur_repair / 100)
                end

                if gun_lifetime ~= GlobalState.GItemsGunDecayDuration then
                  meta.gun_exp_life = gun_lifetime
                end

                meta.gun_exp_time = os.time() + math.floor(gun_lifetime * percent)
                meta_touched = true
                character.debug('Imprinted durability on non-durable gun', item_id, item_id_new)
              end
            end

            if item_definition.gun and meta and (meta.dur_start or meta.dur_cur) then
              exports.blrp_core:ModifyItemMeta(character.source, item_id, 'dur_start', nil)
              exports.blrp_core:ModifyItemMeta(character.source, item_id, 'dur_cur', nil)
            end

            if item_id_new then
              if character.take(item_id, 1, false) then
                character.givePromise(item_id_new, 1, false, meta, false)
              end
            elseif meta and meta_touched then
              for meta_key, meta_value in pairs(meta) do
                exports.blrp_core:ModifyItemMeta(character.source, item_id, meta_key, meta_value)
              end
            end
          end

          if item_definition.client_aware then
            local clientAwareItems = character.get('clientAwareItems') or {}

            if clientAwareItems[item_id_base] then
              clientAwareItems[item_id_base] = clientAwareItems[item_id_base] + amount
            else
              clientAwareItems[item_id_base] = amount
            end

            character.set('clientAwareItems', clientAwareItems)
          end
        end
      end


      -----------------------------------
      --------- AMMO CONVERSION ---------
      -----------------------------------

      if not legacy_ammo_map then
        legacy_ammo_map = exports.blrp_core:GetLegacyAmmoMap()
      end

      for item_id, item_data in pairs(items) do
        local amount = item_data.amount
        local new_ammo_name = legacy_ammo_map[item_id]

        if new_ammo_name and character.take(item_id, amount, false) and character.givePromise(new_ammo_name, amount, false) then
          character.log('COMPENSATION', 'Legacy ammo conversion / old_ammo_name = ' .. item_id .. ' / new_ammo_name = ' .. new_ammo_name)
        end
      end

      ----------------------------------
      ------ BACK WEAPONS / PROPS ------
      ----------------------------------

      character.client('core:client:back-stuff:setItems', character.getAllItems(true))
    end
  end

  TriggerClientEvent('blrp_inventory:show', character.source, exports.blrp_core:GetInventoryData(character.source, should_update_only_if_open))
end)

RegisterNetEvent('blrp_inventory:openEvidence', function(id)
  if not id then
    return
  end

  local character = exports.blrp_core:character(source)

  local allow_transfer_from_storage = tZones.isInsideMatchedZone(character.source, { 'PoliceStationZone' })
  local whitelisted_items = false

  if not allow_transfer_from_storage then
    whitelisted_items = { 'null' }
  end

  if not character.hasPermissionCore('police.transfer_evidence_from_locker') then
    allow_transfer_from_storage = false
  end

  local unpack_evidence_bins = false

  if string.match(id, 'pd_evidence_inc_person') then
    unpack_evidence_bins = true
  end

  character.openChest('pd_evidence_' .. id, 1000.0, false, {
    allow_transfer_from_storage = allow_transfer_from_storage,
    whitelisted_items = whitelisted_items,
    unpack_evidence_bins = unpack_evidence_bins,
  })
end)

RegisterNetEvent('blrp_inventory:openPrisonLockerFromTablet', function(character_number)
  local chestName = 'prison_locker_' .. character_number
  local leo_character = exports.blrp_core:character(source)
  local is_at_police_station = tZones.isInsideMatchedZone(leo_character.source, { 'PoliceStationZone' })

  if not is_at_police_station then
      return  leo_character.notify('You must be at a police station to do this')
  end

  leo_character.openChest(chestName, 50, false, {
    blacklisted_items = exports.blrp_core:GetItemsByCategory({ 'drugs', 'weapons.illegal' })
  })
end)

-- Citizen is taking items out of locker
RegisterNetEvent('blrp_inventory:openPrisonLockerFromDesk', function()
  local self_character = exports.blrp_core:character(source)

  self_character.openChest('prison_locker_' .. self_character.get('id'), 0, false, {
    whitelisted_items = { 'null' }
  })
end)

RegisterNetEvent('blrp_inventory:setItemSlots', function(hotKeysArray, src)
  local _source = src or source
  hotkeys[_source] = hotKeysArray

  local character = exports.blrp_core:character(_source)
  if character then
    exports.blrp_core:DataStore('hotkeys:inventory:' .. character.get('id'), hotKeysArray, true, true)
  end
end)

function doItemAction(player, item_id, amount, action_name, meta, hotkey)
  if not player then
    return
  end

  local character = exports.blrp_core:character(player)

  if not character then
    return
  end

  last_idname[player] = item_id

  local item_definition, item_id_metafree = exports.blrp_core:GetItemDefinition(item_id)

  if not item_definition then
    return
  end

  local item_actions = exports.blrp_core:GetItemActions(item_id, player)

  -- Hot bar item - try to find appropriate action
  if action_name == 'auto' then
    for _, action in ipairs({
      'Use',
      'Show',
      'Equip',
      'Eat',
      'Administer Treatment',
      'Administer Dosage',
      'Drink',
      'Smoke',
      'Deal',
      'Prepare',
      'Plant',
    }) do
      if action_name == 'auto' then
        for name, _ in pairs(item_actions) do
          if action == name then
            action_name = name
            break
          end
        end
      end
    end

    if action_name == 'auto' then
      character.notify('You cannot use this item from the hotbar')
      return
    end
  end

  if action_name == 'Give' or action_name == 'Drop' then
    character.client('blrp_inventory:client:ForceDisarm', 'inventory @ L301')

    -- Restrict Give / Drop based on allowable transfer groups
    if item_definition.transfer_groups then
      local allow_transfer = false

      for _, group_name in pairs(item_definition.transfer_groups) do
        if character.hasGroup(group_name) then
          allow_transfer = true
          break
        end

        if group_name == 'LEO' and character.hasGroup('LEO_OffDuty') then
          allow_transfer = true
          break
        end

      end

      if not allow_transfer then
        character.notify('Your hand slipped')
        return
      end
    end
  end

  -- Show the item as selected on the client
  character.client('blrp_inventory:setItemSelected', action_name, hotkey)

  if action_name == 'Equip' then

    local weaponName = string.gsub(tostring(item_id_metafree), 'wbody|', '')

    local ammo_amount = character.getItemQuantity(getAmmoName(weaponName)) or 0
    local meta = character.hasGetItemMeta(item_id)

    local w_ammo_data = {
      item_id = item_id,
      amount = ammo_amount,
      in_use = true
    }

    -- temporary handling to kill the 2024 christmas PD special carbines
    if weaponName == 'WEAPON_SPECIALCARBINE' then
      if meta and meta.gun_exp_time < 1741331055 then
        character.notify('This firearm is broken and cannot be equipped')
        exports.blrp_core:ModifyItemMeta(character.source, item_id, 'gun_exp_time', 1736035199)
        return
      end
    end

    if meta and meta.dur_cur then
      if meta.dur_cur < ammo_amount then
        ammo_amount = meta.dur_cur
        w_ammo_data.amount = ammo_amount
      end

      if meta.dur_start then
        local durability = (meta.dur_cur / meta.dur_start) * 100

        if durability <= 0 then
          character.notify('This firearm is broken and cannot be equipped')
          return
        end
      end

      w_ammo_data.dur_permissible = meta.dur_cur
    end

    if meta and meta.dur_exp_time then
      local time = os.time()
      if time > meta.dur_exp_time then
        character.notify('This firearm is broken and cannot be equipped')
        return
      end
    end

    if not w_ammo[player] then
      w_ammo[player] = {}
    end

    if not w_ammo[player][weaponName] then
      w_ammo[player][weaponName] = {}
    end

    if not w_ammo[player][weaponName].in_use and ammo_amount > 0 then
      w_ammo[player][weaponName] = w_ammo_data
    end

    if ammo_amount == 0 then
      if weaponName == 'WEAPON_FLARE' then
        return
      end

      if weaponName == 'WEAPON_BEANBAG' then
        character.notify('You do not have any beanbag rounds')
        return
      end
    end

    if character.hasAnyItems({
      's_suitcase1',
      's_suitcase2',
      'drug_package'
    },true) then
      character.notify('Your hands are full!!')
      return
    end

    character.client('blrp_inventory:client:UseWeapon', weaponName, ammo_amount, meta)
    last_weapon_idname[player] = item_id

    return
  end

  if action_name == 'Give' then
    exports.blrp_core:GiveItemPlayerToPlayer(player, item_id, amount, meta)

    return
  end

  if action_name == 'Drop' then
    dropItem(player, item_id, amount, meta)

    return
  end

  local item_action = item_actions[action_name]

  if not item_action then
    return
  end

  local item_callback = item_action.callback

  if not item_callback then
    return
  end

  item_callback(player, item_id)

  if not item_definition.dont_disarm then
    TriggerClientEvent('blrp_inventory:client:ForceDisarm', player, 'inventory @L425')
  end
  
  TriggerEvent('blrp_inventory:openInventory', player, true)
end

-- When a player uses a hotkey from the client, verify ownership and check
RegisterNetEvent('blrp_inventory:activeItemSlot', function(slot)
  local character = exports.blrp_core:character(source)

  if not hotkeys[character.source] then return end
  if not hotkeys[character.source][slot] then return end
  local item = hotkeys[character.source][slot]
  last_active_slot = slot

  if item.idname == 'empty' then
    character.notify('No item in hotkey slot ' .. slot)
    return
  end

  local meta = character.hasGetItemMeta(item.idname)

  if not meta then
    return
  end

  doItemAction(source, item.idname, 1, item.action_name, meta, slot)
end)

RegisterNetEvent('blrp_inventory:performItemAction', function(params, player)
  if player == nil then
    player = source
  end

  local action_name = params.action_name
  local idname = params.item_code
  local item_amount = params.item_amount
  local meta = params.meta
  local hotkey = params.hotkey

  doItemAction(player, idname, item_amount, action_name, meta, hotkey)
end)

local ammo_name_cache = {}

function getAmmoName(weapon_name)
  local ammo_name = ammo_name_cache[weapon_name]

  if ammo_name then
    return ammo_name
  end

  ammo_name = exports.blrp_core:GetWeaponAmmoItem(weapon_name)

  ammo_name_cache[weapon_name] = ammo_name

  return ammo_name
end

exports('GetCurrentWeapon', function(player)
  return last_weapon_idname[player] or 'NONE'
end)

RegisterNetEvent('blrp_inventory:server:getCurrentWeapon', function(target, callback)
  callback(last_weapon_idname[target] or false)
end)

local singleUseThrowables = {
  ['WEAPON_MOLOTOV'] = true,
  ['WEAPON_FLARE'] = true,
  ['WEAPON_FLASHBANG'] = true,
  ['WEAPON_GRENADE'] = true,
}

RegisterNetEvent('blrp_inventory:registerFiredBullet', function(weapon_name, shots)
  if not shots then
    shots = 1
  end

  local player = source

  if not w_ammo[player] or not w_ammo[player][weapon_name] then
    return
  end

  if not w_ammo[player][weapon_name].fired then
    w_ammo[player][weapon_name].fired = 0
  end

  w_ammo[player][weapon_name].fired = w_ammo[player][weapon_name].fired + shots

  if not singleUseThrowables[weapon_name] then
    exports.blrp_core:character(player).take(getAmmoName(weapon_name), shots, false)
  end

  if w_ammo[player][weapon_name].dur_permissible then
    local remaining = w_ammo[player][weapon_name].dur_permissible - w_ammo[player][weapon_name].fired

    if remaining <= 0 then
      TriggerClientEvent(player, 'blrp_inventory:client:ForceDisarm', 'bl inv serv @ L517')
    end
  end
end)

RegisterNetEvent('blrp_inventory:saveUsedBullets', function(idname)
  local character = exports.blrp_core:character(source)

  if not character or not w_ammo[character.source] or not w_ammo[character.source][idname] then
    return
  end

  local shot_count = w_ammo[character.source][idname].fired
  local full_idname = w_ammo[character.source][idname].item_id

  if not shot_count then return end

  -- Reset ammo tracking
  w_ammo[character.source][idname].item_id = nil
  w_ammo[character.source][idname].in_use = false
  w_ammo[character.source][idname].amount = 0
  w_ammo[character.source][idname].fired = 0
  w_ammo[character.source][idname].dur_permissible = nil

  -- Only remove item if it's a single-use throwable
  if not singleUseThrowables[idname] then return end

  local ammo_name = nil

  -- First check hotbar slots
  if hotkeys[character.source] then
    if last_active_slot then
      local item = hotkeys[character.source][last_active_slot]
      if item and item.idname and string.find(item.idname, "wbody|" .. idname) then
        ammo_name = item.idname
      end
    end

    -- Fallback check other hotkeys
    if not ammo_name then
      for _, item in ipairs(hotkeys[character.source]) do
        if item.idname and string.find(item.idname, "wbody|" .. idname) then
          ammo_name = item.idname
          break
        end
      end
    end
  end

  -- Fallback to full inventory
  if not ammo_name then
    for item_id, item in pairs(character.getAllItems()) do
      if string.find(item_id, "wbody|" .. idname) then
        ammo_name = item_id
        break
      end
    end
  end

  if not ammo_name then
    ammo_name = getAmmoName(idname)
  end

  character.take(ammo_name, shot_count or 1, false)
end)

function has_value(tab, val)
    for index, value in ipairs(tab) do
        if value == val then
            return true
        end
    end

    return false
end
