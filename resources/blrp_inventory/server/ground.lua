local chest_counter = 1
local ttl = 60 * 30 -- Time to live for chests on the ground with items inside

tZones = T.getInstance('blrp_zones', 'zones')

GlobalState.ground_chests = {}

function addGlobalChest(id, chest_data)
  local chests = GlobalState.ground_chests

  chests[id] = chest_data

  GlobalState.ground_chests = chests
end

function removeGlobalChest(id)
  local chests = GlobalState.ground_chests

  chests[id] = nil

  GlobalState.ground_chests = chests
end

function modifyGlobalTtl(id, variance, exact)
  local chests = GlobalState.ground_chests

  if not chests[id] then
    return
  end

  if variance then
    chests[id].expiration_time = chests[id].expiration_time + variance
  end

  if exact then
    chests[id].expiration_time = exact
  end

  GlobalState.ground_chests = chests
end

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)

    if GlobalState.ground_chests then
      for chest_key, chest_data in pairs(GlobalState.ground_chests) do
        if chest_data.expiration_time <= os.time() then

          removeGlobalChest(chest_key)
        end
      end
    end
  end
end)

function getClosestChest(coords, create, player_bucket)
  if create == nil then
    create = false
  end

  local chest = nil
  local closest_distance = -1

  -- Normalize player coords to ground-relative level
  coords = coords - vector3(0, 0, 0.85)

  if GlobalState.ground_chests then
    for chest_key, chest_data in pairs(GlobalState.ground_chests) do
      -- Only consider chests from the same bucket
      if chest_data.bucket_name == player_bucket then
        local distance = #(coords - chest_data.coords)

        if distance <= 3.0 and (distance < closest_distance or closest_distance == -1) then
          chest = chest_data
        end
      end
    end
  end

  if chest or not create then
    return chest
  end

  local chest_id = chest_counter
  chest_counter = chest_counter + 1

  local chest_data = {
    chest_id = chest_id,
    coords = coords,
    name = 'grounditems_' .. coords .. '_' .. player_bucket,
    bucket_name = player_bucket,
    expiration_time = os.time() + ttl
  }

  addGlobalChest(chest_id, chest_data)

  return chest_data
end

local ground_chests_to_check = {}

AddEventHandler('core:server:inventory:chestItemIn', function(player, chest_name, item_id, amount)
  if not string.match(chest_name, 'grounditems') then
    return
  end

  for _, chest_data in pairs(GlobalState.ground_chests) do
    if chest_data.name == chest_name then
      modifyGlobalTtl(chest_data.chest_id, false, (os.time() + ttl))
    end
  end
end)

AddEventHandler('core:server:inventory:chestItemOut', function(player, chest_name, item_id, amount)
  if not string.match(chest_name, 'grounditems') then
    return
  end

  ground_chests_to_check[chest_name] = true
end)

function openGroundChest(player, chest_data)
  local character = exports.blrp_core:character(player)
  local coords = chest_data.coords
  local player_bucket = Player(player).state.bucket_name

  -- Verify player is in the same bucket as the chest
  if chest_data.bucket_name ~= player_bucket then
    character.notify('Cannot access this item')
    return
  end

  -- Make sure the chest isn't going to expire while the person is in it
  if (chest_data.expiration_time - os.time()) < 200 then
    modifyGlobalTtl(chest_data.chest_id, 200)
  end

  character.animate({ {'pickup_object', 'putdown_low', 1} })

  character.openChest(chest_data.name, 100000, function()
    if ground_chests_to_check[chest_data.name] then
      ground_chests_to_check[chest_data.name] = nil

      local still_has_items = false
      local contents = exports.blrp_core:DataGet('chest:' .. chest_data.name, false, true) or {}

      for _, __ in pairs(contents) do
        still_has_items = true
        break
      end

      if not still_has_items then
        removeGlobalChest(chest_data.chest_id)
      end
    end
  end)
end

function dropItem(player, idname, amount, meta)
  local character = exports.blrp_core:character(player)
  local character_coords = character.getCoordinates()
  local player_bucket = Player(player).state.bucket_name

  local vehicle = GetVehiclePedIsIn(GetPlayerPed(character.source), false)

  if vehicle and vehicle > 0 then
    character_coords = GetEntityCoords(vehicle) + vector3(0, 0, 0.8)
  end

  local item_definition = exports.blrp_core:GetItemDefinition(idname)

  if item_definition.prevent_removal then
    character.notify('Cannot discard this item')
    return
  end

  if item_definition.prevent_removal_if_worn then
    local _, item_id_base = exports.blrp_core:GetItemDefinition(idname)

    local meta, item_id_real = character.hasGetItemMetaWithProperty(item_id_base, function(meta)
      return meta.is_wearing == true
    end)

    if item_id_real and idname == item_id_real then
      character.notify('You must remove the Hazmat Suit before dropping it.')
      return
    end
  end

  if not character.take(idname, amount, false) then
    character.notify('Invalid amount')
    return
  end

  if tZones.isInsideMatchedZone(character.source, { 'prisonVisitation' }) then
    character.notify('Cannot drop that here')
    return
  end

  local item_name_full = exports.blrp_core:GetItemName(idname, meta)

  local matched_zone = tZones.isInsideMatchedZone(character.source, { 'ApartmentZone', true })

  local log_string = nil

  if not matched_zone then
    local chest = getClosestChest(character_coords, true, player_bucket)

    handlePlayerItemTransfer(character.source, false, idname)

    exports.blrp_core:RemotePutChest(player, chest.name, idname, amount, meta)

    --exports.blrp_core:AntiCheatProcessItemTransfer(character.source, chest.name, 'in', idname, amount)

    log_string = 'Dropped ' .. amount .. ' ' .. item_name_full .. ' ( ' .. idname .. ' ) on the ground ' .. chest.name
  else
    log_string = 'Dropped ' .. amount .. ' ' .. item_name_full .. ' ( ' .. idname .. ' ) on the ground in ' .. matched_zone
  end

  character.log('ACTION', log_string)

  if idname == 'cash' and amount >= 500000 then
    character.log('BIGTRANSACTION', log_string)
  end

  TriggerEvent('blrp_inventory:openInventory', character.source, true)
  exports.blrp_core:HandlePotentialWeaponTransfer(false, character.source, idname)

  character.animate({{ 'pickup_object', 'pickup_low', 1 }}, true, false)
  character.notify('Dropped ' .. amount .. ' ' .. item_name_full)
end

function dropGroundItem(coords, idname, amount, meta, bucket_name)
  -- Default to global bucket if not specified
  if not bucket_name then
    bucket_name = 'global'
  end

  local chest = getClosestChest(coords, true, bucket_name)

  exports.blrp_core:RemotePutChest(false, chest.name, idname, amount, meta)
end

exports('DropGroundItem', dropGroundItem)
