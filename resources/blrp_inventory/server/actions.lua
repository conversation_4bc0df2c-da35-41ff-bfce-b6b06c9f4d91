function handlePlayerItemTransfer(player, to_player, item_id)
  local character = exports.blrp_core:character(player)
  local new_quantity = character.getItemQuantity(item_id)
  local item_definition = exports.blrp_core:GetItemDefinition(item_id)

  if item_definition and item_definition.category == 'phone' then
    is_personal_phone = true
  end

  if (item_id == 'hand_radio' or item_id == 'duty_radio') and new_quantity <= 0 then
    TriggerClientEvent('blrp_radio:client:removeFromAllChannels', character.source)
  elseif item_id == 'hand_gps' and new_quantity <= 0 then
    TriggerClientEvent('customscripts:client:setGpsState', character.source, false)
  elseif is_personal_phone and new_quantity <= 0 then
    TriggerClientEvent('gcphone:setPhoneState', character.source, false)
  elseif item_id == 'safe_kit' and new_quantity <= 0 then
    TriggerEvent('core:server:convenience-stores:cancelRobbery', 'other', character.source)
  elseif string.match(item_id, 'twine:meta:') then
    TriggerEvent('blrp_core:server:items:twine:removed', item_id, character.source, to_player)
  end

  if is_personal_phone and new_quantity <= 0 then
    TriggerEvent('blrp_phone:server:endPlayerCall', player, false, 'item-move')
    TriggerClientEvent('blrp_phone:client:setOwnsPhone', character.source, false)

    if to_player then
      TriggerClientEvent('blrp_phone:client:setOwnsPhone', to_player, true)
    end
  end

  local item_id_base = item_id

  if string.match(item_id_base, ':meta:') then
    item_id_base = string.sub(item_id_base, 0, string.find(item_id_base, ':meta') - 1)
  end

  if new_quantity <= 0 then
    TriggerEvent('blrp_inventory:itemLeftPlayer', player, item_id, item_id_base, to_player)
  end

  TriggerEvent('core:server:animation:pausePlayerByPlayer', player)
  TriggerEvent('core:server:animation:pausePlayerByPlayer', to_player)

  SetTimeout(1000, function()
    TriggerEvent('core:server:animation:resumePlayerByPlayer', player)
    TriggerEvent('core:server:animation:resumePlayerByPlayer', to_player)
  end)
end

exports('HandlePlayerItemTransfer', handlePlayerItemTransfer)


