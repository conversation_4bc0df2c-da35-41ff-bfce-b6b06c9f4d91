<html>
  <head>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />
  </head>

  <body>
    <div id="app">
      <div class="wrap">
        <div class="section-header" v-if="stage == 1 || stage == 2 || stage == 4">
          <div class="header-icon"><i class="fa-solid fa-fw fa-user-secret"></i></div>
          <div class="header-text">{{ minigameLabel }}</div>
        </div>

        <div class="loading-bar" v-if="stage == 1">
          <div class="loading-bar-inner" :style="{width: loadingPercent + '%'}"></div>
        </div>

        <div class="minigame" v-if="stage == 3">
          <div class="symbol no-border-bottom">
            <i
              class="fa-regular fa-fw"
              :class="[symbolTarget]"
              :style="{ color: symbolColor }"
            ></i>
          </div>

          <div id="symbols">
            <div
              v-for="(n, index) in 7"
              class="symbol"
              :class="{ 'no-border-top': index == 3}"
            >
              <i
                class="fa-regular fa-fw"
                :class="[symbols[index]]"
                :style="{ color: captured && index == 3 ? symbolColor : '' }"
              ></i>
            </div>
          </div>

          <div id="correct">
            <div
              v-for="index in symbolsNeeded"
              class="symbol small"
            >
              <i
                class="fa-regular fa-fw"
                :class="{ 'fa-binary-lock': index > symbolsObtained, 'fa-binary-circle-check': index <= symbolsObtained }"
                :style="{ color: index <= symbolsObtained ? '#55FF00' : '' }"
              >
              </i>
            </div>
          </div>

          <div id="capture">
            <button @click="capture">Capture</button>
          </div>

          <!-- TODO: lives? -->

          <div class="timer-bar" v-if="stage == 3">
            <div class="timer-bar-inner" :style="{width: timePercent + '%'}">
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.slim.min.js" integrity="sha256-u7e5khyithlIdTpu22PHhENmPcRdFiHRjhAuHcs05RI=" crossorigin="anonymous"></script>
    <script src="https://cfx-nui-blrp_ui/ui/vue.global.prod.js"></script>
    <script src="js/app.js"></script>
  </body>
</html>
