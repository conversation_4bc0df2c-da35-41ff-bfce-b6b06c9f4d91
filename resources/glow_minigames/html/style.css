@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&family=Roboto:wght@400;500;700&display=swap');

:root {
    --background-1: #0e111a;
    --background-2: #0e111bcc;
    --action-1: #23af57;
    --action-2: #d71f3e;
    --timer: #d71f3e;
}

#screen {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--background-1);
    color: white;
    
    font-family: "Roboto", sans-serif;
    font-size: 1.5vh;
    font-weight: 600;
    
    height: 63.3vmin;
    width: 60vmin;
    padding: 1vh;
    z-index: 2;
}

.screen-icon {
    font-size: 4vh;
    padding-bottom: 1vh;
    margin-top: -2vh;
}

/* Path */
#path-container {
    position: absolute;
    left: 50%;
    top: 48.5%;
    transform: translate(-50%, -50%);
}

#path-game-container {
    height: 62vmin;
    width: 62vmin;
}

#path-grid, #spot-grid {
    display: grid;
    background-color: var(--background-1);
    display: grid;
    gap: 0.5vmin;
    
    height: 60vmin;
    width: 60vmin;
    padding: 1vh;
}

.timer-container {
    margin-top: -0.5vh;
    padding: 1.4vh 2vh 1.7vh;
    background-color: var(--background-1);
}

.timer-outer {
    height: 0.6vh;
    width: 100%;
    background-color: rgb(36, 36, 36);
    border-radius: 2vh;
}

.timer-inner {
    height: 0.6vh;
    width: 100%;
    background-color: var(--timer);
    border-radius: 2vh;
}

.path-grid-square {
    background-color: white;
    margin: 0;
    opacity: 0.05;

    display: flex;
    align-items: center;
    justify-content: center;
}

.path-square {
    opacity: 0.2; 
}

.player {
    background-color: var(--action-1);
    opacity: 1;
}

.off-path-player {
    background-color: var(--action-2);
    opacity: 1;
    font-size: 1.5vh;
    animation: error-shake 0.3s linear 1;
}

@keyframes error-shake{
	0% {
		transform: rotate(0deg)
	}
	30% {
		transform: rotate(8deg)
	}
	50% {
		transform: rotate(-8deg)
	}
	100% {
		transform: rotate(0deg)
	}
}

#spot-container {
    position: absolute;
    left: 50%;
    top: 48.2%;
    transform: translate(-50%, -50%);
    font-family: "Roboto", sans-serif;
    text-transform: uppercase;
}

#spot-target {
    background-color: var(--background-2);
    border: 2px solid white;
    color: white;
    
    font-size: 7vh;
    font-weight: 600;
    text-align: center;
    
    margin: auto;
    margin-top: -6vh;
    margin-bottom: 1vh;
    
    width: 8.5vh;
}

.spot-grid-square  {   
    display: flex;
    align-items: center;
    justify-content: center;
    
    background-color: rgb(25, 25, 25);
    color: white;
    font-size: 3.5vh;
}

.spot-grid-square:hover {
    background-color: var(--action-2);
    cursor: pointer;
}

#math-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    
    display: flex;
    flex-direction: column;
    
    background-color: var(--background-1);
    font-family: "Roboto", sans-serif;
    font-size: 1.5vh;
    font-weight: 600;
    height: 62vmin;
    width: 62vmin;
}

#math-grid {    
    display: grid;
    grid-template-columns: auto auto auto auto auto auto auto;
    gap: 1.5vh;
    margin: auto;
    margin-top: 8vh;
}

.math-grid-item {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5vh;
    
    height: 4vh;
    width: 4vh;
}

.math-op {
    background-color: #ececec;
}

.math-answer {
    background-color: var(--action-1);
}

.math-input {
    background-color: var(--background-1);
    border: 2px solid white;
    color: white;
    font-family: "Roboto", sans-serif;
    font-size: 1.5vh;
    text-align: center;
    padding: 0;
}

input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
    -webkit-appearance: none; 
    margin: 0; 
}

.math-num-input {
    background-color: transparent;
    border: none;
    color: white;
}

#math-buttons {
    display: flex;
    color: white;
    margin: auto;
}

.math-button {
    border: 2px solid white;
    width: 7vh;
    text-align: center;
    padding: 0.5vh;
    border-radius: 0.3vh;
    margin: 0.5vh;
}

.math-button:hover {
    background-color: white;
    color: black;
    cursor: pointer;
}