Citizen.CreateThread(function()
    AddTextEntry("SC<PERSON><PERSON><PERSON><PERSON>", "Schlagen SP")
    AddTextEntry("SCHLASP_BON1", "Vented Bonnet")
    AddTextEntry("SCHLASP_CAGE0", "Remove Frame")
    AddTextEntry("SCHLASP_CAGE1", "Rollcage")
    AddTextEntry("SCHLASP_GRILL1", "Basic Grille")
    AddTextEntry("SCHLASP_GRILL2", "Basic Double Grille")
    AddTextEntry("SCHLASP_GRILL3", "GT Grille")
    AddTextEntry("SCHLASP_GRILL4", "GT Grille V2")
    AddTextEntry("SCHLASP_SKIRT1", "Carbon Skirts")
    AddTextEntry("SCHLASP_SKIRT2", "Carbon Skirts V2")
    AddTextEntry("SCHLASP_SPLIT1", "Carbon Splitter")
    AddTextEntry("SCHLASP_SVENTS1", "Side Vent Cover")
    AddTextEntry("SCHLASP_WING1", "GT Wing")
    AddTextEntry("SCHLASP_WING2", "Tuner Wing")
end)