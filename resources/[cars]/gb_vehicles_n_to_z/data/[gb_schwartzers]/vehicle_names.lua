Citizen.CreateThread(function()
AddTextEntry("GBSCHWARZ", "Schwartzer S")
AddTextEntry("GBSCHWART_BADGE", "Enus-Benefactor Badging")
AddTextEntry("GBSCHWART_SPL1", "Carbon Small Lip")
AddTextEntry("GBSCHWART_SPL1A", "Forged Carbon Small Lip")
AddTextEntry("GBSCHWART_SPL2", "Carbon Medium Lip")
AddTextEntry("GBSCHWART_SPL2A", "Forged Carbon Medium Lip")
AddTextEntry("GBSCHWART_SPL3", "Carbon Sport Lip")
AddTextEntry("GBSCHWART_SPL3A", "Forged Carbon Sport Lip")
AddTextEntry("GBSCHWART_SPL4", "Carbon Wing")
AddTextEntry("GBSCHWART_SPL4A", "Forged Carbon Wing")
AddTextEntry("GBSCHWART_SPL4B", "Color Coded Carbon Wing")
AddTextEntry("GBSCHWART_SPLT1", "Carbon Splitter")
AddTextEntry("GBSCHWART_SPLT1A", "Forged Carbon Splitter")
AddTextEntry("GBSCHWART_SPLT2", "Carbon Sport Splitter")
AddTextEntry("GBSCHWART_SPLT2A", "Forged Carbon Sport Splitter")
AddTextEntry("GBSCHWART_RCANARD1", "Forged Carbon Rear Trim")
AddTextEntry("GBSCHWART_RCANARD2", "Chrome Carbon Rear Trim")
AddTextEntry("GBSCHWART_RCANARD3", "Remove Rear Trim")
AddTextEntry("GBSCHWART_SKIRT1", "Carbon Skirt")
AddTextEntry("GBSCHWART_SKIRT1A", "Forged Carbon Skirt")
AddTextEntry("GBSCHWART_TRIM", "Black Window Trim")
AddTextEntry("GBSCHWART_GRIL0A", "Small Badge Grill")
AddTextEntry("GBSCHWART_GRIL1", "Black Grill")
AddTextEntry("GBSCHWART_GRIL1A", "Small Badge Black Grill")
AddTextEntry("GBSCHWART_GRIL2", "Premium Grill")
AddTextEntry("GBSCHWART_GRIL3", "Aggressor Grill")
AddTextEntry("GBSCHWART_HOOD1", "Inset Hood")
AddTextEntry("GBSCHWART_HOOD1A", "Carbon Inset Hood")
AddTextEntry("GBSCHWART_HOOD1B", "Forged Carbon Inset Hood")
AddTextEntry("GBSCHWART_HOOD2", "Aggressor Hood")
AddTextEntry("GBSCHWART_HOOD2A", "Carbon Aggressor Hood")
AddTextEntry("GBSCHWART_HOOD2B", "Forged Carbon Aggressor Hood")
AddTextEntry("GBSCHWART_GFRAME1", "Chrome Grill Frame")
AddTextEntry("GBSCHWART_GFRAME2", "Painted Grill Frame")
AddTextEntry("GBSCHWART_FCANARD1", "Forged Carbon Front Trim")
AddTextEntry("GBSCHWART_FCANARD2", "Chrome Carbon Front Trim")
AddTextEntry("GBSCHWART_FCANARD3", "Remove Front Trim")
AddTextEntry("GBSCHWART_ROOF", "Solid Roof")

AddTextEntry("GBSCHWART_LIV1", "White Two-Tone")
AddTextEntry("GBSCHWART_LIV2", "Black Two-Tone")
AddTextEntry("GBSCHWART_LIV3", "Cream Two-Tone")
AddTextEntry("GBSCHWART_LIV4", "Sea Blue Two-Tone")
AddTextEntry("GBSCHWART_LIV5", "Diamond Two-Tone")
AddTextEntry("GBSCHWART_LIV6", "Burgundy Two-Tone")
AddTextEntry("GBSCHWART_LIV7", "Sapphire Two-Tone")
AddTextEntry("GBSCHWART_LIV8", "Half Black")
AddTextEntry("GBSCHWART_LIV9", "Half White")
AddTextEntry("GBSCHWART_LIV10", "Germanic Stripes")
AddTextEntry("GBSCHWART_LIV11", "GT Fade Black")
AddTextEntry("GBSCHWART_LIV12", "GT Fade White")
AddTextEntry("GBSCHWART_LIV13", "Diamonds Are Forever")
AddTextEntry("GBSCHWART_LIV14", "Diamonds Are Forever Alt")
end)