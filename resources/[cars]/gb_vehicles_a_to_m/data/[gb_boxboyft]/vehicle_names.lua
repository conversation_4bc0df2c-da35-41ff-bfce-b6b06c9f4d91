Citizen.CreateThread(function()
AddTextEntry("GBBOX<PERSON>YFT", "Boxboy Food Truck")
AddTextEntry("BOXBOYFT_ROOF_1", "Donut")
AddTextEntry("BOXBOYFT_ROOF_2", "Mr. Whoopee")
AddTextEntry("B<PERSON><PERSON>YFT_ROOF_3", "Popsicle")
AddTextEntry("BOXBOYFT_ROOF_4", "Hot Dog")
AddTextEntry("BOXBOYFT_INTERIOR_1", "Generic Kitchen")
AddTextEntry("BOXBOYFT_INTERIOR_2", "Kitchen for Mexican Food")
AddTextEntry("B<PERSON>BOYFT_INTERIOR_3", "Bean Machine Kitchen")
AddTextEntry("BOXBOYFT_INTERIOR_4", "Cat Cafe Kitchen")
AddTextEntry("BOXBOYFT_INTERIOR_5", "Ice Cream Kitchen")
AddTextEntry("<PERSON><PERSON>BOYFT_INTERIOR_6", "Burger Shot Kitchen")
AddTextEntry("<PERSON><PERSON><PERSON><PERSON>FT_INTERIOR_7", "Rusty Browns Kitchen")
AddTextEntry("BOXBOYFT_INTERIOR_8", "Glory Holes Kitchen")
AddTextEntry("BOXBOYFT_LIV1", "Chihuahua Hot Dogs")
AddTextEntry("BOXBOYFT_LIV2", "Candybox")
AddTextEntry("BOXBOYFT_LIV3", "Rusty Browns Donuts")
AddTextEntry("BOXBOYFT_LIV4", "Cherry Popper Ice Cream")
AddTextEntry("BOXBOYFT_LIV5", "Taco Bomb")
AddTextEntry("BOXBOYFT_LIV6", "Burger Shot")
AddTextEntry("BOXBOYFT_LIV7", "Cluckin' Bell")
AddTextEntry("BOXBOYFT_LIV8", "Wigwam Burger")
AddTextEntry("BOXBOYFT_LIV9", "UWU Cafe")
AddTextEntry("BOXBOYFT_LIV10", "Pizza This")
end)
