--[[

This blacklist is really more a whitelist, because the value for each whitelisted attribute is a group that is permitted to wear that item
Multiple groups may be specified, delimited by a | character.

Example:

blacklists[`mp_m_freemode_01`] = {
  ['1'] = {
    [1] = 'LEO|LSFD',
    [2] = {
      [1] = 'LEO',
      [2] = 'LSFD'
    }
  }
}

In the above example, mask DRAWABLE # 1 is restricted to anyone who has the LEO (Given to all LEOs, training or otherwise) OR LSFD group.

mask DRAWABLE # 2 is available to all, but TEXTURE # 1 is restricted to police only and TEXTURE #2 is restricted to EMS only.

]]

blacklists[`mp_m_freemode_01`] = {
  sex = 'male',

  -- BERD - 01 - Mask
  ['1'] = {
    [50] = {
      [10] = 'Petrovich Cartel',
    },
    [51] = {
      [10] = 'item:clth_bsg_mask_a',
    },
    [111] = {
      [16] = 'The Unknown',
    },
    [175] = {
      [1] = 'item:clth_hazmat_suit',
    },
    [234] = {
      [4] = 'item:clth_cmc_mask_a',
      [5] = 'item:clth_cmc_mask_b',
    },
    [249] = {
      [0] = 'item:clth_305_mask_a',
      [1] = 'item:clth_vagos_mask_a',
      [4] = 'item:clth_fdf_mask_a',
      [7] = 'item:clth_ballas_mask_a',
      [9] = 'Blacklisted', -- Alamo Sea Pirates
      [10] = 'item:clth_lost_mask_a',
      [11] = 'item:clth_lost_mask_b',
      [12] = 'item:clth_lost_mask_c',
      [13] = 'item:clth_305_mask_b',
      [14] = 'item:clth_lsf_mask_a',
      [15] = 'item:clth_lsf_mask_b',
      [16] = 'item:clth_rascal_mask_a',
      [17] = 'item:clth_lsf_mask_c',
      [18] = 'item:clth_bd_mask_a',
      [19] = 'item:clth_bd_mask_b',
      [20] = 'Midnight Club',
      [21] = 'item:clth_vagos_mask_b',
      [23] = 'item:clth_ikonz_mask_a',
      [24] = 'item:clth_vend_mask_a',
      [25] = 'item:clth_bsg_mask_b',
    },
    [252] = 'Blacklisted', -- Chanel Beret
    [253] = 'Blacklisted', -- Under Armor Mask
    [254] = {
      [0] = 'item:clth_305_mask_c',
      [1] = 'item:clth_305_mask_d',
      [2] = 'item:clth_aod_mask_a',
      [3] = 'item:clth_aod_mask_b',
      [4] = 'item:clth_aod_mask_c',
      [5] = 'item:clth_cartel_mask_a',
      [6] = 'item:clth_cartel_mask_b',
      [7] = 'item:clth_cartel_mask_c',
      [8] = 'item:clth_cartel_mask_d',
      [9] = 'item:clth_ikonz_mask_b',
      [10] = 'item:clth_ikonz_mask_c',
      [11] = 'item:clth_mcf_mask_a',
      [12] = 'item:clth_mcf_mask_b',
      [13] = 'item:clth_mcf_mask_c',
      [14] = 'item:clth_bd_mask_c',
      [15] = 'item:clth_bd_mask_d',
      [16] = 'item:clth_bd_mask_e',
      [17] = 'item:clth_bd_mask_f',
      [18] = 'item:clth_vagos_mask_c',
      [19] = 'item:clth_vagos_mask_d',
      [20] = 'item:clth_vagos_mask_e',
      [21] = 'Street Side',
      [22] = 'item:clth_gh_mask_a',
    },
    [263] = {
      [0] = 'item:clth_rascal_mask_b',
      [1] = 'item:clth_bd_mask_g',
      [2] = 'item:clth_bd_mask_h',
      [3] = 'item:clth_cartel_mask_e',
      [4] = 'Blacklisted-RealWorldBrand',
      [15] = 'item:clth_305_mask_e',
      [16] = 'item:clth_swat_mask_b',
      [17] = 'item:clth_miradz_mask_a',
      [18] = 'item:clth_mongrels_mask_a',
      [19] = 'item:clth_mongrels_mask_b',
      [20] = 'item:clth_mongrels_mask_c',
    },
    [265] = 'Blacklisted', -- Spiderman Mask
    [268] = 'item:clth_knight_mask',
    [271] = {
      [3] = 'Blacklisted-RealWorldBrand',
      [4] = 'Blacklisted-RealWorldBrand',
    },
    [274] = 'Blacklisted', -- Call of Duty Ghost Mask
    [276] = {
      [0] = 'item:clth_vagos_mask_f',
      [1] = 'item:clth_vagos_mask_g',
    },
    [277] = {
      [0] = 'item:clth_aces_mask_a',
      [1] = 'item:clth_aces_mask_b',
      [2] = 'item:clth_aces_mask_c',
      [3] = 'item:clth_ballas_mask_b',
      [4] = 'item:clth_fdf_mask_b',
      [5] = 'item:clth_fdf_mask_c',
      [6] = 'item:clth_fdf_mask_d',
      [7] = 'item:clth_ballas_mask_c',
      [8] = 'Firm Gang',
      [9] = 'Firm Gang',
      [10] = 'Firm Gang',
      [11] = 'item:clth_mm_mask_a',
      [12] = 'item:clth_mm_mask_b',
      [13] = 'item:clth_rebels_mask_a',
      [14] = 'item:clth_scorpions_mask_a',
      [15] = 'item:clth_jc_mask_a',
      [16] = 'item:clth_jc_mask_b',
      [17] = 'item:clth_jc_mask_c',
      [18] = 'item:clth_jc_mask_d',
      [19] = 'item:clth_misfitz_mask_a',
      [20] = 'item:clth_scorpions_mask_b',
      [21] = 'item:clth_scorpions_mask_c',
      [22] = 'item:clth_ww_mask_a',
      [23] = 'item:clth_ww_mask_b',
      [24] = 'item:clth_ww_mask_c',
      [25] = 'item:clth_ww_mask_d',
    },
    [278] = 'item:p_cayo_mask_fire',
    [279] = 'item:p_cayo_mask_air',
    [280] = 'item:p_cayo_mask_earth',
    [281] = 'item:p_cayo_mask_water',
    [282] = {
      [0] = 'item:clth_pris_mask_a',
      [1] = 'item:clth_pris_mask_b',
    },
    [283] = 'Blacklisted-RealWorldBrand',
    [284] = 'Blacklisted-RealWorldBrand',
    [285] = {
      [0] = '305 Mafia',
      [1] = 'The Unknown',
      [2] = 'Angels of Death',
      [3] = 'Ballas',
      [4] = 'Blacklisted', -- Beechwood Disciples
      [5] = 'Petrovich Cartel',
      [6] = 'Blacklisted', -- Condemed MC
      [7] = 'Forum Family',
      [8] = 'BSG',
      [9] = 'Ikonz',
      [10] = 'Lost MC',
      [11] = 'Messina Crime Family',
      [12] = 'OTF',
      [13] = 'Blacklisted-Rascals',
      [14] = 'Vagos',
      [15] = 'Rebels',
      [16] = 'Scorpions',
      [17] = 'Mutiny Mob',
      [18] = 'The Chosen Few MC',
      [19] = 'Blacklisted', -- Westwoods
      [20] = 'Yokai',
      [21] = 'Vendetta',
      [22] = "Fratelli d'Schnazy",
      [23] = 'Triads',
    },
    [286] = {
      [0] = 'item:clth_hw23_mask_a1',
      [1] = 'item:clth_hw23_mask_a2',
    },
    [287] = {
      [0] = 'item:clth_hw23_mask_b1',
      [1] = 'item:clth_hw23_mask_b2',
      [2] = 'item:clth_hw23_mask_b3',
    },
    [288] = {
      [4] = 'Angels of Death',
    },
    [290] = 'character:19567',
    [291] = { -- Easter Bunny Head
      [0] = 'item:clth_ea24_berd_a',
      [1] = 'item:clth_ea24_berd_b',
      [2] = 'item:clth_ea24_berd_c',
      [3] = 'item:clth_ea24_berd_d',
      [4] = 'item:clth_ea24_berd_e',
      [5] = 'item:clth_ea24_berd_f',
      [6] = 'item:clth_ea24_berd_g',
      [7] = 'item:clth_ea24_berd_h',
      [8] = 'item:clth_ea24_berd_i',
    },
    [292] = {
      [0] = 'item:clth_tcf_mask_a',
      [1] = 'item:clth_tcf_mask_b',
    },
    [293] = {
      [0] = 'item:clth_frt_mask_a',
      [1] = 'item:clth_mm_mask_c',
      [2] = 'item:clth_rebels_mask_b',
      [3] = 'item:clth_swat_mask_a',
      [4] = 'item:clth_lsf_mask_d',
      [5] = 'item:clth_lsf_mask_e',
      [6] = 'item:clth_frt_mask_b',
      [7] = 'item:clth_tri_mask_a',
      [8] = 'item:clth_ctl_mask_a',
      [9] = 'item:clth_ctl_mask_b',
      [10] = 'item:clth_ctl_mask_c',
      [11] = 'item:clth_tc_mask_a',
      [12] = 'item:clth_vend_mask_b',
      [13] = 'item:clth_saints_mask_a',
      [14] = 'item:clth_saints_mask_b',
    },
    [294] = {
      [0] = '305 Mafia',
      [1] = 'The Unknown',
      [2] = 'Angels of Death',
      [3] = 'Ballas',
      [4] = 'Blacklisted', -- Beechwood Disciples
      [5] = 'Petrovich Cartel',
      [6] = 'Ikonz',
      [7] = 'Lost MC',
      [8] = 'Messina Crime Family',
      [9] = 'Rebels',
      [10] = 'Blacklisted-Rascals',
      [11] = 'Vagos',
      [12] = 'Mutiny Mob',
      [13] = 'Blacklisted', -- Westwoods
      [14] = 'Joes Corner',
      [15] = 'BSG',
      [16] = 'The Chosen Few MC',
      [17] = "Fratelli d'Schnazy",
      [18] = 'OTF',
      [19] = 'Blacklisted', -- Condemed MC
      [20] = 'Scorpions',
      [21] = 'Misfitz',
      [22] = 'item:clth_yok_mask_a',
      [23] = 'item:clth_misfitz_mask_b',
    },
    [295] = 'item:clth_tengu_mask',
    [296] = 'Asian Market',
    [301] = {
      [0] = 'item:clth_vangelico_9a',
      [1] = 'item:clth_vangelico_9b',
      [2] = 'item:clth_vangelico_9c',
      [3] = 'item:clth_vangelico_9d',
    },
  },

  -- UPPR - 03 - Hands
  ['3'] = {

  },

  -- LOWR - 04 - Legs
  ['4'] = {
    [6] = {
      [16] = 'Mikes Sporting Goods',
    },
    [14] = {
      [10] = 'LEO',
      [11] = 'LEO',
    },
    [24] = {
      [7] = "Fratelli d'Schnazy",
    },
    [33] = {
      [1] = 'Petrovich Cartel',
    },
    [55] = {
      [4] = 'El Rancho Ravens',
    },
    [78] = {
      [8] = "Fratelli d'Schnazy",
      [9] = "Fratelli d'Schnazy",
      [10] = "Fratelli d'Schnazy",
    },
    [88] = {
      [24] = 'The Boathouse',
    },
    [120] = 'LSFD', -- Bunker pants
    [122] = {
      [1] = 'Rocky Road Towing',
    },
    [196] = {
      [3] = 'LEO',
    },
    [197] = {
      [5] = 'LEO',
    },
    [199] = {
      [9] = 'LEO',
      [10] = 'LEO',
    },
    [203] = {
      [1] = 'LEO',
      [2] = 'LEO',
    },
    [205] = {
      [2] = 'LEO',
    },
    [206] = {
      [4] = 'LEO',
    },
    [208] = {
      [4] = 'LEO',
      [5] = 'LEO',
    },
    [209] = {
      [4] = 'LEO',
      [5] = 'LEO',
    },
    [210] = {
      [5] = 'LEO',
    },
    [213] = {
      [0] = 'LEO',
    },
    [214] = {
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
    },
    [224] = 'Vanilla Unicorn',
    [226] = {
      [6] = 'LSFD',
      [7] = 'LSFD',
      [8] = 'LSFD',
      [9] = 'LSFD',
      [10] = 'LSFD',
    },
    [227] = 'LSFD',
    [228] = 'LSFD', -- Bunker pants
    [232] = {
      [2] = 'Los Santos Customs',
    },
    [243] = {
      [9] = 'Vanilla Unicorn',
      [10] = 'Vanilla Unicorn',
    },
    [245] = 'Blacklisted-Remove',
    [248] = 'Vanilla Unicorn',
    [249] = {
      [2] = 'Nice Dreams Ice Cream',
    },
    [253] = {
      [0] = 'Blacklisted-RealWorldBrand',
      [1] = 'Blacklisted-RealWorldBrand',
      [3] = 'Blacklisted-RealWorldBrand',
      [4] = 'Blacklisted-RealWorldBrand',
    },
    [255] = 'Blacklisted', -- Beechwood Disciples
    [257] = 'The Boathouse',
    [259] = 'Midnight Club',
    [260] = {
      [8] = 'Yellow Jack',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
    },
    [271] = {
      [13] = 'item:clth_bf23_shorts_2',
    },
    [272] = 'Vanilla Unicorn',
    [273] = {
      [0] = 'Messina Crime Family',
      [1] = 'Messina Crime Family',
      [2] = 'The Belmont',
      [3] = 'The Belmont',
      [4] = 'Mutiny Mob',
    },
    [295] = 'character:19567',
    [296] = 'OTF',
    [297] = {
      [0] = 'Messina Crime Family',
      [1] = 'Messina Crime Family',
      [2] = 'Messina Crime Family',
    },
    [299] = {
      [0] = 'Vynchenzo\'s Distillery',
    },
    [318] = 'Go Truck Yourself',
    [320] = 'Scorpions',
    [321] = 'Petrovich Cartel',
    [338] = 'Fort Zancudo',
    [339] = 'LEO',
  },

  -- HAND - 05 -  Bags
  ['5'] = {
    [117] = 'LEO',
    [118] = 'LEO',
    [119] = 'LEO',
    [120] = 'LEO',
    [121] = 'LEO',
    [122] = 'LEO',
    [123] = 'LEO',
    [124] = 'LEO',
    [125] = 'LEO',
    [126] = 'LEO', -- LSPD kingston + badge
    [128] = 'LEO',
    [129] = 'LEO',
    [130] = 'LEO',
    [131] = 'LEO',
    [132] = 'LEO',
    [133] = 'LEO',
    [134] = 'LEO',
    [135] = 'LEO',
    [136] = 'LEO',
    [137] = 'LEO',
    [138] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'character:40222', -- Andersen
      [11] = 'character:151', -- Holliday
      [12] = 'character:46106', -- Novak
      [13] = 'character:32434', -- Denver
      [14] = 'character:380', -- Aloysius
      [15] = 'character:86359', -- Swamp
      [16] = 'character:81519', -- Hurtz
      [17] = 'character:79334', -- Turner
      [18] = 'character:23237', -- Martin
    },
    [139] = 'LEO',
    [140] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'character:69707', -- Mori
      [11] = 'character:7273', -- Thomson
      [12] = 'character:31046', -- Smith
      [13] = 'character:34895', -- Hiker
      [14] = 'character:80141', -- R. Payne
      [15] = 'character:46430', -- Roach
      [16] = 'character:46796', -- Hardy
      [17] = 'character:14081', -- Remington
      [18] = 'character:23044', -- Habibi
    }, -- BCSO badge (heart)
    [141] = 'LEO',
    [142] = 'LEO',
    [143] = 'LEO',
    [144] = 'LEO',
    [145] = 'LEO',
    [149] = 'LEO',
    [150] = 'LSFD',
    [151] = 'LSFD',
    [152] = 'LEO',
    [155] = 'LEO',
    [160] = 'LSFD', -- LSFD Badge
    [206] = 'LEO',
    [207] = {
      [0] = 'LEO',
      [1] = 'character:36305',
      [2] = 'character:69448',
      [3] = 'character:43826',
      [4] = 'character:26366',
      [5] = 'character:19',
      [6] = 'character:13894',
      [7] = 'character:250',
      [8] = 'character:33729',
      [9] = 'character:67158',
      [10] = 'character:30955',
      [11] = 'character:92580',
    },
    [209] = {
      [0] = 'LEO',
      [1] = 'character:36305',
      [2] = 'character:69448',
      [3] = 'character:43826',
      [4] = 'character:250',
      [5] = 'character:33729',
      [6] = 'character:67158',
      [7] = 'character:30955',
      [8] = 'character:92580',
    },
    [213] = 'LSFD',
    [214] = 'LSFD',
    [215] = 'LSFD',
    [216] = 'Blacklisted-Remove',
    [218] = 'LSFD',
    [219] = 'LSFD',
    [220] = 'LSFD',
    [221] = 'LSFD',
    [222] = 'LSFD',
    [223] = 'LSFD',
    [224] = 'LSFD',
    [225] = 'LSFD',
    [236] = 'LEO',
    [237] = 'LEO',
    [238] = 'LEO',
  },

  -- FEET - 06 - Shoes
  ['6'] = {
    [153] = 'Blacklisted',
    [155] = 'Blacklisted',
    [159] = 'FixTmrwBlacklistToday',
    [162] = 'Blacklisted',
    [167] = 'Blacklisted',
    [160] = 'Blacklisted', -- Beechwood Disciples
    [173] = {
      [2] = 'item:clth_cm25_feet_a',
    },
    [176] = {
      [8] = 'item:clth_cm25_feet_b',
    },
    [177] = {
      [0] = 'item:clth_ea25_feet_a',
      [1] = 'item:clth_ea25_feet_b',
      [2] = 'item:clth_ea25_feet_c',
      [3] = 'item:clth_ea25_feet_d',
      [4] = 'item:clth_ea25_feet_e',
      [5] = 'item:clth_ea25_feet_f',
      [6] = 'item:clth_ea25_feet_g',
      [7] = 'item:clth_ea25_feet_h',
      [8] = 'item:clth_ea25_feet_i',
      [9] = 'item:clth_ea25_feet_j',
    },
  },

  -- TEEF - 07 - Neck Accessories
  ['7'] = {
    [125] = 'LEO',
    [127] = 'LSFD',
    [128] = 'LEO',
    [190] = {
      [2] = 'LSFD',
      [3] = 'LSFD',
      [4] = 'LSFD',
    },
    [193] = 'LSFD',
    [208] = {
      [4] = 'LEO',
    },
    [209] = {
      [4] = 'LEO',
    },
    [214] = 'LSFD',
    [232] = {
      [2] = 'item:clth_lanyard_b1',
      [3] = 'item:clth_lanyard_wn',
      [4] = 'item:clth_lanyard_bacv',
      [5] = 'item:clth_lanyard_bacm',
    },
    [237] = 'item:clth_chain_default',
    [238] = 'item:clth_chain_305',
    [239] = 'Blacklisted', -- Old OTF chain
    [240] = 'item:clth_chain_rascal',
    [241] = 'item:clth_chain_crushedcar',
    [242] = 'item:clth_chain_bd',
    [243] = 'item:cp23q1_pendant',
    [244] = 'item:clth_chain_aces',
    [245] = 'item:clth_chain_mcf',
    [246] = 'item:clth_chain_ballas',
    [247] = 'item:clth_chain_vagos',
    [249] = 'item:clth_chain_fdf',
    [250] = 'item:clth_chain_cartel',
    [251] = 'item:clth_chain_bdotm',
    [252] = 'item:clth_chain_04',
    [253] = 'item:clth_chain_misfits',
    [254] = 'item:clth_chain_mm',
    [256] = 'item:clth_chain_rebels',
    [258] = 'item:clth_chain_lsf',
    [259] = 'item:clth_chain_ww',
    [260] = {
      [0] = 'item:clth_chain_scorp_a',
      [1] = 'item:clth_chain_mil_a',
    },
    [261] = 'item:clth_c24_shark',
    [262] = 'item:clth_chain_scorp_b',
    [263] = 'item:clth_chain_rascal_b',
    [264] = {
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'DOJ',
      [14] = 'LEO',
      [15] = 'LSFD',
      [16] = 'LEO',
      [17] = 'LEO',
      [18] = 'LEO',
      [19] = 'LEO',
      [20] = 'El Rancho Ravens',
      [21] = 'Forum Family',
      [22] = 'Big Hawk Taxi',
      [23] = 'Lost MC',
      [24] = 'OTF',
      [25] = 'Messina Crime Family',
    },
    [265] = {
      [0] = 'Misfitz',
      [1] = 'Mutiny Mob',
      [2] = 'Ikonz',
      [3] = 'Scorpions',
      [4] = 'The Chosen Few Mc',
      [5] = 'Triads',
      [6] = 'Vagos',
      [7] = '305 Mafia',
      [8] = 'The Unknown',
      [9] = 'Angels of Death',
      [10] = 'Ballas',
      [11] = 'BSG',
      [12] = 'Petrovich Cartel',
      [13] = 'Joes Corner',
      [14] = 'Blacklisted', -- Condemed MC
      [15] = 'The Continental',
      [16] = 'Mongrels',
      [17] = 'Rebels',
      [18] = "Fratelli d'Schnazy",
      [19] = 'Vendetta',
      [20] = 'Blacklisted', -- Westwoods
      [21] = 'Asian Market',
    },
    [272] = 'item:clth_chain_fdf_b',
    [273] = 'item:clth_chain_jc',
    [275] = 'item:clth_chain_mmotm',
    [277] = 'item:clth_chain_ctl',
    [279] = 'item:clth_chain_reapers',
    [281] = 'item:clth_vangelico_1a',
    [282] = {
      [0] = 'item:clth_vangelico_2a',
      [1] = 'item:clth_vangelico_2b',
      [2] = 'item:clth_vangelico_2c',
    },
    [283] = {
      [0] = 'item:clth_vangelico_4a',
      [1] = 'item:clth_vangelico_4b',
    },
    [284] = 'item:clth_vangelico_10a',
    [285] = 'item:clth_vangelico_11a',
    [286] = 'item:clth_chain_collective',
    [287] = 'item:clth_chain_gh_a',
  },

  -- ACCS - 08 - Shirt
  ['8'] = {
    [58] = 'LEO',
    [123] = 'LSFD',
    [151] = 'LSFD',
    [163] = 'Blacklisted-RealWorldBrand',
    [172] = 'Blacklisted-RealWorldBrand',
    [207] = 'LEO',
    [208] = 'LEO',
    [209] = 'LEO',
    [210] = 'LEO',
    [211] = 'LEO',
    [212] = 'LEO',
    [213] = 'LEO',
    [214] = 'LEO',
    [215] = 'LEO',
    [216] = 'LEO',
    [217] = 'LEO',
    [225] = 'LEO',
    [226] = 'LEO',
    [227] = 'LEO',
    [228] = 'LEO',
    [229] = 'LEO',
    [231] = 'LEO',
    [232] = 'LEO',
    [233] = 'LEO',
    [236] = 'LEO',
    [237] = 'LEO',
    [238] = 'LEO',
    [239] = 'LEO',
    [240] = 'LEO',
    [241] = 'LEO',
    [242] = 'LEO',
    [245] = 'LEO',
    [246] = 'LEO',
    [247] = 'LEO',
    [248] = 'LEO',
    [249] = 'LEO',
    [250] = 'LEO',
    [251] = {
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
    },
    [252] = {
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
    },
    [253] = 'LEO',
    [254] = 'LEO',
    [255] = 'LEO',
    [259] = 'LEO',
    [260] = {
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
    },
    [261] = {
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
    },
    [262] = 'LEO',
    [263] = 'LEO',
    [264] = 'LEO',
    [265] = 'LEO',
    [266] = 'LEO',
    [267] = 'LEO',
    [269] = 'LSFD',
    [272] = 'LSFD',
    [276] = 'LSFD',
    [277] = {
      [4] = 'Blacklisted-Badfest', -- Male Shirt, shirt_1_a.webp
    },
    [280] = 'HIGHPOLY-REMOVED',
    [282] = {
      [8] = 'OTF',
      [9] = 'OTF',
      [10] = '305 Mafia',
      [11] = 'Petrovich Cartel',
      [12] = 'Ikonz',
      [13] = 'Vagos',
      [14] = 'Blacklisted', -- Beechwood Disciples
      [15] = 'Messina Crime Family',
      [16] = 'Blacklisted', -- Westwoods
      [17] = 'Angels of Death',
      [18] = 'Blacklisted', -- Westwoods
      [19] = 'Forum Family',
      [20] = 'Ballas',
      [21] = 'The Unknown',
      [22] = 'Blacklisted', -- Old Streetside
      [23] = 'Angels of Death',
      [24] = 'Rebels',
    },
    [287] = 'Blacklisted-Remove',
    [300] = 'Blacklisted-Remove',
    [305] = {
      [0] = 'Forum Family',
      [1] = 'Messina Crime Family',
      [2] = '305 Mafia',
      [3] = 'Ballas',
    },
    [308] = {
      [0] = 'Forum Family',
      [1] = 'Ballas',
      [2] = '305 Mafia',
      [3] = 'Blacklist', -- OTF headRag needs retextured
      [4] = 'Blacklisted-Rascals',
      [5] = 'Street Side',
      [6] = 'Blacklisted', -- Beechwood Disciples
      [7] = 'Ikonz',
    },
    [313] = 'Blacklisted-RealWorldBrand',
    [316] = {
      [0] = 'item:clth_1pct_ring',
      [1] = 'item:clth_ring_aod',
      [2] = 'item:clth_ring_lost',
      [3] = 'item:clth_ring_tcf',
      [4] = 'item:clth_ring_cmc',
      [5] = 'item:clth_ring_mmc',
    },
    [323] = {
      [0] = 'item:clth_crips_flag_a',
      [1] = 'item:clth_crips_flag_b',
    },
  },

  -- TASK - 09 - Armor
  ['9'] = {
    [29] = 'Empty',
    [30] = 'Empty',
    [31] = 'Empty',
    [32] = 'Empty',
    [33] = 'Empty',
    [34] = 'Empty',
    [35] = 'Empty',
    [36] = 'Empty',
    [37] = 'Empty',
    [38] = 'Empty',
    [39] = 'Empty',
    [40] = 'Empty',
    [41] = 'Empty',
    [42] = 'Empty',
    [43] = 'Empty',
    [44] = 'Empty',
    [45] = 'Empty',
    [46] = 'Empty',
    [47] = 'Empty',
    [48] = 'Empty',
    [49] = 'Empty',
    [50] = 'Empty',
    [51] = 'Empty',
    [52] = 'Empty',
    [53] = 'LEO',
    [54] = 'LEO',
    [55] = 'LEO',
    [62] = 'LEO',
    [63] = 'LEO',
    [64] = 'LEO',
    [65] = 'LEO',
    [66] = 'LEO',
    [67] = 'LEO',
    [68] = 'LEO',
    [69] = 'LEO',
    [70] = 'LEO',
    [71] = 'LEO',
    [72] = 'LEO',
    [75] = 'LEO',
    [76] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [5] = 'Rocky Road Towing',
      [6] = 'LEO',
      [7] = 'LEO',
    },
    [77] = 'LEO',
    [78] = 'LEO',
    [79] = 'LEO',
    [80] = 'LEO',
    [81] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LSFD',
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
      [18] = 'LEO',
    },
    [82] = {
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'LEO',
    },
    [83] = 'LEO',
    [84] = 'LEO',
    [85] = 'LEO',
    [86] = 'LEO',
    [87] = 'LEO',
    [88] = 'LEO',
    [89] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'FIB',
      [4] = 'LEO',
      [5] = 'FIB',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'FIB',
      [9] = 'Fort Zancudo',
    },
    [90] = {
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
    },
    [92] = 'LEO',
    [93] = 'LEO',
    [94] = 'LEO',
    [96] = 'LEO',
    [97] = 'LEO',
    [98] = 'LEO',
    [99] = 'LEO',
    [100] = 'LEO',
    [101] = 'LEO',
    [102] = 'LEO',
    [103] = {
      [2] = 'LSFD',
      [3] = 'LSFD',
      [4] = 'LSFD',
    },
    [105] = {
      [5] = 'Dump and Pump Septic',
      [6] = 'Blacklisted', -- Thomson Scrapyard
    },
    [107] = {
      [5] = 'Blacklisted',
    },
    [109] = 'Broken',
    [110] = 'LSFD',
    [114] = 'Broken',
    [118] = 'LEO',
    [121] = 'HIGHPOLY-REMOVED',
    [122] = {
      [0] = 'Blacklisted', -- Former Sinister Sons
      [1] = 'Blacklisted', -- Former Sinister Sons
      [2] = 'Blacklisted', -- Former Sinister Sons
      [3] = 'Blacklisted', -- Former Sinister Sons
      [4] = 'Blacklisted', -- Former Sinister Sons
      [5] = 'Blacklisted', -- Former Sinister Sons
      [6] = 'Blacklisted', -- Former Sinister Sons
      [7] = 'Blacklisted', -- Former Sinister Sons
    },
    [123] = {
      [0] = 'Blacklisted', -- Former Sinister Sons
      [1] = 'Blacklisted', -- Former Sinister Sons
    },
    [124] = 'Angels of Death',
    [125] = 'Blacklisted', -- Former Sinister Sons
    [126] = 'Blacklisted', -- Old FAMC cuts
    [127] = 'Lost MC',
    [128] = {
      [0] = 'Lost MC',
      [1] = 'Lost MC',
      [2] = 'Lost MC',
    },
    [129] = 'LEO',
    [130] = 'Blacklisted', --gutter tribe
    [131] = 'Blacklisted', -- Condemed MC
    [133] = 'LEO',
    [134] = 'LEO',
    [136] = 'LEO',
    [137] = {
      [1] = 'Blacklisted', -- Beechwood Disciples
      [2] = 'Blacklisted', -- Alamo Sea Pirates
      [3] = 'Blacklisted', -- Alamo Sea Pirates
    },
    [138] = {
      [0] = 'Vagos',
      [1] = 'Petrovich Cartel',
      [2] = 'OTF',
      [3] = 'Blacklisted-Rascals',
      [4] = 'Ikonz',
      [5] = 'Angels of Death',
      [6] = '305 Mafia',
      [7] = 'Mutiny Mob',
      [8] = 'The Unknown',
      [9] = 'Rebels',
      [10] = 'Misfitz',
      [11] = 'Ballas',
      [12] = 'Lost MC',
      [13] = 'Blacklisted', -- Westwoods
      [14] = 'Blacklisted', -- Los Miradz
      [15] = 'Vendetta',
      [16] = 'BSG',
      [17] = 'Yokai',
      [18] = 'The Chosen Few Mc',
      [19] = 'Blacklisted', -- Condemed MC
      [20] = 'The Continental',
      [21] = 'Joes Corner',
      [22] = 'Mongrels',
    },
    [139] = 'LEO',
    [140] = 'LEO',
    [141] = 'LEO',
    [142] = 'LEO',
    [143] = 'LEO',
    [144] = 'LEO',
    [145] = 'LEO',
    [146] = 'LEO',
    [147] = 'LEO',
    [148] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO TMU',
    },
    [149] = 'LEO',
    [150] = 'LEO',
    [152] = {
      [0] = 'Vagos',
      [1] = 'Blacklisted', -- Beechwood Disciples
    },
    [153] = {
      [1] = 'Blacklisted', -- Beechwood Disciples
    },
    [154] = {
      [14] = 'Scorpions',
      [15] = 'Scorpions',
      [16] = 'Scorpions',
    },
    [158] = {
      [6] = 'Asian Market',
    },
    [159] = 'The Chosen Few Mc',
    [160] = 'The Chosen Few Mc',
    [161] = 'Blacklisted', -- Condemed MC
    [162] = 'Mongrels',
    [165] = 'The Chosen Few Mc',
    [166] = 'LEO',
  },

  -- DECL - 10 - Decals
  ['10'] = {
    [46] = {
      [0] = 'La Fenice Saints',
    },
    [70] = 'LEO',
    [72] = 'LEO',
    [77] = 'LEO',
    [152] = 'LEO',
    [156] = 'LEO',
    [157] = 'LEO',
    [165] = 'LSFD',
    [166] = 'LSFD',
    [167] = 'LSFD',
    [170] = 'LEO',
    [196] = 'LSFD',
    [197] = 'LSFD',
    [203] = 'HIGHPOLY-REMOVED',
    [205] = 'HIGHPOLY-REMOVED',
    [234] = 'HIGHPOLY-REMOVED',
    [238] = {
      [8] = 'The Unknown',
      [11] = 'Ballas',
      [14] = 'Blacklisted', -- Beechwood Disciples
      [15] = 'Blacklisted-Rascals',
      [16] = '305 Mafia',
      [17] = 'Petrovich Cartel',
      [18] = 'Ikonz',
      [19] = 'Vagos',
      [20] = 'Blacklisted', -- Beechwood Disciples
      [21] = 'Messina Crime Family',
      [22] = 'Rebels',
      [23] = 'Angels of Death',
      [24] = 'Misfitz',
      [25] = 'Forum Family',
    },
    [241] = 'LSFD',
    [242] = 'LSFD',
    [243] = 'LSFD',
    [244] = 'LSFD',
    [245] = 'LSFD',
    [246] = 'LSFD',
    [247] = 'LSFD',
    [248] = 'LSFD',
    [252] = {
      [0] = 'item:clth_ring_m_a',
      [1] = 'item:clth_ring_m_b',
      [2] = 'item:clth_ring_m_c',
      [3] = 'item:clth_ring_m_d',
      [4] = 'item:clth_ring_m_e',
      [5] = 'item:clth_ring_m_f',
      [6] = 'item:clth_ring_m_g',
      [7] = 'item:clth_ring_m_h',
      [8] = 'item:clth_ring_m_i',
      [9] = 'item:clth_ring_m_j',
      [10] = 'item:clth_ring_m_k',
    },
    [253] = {
      [0] = 'item:clth_ring_f_a',
      [1] = 'item:clth_ring_f_b',
      [2] = 'item:clth_ring_f_c',
      [3] = 'item:clth_ring_f_d',
      [4] = 'item:clth_ring_f_e',
      [5] = 'item:clth_ring_f_f',
      [6] = 'item:clth_ring_f_g',
    },
    [254] = {
      [0] = 'BSG',
      [1] = 'BSG',
      [2] = 'Blacklisted', -- Westwoods
      [3] = 'Blacklisted', -- Condemed MC
      [4] = 'Blacklisted', -- Condemed MC
      [5] = 'Mongrels',
      [6] = 'Vendetta',
    },
    [255] = 'The Chosen Few Mc',
    [256] = {
      [0] = 'Joes Corner',
    },
    [257] = 'item:clth_ring_cartel',
    [258] = 'LEO',
    [259] = 'LEO',
  },

  -- JBIB - 11 - Jacket
  ['11'] = {
   [0] = {
      [15] = 'El Rancho Ravens',
   },
   [11] = {
      [2] = 'blacklist',-- Broken checkerd
      [3] = 'blacklist',-- Broken checkerd
      [4] = 'blacklist',-- Broken checkerd
      [5] = 'blacklist',-- Broken checkerd
      [6] = 'blacklist',-- Broken checkerd
      [8] = 'blacklist',-- Broken checkerd
      [9] = 'blacklist',-- Broken checkerd
      [10] = 'blacklist',-- Broken checkerd
      [11] = 'blacklist',-- Broken checkerd
      [12] = 'blacklist',-- Broken checkerd
      [13] = 'blacklist',-- Broken checkerd
      [15] = 'blacklist',-- Broken checkerd
    },
    [17] ={
      [6] = 'item:clth_bf24_tank_a',
      [7] = 'item:clth_bf24_tank_b',
      [8] = 'item:clth_bf24_tank_c',
      [9] = 'item:clth_bf24_tank_d',
      [10] = 'item:clth_bf24_tank_e',
      [11] = 'item:clth_bf24_tank_f',
      [12] = 'item:clth_bf24_tank_g',
      [13] = 'Blath Alainn Charity',
    },
    [21] = {
      [4] = 'Galaxy',
    },
    [38] = {
      [5] = 'Chihuahua Hotdogs',
    },
    [40] = {
      [2] = 'Vendetta',
    },
    [55] = 'LEO',
    [89] = {
      [4] = 'item:clth_xm23_jbib_a',
      [5] = 'item:clth_xm23_jbib_b',
      [6] = 'item:clth_xm23_jbib_c',
      [7] = 'item:clth_xm23_jbib_d',
    },
    [107] = {
      [5] = 'Triads',
    },
    [113] = {
      [4] = "Fratelli d'Schnazy",
      [5] = "Fratelli d'Schnazy",
      [6] = "Fratelli d'Schnazy",
    },
    [121] = {
      [12] = 'The Kush Korner',
    },
    [146] = {
      [9] = "Boo's Catfe",
    },
    [170] = {
      [4] = 'item:clth_bf24_denim',
    },
    [182] = {
      [2] = "Boo's Catfe",
    },
    [210] = {
      [24] = 'Midnight Club',
      [25] = 'Midnight Club',
    },
    [211] = {
      [24] = 'Midnight Club',
      [25] = 'Midnight Club',
    },
    [212] = {
      [24] = 'Midnight Club',
      [25] = 'Midnight Club',
    },
    [229] = {
      [12] = 'Midnight Club',
      [13] = 'Midnight Club',
    },
    [230] = {
      [12] = 'Midnight Club',
      [13] = 'Midnight Club',
      [14] = 'Gearheads',
    },
    [238] = {
      [6] = 'Blacklisted', -- Flywheels
    },
    [241] = {
      [6] = 'Blath Alainn Charity',
      [7] = 'Dynasty 8',
    },
    [282] = {
      [16] = 'Chihuahua Hotdogs',
    },
    [294] = {
      [10] = 'Misfitz',
    },
    [314] = 'LSFD', --Fire Coat buttoned Neck
    [315] = 'LSFD',
    [321] = {
      [1] = 'Petrovich Cartel',
      [2] = 'blacklisted',
      [4] = 'blacklisted',
    },
    [365] = 'Blacklisted', -- Lost MC jacket that is low quality and unused
    [366] = 'Blacklisted', -- Lost MC jacket that is low quality and unused
    [367] = 'Blacklisted', -- Lost MC jacket that is low quality and unused
    [368] = 'Blacklisted', -- Lost MC jacket that is low quality and unused
    [382] = {
      [8] = 'Saints',
    },
    [455] = {
      [3] = 'Petrovich Cartel',
    },
    [477] = {
      [1] = 'item:clth_xm23_jbib_e',
    },
    [524] = 'LEO',
    [526] = 'LEO',
    [532] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LSFD',
      [4] = 'LSFD',
      [5] = 'LSFD',
      [6] = 'LSFD',
      [7] = 'LSFD',
      [8] = 'LSFD',
      [9] = 'LSFD',
      [10] = 'LSFD',
      [11] = 'LSFD',
      [12] = 'LSFD',
      [13] = 'LSFD',
      [14] = 'LSFD',
      [15] = 'LSFD',
      [16] = 'LSFD',
    },
    [533] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LSFD',
      [4] = 'LSFD',
      [5] = 'LSFD',
      [6] = 'LSFD',
      [7] = 'LSFD',
      [8] = 'LSFD',
      [9] = 'LSFD',
      [10] = 'LSFD',
      [11] = 'LSFD',
      [12] = 'LSFD',
      [13] = 'LSFD',
      [14] = 'LSFD',
      [15] = 'LSFD',
      [16] = 'LSFD',
      [17] = 'LSFD',
      [18] = 'LSFD',
      [19] = 'LSFD',
      [20] = 'LSFD',
      [21] = 'LSFD',
      [22] = 'LSFD',
      [23] = 'LSFD',
      [24] = 'LSFD',
      [25] = 'LSFD',
    },
    [535] = 'LEO',
    [536] = 'LEO',
    [537] = 'LEO',
    [538] = 'LEO',
    [540] = 'LEO',
    [541] = 'LEO', --Green Unzipped Sheriff Jacket
    [542] = 'LEO',
    [543] = 'LEO',
    [544] = 'LEO',
    [545] = {
      [1] = 'BROKEN', --Check
      [2] = 'BROKEN', --Check
    },
    [547] = 'LEO',
    [548] = 'LEO',
    [550] = 'LEO',
    [551] = 'LEO',
    [552] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
    },
    [554] = 'LEO', --Blue Rockford hills Police Puffy jacket
    [555] = 'LEO',
    [556] = 'LEO',
    [557] = 'LEO',
    [558] = 'LEO',
    [559] = 'LEO',
    [560] = 'LEO',
    [561] = 'LEO',
    [562] = 'LEO', --- A.Jaspers Blaine County short Sleeve
    [563] = 'LEO',
    [564] = {
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
      [20] = 'LEO',
      [21] = 'LEO',
      [22] = 'LEO',
      [23] = 'LEO',
    },
    [565] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'BROKEN',
      [17] = 'BROKEN',
      [18] = 'BROKEN',
      [19] = 'BROKEN',
      [20] = 'BROKEN',
      [21] = 'BROKEN',
      [22] = 'BROKEN',
      [23] = 'BROKEN',
      [24] = 'BROKEN',
    },
    [566] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'BROKEN',
      [17] = 'BROKEN',
      [18] = 'BROKEN',
      [19] = 'BROKEN',
      [20] = 'BROKEN',
      [21] = 'BROKEN',
      [22] = 'BROKEN',
      [23] = 'BROKEN',
    },
    [567] = 'LEO', --Police High Vis
    [568] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
    },
    [569] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
      [18] = 'LEO',
      [19] = 'LEO',
      [20] = 'LEO TMU',
    },
    [570] = 'LEO',
    [571] = 'LEO',
    [572] = 'LEO',
    [573] = 'LEO',
    [574] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO|Ranger',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
      [18] = 'LEO',
      [19] = 'LEO',
      [20] = 'LEO',
      [21] = 'LEO',
      [22] = 'LEO',
      [23] = 'LEO',
      [24] = 'LEO',
      [25] = 'LEO',
    },
    [575] = 'LEO',
    [576] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO|Ranger',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
    },
    [577] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO|Ranger',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
      [18] = 'LEO',
      [19] = 'LEO',
      [20] = 'LEO',
      [21] = 'LEO',
      [22] = 'LEO',
      [23] = 'LEO',
      [24] = 'LEO',
      [25] = 'LEO',
    },
    [578] = 'LEO', -- Blaine County Padded arms
    [579] = 'LEO',
    [580] = {
      [14] = 'BROKEN',
      [15] = 'BROKEN',
      [16] = 'BROKEN',
      [17] = 'LEO',
      [18] = 'LEO',
      [19] = 'LEO',
      [20] = 'LEO',
    },
    [581] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'BROKEN',
      [9] = 'BROKEN',
      [10] = 'BROKEN',
      [11] = 'BROKEN',
      [12] = 'BROKEN',
      [13] = 'BROKEN',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
      [18] = 'LEO',
      [19] = 'BROKEN',
      [20] = 'BROKEN',
      [21] = 'BROKEN',
      [22] = 'BROKEN',
      [23] = 'BROKEN',
    },
    [582] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [9] = 'BROKEN',
    },
    [583] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'BROKEN',
    },
    [584] = 'LEO', -- Police Black n White Long Sleeve
    [585] = 'LEO',
    [588] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
      [18] = 'LSFD',
      [19] = 'LSFD',
      [20] = 'LSFD',
      [21] = 'LSFD',
      [22] = 'LSFD',
      [23] = 'LSFD',
      [24] = 'LSFD',
      [25] = 'LSFD',
    },
    [589] = {     -- FIB short Sleeve
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
      [19] = 'Boo\'s Catfe',
      [20] = '305 Mafia',
      [21] = 'AlphaMail',
      [22] = 'AlphaMail',
      [23] = 'AlphaMail',
      [24] = 'BROKEN',
      [25] = 'BROKEN',
    },
    [591] = {
      [0] = 'LSFD',
      [1] = 'LSFD',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LSFD',
    },
    [592] = 'LSFD',
    [595] = {
      [2] = 'LSFD',
      [3] = 'LSFD',
      [4] = 'LSFD',
      [9] = 'LSFD',
      [10] = 'LSFD',
      [11] = 'LSFD',
      [12] = 'Homebrew Cafe',
      [13] = 'Dump and Pump Septic',
      [14] = 'Cool Beans',
      [15] = 'LSFD',
      [16] = 'LSFD',
      [17] = 'LSFD',
      [18] = 'LSFD',
      [19] = 'LSFD',
      [20] = 'LSFD',
      [21] = 'LSFD',
      [22] = 'LSFD',
      [23] = 'LSFD',
      [24] = 'LSFD',
      [25] = 'LSFD',
    },
    [597] = 'LEO',
    [598] = {
      [4] = 'LSFD',
      [5] = 'LSFD',
      [6] = 'LSFD',
      [7] = 'LSFD',
      [8] = 'LSFD',
      [9] = 'LSFD',
      [10] = 'LSFD',
      [11] = 'LSFD',
      [12] = 'LSFD',
      [13] = 'LSFD',
      [14] = 'LSFD',
      [15] = 'LSFD',
      [16] = 'LSFD',
      [17] = 'LSFD',
      [18] = 'LSFD',
      [19] = 'LSFD',
      [20] = 'LSFD',
      [21] = 'LSFD',
      [22] = 'LSFD',
      [23] = 'LSFD',
      [24] = 'LSFD',
      [25] = 'LSFD',
    },
    [599] = {
      [0] = 'LSFD',
      [1] = 'LSFD',
      [2] = 'LSFD',
      [3] = 'LSFD',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
    },
    [600] = 'LSFD',
    [601] = {     -- Tan BCSO with PARKS Name Plate
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [5] = 'BROKEN', -- texture Bleed Thru
      [6] = 'BROKEN',
      [7] = 'BROKEN',
      [8] = 'BROKEN',
      [9] = 'BROKEN',
      [10] = 'BROKEN',
      [11] = 'BROKEN',
      [12] = 'BROKEN',
      [13] = 'BROKEN',
    },
    [602] = {
      [4] = 'staff', -- Bobcat
      [5] = 'LEO',
      [6] = 'LSFD',
      [7] = 'LSFD',
      [13] = 'staff', -- Bobcat
      [16] = 'BROKEN',
      [17] = 'BROKEN',
      [18] = 'BROKEN',
      [19] = 'BROKEN',
      [20] = 'BROKEN',
      [21] = 'BROKEN',
      [22] = 'BROKEN',
      [23] = 'BROKEN',
    },
    [603] = {
      [4] = 'staff', -- Bobcat
      [5] = 'LEO',
      [6] = 'LSFD',
      [7] = 'LSFD',
      [13] = 'staff', -- Bobcat
      [16] = 'BROKEN',
      [17] = 'BROKEN',
      [18] = 'BROKEN',
      [19] = 'BROKEN',
      [20] = 'BROKEN',
      [21] = 'BROKEN',
      [22] = 'BROKEN',
      [23] = 'BROKEN',
    },
    [604] = {
      [4] = 'staff', -- Bobcat
      [5] = 'LEO',
      [6] = 'LSFD',
      [7] = 'LSFD',
      [11] = 'staff', -- Bobcat
      [14] = 'LSFD',
      [15] = 'BROKEN',
      [16] = 'BROKEN',
      [17] = 'BROKEN',
      [18] = 'BROKEN',
      [19] = 'BROKEN',
      [20] = 'BROKEN',
      [21] = 'BROKEN',
      [22] = 'BROKEN',
      [23] = 'BROKEN',
    },
    [605] = {
      [2] = 'LEO',
      [3] = 'LSFD',
    },
    [606] = {
      [2] = 'LEO',
      [3] = 'LSFD',
    },
    [607] = 'LSFD', -- Fire Jacket
    [608] = 'LSFD',
    [609] = 'LSFD',
    [612] = 'LSFD',
    [613] = 'LSFD',
    [614] = 'LSFD',
    [615] = {
      [0] = 'LSFD',
      [1] = 'LSFD',
      [2] = 'LSFD',
      [3] = 'LSFD',
      [4] = 'LSFD',
      [5] = 'LSFD',
      [6] = 'LSFD',
      [7] = 'LSFD',
      [9] = 'staff', -- Bobcat
      [10] = 'staff', -- Bobcat
    },
    [616] = {
      [0] = 'LEO',
      [2] = 'LSFD',
      [3] = 'LSFD',
      [4] = 'LSFD',
      [5] = 'LSFD',
      [6] = 'LEO',
    },
    [617] = 'LSFD',
    [618] = {
      [0] = 'LSFD',
      [1] = 'LSFD',
      [2] = 'LSFD',
      [3] = 'LSFD',
      [4] = 'LSFD',
      [5] = 'Hayes Auto',
      [7] = 'Hayes Auto',
      [8] = 'Vanilla Unicorn',
      [9] = 'Vanilla Unicorn',
      [10] = 'Vanilla Unicorn',
      [11] = 'Dynasty 8',
      [13] = 'Burgershot',
      [14] = 'Burgershot',
      [15] = 'Chihuahua Hotdogs',
      [16] = 'Mikes Sporting Goods',
      [17] = 'Blacklisted', -- Flywheels
      [18] = 'Los Santos Customs',
      [19] = 'LSFD',
      [20] = 'LSFD',
      [21] = 'LSFD',
      [22] = 'LSFD',
      [23] = 'LSFD',
      [24] = 'LSFD',
      [25] = 'LSFD',
    },
    [620] = {
      [0] = 'The Unknown',
      [1] = 'Forum Family',
      [2] = 'Vagos',
      [3] = '305 Mafia',
      [7] = 'Blacklisted', -- Beechwood Disciples
      [8] = 'Delta Kappa Nu',
    },
    [621] = {
      [0] = 'The Unknown',
      [1] = 'Blacklisted', -- Beechwood Disciples
      [2] = 'Blacklisted', -- Beechwood Disciples
      [3] = 'Blacklisted', -- Beechwood Disciples
      [4] = 'Blacklisted', -- Beechwood Disciples
      [5] = 'Blacklisted', -- Beechwood Disciples
      [6] = 'Blacklisted', -- Beechwood Disciples
      [7] = '305 Mafia',
      [8] = 'PDM Auto'
    },
    [624] = {
      [1] = 'Blacklisted',
      [2] = 'Los Santos Customs',
      [3] = 'Nice Dreams Ice Cream',
    },
    [626] = {
      [10] = 'LEO',
    },
    [632] = {
      [0] = 'Blacklisted', -- Branded
      [1] = 'Blacklisted', -- Branded
      [2] = 'Aztecas',
      [3] = 'Forum Family',
      [4] = 'Blacklisted', -- Branded
      [5] = 'Blacklisted', -- Branded
      [6] = 'Blacklisted', -- Branded
      [7] = 'Blacklisted', -- Branded
      [8] = 'Blacklisted', -- Branded
      [9] = 'Blacklisted', -- Branded
      [10] = 'Blacklisted', -- Branded
      [11] = 'Blacklisted', -- Branded
      [12] = 'Blacklisted', -- Branded
      [13] = 'Vagos',
      [14] = 'Blacklisted', -- Branded
      [15] = 'Blacklisted', -- Branded
      [16] = 'Blacklisted', -- Branded
      [17] = 'Blacklisted', -- Branded
      [18] = 'Blacklisted', -- Branded
    },
    [633] = {
      [18] = 'Cobras',
    },
    [634] = 'Blacklisted-RealWorldBrand',
    [640] = 'Blacklisted-RealWorldBrand',
    [641] = 'Blacklisted-RealWorldBrand',
    [642] = 'Los Santos Customs',
    [646] = 'Blacklisted-RealWorldBrand',
    [649] = {
      [12] = 'Blacklisted', -- Old OTF Clothing
      [14] = 'Angels of Death',
      [15] = 'Blacklisted-Rascals',
      [17] = 'Blacklisted-Rascals',
      [18] = 'Blacklisted', -- Beechwood Disciples
      [19] = 'Blacklisted', -- Beechwood Disciples
      [20] = '305 Mafia',
      [21] = 'Paleto Diner',
    },
    [652] = {
      [0] = 'Blacklisted-RealWorldBrand',
      [1] = 'Blacklisted-RealWorldBrand',
      [2] = 'Blacklisted-RealWorldBrand',
      [3] = 'Blacklisted-RealWorldBrand',
      [4] = 'Blacklisted-RealWorldBrand',
      [5] = 'Blacklisted-RealWorldBrand',
      [6] = 'Blacklisted-RealWorldBrand',
      [7] = 'Blacklisted-RealWorldBrand',
      [8] = 'Blacklisted-RealWorldBrand',
      [9] = 'Blacklisted-RealWorldBrand',
      [10] = 'Blacklisted-RealWorldBrand',
      [11] = 'Blacklisted-RealWorldBrand',
      [12] = 'Blacklisted-RealWorldBrand',
      [13] = 'Blacklisted-RealWorldBrand',
      [14] = 'Blacklisted-RealWorldBrand',
    },
    [653] = {
      [0] = 'Blacklisted-RealWorldBrand',
      [1] = 'Blacklisted-RealWorldBrand',
      [2] = 'Blacklisted-RealWorldBrand',
      [3] = 'Blacklisted-RealWorldBrand',
      [4] = 'Blacklisted-RealWorldBrand',
      [7] = 'Blacklisted-RealWorldBrand',
      [8] = 'Blacklisted-RealWorldBrand',
      [9] = 'Blacklisted-RealWorldBrand',
      [11] = 'Blacklisted-RealWorldBrand',
      [12] = 'Blacklisted-RealWorldBrand',
      [13] = 'Blacklisted-RealWorldBrand',
      [16] = 'Blacklisted-RealWorldBrand',
      [17] = 'Blacklisted-RealWorldBrand',
      [18] = 'Blacklisted-RealWorldBrand',
      [21] = '305 Mafia'
    },
    [654] = {
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
      [13] = 'LEO',
      [14] = 'LEO',
      [15] = 'LEO',
      [16] = 'LEO',
      [17] = 'LEO',
      [18] = 'LEO',
      [19] = 'LEO',
    },
    [655] = {
      [0] = 'Blacklisted-RealWorldBrand',
      [1] = 'Blacklisted-RealWorldBrand',
      [2] = 'Blacklisted-RealWorldBrand',
      [3] = 'Blacklisted-RealWorldBrand',
      [12] = 'Blacklisted-RealWorldBrand',
    },
    [657] = 'Blacklisted-RealWorldBrand',
    [658] = 'Pearls',
    [659] = {
      [0] = 'Blacklisted-RealWorldBrand',
      [1] = 'Blacklisted-RealWorldBrand',
      [2] = 'Blacklisted-RealWorldBrand',
      [7] = 'Cool Beans',
      [11] = 'Cool Beans',
      [12] = 'PDM Auto',
      [13] = 'Forum Family',
      [14] = 'Blacklisted', -- Beechwood Disciples
      [15] = 'Los Santos Tuners',
      [16] = 'Blacklisted-Rascals',
    },
    [661] = 'Blacklisted-Remove',
    [666] = {
      [3] = 'Midnight Club',
      [4] = 'Midnight Club',
      [5] = 'Midnight Club',
    },
    [667] = 'Blacklisted-RealWorldBrand',
    [668] = 'Blacklisted-RealWorldBrand',
    [671] = {
      [3] = 'Blacklisted-RealWorldBrand',
      [4] = 'Blacklisted-RealWorldBrand',
      [5] = 'Blacklisted-RealWorldBrand',
      [6] = 'Blacklisted-RealWorldBrand',
    },
    [672] = {
      [3] = 'Blacklisted-RealWorldBrand',
      [5] = 'Blacklisted-RealWorldBrand',
      [6] = 'Blacklisted-RealWorldBrand',
      [7] = 'Blacklisted-RealWorldBrand',
      [8] = 'Blacklisted-RealWorldBrand',
      [9] = 'Blacklisted-RealWorldBrand',
      [10] = 'Blacklisted-RealWorldBrand',
      [11] = 'Blacklisted-RealWorldBrand',
      [12] = 'Blacklisted-RealWorldBrand',
      [14] = 'Blacklisted-RealWorldBrand',
      [17] = 'Blacklisted-RealWorldBrand',
      [18] = 'Blacklisted-RealWorldBrand',
      [19] = 'Blacklisted-RealWorldBrand',
      [20] = 'Blacklisted-RealWorldBrand',
    },
    [678] = 'Blacklisted-RealWorldBrand',
    [679] = 'Blacklisted-RealWorldBrand',
    [687] = 'Los Santos Customs',
    [690] = 'Blacklisted-Remove',
    [691] = {
      [2] = 'Blacklisted-RealWorldBrand',
      [10] = 'Blacklisted-RealWorldBrand',
      [11] = 'Blacklisted-RealWorldBrand',
      [15] = 'Blacklisted-RealWorldBrand',
      [16] = 'Blacklisted-RealWorldBrand',
      [17] = 'Blacklisted-RealWorldBrand',
      [19] = 'Blacklisted-RealWorldBrand',
    },
    [692] = {
      [0] = 'Blacklisted', -- Old gutter tribe
      [1] = 'Blacklisted', -- Old gutter tribe
    },
    [694] = {
      [11] = 'Blacklisted', -- LV Jacket
    },
    [695] = {
      [11] = 'Blacklisted', -- LV Jacket
    },
    [698] = {
      [0] = 'Yellow Jack',
      [1] = 'Yellow Jack',
      [2] = 'Obsidian Tattoos',
      [3] = 'Obsidian Tattoos',
    },
    [700] = 'Camp Morningwood',
    [701] = 'Blacklisted-Remove',
    [704] = 'Blacklisted-Rascals',
    [706] = {
      [0] = 'Blacklisted', -- Beechwood Disciples
      [1] = 'Blacklisted-Rascals',
      [2] = '305 Mafia',
      [3] = 'Ballas',
      [4] = 'Forum Family',
      [5] = 'Panda Gang',
      [6] = 'Vagos',
      [7] = 'Yellow Jack',
      [8] = 'Blacklisted', -- Old Rugrats
      [9] = 'Blacklisted', -- Old Rugrats
      [10] = 'Blacklisted', -- Old OTF Clothing
    },
    [707] = {
      [1] = 'Mikes Sporting Goods',
      [2] = 'Blacklisted', -- To Be replaced
      [3] = 'Nice Dreams Ice Cream',
    },
    [709] = {
      [0] = 'Blacklisted', -- Former Rogers Salvage and Scrap
      [1] = 'The Unknown',
      [2] = 'Los Santos Tuners',
    },
    [710] = {
      [0] = 'Blacklisted', -- Former Rogers Salvage and Scrap
      [1] = 'Blacklisted', -- Former Rogers Salvage and Scrap
      [2] = 'Los Santos Tuners',
      [3] = 'Los Santos Tuners',
      [16] = 'LEO',
      [17] = 'LEO',
      [18] = 'LEO',
      [19] = 'LEO',
      [20] = '305 Mafia',
      [21] = 'LEO',
      [22] = 'LEO',
      [23] = 'LEO',
      [24] = 'LEO',
    },
    [714] = {
      [0] = 'PDM Auto',
      [1] = 'The Belmont',
      [2] = 'The Belmont',
      [3] = 'Mutiny Mob',
    },
    [715] = 'Blacklisted-Remove',
    [718] = {
      [0] = 'Los Santos Tuners',
      [2] = 'Blacklisted-Rascals',
      [3] = 'Cool Beans',
      [6] = 'Blacklisted', -- Beechwood Disciples
    },
    [719] = 'Chihuahua Hotdogs',
    [726] = 'The Boathouse',
    [727] = 'The Boathouse',
    [728] = 'The Boathouse',
    [729] = 'The Boathouse',
    [730] = 'Blacklisted-Remove',
    [731] = 'LEO',
    [732] = 'LEO',
    [733] = 'LEO',
    [734] = 'LEO',
    [735] = 'LEO',
    [740] = 'Blacklisted', -- Old OTF Clothing
    [741] = {
      [0] = 'Blacklisted', -- Old OTF Clothing
    },
    [742] = 'Blacklisted', -- Old OTF Clothing
    [744] = {
      [0] = 'character:19590',
      [1] = 'Blacklisted', -- Beechwood Disciples
      [2] = 'character:56912',
      [3] = 'character:17362',
      [4] = 'character:17161',
      [5] = 'character:56549',
      [6] = 'character:7579',
      [7] = 'character:21274',
      [8] = 'character:68496',
      [9] = 'character:24036',
      [10] = 'character:24611',
      [11] = 'character:22548',
      [12] = 'character:19567',
      [13] = 'character:20873',
      [14] = 'character:46513',
      [15] = 'character:27333',
      [16] = 'character:13484',
      [17] = 'character:8588',
      [18] = 'character:69043',
      [19] = 'character:46512',
      [20] = 'character:55701',
    },
    [745] = {
      [0] = 'Blacklisted', -- Old Rugrats
      [1] = 'Big Hawk Taxi',
      [2] = 'Marshall Towing',
    },
    [749] = {
      [0] = 'Pearls',
      [1] = 'Messina Crime Family',
      [2] = 'La Fenice Saints',
      [3] = 'The Belmont',
      [4] = 'Mutiny Mob',
    },
    [750] = 'LEO',
    [751] = {
      [6] = 'Yellow Jack',
      [19] = 'Blacklisted-RealWorldBrand',
      [20] = 'Blacklisted-RealWorldBrand',
    },
    [753] = {
      [0] = 'Finders Keepers',
      [1] = 'item:clth_bf23_shirt_4b',
      [2] = 'item:clth_bf23_shirt_4a',
    },
    [756] = {
      [5] = 'Blacklisted', -- Old OTF Clothing
    },
    [757] = {
      [20] = 'Yellow Jack'
    },
    [758] = {
      [9] = 'Blacklisted-RealWorldBrand',
      [10] = 'Blacklisted-RealWorldBrand',
      [11] = 'Blacklisted-RealWorldBrand',
      [12] = 'Blacklisted-RealWorldBrand',
      [13] = 'Blacklisted-RealWorldBrand',
      [14] = 'Blacklisted-RealWorldBrand',
      [15] = 'Blacklisted-RealWorldBrand',
      [16] = 'Blacklisted-RealWorldBrand',
      [18] = 'Blacklisted-RealWorldBrand',
      [19] = 'Mikes Sporting Goods',
      [20] = 'item:clth_bf23_shirt_6a',
      [21] = 'item:clth_bf23_shirt_6b',
      [22] = 'item:clth_bf23_shirt_6c',
      [23] = 'item:clth_bf23_shirt_6d',
    },
    [760] = {
      [0] = 'Delta Kappa Nu',
      [1] = 'Asian Market',
    },
    [765] = 'Blacklisted-Remove',
    [772] = {
      [3] = 'Blacklisted-RealWorldBrand',
      [4] = 'Blacklisted-RealWorldBrand',
      [5] = 'Blacklisted-RealWorldBrand',
    },
    [782] = 'Blacklisted-Remove',
    [791] = 'Blacklisted-RealWorldBrand',
    [793] = 'Blacklisted-RealWorldBrand',
    [794] = {
      [0] = 'Finders Keepers',
      [1] = 'item:clth_bf23_shirt_5a',
      [2] = 'item:clth_bf23_shirt_5b',
      [3] = 'item:clth_bf23_shirt_5c',
    },
    [796] = 'Delta Kappa Nu',
    [797] = 'Blacklisted-RealWorldBrand',
    [798] = {
      [0] = 'Forum Family',
      [1] = 'Vagos',
    },
    [799] = 'Blacklisted-RealWorldBrand',
    [800] = 'Blacklisted-RealWorldBrand',
    [801] = 'Blacklisted', -- Old OTF Clothing
    [802] = {
      [0] = 'Blacklisted', -- Old OTF Clothing
      [1] = 'Westside Shootz',
      [2] = 'Urban Shootz',
    },
    [803] = 'Blacklisted', -- Old OTF Clothing
    [804] = 'Blacklisted', -- Old OTF Clothing
    [807] = {
      [0] = 'Vanilla Unicorn',
      [1] = 'Vanilla Unicorn',
      [2] = 'item:clth_bf23_shirt_3',
    },
    [808] = 'Vanilla Unicorn',
    [809] = {
      [0] = 'Messina Crime Family',
      [1] = 'Messina Crime Family',
      [2] = 'The Belmont',
      [3] = 'The Belmont',
      [4] = 'Mutiny Mob',
    },
    [810] = 'Cool Beans',
    [811] = {
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
    },
    [812] = {
      [0] = 'The Belmont',
    },
    [813] = {
      [3] = 'Blacklisted-RealWorldBrand',
      [6] = 'Blacklisted-RealWorldBrand',
      [7] = 'Blacklisted-RealWorldBrand',
      [10] = 'Blacklisted-RealWorldBrand',
      [13] = 'Blacklisted-RealWorldBrand',
      [14] = 'Blacklisted-RealWorldBrand',
    },
    [824] = 'character:19567',
    [825] = { -- Easter Bunny Suit
      [0] = 'item:clth_ea24_jbib_a',
      [1] = 'item:clth_ea24_jbib_b',
      [2] = 'item:clth_ea24_jbib_c',
      [3] = 'item:clth_ea24_jbib_d',
      [4] = 'item:clth_ea24_jbib_e',
      [5] = 'item:clth_ea24_jbib_f',
      [6] = 'item:clth_ea24_jbib_g',
      [7] = 'item:clth_ea24_jbib_h',
      [8] = 'item:clth_ea24_jbib_i',
    },
    [826] = "Fratelli d'Schnazy",
    [827] = "Fratelli d'Schnazy",
    [828] = 'Rocky Road Towing',
    [829] = 'Rocky Road Towing',
    [830] = 'Rocky Road Towing',
    [831] = 'Rocky Road Towing',
    [832] = 'OTF',
    [833] = 'OTF',
    [834] = 'OTF',
    [835] = 'OTF',
    [836] = 'OTF',
    [837] = {
      [0] = 'Messina Crime Family',
      [1] = 'Messina Crime Family',
      [2] = 'Messina Crime Family',
    },
    [838] = {
      [0] = 'Mikes Sporting Goods',
      [1] = 'Mikes Sporting Goods',
    },
    [839] = {
      [3] = 'Camp Morningwood',
      [4] = 'Camp Morningwood',
      [5] = 'Camp Morningwood',
      [6] = 'Camp Morningwood',
      [7] = 'Camp Morningwood',
    },
    [842] = {
      [0] = 'item:clth_bf24_btn_c_a',
      [1] = 'item:clth_bf24_btn_c_b',
    },
    [843] = {
      [0] = 'item:clth_bf24_btn_o_a',
      [1] = 'item:clth_bf24_btn_o_b',
    },
    [845] = {
      [0] = 'Petrovich Cartel',
      [1] = 'Petrovich Cartel',
    },
    [846] = {
      [0] = 'Petrovich Cartel',
      [1] = 'Petrovich Cartel',
    },
    [848] = {
      [0] = 'Vynchenzo\'s Distillery',
    },
    [855] = 'Asian Market',
    [869] = 'FixTmrwBlacklistToday',
    [870] = 'FixTmrwBlacklistToday',
    [886] = 'LEO',
    [887] = 'LEO',
    [888] = {
      [0] = 'SFT Pawn',
      [1] = 'character:57327',
    },
    [889] = 'SFT Pawn',
    [890] = 'Go Truck Yourself',
    [894] = 'East Side Armor',
    [895] = 'Petrovich Cartel',
    [910] = 'El Rancho Ravens',
    [913] = {
      [5] = 'Camp Morningwood',
      [6] = 'Camp Morningwood',
      [7] = 'Camp Morningwood',
    },
    [938] = 'Fort Zancudo',
    [936] = {
      [10] = 'item:clth_cm25_jbib_a',
      [11] = 'item:clth_cm25_jbib_b',
      [12] = 'item:clth_cm25_jbib_c',
      [13] = 'item:clth_cm25_jbib_d',
      [14] = 'item:clth_cm25_jbib_e',
      [15] = 'item:clth_cm25_jbib_f',
      [16] = 'item:clth_cm25_jbib_g',
      [17] = 'item:clth_cm25_jbib_h',
    },
    [939] = 'LEO',
    [940] = 'LEO',
    [941] = 'LEO',
    [942] = 'LEO',
    [943] = 'LEO',
    [944] = 'LEO',
  },
  -- / Jacket

  -- P_HEAD - p0 - Headgear
  ['p0'] = {
    [1] = {
      [1] = 'item:5g_blocker',
    },
    [12] = {
      [8] = "Fratelli d'Schnazy",
    },
    [20] = {
      [6] = 'item:clth_bf23_hat_1',
    },
    [44] = {
      [8] = 'LSFD',
      [9] = 'LEO',
      [13] = 'LSFD',
      [14] = 'LSFD',
    },
    [45] = {
      [11] = 'LSFD',
    },
    [46] = 'LEO',
    [63] = {
      [10] = 'Forum Family',
      [11] = 'Vagos',
    },
    [65] =  {
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
    },
    [66] =  {
      [1] = 'LEO',
      [2] = 'LEO',
      [3] = 'LEO',
      [4] = 'LEO',
      [5] = 'LEO',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
      [9] = 'LEO',
      [10] = 'LEO',
      [11] = 'LEO',
      [12] = 'LEO',
    },
    [105] = {
      [4] = 'The Boathouse',
    },
    [112] = {
      [17] = 'Angels of Death',
    },
    [137] = 'LSFD',
    [138] = 'LSFD',
    [187] = {
      [15] = '305 Mafia',
    },
    [215] = {
      [0] = 'OTF',
      [1] = 'Los Santos Customs',
    },
    [219] = 'LEO',
    [220] = 'LEO',
    [221] = 'LEO',
    [222] = 'LEO',
    [223] = {
      [0] = 'OTF',
      [1] = 'Los Santos Customs',
    },
    [224] = 'Blacklisted-RealWorldBrand',
    [225] = {
      [8] = '305 Mafia',
      [9] = 'The Unknown',
      [10] = 'Blacklisted', -- Beechwood Disciples
      [11] = 'Forum Family',
      [12] = 'Messina Crime Family',
      [13] = 'Blacklisted-Rascals',
      [14] = 'Blacklisted',
      [15] = 'Vagos',
    },
    [226] = {
      [3] = 'LEO',
    },
    [227] = 'LEO',
    [228] = 'LEO',
    [229] = 'LEO',
    [230] = 'LEO',
    [236] = {
      [9] = 'Asian Market',
    },
    [238] = {
      [9] = 'Asian Market',
    },
    [240] = 'Blacklisted-RealWorldBrand',
    [241] = 'LEO',
    [245] = {
      [8] = '305 Mafia',
      [9] = 'The Unknown',
      [10] = 'Blacklisted', -- Beechwood Disciples
      [11] = 'Forum Family',
      [12] = 'Messina Crime Family',
      [13] = 'Blacklisted-Rascals',
      [14] = 'Blacklisted',
      [15] = 'Vagos',
    },
    [246] = 'Blacklisted-RealWorldBrand',
    [248] = 'LSFD',
    [249] = {
      [0] = 'LEO',
      [1] = 'LEO',
      [2] = 'LSFD',
      [3] = 'LSFD',
      [4] = 'LSFD',
      [5] = 'LSFD',
      [6] = 'LEO',
      [7] = 'LEO',
      [8] = 'LEO',
    },
    [251] = {
      [13] = 'The Kush Korner',
    },
    [252] = {
      [0] = 'Mikes Sporting Goods',
      [6] = 'Yellow Jack',
      [7] = 'Camp Morningwood',
      [8] = 'Camp Morningwood',
      [9] = 'Camp Morningwood',
      [10] = 'Camp Morningwood',
      [11] = 'Camp Morningwood',
    },
    [253] = {
      [0] = 'Mikes Sporting Goods',
      [6] = 'Yellow Jack',
      [7] = 'Camp Morningwood',
      [8] = 'Camp Morningwood',
      [9] = 'Camp Morningwood',
      [10] = 'Camp Morningwood',
      [11] = 'Camp Morningwood',
    },
    [263] = 'LEO',
    [264] = 'LEO',
    [265] = 'LEO',
    [266] = 'LEO',
    [267] = 'LEO',
    [268] = 'LEO',
    [269] = 'LEO',
    [270] = 'LEO',
  },

  -- P_EYES - p1 - Glasses
  ['p1'] = {
    [76] = {
      [0] = 'item:clth_vangelico_8a',
      [1] = 'item:clth_vangelico_8b',
      [2] = 'item:clth_vangelico_8c',
      [3] = 'item:clth_vangelico_8d',
      [4] = 'item:clth_vangelico_8e',
      [5] = 'item:clth_vangelico_8f',
    },
  },

  -- P_EARS - p2 - Ears
  ['p2'] = {
    [45] = {
      [0] = 'item:clth_vangelico_3a',
      [1] = 'item:clth_vangelico_3b',
      [2] = 'item:clth_vangelico_3c',
    },
    [46] = {
      [0] = 'item:clth_vangelico_5a',
      [1] = 'item:clth_vangelico_5b',
    },
  },

  -- P_LEFT_WRIST - p6 - Watches
  ['p6'] = {

  },

  -- P_RIGHT_WRIST - p7 - Bracelet
  ['p7'] = {

  },
}
