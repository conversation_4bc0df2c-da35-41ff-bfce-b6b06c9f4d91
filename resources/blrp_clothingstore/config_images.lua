-- Clothing Image Generator Configuration

ImagesConfig = {}

-- Staff permission settings
ImagesConfig.require_staff_for_hands = false -- Set to true to require staff permissions for updating clothing item hands

-- Global amount setting for testing (-1 for all items, or set a number to limit)
ImagesConfig.Amount = 3  -- Change this to limit how many items to generate for testing

-- Where to teleport player before starting image processing
ImagesConfig.TeleportCoords = vector3(-1081.148, -810.157, 19.326)

-- Ped spawn configuration
ImagesConfig.PedSpawn = {
    coords = vector3(-1085.782, -810.841, 18.142),  -- Where to spawn the ped
    heading = 304.4,  -- Which direction the ped should face
    models = {
        male = "mp_m_freemode_01",
        female = "mp_f_freemode_01"
    }
}

-- Base camera configuration
ImagesConfig.BaseCamera = {
    coords = vector3(-1084.181, -809.305, 18.126),  -- Base camera position (same height as ped)
    heading = 130.071  -- Base camera direction (facing directly at ped)
}

-- Undress configuration by gender
ImagesConfig.UndressConfig = {
    male = {
        ["p0"] = {-1, 0},  -- Hat
        ["p1"] = {-1, 0},  -- Glass<PERSON>
        ["p2"] = {-1, 0},  -- Earpiece
        ["p6"] = {-1, 0},  -- Watch
        ["p7"] = {-1, 0},  -- Bracelet
        ['1'] = {0, 0},    -- Mask
        ['3'] = {15, 0},   -- Torso/Hands
        ['4'] = {61, 0},   -- Leg
        ['5'] = {0, 0},    -- Parachute/Bag
        ['6'] = {34, 0},   -- Shoes
        ['7'] = {0, 0},    -- Accessory
        ['8'] = {15, 0},   -- Undershirt
        ['9'] = {0, 0},    -- Body Armor
        ['10'] = {0, 0},   -- Decal
        ['11'] = {15, 0}   -- Jacket
    },
    female = {
        ["p0"] = {-1, 0},  -- Hat
        ["p1"] = {-1, 0},  -- Glasses
        ["p2"] = {-1, 0},  -- Earpiece
        ["p6"] = {-1, 0},  -- Watch
        ["p7"] = {-1, 0},  -- Bracelet
        ['1'] = {0, 0},    -- Mask
        ['3'] = {71, 0},   -- Torso/Hands - Female undressed hands
        ['4'] = {15, 3},   -- Leg
        ['5'] = {0, 0},    -- Parachute/Bag
        ['6'] = {35, 0},   -- Shoes
        ['7'] = {0, 0},    -- Accessory
        ['8'] = {14, 0},   -- Undershirt
        ['9'] = {0, 0},    -- Body Armor
        ['10'] = {0, 0},   -- Decal
        ['11'] = {15, 3}   -- Jacket
    }
}

-- Category-specific clothing overrides by gender (applied after undressing)
ImagesConfig.CategoryOverrides = {
    jacket = {
        male = {
            ['3'] = {222, 0}  -- Set male hands (component 3) to drawable 222, texture 0
        },
        female = {
            ['3'] = {71, 0}   -- Set female hands (component 3) to drawable 71, texture 0
        }
    }
    -- Add more category overrides here as needed
    -- Example:
    -- shirt = {
    --     male = {
    --         ['4'] = {15, 0}  -- Set legs to specific drawable for male shirt photos
    --     },
    --     female = {
    --         ['4'] = {15, 3}  -- Set legs to specific drawable for female shirt photos
    --     }
    -- }
}

-- Configuration for all clothing items
ImagesConfig.ClothingItems = {
    -- TOP SECTION
    ears = {
        section = "top",
        name = "Ears",
        identifier = "p2",
        camera = {
            bone = 31086,  -- Head bone
            fov = 45.0,
            zoom = 0.45,
            offsetX = -0.15,   -- No left/right adjustment
            offsetY = 0.0,   -- No forward/back adjustment
            offsetZ = 0.67
        },
        pose = {
            dict = "lunyx@lookbook_v1@pose011",
            anim = "lookbook_v1@pose011",
            flag = 49
        }
    },

    helmet = {
        section = "top",
        name = "Helmet",
        identifier = "p0",
        camera = {
            bone = 31086,  -- Head bone
            fov = 45.0,
            zoom = 0.56, -- 30% more zoom
            offsetX = 0.0,   -- No left/right adjustment
            offsetY = 0.0,   -- No forward/back adjustment
            offsetZ = 0.73   -- 2% lower (0.75 - 0.02)
        },
        pose = {
          dict = "lunyx@lookbookv3@pose016",
          anim = "lookbookv3@pose016",
          flag = 49
        }
    },

    glasses = {
        section = "top",
        name = "Glasses",
        identifier = "p1",
        camera = {
            bone = 31086,  -- Head bone
            fov = 45.0,
            zoom = 0.4, -- 30% more zoom
            offsetX = 0.0,   -- No left/right adjustment
            offsetY = 0.0,   -- No forward/back adjustment
            offsetZ = 0.67   -- 12% higher (0.55 + 0.12)
        },
        pose = {
          dict = "lunyx@lookbookv3@pose016",
          anim = "lookbookv3@pose016",
          flag = 49
        }
    },

    mask = {
        section = "top",
        name = "Mask",
        identifier = 1,
        camera = {
            bone = 31086,  -- Head bone
            fov = 45.0,
            zoom = 0.6, -- 30% more zoom
            offsetX = 0.0,   -- No left/right adjustment
            offsetY = 0.0,   -- No forward/back adjustment
            offsetZ = 0.67   -- 12% higher (0.55 + 0.12)
        },
        pose = {
          dict = "lunyx@lookbookv3@pose016",
          anim = "lookbookv3@pose016",
          flag = 49
        }
    },

    neck = {
        section = "top",
        name = "Neck Accessories",
        identifier = 7,
        camera = {
            bone = 11816,  -- Neck/chest bone
            fov = 50.0,
            zoom = 0.833, -- 15% more zoom (0.98 * 0.85)
            offsetX = 0.0,   -- No left/right adjustment
            offsetY = 0.0,   -- No forward/back adjustment
            offsetZ = 0.50   -- Move up 13% (0.37 + 0.13)
        }
    },

    -- TORSO SECTION
    shirt = {
        section = "torso",
        name = "Shirt",
        identifier = 3,
        camera = {
            bone = 11816,  -- Chest bone
            fov = 50.0,    -- Tighter FOV for more zoom
            zoom = 0.825,  -- Zoom out 10% (0.75 * 1.1)
            offsetX = -0.01, -- 1% to the left
            offsetY = 0.0,   -- No forward/back adjustment
            offsetZ = 0.23   -- Up 20% more (0.03 + 0.2)
        }
    },

    jacket = {
        section = "torso",
        name = "Jacket",
        identifier = 11,
        camera = {
            bone = 11816,  -- Chest bone
            fov = 50.0,
            zoom = 0.866,  -- Zoom out 5% more (0.825 * 1.05)
            offsetX = -0.01, -- Same as shirt
            offsetY = 0.0,   -- Same as shirt
            offsetZ = 0.23   -- Up 20% more
        }
    },

    hands = {
        section = "torso",
        name = "Hands",
        identifier = 3,  -- Usually part of torso
        camera = {
            bone = 11816,  -- Chest bone
            fov = 50.0,    -- Same as shirt
            zoom = 0.825,  -- Zoom out 10%
            offsetX = -0.01, -- Same as shirt
            offsetY = 0.0,   -- Same as shirt
            offsetZ = 0.23   -- Up 20% more
        }
    },

    armor = {
        section = "torso",
        name = "Armor",
        identifier = 9,
        camera = {
            bone = 11816,  -- Chest bone
            fov = 50.0,
            zoom = 0.825,  -- Zoom out 10%
            offsetX = -0.01, -- Same as shirt
            offsetY = 0.0,   -- Same as shirt
            offsetZ = 0.28   -- Go up 5% more (0.23 + 0.05)
        }
    },

    bags = {
        section = "torso",
        name = "Bags",
        identifier = 5,
        camera = {
            bone = 11816,  -- Chest bone (for back view)
            fov = 50.0,
            zoom = 0.825,  -- Same as armor
            offsetX = -0.01, -- Same as armor
            offsetY = 0.0,   -- Same as armor
            offsetZ = 0.28   -- Same as armor (5% higher)
        }
    },

    decals = {
        section = "torso",
        name = "Decals",
        identifier = 10,
        camera = {
            bone = 11816,  -- Chest bone
            fov = 50.0,    -- Same as armor
            zoom = 0.825,  -- Same as armor
            offsetX = -0.01, -- Same as armor
            offsetY = 0.0,   -- Same as armor
            offsetZ = 0.28   -- Same as armor (5% higher)
        }
    },

    watches = {
        section = "torso",
        name = "Watches",
        identifier = "p6",  -- Left wrist
        camera = {
            bone = 60309,  -- Left hand bone
            fov = 45.0,
            zoom = 0.65,    -- Close zoom for detailed wrist view
            offsetX = -0.4,   -- No left/right adjustment
            offsetY = 0.0,   -- No forward/back adjustment
            offsetZ = 0.0    -- At wrist level
        }
    },

    bracelets = {
        section = "torso",
        name = "Bracelets",
        identifier = "p7",  -- Right wrist
        camera = {
            bone = 28422,  -- Right hand bone
            fov = 45.0,
            zoom = 0.4,    -- Close zoom for detailed wrist view
            offsetX = 0.4,   -- No left/right adjustment
            offsetY = 0.0,   -- No forward/back adjustment
            offsetZ = 0.0    -- At wrist level
        }
    },

    -- BOTTOM SECTION
    legs = {
        section = "bottom",
        name = "Legs",
        identifier = 4,
        camera = {
            bone = 46078,  -- Leg bone
            fov = 60.0,
            zoom = 1.05,  -- Adjusted zoom level
            offsetZ = -0.45  -- Moved down 15% more (from -0.3 to -0.45)
        }
    },

    shoes = {
        section = "bottom",
        name = "Shoes",
        identifier = 6,
        camera = {
            bone = 46078,  -- Leg bone (lower)
            fov = 50.0,
            zoom = 0.75,  -- Close zoom for detailed shoe view
            offsetX = -0.13,  -- Move camera 8% to the right
            offsetY = 0.0,   -- No forward/back adjustment
            offsetZ = -0.85  -- Moved down 15% from -0.8 for optimal shoe framing
        }
    }
}
