-- Server-controlled processing sessions
processingSessions = {}

-- Processing session structure:
-- {
--   playerId = source,
--   clothingKey = "jacket",
--   workQueue = { {drawable=1, texture=0, imageName="jacket_male_1_0", gender="male"}, ... },
--   currentIndex = 1,
--   totalItems = 100,
--   startTime = os.time(),
--   currentGender = "male"
-- }

-- Server-controlled processing proxy function
pClothingStore.startProcessing = function(targetSource, clothingKey, item)
  local src = targetSource or source
  local character = exports.blrp_core:character(src)

  print('starting processing session for clothing images', clothingKey, item)

  if not character then
    print("❌ No character found for source:", src)
    return
  end

  if not character.hasGroup('staff') then
    character.notify('Permission failure')
    return
  end

  -- Log to user
  character.log('ACTION', 'Started image processing for ' .. clothingKey)

  -- Check if already processing
  if processingSessions[src] then
    character.notify('⚠️ Already processing images. Please wait for current session to complete.')
    return
  end

  local config = ImagesConfig.ClothingItems[clothingKey]
  if not config then
    print("❌ Unknown clothing key:", clothingKey)
    character.notify('❌ Unknown clothing type: ' .. clothingKey)
    return
  end

  print(string.format("🚀 Starting server-controlled processing for %s (player: %s)", clothingKey, src))
  character.notify(string.format('🚀 Starting %s image processing...', config.name))

  -- Calculate work to do using proxy
  local workToDo = {}

  -- Process entire category using proxy
  local maleItems = tClothingStore.getAvailableItems(src, { clothingKey, "male" })
  local femaleItems = tClothingStore.getAvailableItems(src, { clothingKey, "female" })

  -- Combine both genders and convert compact format to full format (Was causing client failure due to data size)
  for _, compactItem in ipairs(maleItems) do
    local workItem = {
      drawable = compactItem[1],
      texture = compactItem[2],
      imageName = string.format("%s_%s_%d_%d", clothingKey, "male", compactItem[1], compactItem[2]),
      gender = "male"
    }
    table.insert(workToDo, workItem)
  end

  for _, compactItem in ipairs(femaleItems) do
    local workItem = {
      drawable = compactItem[1],
      texture = compactItem[2],
      imageName = string.format("%s_%s_%d_%d", clothingKey, "female", compactItem[1], compactItem[2]),
      gender = "female"
    }
    table.insert(workToDo, workItem)
  end

  -- Filter out already processed items using name-based checking
  local filteredWork = {}
  for _, workItem in ipairs(workToDo) do
    -- Check if this image has already been processed using the image name
    if not IsImageProcessedByName(workItem.imageName) then
      table.insert(filteredWork, workItem)
    end

    local settings = GetClothingItemSettings(clothingKey, workItem.drawable, workItem.gender)
    if settings then
      workItem.assignedHands = settings.assigned_hands
    end
  end

  print(string.format("📋 Server-controlled work for %s: %d total items, %d already processed, %d to generate",
    clothingKey, #workToDo, #workToDo - #filteredWork, #filteredWork))

  local teleportCoords = ImagesConfig.TeleportCoords

  tSurvival.teleportCoords(src, { teleportCoords })

  if #filteredWork == 0 then
    character.notify('✅ No images to process - all already completed!')
    return
  end

  -- Create processing session
  processingSessions[src] = {
    playerId = src,
    clothingKey = clothingKey,
    workQueue = filteredWork,
    currentIndex = 1,
    totalItems = #filteredWork,
    startTime = os.time(),
    currentGender = filteredWork[1].gender or "male"
  }

  print(string.format("📋 Created processing session for player %s: %d items to process", src, #filteredWork))

  -- Start processing by sending first image request
  SendNextImageRequest(src)

  return {success = true }
end

-- Handle single image result proxy function
pClothingStore.handleImageResult = function(imageName, imageData)
  local src = source
  local session = processingSessions[src]
  if not session then
    print("❌ No processing session found for player:", src)
    return {success = false, error = "No processing session found"}
  end

  local workItem = session.workQueue[session.currentIndex]
  if workItem.imageName ~= imageName then
    print(string.format("❌ Image name mismatch. Expected: %s, Got: %s", workItem.imageName, imageName))
    return {success = false, error = "Image name mismatch"}
  end

  -- Track progress update
  local progress = {
    current = session.currentIndex,
    total = session.totalItems,
    percentage = math.floor((session.currentIndex / session.totalItems) * 100)
  }

  print(string.format("📥 Received image result from player %s (%d/%d - %d%%): %s", src, progress.current, progress.total, progress.percentage, imageName))

  -- Upload the image if we have image data
  if imageData and imageData ~= "" then
    -- Trigger the upload event with the image data
    TriggerEvent('blrp_clothing_images:uploadBase64', imageName, imageData)
  end

  -- Mark as processed in database using image name
  MarkImageProcessedByName(imageName)

  -- Move to next item
  session.currentIndex = session.currentIndex + 1

  -- Continue with next image
  SetTimeout(20, function()
    SendNextImageRequest(src)
  end)

  return {success = true}
end

-- Stop processing session proxy function
pClothingStore.stopProcessing = function()
  local src = source
  local session = processingSessions[src]
  if not session then
    print('❌ [pClothingStore.stopProcessing] No processing session found for player:', src)
    return
  end

  local character = exports.blrp_core:character(src)
  print(string.format("🛑 Processing session stopped for player %s", src))

  if character then
    character.notify('🛑 Image processing stopped.')
  end

  -- Clean up session
  processingSessions[src] = nil

  -- Notify client to cleanup
  tClothingStore.processingComplete(src, { })

  return
end

-- Load all processed images proxy function
pClothingStore.loadAllProcessed = function()
    local processedImages = GetAllProcessedImages()
    return {success = true, processedImages = processedImages}
end

-- Mark image as processed proxy function
pClothingStore.markProcessed = function(imageName)
    MarkImageProcessedByName(imageName)
    return {success = true}
end

-- Reset processed images for specific type proxy function
pClothingStore.resetType = function(clothingType)
    local affectedRows = ResetProcessedImagesForType(clothingType)
    return {success = true, affectedRows = affectedRows, type = clothingType}
end

-- Reset selected image proxy function
pClothingStore.resetSelectedImage = function(clothingKey, item, gender)
    local src = source
    print("📸 Server received resetSelectedImage proxy call")

    local character = exports.blrp_core:character(src)

    -- Check permissions
    if not (character.hasOrInheritsGroup('admin') or character.isStaff()) then
        character.notify('You do not have permission to reset images')
        return
    end

   -- Backslashs are required because mysql considers _ a token
   local searchKey = clothingKey .. '\\_' .. gender  .. '\\_' .. item .. '\\_' .. '%'


    character.log('ACTION', '[Clothing Wardrobe Images] Resetting all variations for ' .. searchKey)

    local query = 'UPDATE clothing_images_log SET processed_at = NULL, updated_at = NOW() WHERE (name LIKE ?)'
    local params = {searchKey}

    character.notify(string.format('🗑️ Reset all variations for %s item %s', clothingKey, item))

    local affectedRows = MySQL.Sync.execute(query, params)
    print(string.format("📊 Reset %d image records for %s item %s by %s", affectedRows, clothingKey, item, character.name))

    -- Clear the processed images cache to force refresh
    SetItemUnprocessed(clothingKey, item, gender)

    -- Start targeted processing after reset
    SetTimeout(1000, function()
        character.notify(string.format('🚀 Processing all variations for %s item %s...', clothingKey, item))
        local result = pClothingStore.startProcessing(src, clothingKey, item)
    end)

    return {success = true, affectedRows = affectedRows}
end

-- Process all categories proxy function
pClothingStore.processAllCategories = function()
    local src = source
    local character = exports.blrp_core:character(src)

    if not character then
        print("❌ No character found for source:", src)
        return {success = false, error = "No character found"}
    end

    if not character.hasGroup('staff') then
        character.notify('Permission failure')
        return {success = false, error = "Permission denied"}
    end

    -- Check if already processing
    if processingSessions[src] then
        character.notify('⚠️ Already processing images. Please wait for current session to complete.')
        return {success = false, error = "Already processing"}
    end

    print(string.format("🚀 Starting all categories processing for player %s", src))
    character.notify('🚀 Starting processing for all clothing categories...')

    -- Get all available clothing categories from config
    local allCategories = {}
    for clothingKey, config in pairs(ImagesConfig.ClothingItems) do
        table.insert(allCategories, clothingKey)
    end

    print(string.format("📋 Found %d categories to process: %s", #allCategories, table.concat(allCategories, ", ")))

    -- Start processing categories sequentially
    ProcessAllCategoriesSequentially(src, allCategories, 1)

    return {success = true, totalCategories = #allCategories}
end

-- Send next image request to client
function SendNextImageRequest(src)
  local session = processingSessions[src]
  if not session then
    print("❌ [SendNextImageRequest] No processing session found for player:", src)
    return
  end

  if session.currentIndex > session.totalItems then
    -- Processing complete
    local character = exports.blrp_core:character(src)
    local duration = os.time() - session.startTime

    print(string.format("✅ Processing session complete for player %s: %d items in %d seconds",
      src, session.totalItems, duration))

    if character then
      character.notify(string.format('✅ Processing complete! Generated %d images in %d seconds.',
        session.totalItems, duration))
    end

    -- Clean up session
    processingSessions[src] = nil

    -- Notify client to cleanup using proxy
    tClothingStore.processingComplete(src, {})
    return
  end

  local workItem = session.workQueue[session.currentIndex]
  local config = ImagesConfig.ClothingItems[session.clothingKey]

  -- Send single image processing request to client using proxy
  local requestPacket = {
    clothingKey = session.clothingKey,
    config = config,
    workItem = workItem,
    assignedHands = workItem.assignedHands,
    progress = {
      current = session.currentIndex,
      total = session.totalItems,
      percentage = math.floor((session.currentIndex / session.totalItems) * 100)
    }
  }

  tClothingStore.processSingleImage(src, { requestPacket })
end

-- Process all categories sequentially
function ProcessAllCategoriesSequentially(src, categories, currentIndex)
  local character = exports.blrp_core:character(src)

  if not character then
    print("❌ Character not found during sequential processing")
    return
  end

  -- Check if we've processed all categories
  if currentIndex > #categories then
    character.notify('✅ All categories processing completed!')
    print(string.format("✅ All categories processing completed for player %s", src))
    return
  end

  local clothingKey = categories[currentIndex]
  print(string.format("📋 Processing category %d/%d: %s", currentIndex, #categories, clothingKey))
  character.notify(string.format('📋 Processing category %d/%d: %s', currentIndex, #categories, clothingKey))

  -- Start processing this category
  local result = pClothingStore.startProcessing(src, clothingKey)

  if not result or not result.success then
    print(string.format("❌ Failed to start processing for category: %s", clothingKey))
    character.notify(string.format('❌ Failed to process category: %s', clothingKey))

    -- Continue to next category after a delay
    SetTimeout(2000, function()
      ProcessAllCategoriesSequentially(src, categories, currentIndex + 1)
    end)
    return
  end

  -- Wait for this category to complete, then move to next
  -- We'll check periodically if the session is complete
  local function CheckCompletion()
    if not processingSessions[src] then
      -- Session completed, move to next category
      print(string.format("✅ Category %s completed, moving to next", clothingKey))
      SetTimeout(2000, function() -- Small delay between categories
        ProcessAllCategoriesSequentially(src, categories, currentIndex + 1)
      end)
    else
      -- Still processing, check again in 5 seconds
      SetTimeout(5000, CheckCompletion)
    end
  end

  -- Start checking for completion after a short delay
  SetTimeout(5000, CheckCompletion)
end


