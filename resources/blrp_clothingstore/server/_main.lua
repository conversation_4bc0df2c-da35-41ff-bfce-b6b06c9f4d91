pClothingStore = {}
P.bindInstance('clothingstore', pClothingStore)

tSurvival = T.getInstance('blrp_core', 'survival')
tClothingStore = T.getInstance('blrp_clothingstore', 'clothingstore')

current_version = GetConvarInt('sv_enforceGameBuild')

function buildCloakroomAccessPoints(player)
  local character = exports.blrp_core:character(player)

  for wardrobe_key, wardrobe_config in ipairs(config_wardrobes.locations) do
    character.drawMarker({
      coords = wardrobe_config.coords - vector3(0, 0, 0.97),
      call_enter = 'blrp_clothingstore:server:resolveWardrobe',
      args_enter = wardrobe_key,
      call_leave = 'badMenu:server:requestClose',

      -- Drawing config
      type = 23,
      scale = {
        x = 0.7,
        y = 0.7,
        z = 0.5
      },
      r = 0,
      g = 255,
      b = 125,
      a = 125,
      trigger_distance = 1.5,
    })
  end

  for cloakroom_key, cloakroom_config in ipairs(config_cloakrooms.locations) do
    character.drawMarker({
      coords = cloakroom_config.coords - vector3(0, 0, 0.97),
      call_enter = 'blrp_clothingstore:server:resolveCloakroom',
      args_enter = cloakroom_key,
      call_leave = 'badMenu:server:requestClose',

      -- Drawing config
      type = 23,
      scale = {
        x = 0.7,
        y = 0.7,
        z = 0.5
      },
      r = 0,
      g = 255,
      b = 125,
      a = 125,
      trigger_distance = 1.5,
    })
  end

  for modelmenu_key, modelmenu_config in ipairs(config_modelmenu.locations) do
    character.drawMarker({
      coords = modelmenu_config.coords - vector3(0, 0, 0.97),
      call_enter = 'blrp_clothingstore:server:openModelMenu',
      call_leave = 'badMenu:server:requestClose',

      -- Drawing config
      type = 23,
      scale = {
        x = 0.7,
        y = 0.7,
        z = 0.5
      },
      r = 165,
      g = 0,
      b = 0,
      a = 175,
      trigger_distance = 1.5,
    })
  end
end

AddEventHandler('core:server:registerSelectedPlayer', function(player)
  buildCloakroomAccessPoints(player)
end)

Citizen.CreateThread(function()
  if not GlobalState.is_dev then
    return
  end

  for _, player in pairs(GetPlayers()) do
    buildCloakroomAccessPoints(player)
  end
end)



RegisterNetEvent('blrp_clothingstore:server:openWardrobe', function(allow_dress, player, allow_undress, force_user_id)
  if not player then
    player = source
  end

  TriggerEvent('blrp_wardrobe:server:openWardrobe', allow_dress, player, allow_undress, force_user_id)
end)

RegisterNetEvent('blrp_clothingstore:server:resolveWardrobe', function(location_id)
  if not location_id then
    return
  end

  local wardrobe = config_wardrobes.locations[location_id]

  if not wardrobe then
    return
  end

  local character = exports.blrp_core:character(source)

  TriggerEvent('blrp_clothingstore:server:openWardrobe', wardrobe.allow_dress, character.source, wardrobe.allow_undress)
end)

local gender_option_cache = {}

RegisterNetEvent('blrp_clothingstore:server:openCloakroom', function(cloakroom_id, player)
  if not cloakroom_id then
    return
  end

  local cloakroom_config = config_cloakrooms.outfits[cloakroom_id]

  if not cloakroom_config then
    return
  end

  if not player then
    player = source
  end

  local character = exports.blrp_core:character(player)

  if cloakroom_config._config.permission and not character.hasPermissionCore(cloakroom_config._config.permission) then
    return
  end

  local section_title_prefix = string.gsub(cloakroom_id, '^%l', string.upper)

  if cloakroom_config._config.alias_prefix then
    section_title_prefix = cloakroom_config._config.alias_prefix
  end

  if cloakroom_config._config.capitalize_prefix then
    section_title_prefix = string.upper(section_title_prefix)
  end

  local menu = BMenu:new(true, {
    resource_title = section_title_prefix .. ' Cloakroom',
    section_title = 'Select Outfit',
    title_bg_image = '',
    position = { top_offset = 500, left_offset = 10 },
    max_items_shown_at_once = 12,
    prevent_sound = true,
    width = 280,
  })

  local is_male = tClothingStore.isPedMale(character.source)

  local show_other_genders = gender_option_cache[character.source]

  -- Sort cloakroom by outfit name

  local tKeys = {}

  for k in pairs(cloakroom_config) do table.insert(tKeys, k) end

  table.sort(tKeys)

  for _, outfit_name in ipairs(tKeys) do
    if outfit_name ~= '_config' then
      local outfit_name_lower = string.lower(outfit_name)
      local is_male_option = (string.match(outfit_name_lower, 'male') and not string.match(outfit_name_lower, 'female'))
      local is_female_option = string.match(outfit_name_lower, 'female')

      if (is_male and is_male_option) or (not is_male and is_female_option) or (not is_male_option and not is_female_option) or show_other_genders then
        menu:addSelection(false, outfit_name, function(player, value, callback)
          callback('')

          local outfit = cloakroom_config[outfit_name]
          local build = outfit.build

          if not build then
            character.notify('Build not specified, aborting: ' .. outfit_name)
            return
          end

          local offsets, counts = calculateOffsetBetweenBuilds(build, current_version)

          if is_male_option then
            offsets = offsets.male
            counts = counts.male
          else
            offsets = offsets.female
            counts = counts.female
          end

          local outfit_final = {}

          for component, value in pairs(outfit) do
            local offset = offsets[tostring(component)]

            if offset and value[1] > (counts[tostring(component)] - 1) then
              outfit_final[component] = { value[1] + offset, value[2] }
            else
              outfit_final[component] = value
            end
          end

          tClothingStore.setCustomization(character.source, { outfit_final })
        end)
      end
    end
  end

  tClothingStore.startProximityMonitor(character.source)
  menu:show(character.source)
end)

RegisterNetEvent('blrp_clothingstore:server:resolveCloakroom', function(location_id)
  if not location_id then
    return
  end

  local cloakroom_config = config_cloakrooms.locations[location_id]

  if not cloakroom_config then
    return
  end

  local character = exports.blrp_core:character(source)

  TriggerEvent('blrp_clothingstore:server:openCloakroom', cloakroom_config.cloakroom, character.source)
end)
