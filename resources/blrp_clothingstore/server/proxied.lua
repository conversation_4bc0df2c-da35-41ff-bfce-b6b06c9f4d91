pClothingStore.getItemQuantities = function()
  local character = exports.blrp_core:character(source)

  local item_quantities = {}

  for item_id, item_data in pairs(exports.blrp_core:character(source).getAllItems()) do
    if string.match(item_id, ':meta:') then
      item_id = string.sub(item_id, 0, string.find(item_id, ':meta') - 1)
    end

    if not item_quantities[item_id] then
      item_quantities[item_id] = 0
    end

    item_quantities[item_id] = item_quantities[item_id] + item_data.amount
  end

  return item_quantities
end

-- Get clothing item settings
pClothingStore.getItemSettings = function(clothingKey, item, gender)
  local settings = GetClothingItemSettings(clothingKey, item, gender)

  return settings
end

-- Update clothing item settings
pClothingStore.updateItemSettings = function(clothingKey, item, gender, assignedHands)
  local src = source
  local character = exports.blrp_core:character(src)

  if ImagesConfig.require_staff_for_hands and not character.hasGroup('staff') then
    return {success = false, error = 'Permission denied'}
  end

  local promise = promise.new()

  if not gender or not assignedHands then
    return {success = false, error = 'Gender and hands value are required'}
  end

  UpdateClothingItemSettings(clothingKey, item, gender, assignedHands, function(affectedRows)
    character.log('ACTION', string.format('Updated settings for %s item %s (%s): %s', clothingKey, item, gender, assignedHands))
    promise:resolve({success = true, affectedRows = affectedRows})
  end)

  return Citizen.Await(promise)
end