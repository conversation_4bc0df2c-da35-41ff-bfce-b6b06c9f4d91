-- Generate hash from clothing item parameters (stored in model_hash column)
function GenerateItemHash(category, gender, drawable, texture)
  local hashInput = string.format("%s_%s_%s_%s", category, gender, drawable, texture)
  return GetHashKey(hashInput)
end

-- Generate image name from parameters
function GenerateImageName(category, gender, drawable, texture)
  return string.format("%s_%s_%s_%s", category, gender, drawable, texture)
end

-- Generate both hash and name from parameters
function GenerateItemIdentifiers(category, gender, drawable, texture)
  local name = GenerateImageName(category, gender, drawable, texture)
  local hash = GenerateItemHash(category, gender, drawable, texture)
  return hash, name
end

-- Helper function to extract gender from image name
function ExtractGenderFromImageName(imageName)
  if string.find(imageName, "_male_") then
    return "male"
  elseif string.find(imageName, "_female_") then
    return "female"
  else
    return nil
  end
end

-- Helper function to extract parameters from image name
function ExtractParametersFromImageName(imageName)
  local category, gender, drawable, texture = imageName:match("^(.-)_(.-)_(.-)_(.-)$")
  if category and gender and drawable and texture then
    return category, gender, tonumber(drawable), tonumber(texture)
  end
  return nil, nil, nil, nil
end

-- Note: GetProcessedFieldName function removed as it's no longer needed
-- Each row is already gender-specific, so we just use processed_at for all items
-- Note: ResetAllProcessedImages function moved to images_database.lua

-- Reset processed images for a specific clothing type (set processed_at to NULL instead of deleting)
function ResetProcessedImagesForType(clothingType, callback)
  local pattern = clothingType .. '_%'
  MySQL.Async.execute('UPDATE clothing_images_log SET processed_at = NULL, updated_at = NOW() WHERE name LIKE ? AND processed_at IS NOT NULL', {
    pattern
  }, function(affectedRows)
    print(string.format("🗑️ Reset %d processed images for %s", affectedRows, clothingType))
    if callback then callback(affectedRows) end
  end)

  -- Clear name cache
  ClearProcessedImagesCache()
end
