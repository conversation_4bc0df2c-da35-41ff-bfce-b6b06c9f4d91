-- Clean up sessions when player disconnects
AddEventHandler('playerDropped', function(reason)
  local src = source
  if processingSessions[src] then
    print(string.format("🔌 Player %s disconnected during processing session, cleaning up", src))
    processingSessions[src] = nil
  end
end)

-- Reset and process event handler
Register<PERSON><PERSON>vent('blrp_clothing_images:resetAndProcess')
AddEventHandler('blrp_clothing_images:resetAndProcess', function(clothingKey)
  local src = source
  local character = exports.blrp_core:character(src)

  if not character then
    print("❌ No character found for source:", src)
    return
  end

  if not character.hasGroup('staff') then
    character.notify('Permission failure')
    return
  end

  print(string.format("🗑️ Resetting processed images for %s (requested by %s)", clothingKey, character.name))

  -- Reset processed status for this clothing type
  local affectedRows = ResetProcessedImagesForType(clothingKey)

  character.notify(string.format('🗑️ Reset %d processed images for %s', affectedRows, clothingKey))

  -- Start processing after a short delay
  SetTimeout(1000, function()
    character.notify(string.format('🚀 Starting %s image processing...', clothingKey))
    local result = pClothingStore.startProcessing(src, clothingKey)
  end)
end)