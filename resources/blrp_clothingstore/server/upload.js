// Server-side DigitalOcean Spaces Upload Handler
// Using FiveM's built-in HTTP capabilities with S3-compatible API

const crypto = require('crypto');

// Get configuration from convars
function getSpacesConfig() {
    return {
        accessKeyId: <PERSON><PERSON><PERSON><PERSON>('SPACES_ACCESS_KEY_ID', ''),
        secretAccessKey: <PERSON><PERSON><PERSON><PERSON>('SPACES_SECRET_ACCESS_KEY', ''),
        region: GetConvar('SPACES_DEFAULT_REGION', 'nyc3'),
        bucket: Get<PERSON><PERSON><PERSON>('SPACES_BUCKET', 'blrpcdn-test'),
        endpoint: <PERSON><PERSON><PERSON><PERSON>('SPACES_ENDPOINT', 'https://blrpcdn.nyc3.digitaloceanspaces.com'),
        cdnUrl: Get<PERSON>onvar('SPACES_URL', 'https://cdn.blrp.net')
    };
}

// Helper functions for AWS Signature Version 4
function getAmzDate() {
    return new Date().toISOString().replace(/[:\-]|\.\d{3}/g, '');
}

function getDateStamp() {
    return new Date().toISOString().substr(0, 10).replace(/-/g, '');
}

function sha256(data) {
    return crypto.createHash('sha256').update(data).digest('hex');
}

function hmacSha256(key, data) {
    return crypto.createHmac('sha256', key).update(data).digest();
}

function hmacSha256Hex(key, data) {
    return crypto.createHmac('sha256', key).update(data).digest('hex');
}

// Initialize upload system
function initializeUploadSystem() {
    const config = getSpacesConfig();

    if (!config.accessKeyId || !config.secretAccessKey) {
        console.log('❌ [UPLOAD] Missing DigitalOcean Spaces credentials');
        return false;
    }

    return true;
}

// Track upload statistics
const uploadStats = {
    total: 0,
    successful: 0,
    failed: 0
};

// Track chunked uploads
const chunkBuffer = {};

// Convert base64 to buffer
function base64ToBuffer(base64Data) {
    try {
        // Remove data URL prefix if present (data:image/jpeg;base64,)
        const cleanBase64 = base64Data.replace(/^data:image\/[^;]+;base64,/, '');
        return Buffer.from(cleanBase64, 'base64');
    } catch (error) {
        console.log('❌ [UPLOAD] Failed to decode base64:', error.message);
        return null;
    }
}

// Create AWS Signature Version 4 for DigitalOcean Spaces
function createSignature(config, method, objectKey, headers, payload) {
    const amzDate = getAmzDate();
    const dateStamp = getDateStamp();

    // Create canonical request
    const canonicalUri = `/${objectKey}`;
    const canonicalQueryString = '';

    // Sort headers and create canonical headers
    const sortedHeaders = Object.keys(headers).sort().map(key =>
        `${key.toLowerCase()}:${headers[key]}`
    ).join('\n');

    const signedHeaders = Object.keys(headers).sort().map(key =>
        key.toLowerCase()
    ).join(';');

    const payloadHash = sha256(payload);

    const canonicalRequest = [
        method,
        canonicalUri,
        canonicalQueryString,
        sortedHeaders,
        '',
        signedHeaders,
        payloadHash
    ].join('\n');

    // Create string to sign
    const credentialScope = `${dateStamp}/${config.region}/s3/aws4_request`;
    const stringToSign = [
        'AWS4-HMAC-SHA256',
        amzDate,
        credentialScope,
        sha256(canonicalRequest)
    ].join('\n');

    // Create signing key
    const kDate = hmacSha256(`AWS4${config.secretAccessKey}`, dateStamp);
    const kRegion = hmacSha256(kDate, config.region);
    const kService = hmacSha256(kRegion, 's3');
    const kSigning = hmacSha256(kService, 'aws4_request');

    // Create signature
    const signature = hmacSha256Hex(kSigning, stringToSign);

    // Create authorization header
    const authorization = `AWS4-HMAC-SHA256 Credential=${config.accessKeyId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

    return {
        authorization,
        amzDate,
        payloadHash
    };
}

// Upload base64 image to DigitalOcean Spaces using HTTP
function uploadBase64ToSpaces(base64Data, fileName, callback) {
    const config = getSpacesConfig();
    const buffer = base64ToBuffer(base64Data);

    if (!buffer) {
        console.log(`❌ [UPLOAD] Failed to decode base64 data for ${fileName}`);
        if (callback) callback(false, 'Failed to decode base64 data');
        return;
    }

        const objectKey = `clothing-store/${fileName}`;
    const url = `${config.endpoint}/${objectKey}`;

    // Prepare headers
    const hostname = config.endpoint.replace('https://', '').replace('http://', '');
    const headers = {
        'Host': hostname,
        'Content-Type': 'image/webp',
        'Content-Length': buffer.length.toString(),
        'x-amz-acl': 'public-read',
        'x-amz-content-sha256': sha256(buffer)
    };

    // Create AWS signature
    const signature = createSignature(config, 'PUT', objectKey, headers, buffer);

    // Add authentication headers
    headers['Authorization'] = signature.authorization;
    headers['x-amz-date'] = signature.amzDate;

    // Use FiveM's server-side HTTP request
    const requestOptions = {
        method: 'PUT',
        headers: headers,
        body: buffer
    };

    // Try using the global fetch if available, otherwise use a workaround
    try {
        // Use FiveM's built-in HTTP functionality
        const https = require('https');
        const urlParts = new URL(url);

        const options = {
            hostname: urlParts.hostname,
            port: 443,
            path: urlParts.pathname,
            method: 'PUT',
            headers: headers
        };

        const req = https.request(options, (res) => {
            let responseData = '';
            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200 || res.statusCode === 204) {
                    // Use the direct DigitalOcean Spaces URL format
                    const publicUrl = `https://${config.bucket}.${config.region}.digitaloceanspaces.com/${objectKey}`;
                    // console.log(`✅ [UPLOAD] ${fileName} → ${publicUrl}`);
                    if (callback) callback(true, publicUrl);
                } else {
                    // console.log(`❌ [UPLOAD] ${fileName} failed (HTTP ${res.statusCode})`);
                    if (callback) callback(false, `HTTP ${res.statusCode}: ${responseData}`);
                }
            });
        });

        req.on('error', (error) => {
            console.log(`❌ [UPLOAD] ${fileName} failed: ${error.message}`);
            if (callback) callback(false, `Request error: ${error.message}`);
        });

        req.write(buffer);
        req.end();

    } catch (error) {
        console.log(`❌ [UPLOAD] ${fileName} failed: ${error.message}`);
        if (callback) callback(false, `HTTP request failed: ${error.message}`);
    }
}

// Upload file to DigitalOcean Spaces (for future use)
function uploadFileToSpaces(filePath, fileName, callback) {
    console.log(`❌ [UPLOAD] File upload not implemented - use base64 upload instead`);
    if (callback) callback(false, 'File upload not implemented - use base64 upload instead');
}

// Test upload function with sample base64 data
function testUpload() {
    const testBase64 = '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A';
    const testFileName = `test_upload_${Date.now()}.webp`;

    uploadBase64ToSpaces(testBase64, testFileName, (success, result) => {
        if (success) {
            console.log('🎉 [TEST] Upload test PASSED!');
        } else {
            console.log(`💥 [TEST] Upload test FAILED: ${result}`);
        }
    });
}

// Event handler for upload requests from client (file path)
onNet('blrp_clothing_images:uploadImage', (fileName, filePath) => {
    const source = global.source;
    
    uploadStats.total++;
    
    uploadFileToSpaces(filePath, fileName, (success, result) => {
        if (success) {
            uploadStats.successful++;
        } else {
            uploadStats.failed++;
        }
        

    });
});

// Event handler for base64 upload requests from client
onNet('blrp_clothing_images:uploadBase64', (fileName, base64Data) => {
    const source = global.source;

    uploadStats.total++;

    uploadBase64ToSpaces(base64Data, fileName, (success, result) => {
        if (success) {
            uploadStats.successful++;
        } else {
            uploadStats.failed++;
        }


    });
});

// Event handler for chunked base64 upload requests from client
onNet('blrp_clothing_images:uploadBase64Chunk', (fileName, chunkData, chunkIndex, totalChunks) => {
    const source = global.source;

    // console.log(`📦 [CHUNK] Received chunk ${chunkIndex}/${totalChunks} for ${fileName} (${chunkData.length} chars)`);

    // Initialize chunk buffer for this file if it doesn't exist
    if (!chunkBuffer[fileName]) {
        chunkBuffer[fileName] = {
            chunks: {},
            totalChunks: totalChunks,
            receivedChunks: 0
        };
    }

    // Store the chunk
    chunkBuffer[fileName].chunks[chunkIndex] = chunkData;
    chunkBuffer[fileName].receivedChunks++;

    // Check if we have all chunks
    if (chunkBuffer[fileName].receivedChunks === totalChunks) {
        // console.log(`📦 [CHUNK] All chunks received for ${fileName}, reassembling...`);

        // Reassemble the data
        let completeData = '';
        for (let i = 1; i <= totalChunks; i++) {
            completeData += chunkBuffer[fileName].chunks[i];
        }

        // Clean up chunk buffer
        delete chunkBuffer[fileName];

        // console.log(`📦 [CHUNK] Reassembled ${fileName} (${completeData.length} chars total)`);

        // Upload the complete data
        uploadStats.total++;
        uploadBase64ToSpaces(completeData, fileName, (success, result) => {
            if (success) {
                uploadStats.successful++;
            } else {
                uploadStats.failed++;
            }
        });
    }
});



// Initialize on startup
setTimeout(() => {
    initializeUploadSystem()
}, 1000);
