-- Clothing Database Modification Script
-- Usage: modifyclothing <gender> <category> <action> <amount>
-- Example: modifyclothing male jacket increment 3

-- Parse image name to extract components
local function parseImageName(imageName)
    local category, gender, drawable, texture = imageName:match("^(.-)_(.-)_(%d+)_(%d+)$")
    if category and gender and drawable and texture then
        return category, gender, tonumber(drawable), tonumber(texture)
    end
    return nil
end

-- Generate new image name and hash
local function generateNewIdentifiers(category, gender, newDrawable, texture)
    local newName = string.format("%s_%s_%d_%d", category, gender, newDrawable, texture)
    local newHash = GetHashKey(newName)
    return newName, newHash
end

-- Preview changes without applying them
local function previewClothingModification(gender, category, action, amount)
    print(string.format("🔍 PREVIEW: %s %s %s by %d", gender, category, action, amount))
    
    local searchPattern = category .. '\\_' .. gender .. '\\_' .. '%' .. '\\_' .. '%'
    local query = 'SELECT name FROM clothing_images_log WHERE name LIKE ?'
    local result = MySQL.Sync.fetchAll(query, {searchPattern})
    
    if not result or #result == 0 then
        print("❌ No records found matching the criteria")
        return false, 0
    end
    
    print(string.format("📊 Found %d records to modify:", #result))
    
    local validRecords = 0
    local conflicts = {}
    
    for i = 1, math.min(#result, 10) do -- Show first 10 as preview
        local record = result[i]
        local cat, gen, drawable, texture = parseImageName(record.name)
        
        if cat and gen then
            local newDrawable = action == "increment" and (drawable + amount) or (drawable - amount)
            local newName, newHash = generateNewIdentifiers(cat, gen, newDrawable, texture)
            
            print(string.format("  %s → %s", record.name, newName))
            validRecords = validRecords + 1
            
            -- Check for potential conflicts
            local conflictCheck = MySQL.Sync.fetchAll('SELECT name FROM clothing_images_log WHERE name = ?', {newName})
            if conflictCheck and #conflictCheck > 0 then
                table.insert(conflicts, newName)
            end
        end
    end
    
    if #result > 10 then
        print(string.format("  ... and %d more records", #result - 10))
    end
    
    if #conflicts > 0 then
        print("⚠️  WARNING: The following conflicts were detected:")
        for _, conflict in ipairs(conflicts) do
            print("  - " .. conflict .. " already exists")
        end
        return false, validRecords
    end
    
    print(string.format("✅ Preview complete: %d records ready for modification", validRecords))
    return true, validRecords
end

-- Apply clothing modifications to database
local function applyClothingModification(gender, category, action, amount)
    print(string.format("🔄 APPLYING: %s %s %s by %d", gender, category, action, amount))
    
    local searchPattern = category .. '\\_' .. gender .. '\\_' .. '%' .. '\\_' .. '%'
    local query = 'SELECT model_hash, name FROM clothing_images_log WHERE name LIKE ?'
    local result = MySQL.Sync.fetchAll(query, {searchPattern})
    
    if not result or #result == 0 then
        print("❌ No records found to modify")
        return 0
    end
    
    local successCount = 0
    local errorCount = 0
    
    for _, record in ipairs(result) do
        local cat, gen, drawable, texture = parseImageName(record.name)
        
        if cat and gen then
            local newDrawable = action == "increment" and (drawable + amount) or (drawable - amount)
            
            -- Validate new drawable number
            if newDrawable < 0 then
                print(string.format("⚠️  Skipping %s: would result in negative drawable (%d)", record.name, newDrawable))
                errorCount = errorCount + 1
            else
                local newName, newHash = generateNewIdentifiers(cat, gen, newDrawable, texture)
                
                -- Update the record
                local updateQuery = [[
                    UPDATE clothing_images_log 
                    SET model_hash = ?, name = ?, updated_at = NOW() 
                    WHERE model_hash = ?
                ]]
                
                local affectedRows = MySQL.Sync.execute(updateQuery, {newHash, newName, record.model_hash})
                
                if affectedRows > 0 then
                    successCount = successCount + 1
                    if successCount <= 5 then -- Show first 5 updates
                        print(string.format("  ✅ %s → %s", record.name, newName))
                    end
                else
                    print(string.format("  ❌ Failed to update %s", record.name))
                    errorCount = errorCount + 1
                end
            end
        else
            print(string.format("⚠️  Skipping invalid name format: %s", record.name))
            errorCount = errorCount + 1
        end
    end
    
    if successCount > 5 then
        print(string.format("  ... and %d more successful updates", successCount - 5))
    end
    
    print(string.format("📊 Results: %d successful, %d errors", successCount, errorCount))
    return successCount
end

-- Main command handler
RegisterCommand('modifyclothing', function(source, args, rawCommand)
    local character = exports.blrp_core:character(source)
    
    -- Check permissions
    if not character or not character.hasGroup('staff') then
        if character then
            character.notify('❌ Permission denied')
        end
        return
    end
    
    -- Validate arguments
    if #args < 4 then
        character.notify('Usage: modifyclothing <gender> <category> <action> <amount>')
        character.notify('Example: modifyclothing male jacket increment 3')
        return
    end
    
    local gender = args[1]:lower()
    local category = args[2]:lower()
    local action = args[3]:lower()
    local amount = tonumber(args[4])
    
    -- Validate parameters
    if gender ~= 'male' and gender ~= 'female' then
        character.notify('❌ Gender must be "male" or "female"')
        return
    end
    
    if action ~= 'increment' and action ~= 'decrement' then
        character.notify('❌ Action must be "increment" or "decrement"')
        return
    end
    
    if not amount or amount <= 0 then
        character.notify('❌ Amount must be a positive number')
        return
    end
    
    -- Check if category exists in config
    local validCategories = {}
    for clothingKey, config in pairs(ImagesConfig.ClothingItems) do
        table.insert(validCategories, clothingKey)
    end
    
    local categoryExists = false
    for _, validCategory in ipairs(validCategories) do
        if validCategory == category then
            categoryExists = true
            break
        end
    end
    
    if not categoryExists then
        character.notify(string.format('❌ Invalid category. Valid categories: %s', table.concat(validCategories, ', ')))
        return
    end
    
    character.log('ACTION', string.format('[Clothing Modify] Starting %s %s %s %d', gender, category, action, amount))
    
    -- First, preview the changes
    local canProceed, recordCount = previewClothingModification(gender, category, action, amount)
    
    if not canProceed then
        character.notify('❌ Cannot proceed due to conflicts or errors. Check server console.')
        return
    end
    
    if recordCount == 0 then
        character.notify('❌ No records found to modify')
        return
    end
    
    character.notify(string.format('🔍 Preview: %d records will be modified. Check console for details.', recordCount))
    character.notify('⚠️  This action cannot be undone! Use "confirmmodify" to proceed. You should make a backup of the table clothing_images_log first.')
    
    -- Store the pending operation
    if not character.pendingClothingModify then
        character.pendingClothingModify = {}
    end
    
    character.pendingClothingModify = {
        gender = gender,
        category = category,
        action = action,
        amount = amount,
        timestamp = GetGameTimer()
    }
    
end, false)

-- Confirmation command
RegisterCommand('confirmmodify', function(source, args, rawCommand)
    local character = exports.blrp_core:character(source)
    
    if not character or not character.hasGroup('staff') then
        if character then
            character.notify('❌ Permission denied')
        end
        return
    end
    
    if not character.pendingClothingModify then
        character.notify('❌ No pending modification found')
        return
    end
    
    local pending = character.pendingClothingModify
    
    -- Check if the pending operation is still valid (within 5 minutes)
    if GetGameTimer() - pending.timestamp > 300000 then
        character.pendingClothingModify = nil
        character.notify('❌ Pending modification expired. Please run modifyclothing again.')
        return
    end
    
    character.notify('🔄 Applying clothing modifications...')
    
    local successCount = applyClothingModification(pending.gender, pending.category, pending.action, pending.amount)
    
    character.log('ACTION', string.format('[Clothing Modify] Completed %s %s %s %d - %d records modified', 
        pending.gender, pending.category, pending.action, pending.amount, successCount))
    
    character.notify(string.format('✅ Modified %d clothing records', successCount))
    
    -- Clear the pending operation
    character.pendingClothingModify = nil
    
end, false)
