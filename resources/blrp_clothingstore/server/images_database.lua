-- Simple cache tables
local processedImagesCache = {}
local processedNamesLookup = {}
local itemSettingsCache = {}
local categorySettingsCache = {}

-- Initialize processed names cache on resource start
local function InitializeProcessedNamesCache()
  print("🔄 Loading processed images cache...")
  local result = MySQL.Sync.fetchAll('SELECT name, processed_at, assigned_hands FROM clothing_images_log', {})

  processedNamesLookup = {}
  if result then
    for i = 1, #result do
      if result[i].processed_at then
        processedNamesLookup[result[i].name] = true
      end

      if result[i].assigned_hands then
        -- For each setting, replace last _x or _xx with "" to get the clothing key without the variation
        local cacheKey = result[i].name:gsub("_%d+$", "")

        itemSettingsCache[cacheKey] = {
          assigned_hands = result[i].assigned_hands,
        }
      end
    end
    print(string.format("✅ Loaded %d processed images into cache", #result))
  end
end

-- Call initialization when resource starts
CreateThread(function()
  InitializeProcessedNamesCache()
end)

function SetItemUnprocessed(clothingKey, item, gender)
  for name, processed in pairs(processedNamesLookup) do
    if string.match(name, clothingKey .. "_" .. gender .. "_" .. item .. "_%d+") then
      processedNamesLookup[name] = nil
    end
  end
end

-- Function to clear processed images cache (for external use)
function ClearProcessedImagesCache()
  processedNamesLookup = {}
  processedImagesCache = {}
  print("🗑️ Cleared processed images cache")
end

-- Get all processed images with assigned hands data (name-based)
function GetAllProcessedImages()
  if processedImagesCache.data then
    return processedImagesCache.processedImages, processedImagesCache.rawData
  end

  local result = MySQL.Sync.fetchAll([[
        SELECT name, assigned_hands FROM clothing_images_log
        WHERE processed_at IS NOT NULL
    ]], {})

  local processedImages = {}
  if result then
    for i = 1, #result do
      table.insert(processedImages, result[i].name)
    end
  end

  -- Cache the result
  processedImagesCache.data = true
  processedImagesCache.processedImages = processedImages
  processedImagesCache.rawData = result

  return processedImages, result
end

-- Get all processed images with assigned hands data as a lookup table (name-based)
function GetAllProcessedImagesWithHands()
  local processedImages, rawData = GetAllProcessedImages()
  local handsLookup = {}

  if rawData then
    for i = 1, #rawData do
      local row = rawData[i]
      handsLookup[row.name] = row.assigned_hands
    end
  end

  return processedImages, handsLookup
end



-- Check if an image has been processed (using fast lookup cache)
function IsImageProcessedByName(imageName)
  return processedNamesLookup[imageName] == true
end



-- Mark image as processed (name-based only)
function MarkImageProcessedByName(imageName)
  -- Extract parameters from name to generate hash for database compatibility
  local category, gender, drawable, texture = ExtractParametersFromImageName(imageName)
  if category and gender and drawable and texture then
    local itemHash = GenerateItemHash(category, gender, drawable, texture)

    local query = 'INSERT INTO clothing_images_log (model_hash, name, processed_at) VALUES (?, ?, NOW()) ON DUPLICATE KEY UPDATE processed_at = NOW(), updated_at = NOW()'

    local affectedRows = MySQL.Sync.execute(query, {
      itemHash,
      imageName
    })

    -- Update the processed names cache
    if affectedRows > 0 then
      processedNamesLookup[imageName] = true
      --print(string.format("✅ Marked %s as processed", imageName))
    else
      print(string.format("❌ Failed to mark %s as processed", imageName))
    end
  else
    print(string.format("❌ Could not extract parameters from image name: %s", imageName))
  end
end

-- Reset processed images for a specific clothing type and gender
function ResetProcessedImagesForTypeAndGender(clothingType, gender)
  local pattern = clothingType .. '_' .. gender .. '_%'

  local query = 'UPDATE clothing_images_log SET processed_at = NULL, updated_at = NOW() WHERE name LIKE ? AND processed_at IS NOT NULL'

  local affectedRows = MySQL.Sync.execute(query, {
    pattern
  })

  -- Remove affected items from cache immediately
  for imageName, _ in pairs(processedNamesLookup) do
    if string.match(imageName, pattern:gsub('%%', '.*')) then
      processedNamesLookup[imageName] = nil
    end
  end

  -- Clear other caches
  processedImagesCache = {}

  print(string.format("🗑️ Reset %d processed %s images for %s", affectedRows, gender, clothingType))
  return affectedRows
end

-- Reset all processed images
function ResetAllProcessedImages()
  local query = 'UPDATE clothing_images_log SET processed_at = NULL, updated_at = NOW() WHERE processed_at IS NOT NULL'

  local affectedRows = MySQL.Sync.execute(query, {})

  -- Clear all caches completely
  processedNamesLookup = {}
  processedImagesCache = {}
  itemSettingsCache = {}
  categorySettingsCache = {}

  print(string.format("🗑️ Reset %d processed images for all clothing types", affectedRows))
  return affectedRows
end

-- Reset processed images for a specific clothing type (both genders)
function ResetProcessedImagesForType(clothingType)
  local pattern = clothingType .. '_%'
  local query = 'UPDATE clothing_images_log SET processed_at = NULL, updated_at = NOW() WHERE name LIKE ? AND processed_at IS NOT NULL'

  local affectedRows = MySQL.Sync.execute(query, {
    pattern
  })

  -- Remove affected items from cache immediately
  for imageName, _ in pairs(processedNamesLookup) do
    if string.match(imageName, pattern:gsub('%%', '.*')) then
      processedNamesLookup[imageName] = nil
    end
  end

  -- Clear other caches
  processedImagesCache = {}

  -- Clear category cache for this type
  local cacheKey = "category_" .. clothingType
  categorySettingsCache[cacheKey] = nil

  print(string.format("🗑️ Reset %d processed images for %s", affectedRows, clothingType))
  return affectedRows
end

-- Get clothing item settings (hands assignments and custom name)
function GetClothingItemSettings(category, item, gender)
    local cacheKey = category .. "_" .. gender .."_" .. item

    if itemSettingsCache[cacheKey] then
        return itemSettingsCache[cacheKey]
    end

  --print('Attempted to get settings for ' .. cacheKey .. ' but not found in cache itemSettingsCache')
  return nil
end

-- Update clothing item settings
function UpdateClothingItemSettings(clothingKey, item, gender, assignedHands, callback)
    -- Generate hash and name for the exact specific item (variation 0 only)
    local itemHash = GenerateItemHash(clothingKey, gender, item, 0)
    local exactImageName = string.format("%s_%s_%s_0", clothingKey, gender, item)

    print(string.format("🔧 Updating hands for EXACT item only: %s (hash: %s) = %s", exactImageName, itemHash, assignedHands))

    -- Insert or update ONLY the exact specific record using hash as primary identifier
    MySQL.Async.execute([[
        INSERT INTO clothing_images_log (model_hash, name, assigned_hands)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE
            assigned_hands = VALUES(assigned_hands),
            updated_at = NOW()
    ]], {
        itemHash,
        exactImageName,
        assignedHands
    }, function(affectedRows)
        print(string.format("✅ Updated exactly 1 record: %s", exactImageName))

        -- Clear caches to ensure fresh data on next request
        local cacheKey = "category_" .. clothingKey
        categorySettingsCache[cacheKey] = nil

        -- Also clear item settings cache for this specific item
        local itemCacheKey = "item_" .. clothingKey .. "_" .. item
        itemSettingsCache[itemCacheKey] = nil

        if callback then callback(affectedRows) end
    end)
end

