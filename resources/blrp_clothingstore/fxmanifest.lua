fx_version 'cerulean'

game 'gta5'

ui_page "nui/index.html"

shared_scripts {
  'shared/*.js',
  'shared/*.lua',

  'config_overlays.lua',
  'config_wardrobes.lua',
  'config_skinshops.lua',
  'config_blacklist.lua',
  'config_versioning.lua',
  'config_images.lua',

  'blacklists/*.lua',
}

client_scripts {
  '@blrp_rpc/tunnel/client.lua',
  '@blrp_rpc/proxy/client.lua',

  '@bad_menu/lua/api.lua',

  '@PolyZone/client.lua',
  '@PolyZone/BoxZone.lua',

  -- Load order need to be manually defined here because _images is also an entry point and needs to load before _images.lua
  'client/_main.lua',
  'client/_images.lua',
  'client/camera.lua',
  'client/hoody.lua',
  'client/images_helpers.lua',
  'client/images_hotkeys.lua',
  'client/visor.lua',
  'client/zones.lua',
}

server_scripts {
  '@oxmysql/lib/MySQL.lua',

  '@blrp_rpc/tunnel/server.lua',
  '@blrp_rpc/proxy/server.lua',

  '@bad_menu/lua/api.lua',

  'config_cloakrooms.lua',
  'cloakrooms/*.lua',

  'config_modelmenu.lua',

  -- Load order need to be manually defined here because _images is also an entry point and needs to load before _images.lua
  'server/_main.lua',
  'server/_images.lua',
  'server/hoody.lua',
  'server/images_database.lua',
  'server/images_events.lua',
  'server/images_helpers.lua',
  'server/modelmenu.lua',
  'server/offset_calculator.lua',
  'server/proxied.lua',
  'server/visor.lua',

  'server/upload.js' -- Uploads images for clothing store
}

files {
  'nui/index.html',
  'nui/style.css',
  'nui/js/main.js',
}
