// Clothing Store Main JavaScript
let category_options = {};
let category_names = {}; // Store category ID to name mapping
let clothing_category_mappings = {}; // Store clothing image category mappings
let popup_promise = null;
let require_confirmation = null;
let interfaceShowing = false;
let currentGender = 'male'; // Will be set when interface opens
let selectedCategory = null;
let selectedItem = null;
let hasDragged = false; // Global variable to track drag state
let currentPage = 1;
let itemsPerPage = 50; // Reduced from 100 to 50 for better performance
let totalItems = 0;
let totalPages = 0;
let categorySettings = {};
let contextMenuTarget = null;
let leftPanelEnabled = false; // Controls whether left panel shows when clicking categories

// Performance optimization variables
let imageObserver = null;
let loadedImages = new Set(); // Cache for successfully loaded images
let failedImages = new Set(); // Cache for failed images
let imageLoadQueue = []; // Queue for batch loading images

// Template rendering function
function renderTemplate(props) {
    return function(tok, i) {
        return (i % 2) ? props[tok] : tok;
    };
}

// Get icon for category name
function getCategoryIcon(categoryName) {
    const name = categoryName.toLowerCase();

    // Map category names to FontAwesome icons
    if (name.includes('hat') || name.includes('cap') || name.includes('helmet') || name.includes('headgear')) {
        return 'fa-solid fa-hat-cowboy';
    } else if (name.includes('hair') || name.includes('hairstyle')) {
        return 'fa-solid fa-scissors';
    } else if (name.includes('mask') || name.includes('face')) {
        return 'fa-solid fa-mask';
    } else if (name.includes('top') || name.includes('shirt') || name.includes('jacket') || name.includes('coat') || name.includes('torso')) {
        return 'fa-solid fa-shirt';
    } else if (name.includes('undershirt') || name.includes('vest') || name.includes('tank')) {
        return 'fa-solid fa-vest';
    } else if (name.includes('leg') || name.includes('pant') || name.includes('jean') || name.includes('trouser')) {
        return 'fa-solid fa-socks';
    } else if (name.includes('shoe') || name.includes('boot') || name.includes('sneaker') || name.includes('feet')) {
        return 'fa-solid fa-shoe-prints';
    } else if (name.includes('bag') || name.includes('backpack') || name.includes('purse')) {
        return 'fa-solid fa-bag-shopping';
    } else if (name.includes('chain') || name.includes('necklace') || name.includes('jewelry') || name.includes('neck')) {
        return 'fa-solid fa-gem';
    } else if (name.includes('watch') || name.includes('bracelet') || name.includes('wrist')) {
        return 'fa-regular fa-clock';
    } else if (name.includes('glass') || name.includes('sunglasses') || name.includes('eyewear')) {
        return 'fa-solid fa-glasses';
    } else if (name.includes('ear') || name.includes('earring')) {
        return 'fa-solid fa-ear-listen';
    } else if (name.includes('glove') || name.includes('hand')) {
        return 'fa-regular fa-hand';
    } else if (name.includes('armor') || name.includes('kevlar') || name.includes('bulletproof')) {
        return 'fa-solid fa-shield';
    } else if (name.includes('decal') || name.includes('badge') || name.includes('patch')) {
        return 'fa-solid fa-certificate';
    } else {
        // Default icon for unknown categories
        return 'fa-solid fa-tshirt';
    }
}

// Update category inputs and send to server
function updateCategoryInputs(selector) {
    let category_inputs = {
        category: 0,
        drawable: 0,
        texture: 0,
    }

    selector.find('input.form-input').each((key, input) => {
        input = $(input);

        let category = selector.data('category');
        let scope = input.data('scope');
        let drawable_key = selector.data('drawable-key');

        category_inputs.category = category

        if(scope == 'drawable') {
            category_inputs.drawable = category_options[category].drawables[drawable_key]
            input.val(category_options[category].drawables[drawable_key])
        } else {
            category_inputs.texture = category_options[category].textures[category_options[category].drawables[drawable_key]][selector.data('texture-key')]
            input.val(category_options[category].textures[category_options[category].drawables[drawable_key]][selector.data('texture-key')])
        }
    });

    $.post('https://blrp_clothingstore/setDual', JSON.stringify({
        category: category_inputs.category,
        inputs: category_inputs
    }));
}

// Camera rotation variables
var moving = false
var lastOffsetX = 0
var lastOffsetY = 0
var lastScreenX = 0.5 * screen.width
var lastScreenY = 0.5 * screen.height

// Category to section mapping
const categoryMapping = {
    // Top section
    'head': 'top', 'hair': 'top', 'ears': 'top', 'helmet': 'top',
    'glasses': 'top', 'mask': 'top', 'neck': 'top',
    // Torso section
    'shirt': 'torso', 'jacket': 'torso', 'hands': 'torso', 'armor': 'torso',
    'bags': 'torso', 'torso': 'torso', 'undershirt': 'torso', 'vest': 'torso',
    // Accessories section
    'watch': 'accessories', 'bracelet': 'accessories', 'decals': 'accessories',
    'chain': 'accessories', 'jewelry': 'accessories',
    // Bottom section
    'legs': 'bottom', 'shoes': 'bottom', 'pants': 'bottom', 'boots': 'bottom'
};

// Get section for category name
function getCategorySection(categoryName) {
    const name = categoryName.toLowerCase();

    // Check for exact matches first
    for (const [key, section] of Object.entries(categoryMapping)) {
        if (name.includes(key)) {
            return section;
        }
    }

    // Default to torso if no match found
    return 'torso';
}

// Generate image URL for clothing item
function generateImageUrl(category, gender, item, variation) {
    // Get the correct category name for the image URL
    const categoryName = clothing_category_mappings[category] || category;
    const url = `https://blrpcdn-test.nyc3.digitaloceanspaces.com/clothing-store/${categoryName}_${gender}_${item}_${variation}.webp`;

    // Debug logging for jacket category
    if (category === '11' || category === 11) {
        console.log(`[JACKET DEBUG] generateImageUrl: category=${category}, categoryName=${categoryName}, gender=${gender}, item=${item}, variation=${variation}, url=${url}`);
    }

    return url;
}

// Get the first available variation for an item
function getFirstAvailableVariation(category, item) {
    const categoryOptions = category_options[category];
    if (!categoryOptions || !categoryOptions.textures || !categoryOptions.textures[item]) {
        // Debug logging for jacket category
        if (category === '11' || category === 11) {
            console.log(`[JACKET DEBUG] getFirstAvailableVariation: No data for category=${category}, item=${item}, returning 0`);
        }
        return 0; // Fallback to 0 if no data available
    }

    const availableTextures = categoryOptions.textures[item];
    if (availableTextures && availableTextures.length > 0) {
        const firstVariation = availableTextures[0];
        // Debug logging for jacket category
        if (category === '11' || category === 11) {
            console.log(`[JACKET DEBUG] getFirstAvailableVariation: category=${category}, item=${item}, availableTextures=${JSON.stringify(availableTextures)}, firstVariation=${firstVariation}`);
        }
        return firstVariation; // Return the first available texture
    }

    // Debug logging for jacket category
    if (category === '11' || category === 11) {
        console.log(`[JACKET DEBUG] getFirstAvailableVariation: No textures for category=${category}, item=${item}, returning 0`);
    }
    return 0; // Fallback to 0 if no textures available
}

// Initialize Intersection Observer for performance
function initializeImageObserver() {
    if ('IntersectionObserver' in window) {
        imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const imgElement = entry.target;
                    const container = imgElement.closest('.item-slot, .variation-slot');
                    const fallbackElement = container?.querySelector('.item-fallback-icon, .variation-fallback-icon');
                    const item = container?.dataset.item;
                    loadImageWhenVisible(imgElement, fallbackElement, item);
                    imageObserver.unobserve(imgElement);
                }
            });
        }, {
            rootMargin: '50px', // Start loading 50px before visible
            threshold: 0.1
        });
    }
}

// Optimized lazy load with caching and intersection observer
function lazyLoadImage(imgElement, fallbackElement, item) {
    // Clean up any existing custom icons
    const container = fallbackElement.parentNode;
    const existingCustomIcon = container.querySelector('.custom-x-icon');
    if (existingCustomIcon) {
        existingCustomIcon.remove();
    }

    // Check if item is -1 (nothing equipped)
    if (item == -1) {
        imgElement.style.display = 'none';
        fallbackElement.style.display = 'none';

        // Create custom SVG X icon
        const svgIcon = document.createElement('div');
        svgIcon.innerHTML = `
            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line x1="12" y1="12" x2="36" y2="36" stroke="#e01e5a" stroke-width="4" stroke-linecap="round"/>
                <line x1="36" y1="12" x2="12" y2="36" stroke="#e01e5a" stroke-width="4" stroke-linecap="round"/>
            </svg>
        `;
        svgIcon.style.display = 'flex';
        svgIcon.style.alignItems = 'center';
        svgIcon.style.justifyContent = 'center';
        svgIcon.className = 'custom-x-icon';

        // Insert the SVG into the container
        container.appendChild(svgIcon);
        return;
    }

    const src = imgElement.dataset.src;
    if (!src) return;

    // Check if image already failed to load
    if (failedImages.has(src)) {
        imgElement.style.display = 'none';
        fallbackElement.style.display = 'block';
        imgElement.classList.add('error');
        return;
    }

    // Check if image is already loaded in cache
    if (loadedImages.has(src)) {
        imgElement.src = src;
        imgElement.style.display = 'block';
        fallbackElement.style.display = 'none';
        imgElement.classList.add('loaded');
        return;
    }

    // For now, let's use immediate loading to ensure images work
    // TODO: Re-enable intersection observer after testing
    loadImageWhenVisible(imgElement, fallbackElement, item);
}

// Load image when it becomes visible (simplified for debugging)
function loadImageWhenVisible(imgElement, fallbackElement, item) {
    const src = imgElement.dataset.src;
    if (!src) {
        console.log('No src found for image element:', imgElement);
        return;
    }

    console.log('Loading image:', src);

    // Simple immediate loading for debugging
    const img = new Image();

    img.onload = function() {
        console.log('Image loaded successfully:', src);
        loadedImages.add(src);
        imgElement.src = src;
        imgElement.style.display = 'block';
        if (fallbackElement) fallbackElement.style.display = 'none';
        imgElement.classList.add('loaded');
    };

    img.onerror = function() {
        console.log('Image failed to load:', src);
        failedImages.add(src);
        imgElement.style.display = 'none';
        if (fallbackElement) fallbackElement.style.display = 'block';
        imgElement.classList.add('error');
    };

    img.src = src;
}

// Show items for selected category
function showItemsForCategory(category, page = 1) {
    // Check if left panel is enabled
    if (!leftPanelEnabled) {
        console.log('Left panel is disabled. Press F8 to enable it.');
        return;
    }

    selectedCategory = category;
    selectedItem = null;
    currentPage = page;

    const leftPanel = $('#left-panel');
    const itemsGrid = $('#items-grid');
    const variationsGrid = $('#variations-grid');
    const panelTitle = $('#left-panel-title');
    const applyHandsButton = $('#apply-hands-button');

    // Hide variations panel when changing category
    $('.variations-panel').remove();

    // Show left panel
    leftPanel.show();

    // Show/hide Apply Hands button based on category
    // Category '11' is jacket according to config_skinshops.lua
    // console.log('Category:', category, 'Type:', typeof category);
    // console.log('Apply Hands Button found:', applyHandsButton.length);

    if (category === '11' || category === 11) {
        // console.log('Showing Apply Hands button for jacket category');
        applyHandsButton.removeClass('hidden');
    } else {
        // console.log('Hiding Apply Hands button for non-jacket category');
        applyHandsButton.addClass('hidden');
    }

    // Instantly scroll to top of items window to prevent loading all images
    const itemsWindow = $('#items-window');
    if (itemsWindow.length) {
        itemsWindow.scrollTop(0);
    }

    // Check staff permissions and show/hide staff menu
    $.post('https://blrp_clothingstore/checkStaffPermissions', JSON.stringify({}))
        .done(function(response) {
            if (response && response.isStaff) {
                $('#staff-image-menu').show();
                // Load category settings for red dot indicators
            } else {
                $('#staff-image-menu').hide();
            }
        })
        .fail(function(xhr, status, error) {
            $('#staff-image-menu').hide();
        });

    // Clear grids and variations rows
    itemsGrid.empty();
    $('.variations-row').remove();

    // Get category options
    const categoryOptions = category_options[category];
    if (!categoryOptions || !categoryOptions.drawables) {
        $('#pagination-controls').hide();
        return;
    }

    // Calculate pagination
    totalItems = categoryOptions.drawables.length;
    totalPages = Math.ceil(totalItems / itemsPerPage);

    // Ensure current page is valid
    if (currentPage > totalPages) {
        currentPage = totalPages;
    }
    if (currentPage < 1) {
        currentPage = 1;
    }

    // Calculate start and end indices for current page
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, totalItems);

    // Get items for current page
    const pageItems = categoryOptions.drawables.slice(startIndex, endIndex);

    // Generate items for current page with performance optimizations
    const fragment = document.createDocumentFragment();
    const template = $('script[data-template="item-slot"]').text().split(/\$\{(.+?)\}/g);

    pageItems.forEach((drawable, index) => {
        // Use the first available variation instead of hardcoding 0
        const firstVariation = getFirstAvailableVariation(category, drawable);
        const imageUrl = generateImageUrl(category, currentGender, drawable, firstVariation);

        const itemElement = $(template.map(renderTemplate({
            item: drawable,
            item_label: drawable == -1 ? 'None' : drawable,
            category: category,
            image_url: imageUrl
        })).join(''));

        // Add to fragment instead of directly to DOM for better performance
        fragment.appendChild(itemElement[0]);
    });

    // Append all items at once
    itemsGrid[0].appendChild(fragment);

    // Lazy load images after DOM insertion
    itemsGrid.find('.item-image').each(function(index) {
        const img = this;
        const itemSlot = $(this).closest('.item-slot')[0];
        const fallback = itemSlot?.querySelector('.item-fallback-icon');
        const drawable = pageItems[index];

        // console.log('Setting up lazy load for item:', drawable, 'img:', img, 'fallback:', fallback);

        if (img && fallback && drawable !== undefined) {
            lazyLoadImage(img, fallback, drawable);
        }
    });

    // Update pagination controls
    updatePaginationControls();

    // Update red dot indicators after items are rendered
    setTimeout(() => {
        updateRedDotIndicators(category);
    }, 100);
}

// Update pagination controls
function updatePaginationControls() {
    const paginationControls = $('#pagination-controls');
    const pageInfo = $('#page-info');
    const itemsCount = $('#items-count');
    const firstButton = $('#first-page');
    const prevButton = $('#prev-page');
    const nextButton = $('#next-page');
    const lastButton = $('#last-page');

    if (totalPages <= 1) {
        paginationControls.hide();
        return;
    }

    // Show pagination controls
    paginationControls.show();

    // Update page info
    pageInfo.text(`Page ${currentPage} of ${totalPages}`);
    itemsCount.text(`${totalItems} items`);

    // Update button states
    const isFirstPage = currentPage <= 1;
    const isLastPage = currentPage >= totalPages;

    firstButton.prop('disabled', isFirstPage);
    prevButton.prop('disabled', isFirstPage);
    nextButton.prop('disabled', isLastPage);
    lastButton.prop('disabled', isLastPage);

    // Update button hover states based on disabled status
    if (isFirstPage) {
        firstButton.removeClass('hover:bg-dark-100 hover:text-gray-100');
        prevButton.removeClass('hover:bg-dark-100 hover:text-gray-100');
    } else {
        firstButton.addClass('hover:bg-dark-100 hover:text-gray-100');
        prevButton.addClass('hover:bg-dark-100 hover:text-gray-100');
    }

    if (isLastPage) {
        nextButton.removeClass('hover:bg-dark-100 hover:text-gray-100');
        lastButton.removeClass('hover:bg-dark-100 hover:text-gray-100');
    } else {
        nextButton.addClass('hover:bg-dark-100 hover:text-gray-100');
        lastButton.addClass('hover:bg-dark-100 hover:text-gray-100');
    }
}

// Show variations for selected item
function showVariationsForItem(category, item) {
    selectedItem = item;

    // Remove any existing variations panels
    $('.variations-panel').remove();

    // Get category options
    const categoryOptions = category_options[category];
    if (!categoryOptions || !categoryOptions.textures || !categoryOptions.textures[item]) {
        return;
    }

    // Check if there's only one variation - if so, don't show variations panel
    const availableTextures = categoryOptions.textures[item];
    if (availableTextures.length <= 1) {
        // If there's only one variation, just apply it directly without showing the panel
        if (availableTextures.length === 1) {
            const singleTexture = availableTextures[0];
            updateCategoryToItemAndVariation(category, item, singleTexture);
        }
        return;
    }

    // Find the selected item element
    const selectedItemElement = $(`.item-slot[data-item="${item}"][data-category="${category}"]`);
    if (!selectedItemElement.length) return;

    // Generate variations content
    let variationsContent = '';
    categoryOptions.textures[item].forEach((texture, index) => {
        const template = $('script[data-template="variation-slot"]').text().split(/\$\{(.+?)\}/g);
        const imageUrl = generateImageUrl(category, currentGender, item, texture);

        variationsContent += template.map(renderTemplate({
            variation: texture,
            variation_label: texture == -1 ? 'None' : texture,
            item: item,
            category: category,
            image_url: imageUrl
        })).join('');
    });

    // Create floating variations panel
    const variationsPanelTemplate = $('script[data-template="variations-panel"]').text().split(/\$\{(.+?)\}/g);
    const variationsPanelElement = $(variationsPanelTemplate.map(renderTemplate({
        item: item,
        category: category,
        variations_content: variationsContent
    })).join(''));

    // Add to body instead of inserting into grid
    $('body').append(variationsPanelElement);

    // Position the panel next to the selected item
    const itemRect = selectedItemElement[0].getBoundingClientRect();
    const leftPanelRect = $('#left-panel')[0].getBoundingClientRect();

    // Position to the right of the left panel
    variationsPanelElement.css({
        position: 'fixed',
        left: leftPanelRect.right + 10 + 'px',
        top: itemRect.top + 'px',
        zIndex: 1000
    });

    // Initialize lazy loading for variation images
    variationsPanelElement.find('.variation-image').each(function() {
        const img = this;
        const fallback = $(this).siblings('.variation-fallback-icon')[0];
        if (img && fallback) {
            const texture = $(this).closest('.variation-slot').data('variation');
            lazyLoadImage(img, fallback, texture);
        }
    });
}

// Hide the left panel
function hideLeftPanel() {
    const leftPanel = $('#left-panel');

    // Hide left panel
    leftPanel.hide();

    // Hide pagination controls
    $('#pagination-controls').hide();

    // Remove any variations panels
    $('.variations-panel').remove();

    // Clear selections
    $('.clickable-category').removeClass('selected');
    $('.item-slot').removeClass('selected');
    $('.variation-slot').removeClass('selected');
    $('.item-slot .selected-indicator').css('opacity', '0');
    $('.variation-slot .selected-indicator').css('opacity', '0');
    $('.variation-slot .active-indicator').css('opacity', '0');

    // Reset pagination state
    currentPage = 1;
    totalItems = 0;
    totalPages = 0;

    selectedCategory = null;
    selectedItem = null;
}

// Reset selected item or variation
function resetSelectedItemOrVariation() {
    console.log('resetSelectedItemOrVariation called');

    // First check if we have contextMenuTarget (from right-click)
    if (contextMenuTarget) {
        const category = contextMenuTarget.category;
        const item = contextMenuTarget.item;
        const variation = contextMenuTarget.variation;

        console.log('Using context menu target:', { category, item, variation });

        $.post('https://blrp_clothingstore/resetSelectedImage', JSON.stringify({
            category: category,
            item: item,
            variation: variation || null // null means all variations for this item
        }))
        .done(function(response) {
            console.log('Reset response:', response);
        })
        .fail(function(xhr, status, error) {
            console.error('Reset failed:', error);
        });
        return;
    }

    // Fallback to checking selected elements (for staff dropdown menu)
    const selectedVariation = $('.variation-slot.selected');
    const selectedItemElement = $('.item-slot.selected');

    console.log('Selected variations:', selectedVariation.length);
    console.log('Selected items:', selectedItemElement.length);

    if (selectedVariation.length > 0) {
        // Reset specific variation
        const variation = selectedVariation.data('variation');
        const item = selectedVariation.data('item');
        const category = selectedVariation.data('category');

        console.log('Resetting variation:', { category, item, variation });

        $.post('https://blrp_clothingstore/resetSelectedImage', JSON.stringify({
            category: category,
            item: item,
            variation: variation
        }))
        .done(function(response) {
            console.log('Reset variation response:', response);
        })
        .fail(function(xhr, status, error) {
            console.error('Reset variation failed:', error);
        });
    } else if (selectedItemElement.length > 0) {
        // Reset all variations for this item
        const item = selectedItemElement.data('item');
        const category = selectedItemElement.data('category');

        console.log('Resetting item:', { category, item });

        $.post('https://blrp_clothingstore/resetSelectedImage', JSON.stringify({
            category: category,
            item: item,
            variation: null // null means all variations for this item
        }))
        .done(function(response) {
            console.log('Reset item response:', response);
        })
        .fail(function(xhr, status, error) {
            console.error('Reset item failed:', error);
        });
    } else {
        console.log('Nothing selected - please select an item or variation to reset');
    }
}

// Update category to specific item
function updateCategoryToItem(category, item) {
    const categoryElement = $(`.category[data-category="${category}"]`);
    if (!categoryElement.length) return;

    const categoryOptions = category_options[category];
    if (!categoryOptions || !categoryOptions.drawables) return;

    // Find the item key for this drawable
    const itemKey = categoryOptions.drawables.findIndex(drawable => drawable == item);
    if (itemKey === -1) return;

    // Update the category element
    categoryElement.data('drawable-key', itemKey);
    categoryElement.data('texture-key', 0);

    // Update the input values
    categoryElement.find('input[data-scope="drawable"]').val(item);
    categoryElement.find('input[data-scope="texture"]').val(categoryOptions.textures[item] ? categoryOptions.textures[item][0] : 0);

    // Send update to server
    updateCategoryInputs(categoryElement);
}

// Update category to specific item and variation
function updateCategoryToItemAndVariation(category, item, variation) {
    const categoryElement = $(`.category[data-category="${category}"]`);
    if (!categoryElement.length) return;

    const categoryOptions = category_options[category];
    if (!categoryOptions || !categoryOptions.drawables || !categoryOptions.textures[item]) return;

    // Find the item key for this drawable
    const itemKey = categoryOptions.drawables.findIndex(drawable => drawable == item);
    if (itemKey === -1) return;

    // Find the texture key for this variation
    const textureKey = categoryOptions.textures[item].findIndex(texture => texture == variation);
    if (textureKey === -1) return;

    // Update the category element
    categoryElement.data('drawable-key', itemKey);
    categoryElement.data('texture-key', textureKey);

    // Update the input values
    categoryElement.find('input[data-scope="drawable"]').val(item);
    categoryElement.find('input[data-scope="texture"]').val(variation);

    // Send update to server
    updateCategoryInputs(categoryElement);
}

// Update left panel selection to match right panel item
function updateLeftPanelSelection(category, item) {
    // Only update if the left panel is showing and the category matches
    if (!selectedCategory || selectedCategory != category) return;

    // Remove selection from all items
    $('.item-slot').removeClass('selected');
    $('.item-slot .selected-indicator').css('opacity', '0');

    // Find and select the matching item
    const targetItem = $(`.item-slot[data-category="${category}"][data-item="${item}"]`);
    if (targetItem.length) {
        targetItem.addClass('selected');
        targetItem.find('.selected-indicator').css('opacity', '1');

        // Update global selected item
        selectedItem = item;

        // Scroll to the selected item if it's not visible
        const itemsWindow = $('#items-window');
        const itemOffset = targetItem.offset();
        const windowOffset = itemsWindow.offset();

        if (itemOffset && windowOffset) {
            const relativeTop = itemOffset.top - windowOffset.top;
            const windowHeight = itemsWindow.height();

            // If item is not visible, scroll to it
            if (relativeTop < 0 || relativeTop > windowHeight - targetItem.height()) {
                const scrollTop = itemsWindow.scrollTop() + relativeTop - (windowHeight / 2);
                itemsWindow.scrollTop(Math.max(0, scrollTop));
            }
        }
    }
}

// Update variations panel selection to match right panel texture
function updateVariationsPanelSelection(category, item, variation) {
    // Only update if variations panel is showing for this item
    const variationsPanel = $(`.variations-panel[data-category="${category}"][data-item="${item}"]`);
    if (!variationsPanel.length) return;

    // Remove selection from all variations
    $('.variation-slot').removeClass('selected');
    $('.variation-slot .selected-indicator').css('opacity', '0');
    $('.variation-slot .active-indicator').css('opacity', '0');

    // Find and select the matching variation
    const targetVariation = $(`.variation-slot[data-category="${category}"][data-item="${item}"][data-variation="${variation}"]`);
    if (targetVariation.length) {
        targetVariation.addClass('selected');
        targetVariation.find('.selected-indicator').css('opacity', '1');
        targetVariation.find('.active-indicator').css('opacity', '1');
    }
}

// Initialize event handlers
function initializeEventHandlers() {
    // Section toggle functionality - only one panel open at a time
    $(document).on('click', '.section-header', function() {
        const clickedSection = $(this).closest('.section');
        const clickedContent = clickedSection.find('.section-content');
        const clickedToggle = clickedSection.find('.section-toggle');

        // Close all other sections
        $('.section').not(clickedSection).each(function() {
            $(this).find('.section-content').addClass('collapsed');
            $(this).find('.section-toggle').addClass('collapsed');
        });

        // Toggle the clicked section
        if (clickedContent.hasClass('collapsed')) {
            clickedContent.removeClass('collapsed');
            clickedToggle.removeClass('collapsed');
        } else {
            clickedContent.addClass('collapsed');
            clickedToggle.addClass('collapsed');
        }
    });
    // Input validation for number inputs
    $(document).on('keypress', '.form-input', function(e) {
        return
            e.metaKey || // cmd/ctrl
            e.which <= 0 || // arrow keys
            e.which == 8 || // delete key
            /[0-9]/.test(String.fromCharCode(e.which)); // numbers
    }).on('change', '.form-input', function(e) {
        let value = $(this).val();

        if(!/[\-0-9]/.test(value)) {
            $(this).val($(this).data('value-cached'));
            return false;
        }

        value = parseInt(value);
        let category_selector = $($(this).closest('.category')[0]);
        let scope = $(this).data('scope');
        let category = category_selector.data('category');
        let drawable_key = category_selector.data('drawable-key');

        if(scope == 'drawable') {
            let item_key = Object.keys(category_options[category].drawables).find(k => category_options[category].drawables[k] === value);

            if(item_key === null || item_key === undefined) {
                $(this).val($(this).data('value-cached'));
                return false;
            }

            category_selector.data('drawable-key', item_key)
        } else if(scope == 'texture') {
            let texture_key = Object.keys(category_options[category].textures[category_options[category].drawables[drawable_key]]).find(k => category_options[category].textures[category_options[category].drawables[drawable_key]][k] === value);

            if(texture_key === null || texture_key === undefined) {
                $(this).val($(this).data('value-cached'));
                return false;
            }

            category_selector.data('texture-key', texture_key)
        }

        updateCategoryInputs(category_selector)
    }).on('focusin', '.form-input', function(){
        $(this).data('value-cached', $(this).val());
    });

    // Button click handlers for navigation
    $(document).on('click', 'button[data-direction]', function(e) {
        let direction = $(this).data('direction');
        let category_selector = $($(this).closest('.category')[0]);
        let scope = $(this).data('scope');
        let key = category_selector.data(scope + '-key');
        let category = category_selector.data('category');

        if(direction == '+') {
            key = parseInt(key) + 1
        } else {
            key = parseInt(key) - 1
        }

        if(scope == 'drawable') {
            let length = category_options[category].drawables.length

            if(key < 0) {
                key = (length - 1)
            } else if(key == length) {
                key = 0
            }

            category_selector.data('drawable-key', key)
            category_selector.data('texture-key', 0)
            updateCategoryInputs(category_selector)

            // Update left panel selection to match the new item
            const newItem = category_options[category].drawables[key];
            updateLeftPanelSelection(category, newItem);

            // If variations panel is open for this item, close it since we changed items
            $('.variations-panel').remove();
        } else {
            let drawable_id = category_options[category].drawables[category_selector.data('drawable-key')];
            let length = category_options[category].textures[drawable_id].length

            if(key < 0) {
                key = (length - 1)
            } else if(key == length) {
                key = 0
            }

            category_selector.data('texture-key', key)
            updateCategoryInputs(category_selector)

            // Update variations panel selection to match the new texture
            const newVariation = category_options[category].textures[drawable_id][key];
            updateVariationsPanelSelection(category, drawable_id, newVariation);
        }
    });

    // Camera controls
    $(document).on('click', 'button.camera-btn', function(e) {
        // Remove active class from all camera buttons
        $('button.camera-btn').removeClass('active');

        // Add active class to clicked button
        $(this).addClass('active');

        $.post('https://blrp_clothingstore/updateCameraView', JSON.stringify({
            view: $(this).data('view'),
        }));
    });

    // Remove button
    $(document).on('click', 'button.btn-remove', function(e) {
        e.preventDefault();
        e.stopPropagation();

        let category_selector = $($(this).closest('.category')[0]);
        let category = category_selector.data('category');

        category_selector.data('drawable-key', 0);
        category_selector.data('texture-key', 0);
        category_selector.find('input.form-input').val(0);

        // Send remove command with category
        $.post('https://blrp_clothingstore/removeClothingItem', JSON.stringify({
            category: category
        }));
    });

    // Popup buttons
    $(document).on('click', 'button.popup-btn[data-option]', function(e) {
        e.preventDefault();

        if(popup_promise) {
            popup_promise($(this).data('option') == 'yes');
        }
    });

    // Menu dropdown functionality
    $(document).on('click', '.menu-btn', function(e) {
        e.stopPropagation();
        const dropdown = $(this).siblings('.menu-dropdown');

        // Close all other dropdowns
        $('.menu-dropdown').not(dropdown).removeClass('show').addClass('hidden');

        // Toggle this dropdown
        dropdown.toggleClass('hidden show');
    });

    // Close dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.menu-dropdown, .menu-btn').length) {
            $('.menu-dropdown').removeClass('show').addClass('hidden');
        }
    });

    // Menu item actions
    $(document).on('click', '.menu-item', function(e) {
        e.preventDefault();
        const action = $(this).data('action');

        // Close dropdown
        $('.menu-dropdown').removeClass('show').addClass('hidden');

        if (action === 'undress') {
            // Send undress command to game
            $.post('https://blrp_clothingstore/undress', JSON.stringify({}));
        }
    });

    // Wardrobe button
    $(document).on('click', '.wardrobe-btn', function(e) {
        e.preventDefault();

        // Send command to close clothing store and open wardrobe
        $.post('https://blrp_clothingstore/openWardrobe', JSON.stringify({}));
    });



    // Category click handler for left panel
    $(document).on('click', '.clickable-category', function(e) {
        // Don't trigger if clicking on buttons or inputs
        if ($(e.target).is('button, input, .btn-remove, .btn-remove *')) {
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        const category = $(this).data('category');

        // Remove selected class from all categories
        $('.clickable-category').removeClass('selected');

        // Add selected class to clicked category
        $(this).addClass('selected');

        // Show items for this category
        showItemsForCategory(category);
    });

    // Item slot click handler
    $(document).on('click', '.item-slot', function(e) {
        // Prevent click if we just finished dragging
        if (hasDragged) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        e.preventDefault();

        const item = $(this).data('item');
        const category = $(this).data('category');

        // Remove selected class and indicators from all items
        $('.item-slot').removeClass('selected');
        $('.item-slot .selected-indicator').css('opacity', '0');

        // Add selected class and show indicator for clicked item
        $(this).addClass('selected');
        $(this).find('.selected-indicator').css('opacity', '1');

        // Show variations for this item
        showVariationsForItem(category, item);

        // Update the category in the right panel to this item
        updateCategoryToItem(category, item);
    });

    // Variation slot click handler
    $(document).on('click', '.variation-slot', function(e) {
        // Prevent click if we just finished dragging
        if (hasDragged) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        e.preventDefault();

        const variation = $(this).data('variation');
        const item = $(this).data('item');
        const category = $(this).data('category');

        // Remove selected class and indicators from all variations
        $('.variation-slot').removeClass('selected');
        $('.variation-slot .selected-indicator').css('opacity', '0');
        $('.variation-slot .active-indicator').css('opacity', '0');

        // Add selected class and show indicators for clicked variation
        $(this).addClass('selected');
        $(this).find('.selected-indicator').css('opacity', '1');
        $(this).find('.active-indicator').css('opacity', '1');

        // Update the category in the right panel to this item and variation
        updateCategoryToItemAndVariation(category, item, variation);
    });

    // Close left panel button
    $(document).on('click', '#close-left-panel', function(e) {
        e.preventDefault();
        hideLeftPanel();
    });

    // Close variations button
    $(document).on('click', '.close-variations', function(e) {
        e.preventDefault();

        // Remove the variations panel
        $(this).closest('.variations-panel').remove();

        // Clear item selection
        $('.item-slot').removeClass('selected');
        $('.item-slot .selected-indicator').css('opacity', '0');
        $('.variation-slot').removeClass('selected');
        $('.variation-slot .selected-indicator').css('opacity', '0');
        $('.variation-slot .active-indicator').css('opacity', '0');

        selectedItem = null;
    });

    // Staff menu button click handler
    $(document).on('click', '.staff-menu-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const dropdown = $('.staff-menu-dropdown');
        dropdown.toggleClass('hidden');
    });

    // Staff menu item click handler
    $(document).on('click', '.staff-menu-item', function(e) {
        e.preventDefault();

        const action = $(this).data('action');
        const dropdown = $('.staff-menu-dropdown');

        // Close dropdown
        dropdown.addClass('hidden');

        // Handle different actions
        switch(action) {
            case 'process-current':
                if (selectedCategory) {
                    $.post('https://blrp_clothingstore/processCurrentCategory', JSON.stringify({
                        category: selectedCategory
                    }));
                }
                break;
            case 'process-all':
                $.post('https://blrp_clothingstore/processAllCategories', JSON.stringify({}));
                break;
            case 'reset-selected':
                resetSelectedItemOrVariation();
                break;
            case 'reset-current':
                if (selectedCategory) {
                    $.post('https://blrp_clothingstore/resetCurrentCategory', JSON.stringify({
                        category: selectedCategory
                    }));
                }
                break;
            case 'reset-all':
                $.post('https://blrp_clothingstore/resetAllCategories', JSON.stringify({}));
                break;
        }
    });

    // Close staff dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#staff-image-menu').length) {
            $('.staff-menu-dropdown').addClass('hidden');
        }
        // Also hide context menu when clicking outside
        if (!$(e.target).closest('#context-menu').length) {
            hideContextMenu();
        }
    });

    // Hover effects for item slots to show quick hands button




    // Right-click context menu for item slots and variation slots
    $(document).on('contextmenu', '.item-slot, .variation-slot', function(e) {
        e.preventDefault();

        // Store target information
        const $target = $(this);
        contextMenuTarget = {
            category: $target.data('category'),
            item: $target.data('item'),
            variation: $target.data('variation') || null
        };

        // Check if staff permissions are available
        $.post('https://blrp_clothingstore/checkStaffPermissions', JSON.stringify({}))
            .done(function(response) {
                if (response && response.isStaff) {
                    showContextMenu(e.pageX, e.pageY);
                }
            })
            .fail(function() {
                // Hide context menu if permission check fails
                hideContextMenu();
            });
    });

    // Context menu item click handler
    $(document).on('click', '.context-menu-item', function(e) {
        e.preventDefault();

        const action = $(this).data('action');

        // Hide context menu
        hideContextMenu();

        // Handle different actions
        switch(action) {
            case 'save-current-hands':
                saveCurrentHandsForItem();
                break;
            case 'process-selected':
                processSelectedItem();
                break;
            case 'reset-selected':
                resetSelectedItemOrVariation();
                break;
            case 'update-settings':
                if (contextMenuTarget) {
                    showSettingsModal(contextMenuTarget);
                }
                break;
        }
    });

    // Settings modal event handlers
    $(document).on('click', '#cancel-settings', function(e) {
        e.preventDefault();
        hideSettingsModal();
    });

    $(document).on('click', '#save-settings', function(e) {
        e.preventDefault();
        saveSettings();
    });

    // Close settings modal when clicking outside (on the overlay)
    $(document).on('click', '#settings-modal', function(e) {
        if (e.target === this) {
            hideSettingsModal();
        }
    });

    // Prevent modal content clicks from closing the modal
    $(document).on('click', '#settings-modal > div', function(e) {
        e.stopPropagation();
    });

    // Pagination event handlers
    $(document).on('click', '#first-page', function(e) {
        e.preventDefault();
        if (currentPage > 1 && selectedCategory) {
            // Instantly scroll to top before changing page
            $('#items-window').scrollTop(0);
            showItemsForCategory(selectedCategory, 1);
        }
    });

    $(document).on('click', '#prev-page', function(e) {
        e.preventDefault();
        if (currentPage > 1 && selectedCategory) {
            // Instantly scroll to top before changing page
            $('#items-window').scrollTop(0);
            showItemsForCategory(selectedCategory, currentPage - 1);
        }
    });

    $(document).on('click', '#next-page', function(e) {
        e.preventDefault();
        if (currentPage < totalPages && selectedCategory) {
            // Instantly scroll to top before changing page
            $('#items-window').scrollTop(0);
            showItemsForCategory(selectedCategory, currentPage + 1);
        }
    });

    $(document).on('click', '#last-page', function(e) {
        e.preventDefault();
        if (currentPage < totalPages && selectedCategory) {
            // Instantly scroll to top before changing page
            $('#items-window').scrollTop(0);
            showItemsForCategory(selectedCategory, totalPages);
        }
    });

    // Apply Hands button click handler
    $(document).on('click', '#apply-hands-button button', function(e) {
        e.preventDefault();
        applyHandsToCurrentItem();
    });

    // F8 key handler to toggle left panel
    $(document).on('keydown', function(e) {
        // F8 key (keyCode 119)
        if (e.keyCode === 119) {
            e.preventDefault();
            leftPanelEnabled = !leftPanelEnabled;

            console.log(`Left panel ${leftPanelEnabled ? 'enabled' : 'disabled'}`);

            // If disabling and panel is currently open, hide it
            if (!leftPanelEnabled && $('#left-panel').is(':visible')) {
                hideLeftPanel();
            }
        }
    });

}

// Initialize mouse controls
function initializeMouseControls() {
    $('body').on('mousedown', function(e) {
        let element = $(e.target);

        if(element.prop('tagName') === 'DIV' && element.attr('id') === 'container') {
            moving = true;
        }
    }).on('mouseup', function(e) {
        if(moving) {
            moving = false;
        }
    }).on('mousemove', function(event) {
        if(moving) {
            let offsetX = event.screenX - lastScreenX;
            let offsetY = event.screenY - lastScreenY;
            if ((lastOffsetX > 0 && offsetX < 0) || (lastOffsetX < 0 && offsetX > 0)) {
                offsetX = 0
            }
            if ((lastOffsetY > 0 && offsetY < 0) || (lastOffsetY < 0 && offsetY > 0)) {
                offsetY = 0
            }
            lastScreenX = event.screenX;
            lastScreenY = event.screenY;
            lastOffsetX = offsetX;
            lastOffsetY = offsetY;
            $.post('https://blrp_clothingstore/updateCameraRotation', JSON.stringify({
                x: offsetX,
                y: offsetY,
            }));
        }
    }).on('wheel', function(e) {
        let element = $(e.target);

        if(element !== null && element.prop('tagName') === 'DIV' && element.attr('id') === 'container') {
            let zoom = e.originalEvent.deltaY / 2000;
            $.post('https://blrp_clothingstore/updateCameraZoom', JSON.stringify({
                zoom: zoom,
            }));
        }
    });
}

// Initialize drag scrolling for left panel
function initializeDragScrolling() {
    let isDragging = false;
    let startY = 0;
    let startScrollTop = 0;
    let dragThreshold = 5; // Minimum pixels to move before considering it a drag

    // Mouse events - allow dragging anywhere in the items window
    $('#items-window').on('mousedown', function(e) {
        isDragging = true;
        hasDragged = false;
        startY = e.clientY;
        startScrollTop = $(this).scrollTop();
        $(this).addClass('dragging').css('cursor', 'grabbing');
        e.preventDefault();
    });

    $(document).on('mousemove', function(e) {
        if (!isDragging) return;

        const deltaY = startY - e.clientY;
        const itemsWindow = $('#items-window');

        // Check if we've moved enough to consider it a drag
        if (Math.abs(deltaY) > dragThreshold) {
            hasDragged = true;
        }

        // Update scroll position
        itemsWindow.scrollTop(startScrollTop + deltaY);
        e.preventDefault();
    });

    $(document).on('mouseup', function(e) {
        if (isDragging) {
            isDragging = false;
            $('#items-window').removeClass('dragging').css('cursor', 'grab');

            // If we dragged, prevent click events on items
            if (hasDragged) {
                setTimeout(() => {
                    hasDragged = false;
                }, 10);
            }
        }
    });

    // Touch events for mobile-like behavior - allow dragging anywhere in the items window
    $('#items-window').on('touchstart', function(e) {
        isDragging = true;
        hasDragged = false;
        startY = e.originalEvent.touches[0].clientY;
        startScrollTop = $(this).scrollTop();
        $(this).addClass('dragging');
    });

    $('#items-window').on('touchmove', function(e) {
        if (!isDragging) return;

        const deltaY = startY - e.originalEvent.touches[0].clientY;

        // Check if we've moved enough to consider it a drag
        if (Math.abs(deltaY) > dragThreshold) {
            hasDragged = true;
        }

        // Update scroll position
        $(this).scrollTop(startScrollTop + deltaY);
        e.preventDefault();
    });

    $('#items-window').on('touchend', function(e) {
        if (isDragging) {
            isDragging = false;
            $(this).removeClass('dragging');

            // If we dragged, prevent click events on items
            if (hasDragged) {
                setTimeout(() => {
                    hasDragged = false;
                }, 10);
            }
        }
    });
}

// Initialize keyboard controls
function initializeKeyboardControls() {
    window.addEventListener('keydown', function(event) {
        if(!interfaceShowing) {
            return false;
        }

        if(event.key === 'Escape') {
            event.preventDefault();

            if (!require_confirmation) {
                $.post('https://blrp_clothingstore/escape', JSON.stringify({}));
                return;
            }

            new Promise((resolve, reject) => {
                popup_promise = resolve;
            }).then(close => {
                if(close) {
                    $.post('https://blrp_clothingstore/escape', JSON.stringify({}));
                }
                popup_promise = null;
            }).finally(() => {
                $('.popup-overlay').fadeOut(100);
                $('#container > div').css('pointer-events', 'auto');
            });

            $('.popup-overlay').fadeIn(100);
            $('#container > div').css('pointer-events', 'none');
        } else if(event.key === 'x') {
            $.post('https://blrp_clothingstore/passthrough', JSON.stringify({
                key: event.key
            }));
        }
    });
}

// Handle messages from the game
function initializeMessageHandler() {
    window.addEventListener("message", (e) => {
        let data = e.data
        let action = data.action
        let container = document.getElementById('container');

        if(action === 'show') {
            require_confirmation = data.new_character;

            if(data.category_options) {
                category_options = data.category_options;
            }

            if(data.categories) {
                category_names = data.categories;
            }

            // Detect gender from server data or make a request
            $.post('https://blrp_clothingstore/getGender', JSON.stringify({}), function(response) {
                if (response && response.gender) {
                    currentGender = response.gender;
                }
            }).fail(function() {
                // Fallback to male if request fails
                currentGender = 'male';
            });

            // Get clothing category mappings for image URLs
            $.post('https://blrp_clothingstore/getClothingMappings', JSON.stringify({}), function(response) {
                if (response && response.mappings) {
                    clothing_category_mappings = response.mappings;
                }
            }).fail(function() {
                console.log('Failed to get clothing category mappings');
            });

            if(data.customization !== null && data.categories !== null) {
                // Clear all section contents and collapse all sections
                $('.section-content').html('').addClass('collapsed');
                $('.section-toggle').addClass('collapsed');

                // Group categories by section
                const sectionCategories = {
                    top: [],
                    torso: [],
                    accessories: [],
                    bottom: []
                };

                Object.keys(data.customization).forEach(function(key) {
                    const categoryName = data.categories[key];
                    const section = getCategorySection(categoryName);
                    sectionCategories[section].push(key);
                });

                // Sort categories within each section
                Object.keys(sectionCategories).forEach(sectionName => {
                    sectionCategories[sectionName].sort((a, b) => {
                        a = data.categories[a].toUpperCase();
                        b = data.categories[b].toUpperCase();
                        return a.localeCompare(b);
                    });
                });

                // Render categories in their respective sections
                Object.keys(sectionCategories).forEach(sectionName => {
                    const sectionElement = $(`.section[data-section="${sectionName}"] .section-content`);

                    sectionCategories[sectionName].forEach(function(key) {
                        let value = data.customization[key];
                        let item = value[0]
                        let texture = value[1]
                        let template = $('script[data-template="category"]').text().split(/\$\{(.+?)\}/g);
                        let item_key = 0
                        let texture_key = 0

                        try {
                            item_key = Object.keys(category_options[key].drawables).find(k => category_options[key].drawables[k] === item);
                            texture_key = Object.keys(category_options[key].textures[item]).find(k => category_options[key].textures[item][k] === texture);
                        } catch(e) {
                            // Exception happens when the person is wearing something they aren't whitelisted for
                            // If that happens, set everything to 0
                            item = 0
                            item_key = 0
                            texture = 0
                            texture_key = 0
                        }

                        template_rendered = $(template.map(renderTemplate({
                            name: data.categories[key],
                            icon: getCategoryIcon(data.categories[key]),
                            item: item,
                            item_key: item_key,
                            texture: texture,
                            texture_key: texture_key,
                            category: key,
                        })).join(''));

                        // Keep remove button for all categories now

                        sectionElement.append(template_rendered);
                    });
                });
            }

            interfaceShowing = true;
            container.style.display = 'flex';
        } else if (action === 'hide') {
            interfaceShowing = false;
            container.style.display = 'none';

            // Hide left panel and reset selections
            hideLeftPanel();
        }
    })
}

// Context menu helper functions
function showContextMenu(x, y) {
    const contextMenu = $('#context-menu');
    const windowWidth = $(window).width();
    const windowHeight = $(window).height();
    const menuWidth = contextMenu.outerWidth();
    const menuHeight = contextMenu.outerHeight();

    // Adjust position if menu would go off screen
    let posX = x;
    let posY = y;

    if (x + menuWidth > windowWidth) {
        posX = x - menuWidth;
    }

    if (y + menuHeight > windowHeight) {
        posY = y - menuHeight;
    }

    contextMenu.css({
        left: posX + 'px',
        top: posY + 'px'
    }).removeClass('hidden');
}

function hideContextMenu() {
    $('#context-menu').addClass('hidden');
}

// Save current hands for the selected item
function saveCurrentHandsForItem() {
    if (!contextMenuTarget) return;

    const category = contextMenuTarget.category;
    const item = contextMenuTarget.item;

    // Call updateItemSettings without hands (client will use current ped hands)
    $.post('https://blrp_clothingstore/updateItemSettings', JSON.stringify({
        category: category,
        item: item,
        settings: {} // Empty settings - client will detect current hands
    }))
    .done(function(response) {
        if (response && response.success) {
            console.log('Successfully saved current hands for item', item);
        } else {
            console.error('Failed to save hands:', response.error || 'Unknown error');
        }
    })
    .fail(function() {
        console.error('Failed to save hands');
    });
}

// Settings modal functions
function showSettingsModal(target) {
    const modal = $('#settings-modal');
    const itemInfo = $('#settings-item-info');

    // Set item info
    itemInfo.text(`${target.category} - Item ${target.item}`);

    // Get current gender first
    $.post('https://blrp_clothingstore/getGender', JSON.stringify({}))
    .done(function(genderResponse) {
        const currentGender = genderResponse.gender || 'male';

        // Load current settings
        $.post('https://blrp_clothingstore/getItemSettings', JSON.stringify({
            category: target.category,
            item: target.item
        }))
        .done(function(response) {
            if (response && response.success) {
                const settings = response.settings;
                $('#male-hands').val(settings.assigned_hands_male || '');
                $('#female-hands').val(settings.assigned_hands_female || '');

                // Store current gender for later use
                modal.data('current-gender', currentGender);

                // Update UI to show only current gender field
                updateGenderFields(currentGender);
            }
        })
        .fail(function() {
            // Clear form on error
            $('#male-hands').val('');
            $('#female-hands').val('');
            modal.data('current-gender', currentGender);

            // Update UI to show only current gender field
            updateGenderFields(currentGender);
        });
    })
    .fail(function() {
        // Default to male if gender detection fails
        modal.data('current-gender', 'male');
        updateGenderFields('male');
    });

    // Show modal with proper display
    modal.removeClass('hidden').css('display', 'flex');
}

function hideSettingsModal() {
    const modal = $('#settings-modal');
    modal.addClass('hidden').css('display', 'none');

    // Show both fields when modal is closed
    $('#male-hands-field').show();
    $('#female-hands-field').show();
}

function updateGenderFields(gender) {
    const indicator = $('#current-gender-indicator');
    const maleField = $('#male-hands-field');
    const femaleField = $('#female-hands-field');

    // Update indicator text
    indicator.text(`Current Ped: ${gender.charAt(0).toUpperCase() + gender.slice(1)}`);

    // Show only the current gender field
    if (gender === 'male') {
        maleField.show();
        femaleField.hide();
    } else {
        maleField.hide();
        femaleField.show();
    }
}

// Apply hands to currently equipped jacket item
function applyHandsToCurrentItem() {
    if (selectedCategory !== '11' && selectedCategory !== 11) {
        return;
    }

    // Single server call that does everything
    $.post('https://blrp_clothingstore/applyHandsForCurrentJacket', JSON.stringify({
        category: selectedCategory
    }))
    .done(function(response) {
        if (response && response.success) {
            console.log('Successfully applied hands');
        } else {
            console.error('Failed to apply hands:', response.error || 'Unknown error');
        }
    })
    .fail(function() {
        console.error('Failed to apply hands');
    });
}

function saveSettings() {
    if (!contextMenuTarget) return;

    const modal = $('#settings-modal');
    const currentGender = modal.data('current-gender') || 'male';

    // Get current hands value from the appropriate field
    const currentHandsValue = currentGender === 'male' ?
        $('#male-hands').val().trim() :
        $('#female-hands').val().trim();

    const settings = {
        assigned_hands_male: $('#male-hands').val().trim() || null,
        assigned_hands_female: $('#female-hands').val().trim() || null,
        current_gender: currentGender,
        current_hands: currentHandsValue || null
    };

    $.post('https://blrp_clothingstore/updateItemSettings', JSON.stringify({
        category: contextMenuTarget.category,
        item: contextMenuTarget.item,
        settings: settings
    }))
    .done(function(response) {
        if (response && response.success) {
            hideSettingsModal();
        } else {
            console.error('Failed to update settings:', response.error || 'Unknown error');
        }
    })
    .fail(function() {
        console.error('Failed to update settings');
    });
}

// Update red dot indicators for items without assigned hands (gender-specific)
function updateRedDotIndicators(category) {
    const settings = categorySettings[category] || {};

    // Get current gender to check appropriate hands field
    $.post('https://blrp_clothingstore/getGender', JSON.stringify({}))
    .done(function(genderResponse) {
        const currentGender = genderResponse.gender || 'male';

        $('.item-slot').each(function() {
            const $item = $(this);
            const itemNumber = $item.data('item');
            const itemCategory = $item.data('category');

            if (itemCategory === category) {
                const itemSettings = settings[itemNumber];

                // Check hands assignment for current gender only
                const hasAssignedHands = itemSettings &&
                    (currentGender === 'male' ?
                        itemSettings.assigned_hands_male :
                        itemSettings.assigned_hands_female);

                // Remove existing indicators
                $item.find('.missing-hands-indicator').remove();
                $item.find('.hands-debug-indicator').remove();
            }
        });
    })
    .fail(function() {
        // Fallback to checking both genders if gender detection fails
        $('.item-slot').each(function() {
            const $item = $(this);
            const itemNumber = $item.data('item');
            const itemCategory = $item.data('category');

            if (itemCategory === category) {
                const itemSettings = settings[itemNumber];
                const maleHands = itemSettings && itemSettings.assigned_hands_male;
                const femaleHands = itemSettings && itemSettings.assigned_hands_female;
                const hasAssignedHands = maleHands || femaleHands;

                // Remove existing indicators
                $item.find('.missing-hands-indicator').remove();
                $item.find('.hands-debug-indicator').remove();

                // Indicators removed - no longer showing hands debug info or missing hands dots
            }
        });
    });
}

// Initialize everything when document is ready
$(document).ready(function() {
    initializeEventHandlers();
    initializeMouseControls();
    initializeDragScrolling();
    initializeKeyboardControls();
    initializeImageObserver();
    initializeMessageHandler();
});
