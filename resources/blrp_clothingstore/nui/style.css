/* Modern Clothing Store Interface - Custom CSS for Tailwind */

/* Custom animations and utilities */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideLeft {
    from {
        opacity: 0;
        transform: translateX(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.animate-fade-in {
    animation: fadeIn 0.2s ease-out;
}

.animate-slide-left {
    animation: slideLeft 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/*!* Custom scrollbar styling *!*/
/*::-webkit-scrollbar {*/
/*    width: 6px;*/
/*}*/

/*::-webkit-scrollbar-track {*/
/*    background: transparent;*/
/*}*/

/*::-webkit-scrollbar-thumb {*/
/*    background: #3e4146;*/
/*    border-radius: 3px;*/
/*}*/

/*::-webkit-scrollbar-thumb:hover {*/
/*    background: #868686;*/
/*}*/

/* User select prevention */
*:not(input) {
    user-select: none;
}

/* Right-click context menu */
#context-menu {
    position: fixed;
    background: #232629;
    border: 1px solid #3e4146;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    z-index: 10000;
    min-width: 200px;
}

#context-menu .context-menu-item {
    padding: 8px 12px;
    color: #d1d2d3;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
}

#context-menu .context-menu-item:hover {
    background: #2c2f33;
    color: #ffffff;
}

/* Missing hands indicator */
.missing-hands-indicator {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 12px;
    height: 12px;
    background: #ef4444;
    border: 2px solid #ffffff;
    border-radius: 50%;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Settings modal */
#settings-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 30000 !important;
    pointer-events: auto !important;
}

#settings-modal.hidden {
    display: none !important;
}

#settings-modal input:focus {
    border-color: #00bfff !important;
    outline: none !important;
}

/* Apply Hands Button */
#apply-hands-button button {
    background: linear-gradient(135deg, #00bfff, #0099cc) !important;
    border: none;
    color: white !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 12px rgba(0, 191, 255, 0.3);
    transition: all 0.2s ease;
}

/* Remove Button Styling */
.btn-remove {
    font-size: 16px !important;
    font-weight: bold;
    line-height: 1;
    transition: all 0.2s ease;
}

.btn-remove:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.4);
}

#apply-hands-button button:hover {
    background: linear-gradient(135deg, #0099cc, #007aa3) !important;
    box-shadow: 0 6px 16px rgba(0, 191, 255, 0.4);
    transform: translateY(-1px);
}

#apply-hands-button button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 191, 255, 0.3);
}

/* Current gender field highlighting */
.current-gender-field {
    background: rgba(0, 191, 255, 0.1) !important;
    border: 1px solid rgba(0, 191, 255, 0.3) !important;
    border-radius: 8px !important;
    padding: 8px !important;
}

.current-gender-field label {
    color: #00bfff !important;
    font-weight: 600 !important;
}

/*!* Drag scrolling styles *!*/
/*#items-window {*/
/*    cursor: grab;*/
/*    touch-action: none; !* Prevent default touch behaviors *!*/
/*}*/

#items-window:active {
    cursor: grabbing;
}

/*!* Smooth scrolling for better drag experience *!*/
/*#items-window {*/
/*    scroll-behavior: auto; !* Disable smooth scrolling during drag *!*/
/*}*/

/* Prevent text selection during drag */
#items-window.dragging {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Pagination controls styling */
#pagination-controls {
    min-height: 50px;
    border-top: 1px solid #3e4146;
}

#pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#pagination-controls button:disabled:hover {
    background-color: #232629 !important;
    color: #868686 !important;
}

#page-info {
    min-width: 100px;
    text-align: center;
}

#items-count {
    white-space: nowrap;
}

/* Performance optimizations */
.item-image, .variation-image {
    will-change: opacity;
    transition: opacity 0.2s ease-in-out;
}

.item-image.loaded, .variation-image.loaded {
    opacity: 1;
}

.item-image.error, .variation-image.error {
    opacity: 0;
}

/* Optimize grid rendering */
#items-grid {
    contain: layout style paint;
}

.item-slot, .variation-slot {
    contain: layout style paint;
    transform: translateZ(0); /* Force hardware acceleration */
}

/*!* Reduce repaints during scrolling *!*/
/*#items-window {*/
/*    contain: layout style paint;*/
/*    transform: translateZ(0);*/
/*}*/

/* Floating Variations Panel */
.variations-panel {
    animation: fadeIn 0.3s ease-out forwards;
    position: fixed;
    z-index: 1000;
    width: 300px; /* Fixed width for 2 columns */
}

/*!* Variations grid with scrolling *!*/
/*.variations-panel .variations-grid {*/
/*    max-height: 500px; !* Height for approximately 5 rows *!*/
/*    overflow-y: auto;*/
/*    padding-right: 5px; !* Space for scrollbar *!*/
/*}*/

/*!* Custom scrollbar for variations *!*/
/*.variations-panel .variations-grid::-webkit-scrollbar {*/
/*    width: 6px;*/
/*}*/

/*.variations-panel .variations-grid::-webkit-scrollbar-track {*/
/*    background: #1a1d21;*/
/*    border-radius: 3px;*/
/*}*/

/*.variations-panel .variations-grid::-webkit-scrollbar-thumb {*/
/*    background: #3e4146;*/
/*    border-radius: 3px;*/
/*}*/

/*.variations-panel .variations-grid::-webkit-scrollbar-thumb:hover {*/
/*    background: #4a5058;*/
/*}*/

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Variations panel container styling */
.variations-panel .bg-dark-200 {
    background: #232629 !important;
    border: 1px solid #3e4146;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.variations-panel h4 {
    color: #e5e7eb;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
}

/* Ensure variations panel stays above other elements */
.variations-panel {
    z-index: 9999 !important;
}

/* Enhanced item and variation slots */
.item-slot.selected {
    border-color: #00bfff !important;
    background: rgba(0, 191, 255, 0.1) !important;
    box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.3);
}

.variation-slot.selected {
    background: rgba(0, 191, 255, 0.1) !important;
    box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.3);
}

.variation-slot:hover {
    /* Removed scale transform to prevent image zoom */
}

/* Compact variations in inline row */
.variations-row .variation-slot {
    min-height: 80px;
}

.variations-row .variation-image-container {
    /* Remove height restriction to allow full coverage */
}

.variations-row .variation-label {
    font-size: 11px;
    padding: 4px 8px;
}





/* Custom camera button active state */
.camera-btn.active {
    background: #00bfff !important;
    color: white !important;
}

.camera-btn.active:hover {
    background: #0099cc !important;
    color: white !important;
}

/* Large category icons */
.category-icon-large {
    width: 60px;
    min-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px 0 25px;
}

.category-icon-large i {
    font-size: 36px !important;
    line-height: 1;
    text-align: center;
    color: #6b7280; /* Gray by default */
    transition: color 0.2s ease-in-out;
}

/* Show blue color on hover */
.category:hover .category-icon-large i {
    color: deepskyblue;
}

/* Show blue color when selected */
.category.selected .category-icon-large i {
    color: deepskyblue;
}

/* Section styling */
.section-content {
    max-height: 2000px;
    overflow: hidden;
    transition: max-height 0.3s ease-out, opacity 0.2s ease-out;
    opacity: 1;
}

.section-content.collapsed {
    max-height: 0;
    opacity: 0;
}

.section-toggle {
    transition: transform 0.3s ease-out;
}

.section-toggle.collapsed {
    transform: rotate(-90deg);
}

/* Section header hover - darker background */
.section-header:hover {
    background: #1a1d21 !important;
}

/* Menu dropdown styling */
.menu-dropdown {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
}

.menu-dropdown.show {
    display: block;
}

.menu-item:hover {
    background: #1a1d21 !important;
}

/* FontAwesome stacked icons */
.fa-stack {
    position: relative;
    display: inline-block;
    width: 2em;
    height: 2em;
    line-height: 2em;
    vertical-align: middle;
}

.fa-stack-1x, .fa-stack-2x {
    position: absolute;
    left: 0;
    width: 100%;
    text-align: center;
}

.fa-stack-1x {
    line-height: inherit;
}

.fa-stack-2x {
    font-size: 2em;
    line-height: 1em;
}

/* Left Panel Styles */
#left-panel {
    animation: slideLeft 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Clickable category styling */
.clickable-category {
    cursor: pointer;
}

.clickable-category:hover {
    background: #1a1d21 !important;
    border-color: #00bfff !important;
}

.clickable-category.selected {
    background: #0f1114 !important;
    border-color: #3e4146 !important;
}

/* Item and Variation Slots */
.item-slot {
    aspect-ratio: 1;
    min-height: 100px;
}

.variation-slot {
    aspect-ratio: 1; /* 15% wider than square */
    min-height: 100px;
}

.item-slot:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 191, 255, 0.3);
}

.variation-slot:hover {
    /* Removed translateY transform to prevent movement */
    box-shadow: 0 4px 8px rgba(0, 191, 255, 0.3);
}

.item-slot.selected {
    background: #1a1d21 !important;
    border-color: #00bfff !important;
    box-shadow: 0 0 0 2px #00bfff;
}

.variation-slot.selected {
    background: rgba(0, 191, 255, 0.1) !important;
    box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.3);
}

/* Lazy loading image styles */
.lazy-load {
    transition: opacity 0.3s ease-in-out;
}

.lazy-load.loaded {
    opacity: 1;
}

.lazy-load.error {
    display: none !important;
}

/* Image container styles */
.item-image-container, .variation-image-container {
    background: #232629;
    position: relative;
}

.item-image, .variation-image {
    transition: all 0.2s ease-in-out;
}

.item-slot:hover .item-image {
    transform: scale(1.05);
}

/* Removed image scale on variation hover to prevent zoom */

/* Variation label overlay styles */
.variation-label {
    backdrop-filter: blur(2px);
    transition: all 0.2s ease-in-out;
}

.variation-slot:hover .variation-label {
    background: rgba(0, 0, 0, 0.9);
}

/* Fallback icon styles */
.item-fallback-icon, .variation-fallback-icon {
    transition: color 0.2s ease-in-out;
}

.item-slot:hover .item-fallback-icon, .variation-slot:hover .variation-fallback-icon {
    color: #00bfff !important;
}

/*!* Always show scrollbars *!*/
/*html, body {*/
/*    overflow-y: scroll;*/
/*}*/

/** {*/
/*    scrollbar-width: auto; !* Firefox *!*/
/*}*/

/*::-webkit-scrollbar {*/
/*    width: 12px; !* Chrome, Safari, Edge *!*/
/*}*/

/*::-webkit-scrollbar-track {*/
/*    background: #1a1d21;*/
/*}*/

/*::-webkit-scrollbar-thumb {*/
/*    background: #3e4146;*/
/*    border-radius: 6px;*/
/*}*/

/*::-webkit-scrollbar-thumb:hover {*/
/*    background: #4a4f56;*/
/*}*/

/* Custom X icon styles */
.custom-x-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.custom-x-icon svg {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}
