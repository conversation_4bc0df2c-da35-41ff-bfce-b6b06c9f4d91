<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BLRP Clothing Store</title>

    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto+Mono">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#00bfff',
                        secondary: '#616061',
                        success: '#007a5a',
                        warning: '#e01e5a',
                        danger: '#e01e5a',
                        dark: {
                            50: '#35383c',
                            100: '#2c2f33',
                            200: '#232629',
                            300: '#1a1d21',
                        },
                        gray: {
                            100: '#ffffff',
                            200: '#d1d2d3',
                            300: '#868686',
                            400: '#4a4d52',
                            500: '#3e4146',
                        }
                    },
                    fontFamily: {
                        'mono': ['Roboto Mono', 'monospace']
                    }
                }
            }
        }
    </script>

    <!-- FontAwesome Icons -->
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">
</head>

<body class="font-mono bg-transparent overflow-hidden">
    <div id="container" style="position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: transparent; display: none; align-items: flex-start; justify-content: flex-end; z-index: 9999;">
        <!-- Left Panel for Items and Variations (Hidden by default) -->
        <div id="left-panel" class="w-full max-w-lg h-full max-h-[90vh] bg-dark-300 border border-gray-500 rounded-2xl shadow-2xl overflow-hidden flex flex-col" style="width: 450px; height: 90%; max-height: 90%; background: #1a1d21; border: 1px solid #3e4146; border-radius: 25px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5); overflow: hidden; display: none; flex-direction: column; margin-top: 25px; margin-left: 25px; color: white; position: fixed; left: 25px; top: 25px; z-index: 10001;">
            <!-- Header -->
            <div class="bg-dark-200 border-b border-gray-500 px-8 py-4 flex items-center justify-between gap-3 flex-shrink-0">
                <!-- Left Side - Apply Hands Button -->
                <div class="flex items-center gap-3">
                    <!-- Apply Hands Button (only shows for jacket category) -->
                    <div id="apply-hands-button" class="hidden">
                        <button class="bg-primary text-white px-3 py-2 rounded-lg cursor-pointer transition-all duration-200 hover:bg-blue-600 flex items-center gap-2 text-sm font-medium shadow-lg" title="Apply Hands">
                            <i class="fa-solid fa-hand"></i>
                            <span>Apply Hands</span>
                        </button>
                    </div>
                </div>

                <!-- Right Side - Staff Menu and Close Button -->
                <div class="flex items-center gap-3">
                    <!-- Staff Image Management Dropdown (Hidden by default) -->
                    <div id="staff-image-menu" class="relative" style="display: none;">
                    <button class="staff-menu-btn bg-dark-300 text-gray-200 px-3 py-2 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center hover:bg-dark-100 hover:text-gray-100">
                        <i class="fa-solid fa-ellipsis-vertical text-sm"></i>
                    </button>

                    <!-- Dropdown Menu -->
                    <div class="staff-menu-dropdown hidden absolute right-0 top-full mt-2 bg-dark-200 border border-gray-500 rounded-lg shadow-xl z-50" style="min-width: 280px;">
                        <div class="p-2">
                            <div class="text-gray-300 text-xs font-semibold px-2 py-1 border-b border-gray-600 mb-2">Process Images</div>
                            <button class="staff-menu-item w-full px-3 py-2 text-left text-gray-200 hover:bg-dark-100 transition-all duration-200 flex items-center gap-2 rounded" data-action="process-current">
                                <i class="fa-solid fa-camera text-blue-400 text-xs"></i>
                                <span class="text-sm">Process Current Category</span>
                            </button>

<!--                            <button class="staff-menu-item w-full px-3 py-2 text-left text-gray-200 hover:bg-dark-100 transition-all duration-200 flex items-center gap-2 rounded" data-action="process-all">-->
<!--                                <i class="fa-solid fa-images text-blue-400 text-xs"></i>-->
<!--                                <span class="text-sm">Process All Categories</span>-->
<!--                            </button>-->

                            <div class="text-gray-300 text-xs font-semibold px-2 py-1 border-b border-gray-600 mb-2 mt-3">Reset Images</div>
                            <button class="staff-menu-item w-full px-3 py-2 text-left text-gray-200 hover:bg-dark-100 transition-all duration-200 flex items-center gap-2 rounded" data-action="reset-current">
                                <i class="fa-solid fa-trash text-red-400 text-xs"></i>
                                <span class="text-sm">Reset Current Category</span>
                            </button>
<!--                            <button class="staff-menu-item w-full px-3 py-2 text-left text-gray-200 hover:bg-dark-100 transition-all duration-200 flex items-center gap-2 rounded" data-action="reset-all">-->
<!--                                <i class="fa-solid fa-trash-can text-red-400 text-xs"></i>-->
<!--                                <span class="text-sm">Reset All Categories</span>-->
<!--                            </button>-->
                        </div>
                    </div>
                </div>

                    <button id="close-left-panel" class="bg-dark-300 text-gray-300 px-3 py-2 rounded-lg cursor-pointer transition-all duration-200 hover:bg-danger hover:text-white flex items-center justify-center text-lg min-w-10 h-10">
                        <i class="fa-solid fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Items Window (Full Height) -->
            <div id="items-window" class="flex-1 overflow-y-auto p-4 bg-dark-100">
                <!-- Items Grid -->
                <div id="items-grid" class="grid grid-cols-3 gap-3">
                    <!-- Items will be populated here -->
                    <!-- Variations will be dynamically inserted after selected item -->
                </div>
            </div>

            <!-- Pagination Controls (Always Visible) -->
            <div id="pagination-controls" class="bg-dark-200 border-t border-gray-500 px-4 py-3 flex items-center justify-between flex-shrink-0" style="display: none;">
                <div class="flex items-center gap-2">
                    <button id="first-page" class="bg-dark-300 text-gray-200 px-3 py-2 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center text-sm min-w-8 h-8 hover:bg-dark-100 hover:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed" disabled title="First Page">
                        <i class="fa-solid fa-angles-left"></i>
                    </button>
                    <button id="prev-page" class="bg-dark-300 text-gray-200 px-3 py-2 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center text-sm min-w-8 h-8 hover:bg-dark-100 hover:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <i class="fa-solid fa-chevron-left"></i>
                    </button>
                    <span id="page-info" class="text-gray-300 text-sm font-medium px-2">Page 1 of 1</span>
                    <button id="next-page" class="bg-dark-300 text-gray-200 px-3 py-2 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center text-sm min-w-8 h-8 hover:bg-dark-100 hover:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <i class="fa-solid fa-chevron-right"></i>
                    </button>
                    <button id="last-page" class="bg-dark-300 text-gray-200 px-3 py-2 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center text-sm min-w-8 h-8 hover:bg-dark-100 hover:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed" disabled title="Last Page">
                        <i class="fa-solid fa-angles-right"></i>
                    </button>
                </div>
                <div class="text-gray-400 text-xs">
                    <span id="items-count">0 items (50 per page)</span>
                </div>
            </div>
        </div>

        <!-- Right Panel (Main Window - Always Visible) -->
        <div class="w-full max-w-lg h-full max-h-[90vh] bg-dark-300 border border-gray-500 rounded-2xl shadow-2xl overflow-hidden flex flex-col mt-6 mr-6" style="width: 450px; height: 90%; max-height: 90%; background: #1a1d21; border: 1px solid #3e4146; border-radius: 25px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5); overflow: hidden; display: flex; flex-direction: column; margin-top: 25px; margin-right: 25px; color: white;">
            <!-- Header -->
            <div class="bg-dark-200 border-b border-gray-500 px-8 py-4 flex items-center justify-center flex-shrink-0">
                <h2 class="text-gray-100 text-2xl font-bold m-0" style="text-shadow: 0px 0px 6px rgba(9, 10, 26, 0.8);">Clothing Store</h2>
            </div>

            <!-- Controls Bar -->
            <div class="bg-dark-100 border-b border-gray-500 px-8 py-4 flex justify-between items-center flex-shrink-0">
                <!-- Camera Controls (Left) -->
                <div class="flex gap-2">
                    <button class="camera-btn bg-dark-200 text-gray-200 px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center text-xl min-w-12 h-12 hover:bg-dark-100 hover:text-gray-100" data-view="head">
                        <i class="fa-regular fa-fw fa-head-side"></i>
                    </button>
                    <button class="camera-btn active bg-dark-200 text-gray-200 px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center text-xl min-w-12 h-12 hover:bg-dark-100 hover:text-gray-100" data-view="body">
                        <i class="fa-regular fa-fw fa-person"></i>
                    </button>
                    <button class="camera-btn bg-dark-200 text-gray-200 px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center text-xl min-w-12 h-12 hover:bg-dark-100 hover:text-gray-100" data-view="legs">
                        <i class="fa-regular fa-fw fa-boot-heeled"></i>
                    </button>
                </div>

                <!-- Right Side Buttons -->
                <div class="flex gap-2 items-center">
                    <!-- Wardrobe Button -->
                    <button class="wardrobe-btn bg-dark-200 text-gray-200 px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center text-xl min-w-12 h-12 hover:bg-dark-100 hover:text-gray-100" title="Open Wardrobe">
                        <i class="fa-solid fa-cabinet-filing"></i>
                    </button>

                    <!-- Menu Button -->
                    <div class="relative">
                        <button class="menu-btn bg-dark-200 text-gray-200 px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 flex items-center justify-center text-xl min-w-12 h-12 hover:bg-dark-100 hover:text-gray-100">
                            <i class="fa-solid fa-ellipsis-vertical"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div class="menu-dropdown hidden absolute right-0 top-full mt-2 bg-dark-200 border border-gray-500 rounded-lg shadow-xl min-w-48 z-50">
                            <button class="menu-item w-full px-4 py-3 text-left text-gray-200 hover:bg-dark-100 transition-all duration-200 flex items-center gap-3 rounded-lg" data-action="undress">
                                <i class="fa-solid fa-shirt-tank-top text-gray-400"></i>
                                <span>Undress</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="flex-1 overflow-y-auto p-4 bg-dark-100">
                <div id="sections">
                    <!-- Top Section -->
                    <div class="section mb-3" data-section="top">
                        <div class="section-header bg-dark-200 px-3 py-2 rounded-lg cursor-pointer flex items-center justify-between hover:bg-dark-100 transition-all duration-200">
                            <h2 class="text-gray-100 font-semibold text-base m-0">Top</h2>
                            <i class="section-toggle fa-solid fa-chevron-down text-gray-400 transition-transform duration-200"></i>
                        </div>
                        <div class="section-content mt-2" data-categories="head,hair,ears,helmet,glasses,mask,neck"></div>
                    </div>

                    <!-- Torso Section -->
                    <div class="section mb-3" data-section="torso">
                        <div class="section-header bg-dark-200 px-3 py-2 rounded-lg cursor-pointer flex items-center justify-between hover:bg-dark-100 transition-all duration-200">
                            <h2 class="text-gray-100 font-semibold text-base m-0">Torso</h2>
                            <i class="section-toggle fa-solid fa-chevron-down text-gray-400 transition-transform duration-200"></i>
                        </div>
                        <div class="section-content mt-2" data-categories="shirt,jacket,hands,armor,bags"></div>
                    </div>

                    <!-- Accessories Section -->
                    <div class="section mb-3" data-section="accessories">
                        <div class="section-header bg-dark-200 px-3 py-2 rounded-lg cursor-pointer flex items-center justify-between hover:bg-dark-100 transition-all duration-200">
                            <h2 class="text-gray-100 font-semibold text-base m-0">Accessories</h2>
                            <i class="section-toggle fa-solid fa-chevron-down text-gray-400 transition-transform duration-200"></i>
                        </div>
                        <div class="section-content mt-2" data-categories="watches,decals,bracelets"></div>
                    </div>

                    <!-- Bottom Section -->
                    <div class="section mb-3" data-section="bottom">
                        <div class="section-header bg-dark-200 px-3 py-2 rounded-lg cursor-pointer flex items-center justify-between hover:bg-dark-100 transition-all duration-200">
                            <h2 class="text-gray-100 font-semibold text-base m-0">Bottom</h2>
                            <i class="section-toggle fa-solid fa-chevron-down text-gray-400 transition-transform duration-200"></i>
                        </div>
                        <div class="section-content mt-2" data-categories="legs,shoes"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Popup Overlay -->
    <div class="popup-overlay" style="position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: rgba(0, 0, 0, 0.75); backdrop-filter: blur(4px); display: none; align-items: center; justify-content: center; z-index: 20000;">
        <div class="popup bg-dark-300 border border-gray-500 rounded-xl max-w-md w-11/12 shadow-2xl overflow-hidden">
            <div class="popup-header bg-dark-200 border-b border-gray-500 px-6 py-4 text-center">
                <div class="popup-title text-gray-100 text-lg font-semibold m-0 uppercase">Confirmation</div>
            </div>
            <div class="popup-body px-6 py-6 text-center text-gray-200">
                Are you sure you want to close the clothing shop?
            </div>
            <div class="popup-footer bg-dark-200 border-t border-gray-500 px-4 py-4 flex justify-center gap-3">
                <button class="popup-btn px-6 py-3 border border-gray-500 rounded-lg bg-dark-100 text-gray-100 cursor-pointer transition-all duration-200 font-medium min-w-20 hover:bg-primary hover:border-primary hover:text-white" data-option="yes">Yes</button>
                <button class="popup-btn px-6 py-3 border border-gray-500 rounded-lg bg-dark-100 text-gray-100 cursor-pointer transition-all duration-200 font-medium min-w-20 hover:bg-primary hover:border-primary hover:text-white" data-option="no">No</button>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

    <!-- Main JavaScript -->
    <script src="js/main.js"></script>



    <script type="text/template" data-template="category">
        <div class="category bg-dark-200 rounded-lg mb-2 transition-all duration-200 hover:bg-dark-100 flex clickable-category" data-category="${category}" data-drawable-key="${item_key}" data-texture-key="${texture_key}">
            <!-- Icon Column -->
            <div class="category-icon-column flex items-center justify-center rounded-l category-icon-large">
                <i class="category-icon ${icon} text-gray-400"></i>
            </div>

            <!-- Content Column -->
            <div class="category-content flex-1">
                <div class="category-header px-3 py-2 flex items-center justify-between">
                    <h3 class="category-title text-gray-100 font-medium text-sm m-0">${name}</h3>
                    <button class="btn-remove bg-dark-300 text-gray-300 px-2 py-1 text-sm rounded hover:bg-danger hover:text-white transition-all duration-200 flex items-center justify-center min-w-6 h-6">
                        <span>×</span>
                    </button>
                </div>

                <div class="category-body px-3 pt-2 pb-3 flex justify-between gap-3">
                    <div class="selector-group flex items-center gap-2 flex-1">
                        <button class="btn btn-primary bg-dark-300 text-gray-200 px-2 py-1 rounded cursor-pointer transition-all duration-200 flex items-center justify-center text-sm min-w-8 h-8 hover:bg-gray-400 hover:text-white" data-direction="-" data-scope="drawable">‹</button>
                        <input class="form-input w-12 px-2 py-1 bg-dark-300 text-gray-100 text-sm text-center transition-all duration-200 focus:outline-none focus:bg-dark-100 h-8" type="text" value="${item}" data-scope="drawable" />
                        <button class="btn btn-primary bg-dark-300 text-gray-200 px-2 py-1 rounded cursor-pointer transition-all duration-200 flex items-center justify-center text-sm min-w-8 h-8 hover:bg-gray-400 hover:text-white" data-direction="+" data-scope="drawable">›</button>
                    </div>

                    <div class="selector-group flex items-center gap-2 flex-1">
                        <span class="text-gray-300 text-sm w-10">V</span>
                        <button class="btn btn-primary bg-dark-300 text-gray-200 px-2 py-1 rounded cursor-pointer transition-all duration-200 flex items-center justify-center text-sm min-w-8 h-8 hover:bg-gray-400 hover:text-white" data-direction="-" data-scope="texture">‹</button>
                        <input class="form-input w-12 px-2 py-1 bg-dark-300 text-gray-100 text-sm text-center transition-all duration-200 focus:outline-none focus:bg-dark-100 h-8" type="text" value="${texture}" data-scope="texture" />
                        <button class="btn btn-primary bg-dark-300 text-gray-200 px-2 py-1 rounded cursor-pointer transition-all duration-200 flex items-center justify-center text-sm min-w-8 h-8 hover:bg-gray-400 hover:text-white" data-direction="+" data-scope="texture">›</button>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <!-- Item Slot Template -->
    <script type="text/template" data-template="item-slot">
        <div class="item-slot bg-dark-200 rounded-lg cursor-pointer transition-all duration-200 hover:bg-dark-100 hover:border-primary border border-gray-500 flex flex-col relative" data-item="${item}" data-category="${category}">
            <!-- Selected Indicator -->
            <div class="selected-indicator absolute top-2 right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center opacity-0 transition-opacity duration-200">
                <i class="fa-solid fa-check text-white text-xs"></i>
            </div>

            <div class="item-image-container flex-1 flex items-center justify-center rounded-t-lg overflow-hidden" style="height: 80px;">
                <img class="item-image w-full h-full object-cover lazy-load" data-src="${image_url}" style="display: none;" alt="Item ${item}">
                <i class="item-fallback-icon fa-solid fa-tshirt text-gray-400 text-2xl" style="display: block;"></i>
            </div>
            <div class="item-label bg-dark-300 px-2 py-1 text-center text-gray-100 text-xs font-medium rounded-b-lg">
                ${item_label}
            </div>
        </div>
    </script>

    <!-- Floating Variations Panel Template -->
    <script type="text/template" data-template="variations-panel">
        <div class="variations-panel fixed animate-fadeIn z-50" data-item="${item}" data-category="${category}">
            <div class="bg-dark-200 rounded-lg p-4 border border-gray-500 shadow-2xl">
                <div class="flex justify-between items-center mb-3">
                    <h4 class="text-gray-200 text-sm font-semibold">Variations for Item ${item}</h4>
                    <button class="close-variations text-gray-500 hover:text-gray-300 transition-colors duration-200 text-xs">
                        <i class="fa-solid fa-times"></i>
                    </button>
                </div>
                <div class="variations-grid grid grid-cols-2 gap-3">
                    ${variations_content}
                </div>
            </div>
        </div>
    </script>

    <!-- Compact Variation Slot Template -->
    <script type="text/template" data-template="variation-slot">
        <div class="variation-slot bg-dark-200 rounded-lg cursor-pointer transition-all duration-200 hover:bg-dark-100 relative transform hover:scale-105 overflow-hidden" data-variation="${variation}" data-item="${item}" data-category="${category}">
            <!-- Selected Indicator -->
            <div class="selected-indicator absolute top-2 right-2 w-4 h-4 bg-primary rounded-full flex items-center justify-center opacity-0 transition-opacity duration-200 z-10">
                <i class="fa-solid fa-check text-white text-xs"></i>
            </div>
            <!-- Full-size image container -->
            <div class="variation-image-container absolute inset-0 flex items-center justify-center">
                <img class="variation-image w-full h-full object-cover lazy-load" data-src="${image_url}" style="display: none;" alt="Variation ${variation}">
                <i class="variation-fallback-icon fa-solid fa-palette text-gray-400 text-lg" style="display: block;"></i>
            </div>
            <!-- Label overlay at bottom -->
            <div class="variation-label absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 px-2 py-1 text-center text-gray-100 text-xs font-medium">
                ${variation_label}
            </div>
        </div>
    </script>

    <!-- Right-Click Context Menu -->
    <div id="context-menu" class="hidden absolute bg-dark-200 border border-gray-500 rounded-lg shadow-xl z-50" style="min-width: 200px;">
        <div class="p-2">
            <button class="context-menu-item w-full px-3 py-2 text-left text-gray-200 hover:bg-dark-100 transition-all duration-200 flex items-center gap-2 rounded" data-action="save-current-hands">
                <i class="fa-solid fa-check text-green-400 text-xs"></i>
                <span class="text-sm">Save Current Hands</span>
            </button>
            <button class="context-menu-item w-full px-3 py-2 text-left text-gray-200 hover:bg-dark-100 transition-all duration-200 flex items-center gap-2 rounded" data-action="reset-selected">
                <i class="fa-solid fa-eraser text-orange-400 text-xs"></i>
                <span class="text-sm">Process Selected Images</span>
            </button>
            <button class="context-menu-item w-full px-3 py-2 text-left text-gray-200 hover:bg-dark-100 transition-all duration-200 flex items-center gap-2 rounded" data-action="update-settings">
                <i class="fa-solid fa-cog text-blue-400 text-xs"></i>
                <span class="text-sm">Update Settings</span>
            </button>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="hidden" style="position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: rgba(0, 0, 0, 0.75); backdrop-filter: blur(4px); display: none; align-items: center; justify-content: center; z-index: 30000;">
        <div class="bg-dark-300 border border-gray-500 rounded-xl max-w-md w-11/12 shadow-2xl overflow-hidden" style="background: #1a1d21; border: 1px solid #3e4146;">
            <div class="bg-dark-200 border-b border-gray-500 px-6 py-4 text-center" style="background: #232629; border-bottom: 1px solid #3e4146;">
                <div class="text-gray-100 text-lg font-semibold m-0 uppercase" style="color: #d1d2d3;">Update Item Settings</div>
                <div id="settings-item-info" class="text-gray-400 text-sm mt-1" style="color: #868686;"></div>
                <div id="current-gender-indicator" class="text-blue-400 text-xs mt-2 font-medium" style="color: #00bfff;"></div>
            </div>
            <div class="px-6 py-6">
                <form id="settings-form">
                    <div id="male-hands-field" class="mb-6">
                        <label class="block text-gray-300 text-sm font-medium mb-2" style="color: #d1d2d3;">Male Hands Assignment</label>
                        <input type="text" id="male-hands" class="w-full px-3 py-2 bg-dark-200 border border-gray-500 rounded-lg text-gray-100 focus:outline-none focus:border-primary" style="background: #232629; border: 1px solid #3e4146; color: #d1d2d3;" placeholder="e.g., 03 hands to 222">
                    </div>
                    <div id="female-hands-field" class="mb-6">
                        <label class="block text-gray-300 text-sm font-medium mb-2" style="color: #d1d2d3;">Female Hands Assignment</label>
                        <input type="text" id="female-hands" class="w-full px-3 py-2 bg-dark-200 border border-gray-500 rounded-lg text-gray-100 focus:outline-none focus:border-primary" style="background: #232629; border: 1px solid #3e4146; color: #d1d2d3;" placeholder="e.g., 03 hands to 222">
                    </div>
                </form>
            </div>
            <div class="bg-dark-200 border-t border-gray-500 px-4 py-4 flex justify-end gap-3" style="background: #232629; border-top: 1px solid #3e4146;">
                <button id="cancel-settings" class="px-6 py-3 border border-gray-500 rounded-lg bg-dark-100 text-gray-100 cursor-pointer transition-all duration-200 font-medium min-w-20 hover:bg-gray-500 hover:border-gray-400" style="background: #2c2f33; border: 1px solid #3e4146; color: #d1d2d3;">Cancel</button>
                <button id="save-settings" class="px-6 py-3 border border-primary rounded-lg bg-primary text-white cursor-pointer transition-all duration-200 font-medium min-w-20 hover:bg-blue-600 hover:border-blue-600" style="background: #00bfff; border: 1px solid #00bfff; color: white;">Save</button>
            </div>
        </div>
    </div>

  </body>
</html>
