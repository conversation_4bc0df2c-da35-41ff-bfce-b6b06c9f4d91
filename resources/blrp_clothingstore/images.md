Rizz here. 

Clothing images are stored based on the clothing combination. Due to this I've implemented the following

1. I planned this to work with updates for fivem. For each category run this command
2. This is done inside of `images_migrate.lua`

```
modifyclothing <gender> <category> <modifier> <number>

Examples:
modifyclothing male jacket increment 3

To undo this change, 

modifyclothing male jacket decrement 3
```

