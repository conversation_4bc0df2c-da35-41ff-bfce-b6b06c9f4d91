-- Clothing Images Log Table
-- This table tracks which clothing images have been processed
-- Each row represents a gender-specific item (gender is embedded in the name)

CREATE TABLE IF NOT EXISTS `clothing_images_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'File name (ex: jacket_male_321_1)',
  `model_hash` varchar(255) NOT NULL COMMENT 'Hash of clothing item (category_gender_drawable_texture)',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT 'When this clothing image was processed',
  `assigned_hands` varchar(255) DEFAULT NULL COMMENT 'Assigned hands for this clothing item',
  `custom_name` varchar(255) DEFAULT NULL COMMENT 'Custom name override',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON><PERSON>AR<PERSON> KEY (`id`),
  UNIQUE KEY `unique_model_hash` (`model_hash`),
  UNIQUE KEY `unique_name` (`name`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_processed_at` (`processed_at`),
  INDEX `idx_model_hash` (`model_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;