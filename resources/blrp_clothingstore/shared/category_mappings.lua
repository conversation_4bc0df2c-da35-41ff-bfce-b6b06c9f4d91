-- TODO: Refactor this to use existing mapping from config

CategoryMappings = {
  -- Category ID to clothing key mapping
  categoryToClothingKey = {
    ['1'] = 'mask',
    ['3'] = 'shirt',
    ['4'] = 'legs',
    ['5'] = 'bags',
    ['6'] = 'shoes',
    ['7'] = 'neck',
    ['8'] = 'shirt', -- undershirt maps to shirt
    ['9'] = 'armor',
    ['10'] = 'decals',
    ['11'] = 'jacket',
    ['p0'] = 'helmet',
    ['p1'] = 'glasses',
    ['p2'] = 'ears',
    ['p6'] = 'watches',
    ['p7'] = 'bracelets'
  },

  -- Clothing key to category ID mapping (reverse lookup)
  clothingKeyToCategory = {
    ['mask'] = '1',
    ['shirt'] = '3', -- Note: both '3' and '8' map to 'shirt'
    ['legs'] = '4',
    ['bags'] = '5',
    ['shoes'] = '6',
    ['neck'] = '7',
    ['armor'] = '9',
    ['decals'] = '10',
    ['jacket'] = '11',
    ['helmet'] = 'p0',
    ['glasses'] = 'p1',
    ['ears'] = 'p2',
    ['watches'] = 'p6',
    ['bracelets'] = 'p7'
  }
}

-- Helper function to get clothing key from category
function GetClothingKeyFromCategory(category)
  return CategoryMappings.categoryToClothingKey[tostring(category)]
end

-- Helper function to get category from clothing key
function GetCategoryFromClothingKey(clothingKey)
  return CategoryMappings.clothingKeyToCategory[clothingKey]
end
