function getBlacklist(model)
  local blacklist = blacklists[model] or blacklist_empty

  local blacklist_male_enabled_real = blacklist_male_enabled
  local blacklist_female_enabled_real = blacklist_female_enabled

  if not GlobalState.is_dev then
    blacklist_male_enabled_real = true
    blacklist_female_enabled_real = true
  end

  if blacklist.sex then
    if blacklist.sex == 'male' and not blacklist_male_enabled_real then
      blacklist = blacklist_empty
    end

    if blacklist.sex == 'female' and not blacklist_female_enabled_real then
      blacklist = blacklist_empty
    end
  end

  return blacklist
end

exports('GetBlacklist', getBlacklist)
