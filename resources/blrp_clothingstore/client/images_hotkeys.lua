-- Escape key handler for stopping processing
Citizen.CreateThread(function()
  while true do
    <PERSON>.Wait(0)

    if isGenerating then
      -- Check for ESC key (multiple control IDs to be sure)
      if IsControlJustPressed(0, 322) or IsControlJustPressed(0, 200) or IsDisabledControlJustPressed(0, 322) then
        pClothingStore.stopProcessing({})
      end

      -- Also check for raw key input
      if IsControlJustPressed(1, 322) then
        pClothingStore.stopProcessing({})
      end

      -- Check for X key as alternative
      if IsControlJustPressed(0, 73) then -- X key
        pClothingStore.stopProcessing({})
      end
    end
  end
end)
