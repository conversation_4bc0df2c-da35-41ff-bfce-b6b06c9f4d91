-- Helper function to parse category (same as clothing store)
function parseCategory(category)
  if type(category) == 'string' and string.sub(category, 1, 1) == 'p' then
    return true, tonumber(string.sub(category, 2))
  else
    return false, tonumber(category)
  end
end

-- Helper function to get drawable count (same as clothing store)
function getDrawableCount(category)
  local is_prop, index = parseCategory(category)

  if is_prop then
    return GetNumberOfPedPropDrawableVariations(PlayerPedId(), index)
  else
    return GetNumberOfPedDrawableVariations(PlayerPedId(), index)
  end
end

-- Helper function to get texture count (same as clothing store)
function getTextureCount(category, drawable)
  local is_prop, index = parseCategory(category)

  if is_prop then
    return GetNumberOfPedPropTextureVariations(PlayerPedId(), index, drawable)
  else
    return GetNumberOfPedTextureVariations(PlayerPedId(), index, drawable)
  end
end

-- Helper function to get component identifier from clothing key
function GetComponentIdentifier(clothingKey)
  local config = ImagesConfig.ClothingItems[clothingKey]
  if config and config.identifier then
    return tostring(config.identifier)
  end
  return nil
end

-- Helper function to set clothing item (component or prop)
function SetClothingItem(ped, identifier, drawable, texture)
  if type(identifier) == "string" and string.sub(identifier, 1, 1) == "p" then
    -- Prop
    local propIndex = tonumber(string.sub(identifier, 2))
    SetPedPropIndex(ped, propIndex, drawable, texture, true)
  else
    -- Component
    SetPedComponentVariation(ped, identifier, drawable, texture, 0)
  end
end

-- Helper function to set ped hands (component 3)
function SetPedHands(ped, handsValue)
  if handsValue and handsValue ~= "" then
    -- Parse hands value (format: "drawable,texture" or just "drawable")
    local drawable, texture = string.match(handsValue, "(%d+),(%d+)")
    if drawable and texture then
      SetPedComponentVariation(ped, 3, tonumber(drawable), tonumber(texture), 0)
      print(string.format("🖐️ Set hands to drawable %s, texture %s", drawable, texture))
    else
      -- Try parsing as just drawable with texture 0
      drawable = tonumber(handsValue)
      if drawable then
        SetPedComponentVariation(ped, 3, drawable, 0, 0)
        print(string.format("🖐️ Set hands to drawable %s, texture 0", drawable))
      else
        print(string.format("❌ Invalid hands value format: %s", handsValue))
      end
    end
  end
end

-- Export function to get clothing category name by key
exports('GetClothingCategoryName', function(categoryKey)
  local config = ImagesConfig.ClothingItems[categoryKey]
  if config then
    return config.name
  end
  return nil
end)

-- Export function to get all clothing category mappings
exports('GetAllClothingCategories', function()
  local categories = {}
  for key, config in pairs(ImagesConfig.ClothingItems) do
    categories[key] = config.name
  end
  return categories
end)
