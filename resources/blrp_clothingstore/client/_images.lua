-- Clothing Image Generator
-- Generates preview images for clothing items

-- Global variables
isGenerating = false
generatorPed = nil
generatorCam = nil

-- Server-controlled processing variables
local serverControlledSession = {
  active = false,
  clothingKey = nil,
  config = nil,
  currentGender = nil,
  pedSetup = false
}

Citizen.CreateThread(function()
  Citizen.Wait(1000)
  -- local result = pClothingStore.startProcessing({ GetPlayerServerId(PlayerId()), 'glasses' })
end)

-- Function to spawn a ped at configured location
local function SpawnGeneratorPed(gender)
    -- Remove existing ped if it exists
    if generatorPed and DoesEntityExist(generatorPed) then
        DeleteEntity(generatorPed)
        generatorPed = nil
        Citizen.Wait(100) -- Small wait to ensure deletion
    end

    local spawnCoords = ImagesConfig.PedSpawn.coords
    local spawnHeading = ImagesConfig.PedSpawn.heading
    local modelName = gender == "female" and ImagesConfig.PedSpawn.models.female or ImagesConfig.PedSpawn.models.male
    local model = GetHashKey(modelName)

    RequestModel(model)
    while not HasModelLoaded(model) do
        Citizen.Wait(100)
    end

    generatorPed = CreatePed(4, model, spawnCoords.x, spawnCoords.y, spawnCoords.z, spawnHeading, false, true)

    -- Set basic appearance for freemode ped
    if modelName == "mp_m_freemode_01" or modelName == "mp_f_freemode_01" then
        SetPedHeadBlendData(generatorPed, 0, 0, 0, 0, 0, 0, 0.0, 0.0, 0.0, false)
    end


  -- Ensure ped is properly created
    if not DoesEntityExist(generatorPed) then
        print("❌ Failed to create generator ped")
        return nil
    end

    print(string.format("✅ Successfully spawned %s generator ped at %s", gender, spawnCoords))

    SetBlockingOfNonTemporaryEvents(generatorPed, true)

  return generatorPed
end

-- Function to undress the ped manually (for spawned peds)
local function UndressPed(ped, category, gender)
    gender = gender or serverControlledSession.currentGender or "male"
    print(string.format("🧥 Undressing spawned %s ped...", gender))

    -- Get gender-specific undress configuration
    local undress_data = ImagesConfig.UndressConfig[gender]
    if not undress_data then
        print(string.format("❌ No undress configuration found for gender: %s", gender))
        return
    end

    -- Apply undress configuration directly to the ped
    for component, values in pairs(undress_data) do
        local drawable = values[1]
        local texture = values[2]

        if type(component) == "string" and string.sub(component, 1, 1) == "p" then
            -- Prop
            local propIndex = tonumber(string.sub(component, 2))
            if drawable == -1 then
                ClearPedProp(ped, propIndex)
                print(string.format("  Cleared prop %d", propIndex))
            else
                SetPedPropIndex(ped, propIndex, drawable, texture, true)
                print(string.format("  Set prop %d to drawable %d, texture %d", propIndex, drawable, texture))
            end
        else
            -- Component
            local componentIndex = tonumber(component)
            SetPedComponentVariation(ped, componentIndex, drawable, texture, 0)
            --print(string.format("  Set component %d to drawable %d, texture %d", componentIndex, drawable, texture))
        end
    end

    -- Apply category-specific overrides
    if category and ImagesConfig.CategoryOverrides[category] then
        local category_config = ImagesConfig.CategoryOverrides[category][gender]
        if category_config then
            --print(string.format("🎯 Applying %s-specific overrides for %s...", category, gender))
            for component, values in pairs(category_config) do
                local drawable = values[1]
                local texture = values[2]

                if type(component) == "string" and string.sub(component, 1, 1) == "p" then
                    -- Prop
                    local propIndex = tonumber(string.sub(component, 2))
                    if drawable == -1 then
                        ClearPedProp(ped, propIndex)
                        --print(string.format("  Override: Cleared prop %d", propIndex))
                    else
                        SetPedPropIndex(ped, propIndex, drawable, texture, true)
                        --print(string.format("  Override: Set prop %d to drawable %d, texture %d", propIndex, drawable, texture))
                    end
                else
                    -- Component
                    local componentIndex = tonumber(component)
                    SetPedComponentVariation(ped, componentIndex, drawable, texture, 0)
                    --print(string.format("  Override: Set component %d to drawable %d, texture %d", componentIndex, drawable, texture))
                end
            end
        else
            print(string.format("  No %s-specific overrides found for %s gender", category, gender))
        end
    end

    print("✅ Ped undressed successfully")
end

-- Function to set clothing item on ped
local function SetClothingItem(ped, identifier, drawable, texture)
    if type(identifier) == "string" and string.sub(identifier, 1, 1) == "p" then
        -- Prop
        local propIndex = tonumber(string.sub(identifier, 2))
        if drawable == -1 then
            ClearPedProp(ped, propIndex)
        else
            SetPedPropIndex(ped, propIndex, drawable, texture, true)
        end
        --print(string.format("Set prop %s: drawable=%d, texture=%d", identifier, drawable, texture))
    else
        -- Component
        SetPedComponentVariation(ped, identifier, drawable, texture, 0)
        --print(string.format("Set component %d: drawable=%d, texture=%d", identifier, drawable, texture))
    end
end

-- Function to setup camera for clothing photography with bone targeting
local function SetupCamera(ped, cameraSettings, clothingConfig)
    if generatorCam then
        DestroyCam(generatorCam, false)
    end

    generatorCam = CreateCam('DEFAULT_SCRIPTED_CAMERA', true)

    -- Get ped position and heading
    local pedPos = GetEntityCoords(ped)
    local pedHeading = GetEntityHeading(ped)

    -- Use camera settings from config
    local distance = cameraSettings.zoom
    local offsetX = cameraSettings.offsetX or 0.0
    local offsetY = cameraSettings.offsetY or 0.0
    local offsetZ = cameraSettings.offsetZ or 0.0

    -- Calculate position directly in front of the ped
    local angleRad = math.rad(pedHeading + 90.0)

    -- Apply offsets to ped position
    local targetCoords = vector3(
        pedPos.x + offsetX,
        pedPos.y + offsetY,
        pedPos.z + offsetZ
    )

    -- Position camera
    local camPos = vector3(
        targetCoords.x + (math.cos(angleRad) * distance),
        targetCoords.y + (math.sin(angleRad) * distance),
        targetCoords.z
    )

    SetCamCoord(generatorCam, camPos.x, camPos.y, camPos.z)
    PointCamAtCoord(generatorCam, targetCoords.x, targetCoords.y, targetCoords.z)
    SetCamFov(generatorCam, cameraSettings.fov)
    SetCamActive(generatorCam, true)
    RenderScriptCams(true, false, 0, true, true)
end

-- Function to take screenshot and upload to Spaces
local function TakeScreenshot(filename, callback)
    exports['screenshot-basic']:requestScreenshot({
        fileName = filename,
        encoding = 'webp',
        quality = 0.3  -- Reduced quality to prevent large files
    }, function(result)
        if result and result ~= "" then

            -- Check if result is base64 data
            local isBase64 = string.find(result, "data:image") or string.find(result, "base64,")

            if isBase64 then
                local dataSize = string.len(result)
                -- Check if data is too large for single event (FiveM limit ~64KB)
                local maxChunkSize = 20000 -- 50KB chunks to be safe


              -- Split into chunks
              local chunks = {}
              local totalChunks = math.ceil(dataSize / maxChunkSize)

              for i = 1, totalChunks do
                local startPos = (i - 1) * maxChunkSize + 1
                local endPos = math.min(i * maxChunkSize, dataSize)
                chunks[i] = string.sub(result, startPos, endPos)
              end

              -- Send chunks one by one
              for i = 1, totalChunks do
                TriggerServerEvent('blrp_clothing_images:uploadBase64Chunk', filename, chunks[i], i, totalChunks)
                Citizen.Wait(100) -- Small delay between chunks
              end
            end

            -- Continue with callback immediately (don't wait for upload)
            if callback then
                callback(result)
            end
        else
            print("❌ Screenshot failed or returned empty result")
            if callback then
                callback(result)
            end
        end
    end)
end

-- Cleanup server-controlled session
local function CleanupServerControlledSession()
    if not serverControlledSession.active then
        return
    end

    print("🧹 Cleaning up server-controlled session")

    if generatorCam then
      SetCamActive(generatorCam, false)
      RenderScriptCams(false, false, 0, true, true)
      DestroyCam(generatorCam, false)
      generatorCam = nil
      print("📷 Camera cleaned up")
    end

    -- Re-enable ambient idles when camera is cleaned up
    if generatorPed then
      SetPedCanPlayAmbientIdles(generatorPed, true, false)
    end

    if generatorPed and DoesEntityExist(generatorPed) then
      -- Delete the spawned ped
      DeleteEntity(generatorPed)
      generatorPed = nil
      print("🗑️ Generator ped deleted")
    end

   -- Disable interface state so controls can be used again
    TriggerEvent('core:client:enableInterface', 'clothing-image-generator', false)

  -- Reset session state
    serverControlledSession.active = false
    serverControlledSession.clothingKey = nil
    serverControlledSession.config = nil
    serverControlledSession.currentGender = nil
    serverControlledSession.pedSetup = false
    isGenerating = false

    print("✅ Server-controlled session cleanup complete")
end

-- Handle single image processing request from server
tClothingStore.processSingleImage = function(requestData)
  local clothingKey = requestData.clothingKey
  local config = requestData.config
  local workItem = requestData.workItem
  local assignedHands = requestData.assignedHands

  print('Processing single image: ', json.encode(requestData))

  -- Initialize session if needed
  if not serverControlledSession.active then
    if serverControlledSession.active then
      print("❌ Server-controlled session already active")
      return
    end

    serverControlledSession.active = true
    serverControlledSession.clothingKey = clothingKey
    serverControlledSession.config = config
    serverControlledSession.currentGender = workItem.gender
    serverControlledSession.pedSetup = false
    isGenerating = true
  end

  -- Setup ped and camera if needed
  if not serverControlledSession.pedSetup then

    -- Spawn generator ped
    SpawnGeneratorPed(workItem.gender)

    -- Undress the ped
    UndressPed(generatorPed, clothingKey, workItem.gender)
    Citizen.Wait(500)

    -- Setup camera
    SetupCamera(generatorPed, config.camera, config)
    Citizen.Wait(700)

    serverControlledSession.pedSetup = true
  end

  -- Check if gender changed (need to respawn ped)
  if serverControlledSession.currentGender ~= workItem.gender then
    -- Clean up current ped but keep camera
    if generatorPed and DoesEntityExist(generatorPed) then
      DeleteEntity(generatorPed)
      generatorPed = nil
    end

    -- Spawn new ped for different gender
    if not SpawnGeneratorPed(workItem.gender) then
      CleanupServerControlledSession()
      return
    end

    -- Undress the new ped
    UndressPed(generatorPed, clothingKey, workItem.gender)
    Citizen.Wait(20)

    -- Update session gender
    serverControlledSession.currentGender = workItem.gender
  end

  -- Set assigned hands if provided
  if assignedHands then
    SetPedHands(generatorPed, assignedHands)
    Citizen.Wait(100)
  else
    print("❌ No assigned hands, setting back to original hands from config")
  end

  -- Apply pose if configured for this category
  if config.pose then
    -- Check if pose is already active first
    if IsEntityPlayingAnim(generatorPed, config.pose.dict, config.pose.anim, 3) then
      print("🎭 Pose already active, skipping")
    else
      print(string.format("🎭 Applying pose for %s: %s/%s", clothingKey, config.pose.dict, config.pose.anim))

      -- Request animation dictionary
      RequestAnimDict(config.pose.dict)
      while not HasAnimDictLoaded(config.pose.dict) do
        Citizen.Wait(0)
      end

      -- Play the pose animation
      TaskPlayAnim(generatorPed, config.pose.dict, config.pose.anim, 8.0, -8.0, -1, config.pose.flag or 1, 0, false, false, false)
      Citizen.Wait(700) -- Wait for pose to settle
    end
  end

  -- Set the clothing item
  print('Setting clothing item now Drawable/Texture: ', workItem.drawable, workItem.texture)
  SetClothingItem(generatorPed, config.identifier, workItem.drawable, workItem.texture)

  -- Generate filename
  local filename = string.format("%s_%s_%d_%d.webp", clothingKey, workItem.gender, workItem.drawable, workItem.texture)

  Citizen.Wait(200)

  -- Take screenshot and send result to server
  TakeScreenshot(filename, function(result)

    -- Send result back to server using proxy with image name only
    local serverResult = pClothingStore.handleImageResult({ workItem.imageName, result })

    if not serverResult.success then
      print("❌ Failed to handle image result:", serverResult.error)
      exports.blrp_core:me().notify('❌ Processing error occurred')
    end
  end)
end

-- Handle processing completion
tClothingStore.processingComplete = function()
    print("✅ Server-controlled processing complete")
    CleanupServerControlledSession()
end

-- Command to process all categories
RegisterCommand('processallcategories', function(source, args, rawCommand)
    print("🚀 Processing all categories command triggered")

    -- Call server proxy to handle processing all categories
    local result = pClothingStore.processAllCategories({})

    if result and result.success then
        print("✅ All categories processing started successfully")
    else
        print("❌ Failed to start all categories processing:", result and result.error or "Unknown error")
    end
end, false)

-- Function to get available items for a clothing category and gender
tClothingStore.getAvailableItems = function(clothingKey, gender)
  gender = gender or "male"

  local componentKey = GetComponentIdentifier(clothingKey)
  if not componentKey then
    return {}
  end

  local availableItems = {}
  local drawableCount = getDrawableCount(componentKey)

  for drawable = 0, drawableCount - 1 do
    if exports.blrp_clothingstore:CheckCanWear(componentKey, drawable) then
      local textureCount = getTextureCount(componentKey, drawable)
      for texture = 0, textureCount - 1 do
        if exports.blrp_clothingstore:CheckCanWear(componentKey, drawable, texture) then
          table.insert(availableItems, {
            drawable,  -- Just the numbers, no keys to reduce data size
            texture
          })
        end
      end
    end
  end

  return availableItems
end

-- Function to get available items for a specific item (all variations)
tClothingStore.getSpecificItemVariations = function(clothingKey, specificItem)
  local componentKey = GetComponentIdentifier(clothingKey)
  if not componentKey then
    return {}
  end

  local availableItems = {}
  local drawable = tonumber(specificItem)

  if exports.blrp_clothingstore:CheckCanWear(componentKey, drawable) then
    local textureCount = getTextureCount(componentKey, drawable)
    for texture = 0, textureCount - 1 do
      if exports.blrp_clothingstore:CheckCanWear(componentKey, drawable, texture) then
        -- Add both genders
        for _, gender in ipairs({"male", "female"}) do
          local imageName = string.format("%s_%s_%d_%d", clothingKey, gender, drawable, texture)

          table.insert(availableItems, {
            drawable = drawable,
            texture = texture,
            imageName = imageName,
            gender = gender
          })
        end
      end
    end
  end

  return availableItems
end

RegisterNUICallback('openWardrobe', function()
  -- Close clothing store interface
  escapeNUI()

  -- Open wardrobe interface via server event (which loads proper data)
  TriggerServerEvent('blrp_wardrobe:server:openWardrobe', true, nil, true, nil)
end)

RegisterNUICallback('checkStaffPermissions', function(data, cb)
  local me = exports.blrp_core:me()
  local isStaff = me.hasOrInheritsGroup('admin') or me.isStaff()

  -- If staff requirement is disabled, allow everyone to access hands settings
  if not ImagesConfig.require_staff_for_hands then
    isStaff = true
  end

  cb({
    isStaff = isStaff
  })
end)

RegisterNUICallback('processCurrentCategory', function(data, cb)
  local category = tostring(data.category)  -- Convert to string!
  local clothingKey = GetClothingKeyFromCategory(category)

  if clothingKey then
    -- Close clothing store interface
    escapeNUI()

    -- Start server-controlled processing for this category using proxy
    local result = pClothingStore.startProcessing({ GetPlayerServerId(PlayerId()), clothingKey })
  end

  cb('ok')
end)

RegisterNUICallback('resetCurrentCategory', function(data, cb)
  local category = tostring(data.category)  -- Convert to string!
  local clothingKey = GetClothingKeyFromCategory(category)

  if clothingKey then
    -- Reset processed status for this category using proxy
    local result = pClothingStore.resetType({ clothingKey })

    if result and result.success then
      exports.blrp_core:me().notify(string.format('🗑️ Reset %d processed images for %s', result.affectedRows, clothingKey))

      -- Start processing after reset
      SetTimeout(1000, function()
        local processResult = pClothingStore.startProcessing({ GetPlayerServerId(PlayerId()), clothingKey })
      end)
    else
      exports.blrp_core:me().notify('❌ Failed to reset processed images')
    end
  end

  cb('ok')
end)

RegisterNUICallback('resetSelectedImage', function(data, cb)
  print("📸 resetSelectedImage callback received")
  print("📸 Data:", json.encode(data))

  local category = tostring(data.category) -- Convert to string
  local item = data.item
  local clothingKey = CategoryMappings.categoryToClothingKey[category]

  print("📸 Category:", category)
  print("📸 Item:", item)
  print("📸 Clothing Key:", clothingKey)

  if clothingKey then
    print("📸 Sending to server:", clothingKey, item)
    -- Send to server to reset specific item (all variations) using proxy
    local gender = GetEntityModel(PlayerPedId()) == `mp_f_freemode_01` and 'female' or 'male'
    local result = pClothingStore.resetSelectedImage({ clothingKey, item, gender })

    if result and result.success then
      exports.blrp_core:me().notify(string.format('🗑️ Reset %d images for %s item %s', result.affectedRows or 0, clothingKey, item))
    else
      exports.blrp_core:me().notify(string.format('❌ Failed to reset images: %s', result and result.error or "Unknown error"))
    end
  else
    print("📸 No clothing key found for category:", category)
    print("📸 Available categories:", json.encode(CategoryMappings.categoryToClothingKey))
  end

  cb('ok')
end)

RegisterNUICallback('removeShirt', function()
  -- Rufus
  if GetEntityModel(PlayerPedId()) == `a_m_y_methhead_01` then
    SetPedComponentVariationInternal(PlayerPedId(), 8, 1, 0, GetPedPaletteVariation(PlayerPedId(), 8))
    return
  end

  SetPedComponentVariationInternal(PlayerPedId(), 8, 15, 0, GetPedPaletteVariation(PlayerPedId(), 8))
end)

-- Generic remove clothing item callback
RegisterNUICallback('removeClothingItem', function(data, cb)
  local category = tostring(data.category)
  local ped = PlayerPedId()
  local pedModel = GetEntityModel(ped)

  -- Define default/remove values for each category
  local removeValues = {
    -- Components
    ['1'] = {0, 0},    -- Mask
    ['3'] = {15, 0},   -- Hands/Arms (male default)
    ['4'] = {61, 0},   -- Legs (male default)
    ['5'] = {0, 0},    -- Bags/Parachute
    ['6'] = {34, 0},   -- Shoes (male default)
    ['7'] = {0, 0},    -- Neck Accessories
    ['8'] = {15, 0},   -- Undershirt (male default)
    ['9'] = {0, 0},    -- Body Armor
    ['10'] = {0, 0},   -- Decals
    ['11'] = {15, 0},  -- Jacket/Torso (male default)

    -- Props
    ['p0'] = {-1, 0},  -- Hat/Helmet
    ['p1'] = {-1, 0},  -- Glasses
    ['p2'] = {-1, 0},  -- Earpiece
    ['p6'] = {-1, 0},  -- Watch
    ['p7'] = {-1, 0},  -- Bracelet
  }

  -- Adjust values for female model
  if pedModel == `mp_f_freemode_01` then
    removeValues['3'] = {15, 0}   -- Female hands default
    removeValues['4'] = {15, 0}   -- Female legs default
    removeValues['6'] = {35, 0}   -- Female shoes default
    removeValues['8'] = {14, 0}   -- Female undershirt default
    removeValues['11'] = {14, 0}  -- Female jacket default
  end

  -- Special case for Rufus model
  if pedModel == `a_m_y_methhead_01` and category == '8' then
    SetPedComponentVariationInternal(ped, 8, 1, 0, GetPedPaletteVariation(ped, 8))
    cb('ok')
    return
  end

  local removeValue = removeValues[category]
  if not removeValue then
    print('Unknown category for removal:', category)
    cb('error')
    return
  end

  -- Apply the removal
  if string.sub(category, 1, 1) == 'p' then
    -- It's a prop
    local propIndex = tonumber(string.sub(category, 2))
    if removeValue[1] == -1 then
      ClearPedProp(ped, propIndex)
    else
      SetPedPropIndex(ped, propIndex, removeValue[1], removeValue[2], true)
    end
  else
    -- It's a component
    local componentIndex = tonumber(category)
    SetPedComponentVariationInternal(ped, componentIndex, removeValue[1], removeValue[2], GetPedPaletteVariation(ped, componentIndex))
  end

  cb('ok')
end)

RegisterNUICallback('getGender', function(data, cb)
  local gender = 'male'
  if GetEntityModel(PlayerPedId()) == `mp_f_freemode_01` then
    gender = 'female'
  end

  cb({
    gender = gender
  })
end)

RegisterNUICallback('getClothingMappings', function(data, cb)
  -- Use the shared category mappings instead of hardcoded values
  local mappings = {}

  -- Copy from shared category mappings
  for categoryId, clothingKey in pairs(CategoryMappings.categoryToClothingKey) do
    mappings[categoryId] = clothingKey
  end

  cb({
    mappings = mappings
  })
end)

-- Get item settings callback
RegisterNUICallback('getItemSettings', function(data, cb)
  local result = pClothingStore.getItemSettings({ data.category, data.item })

  cb(result)
end)

-- Update item settings callback
RegisterNUICallback('updateItemSettings', function(data, cb)
  -- Convert category to clothing key
  local clothingKey = GetClothingKeyFromCategory(data.category)
  if not clothingKey then
    cb({success = false, error = 'Unknown category: ' .. tostring(data.category)})
    return
  end

  -- Get current gender
  local gender = 'male'
  if GetEntityModel(PlayerPedId()) == `mp_f_freemode_01` then
    gender = 'female'
  end

  -- Get the hands value for the current gender
  local assignedHands = gender == 'male' and data.settings.assigned_hands_male or data.settings.assigned_hands_female

  -- If no hands are passed, get the current hands on the ped
  if not assignedHands or assignedHands == '' then
    local ped = PlayerPedId()
    assignedHands = GetPedDrawableVariation(ped, 3) -- Component 3 is hands

    print(string.format("🖐️ No hands specified, using current ped hands: %s", assignedHands))
  end

  local result = pClothingStore.updateItemSettings({ clothingKey, data.item, gender, assignedHands })
  cb(result)
end)

-- Apply hands to current jacket item callback
RegisterNUICallback('applyHandsForCurrentJacket', function(data, cb)
  local category = data.category
  local categoryId = tonumber(category) or 11 -- Default to jacket
  local ped = PlayerPedId()
  local currentItem = GetPedDrawableVariation(ped, categoryId)

  -- Get ped gender
  local gender = GetEntityModel(ped) == `mp_f_freemode_01` and 'female' or 'male'

  local clothingKey = GetClothingKeyFromCategory(category)

  -- Send to server with current item and gender
  local settings = pClothingStore.getItemSettings({ clothingKey, currentItem, gender })

  print('settings fetched', settings.assigned_hands)

  if not settings then
    cb({ success = false, error = 'Failed to get item settings' })
    exports.blrp_core:me().notify('No settings set for item. This is not a bug.')
    return
  end

  if not settings.assigned_hands then
    cb({ success = false, error = 'No hands assigned for item. This is not a bug.' })
    exports.blrp_core:me().notify('No hands assigned for item. This is not a bug')
    return
  end

  exports.blrp_core:me().notify('Applying hands: ' .. settings.assigned_hands)
  SetPedComponentVariation(ped, 3, tonumber(settings.assigned_hands), 0, 0) -- Component 3 is hands

  cb({ result = success })
end)
