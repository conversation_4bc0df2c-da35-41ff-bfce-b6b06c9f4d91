tQuests = T.getInstance('blrp_quests', 'main')
tZones = T.getInstance('blrp_zones', 'zones')
tCore = T.getInstance('blrp_core', 'core')

RegisterNetEvent('camp_morningwood:server:swipeKeycard', function(_, event_data)
  local character = exports.blrp_core:character(source)

  if not event_data or not event_data.position then
    return
  end

  local keypad_coords

  if event_data.keypad_coords == 'inner' then
    keypad_coords = vector3(2540.941, 1717.936, 19.613)
  else
    keypad_coords = vector3(2542.931, 1717.035, 19.613)
  end

  if #(character.getCoordinates() - keypad_coords) > 3.0 then
    return
  end

  if not character.hasItemQuantity('cm25_dbunkercard', 1, false, true) then
    character.notify("You don't have a keycard for this.")
    return
  end

  tCore.swipeKeycardScene(character.source, { keypad_coords, `ch_prop_fingerprint_scanner_01e`, `ch_prop_swipe_card_01d` }, {
    swiped = function()
      exports.blrp_doors:OverrideByUid('bunker_door_1', false)

      Citizen.Wait(3000)

      exports.blrp_doors:OverrideByUid('bunker_door_1', true)
    end
  })
end)

RegisterNetEvent('camp_morningwood:server:swipeKeycardBunker', function(_, event_data)
  local character = exports.blrp_core:character(source)
  if not character then return end

  if not event_data or not event_data.position then
    return
  end

  local keypad_coords
  if event_data.keypad_coords == 'inner' then
    keypad_coords = vector3(-2056.896, 3230.69, -16.045)
  else
    keypad_coords = vector3(-2059.022, 3228.012, -16.045)
  end

  if #(character.getCoordinates() - keypad_coords) > 3.0 then
    return
  end

  if os.time() < os.time({ year = 2025, month = 8, day = 4, hour = 14 }) then
    character.notify('Door security system time-locked, pending scheduled maintenance 08-04-2025')
    return
  end

  local meta, item_id = character.hasGetItemMetaWithProperty('cm25_sublvlkey', function(meta)
    return meta and tonumber(meta.dur_cur or 0) > 0
  end)

  if not item_id or not meta then
    character.notify("You don't have a working keycard for this door.")
    return
  end

  local current_dur = tonumber(meta.dur_cur or 0)
  local new_dur = math.max(current_dur - 1, 0)
  exports.blrp_core:ModifyItemMeta(source, item_id, 'dur_cur', new_dur)

    if new_dur == 0 then
    character.notify("The screen on the keypad displays: Keycard disabled")
  elseif new_dur <= 2 then
    character.notify("The screen on the keypad displays: Keycard has 1 remaining swipe")
  end

  tCore.swipeKeycardScene(character.source, { keypad_coords, `ch_prop_fingerprint_scanner_01e`, `ch_prop_swipe_card_01d` }, {
    swiped = function()
      exports.blrp_doors:OverrideByUid('bnkr_clearance_13_a', false)
      exports.blrp_doors:OverrideByUid('bnkr_clearance_13_b', false)

      Citizen.Wait(15000)

      exports.blrp_doors:OverrideByUid('bnkr_clearance_13_a', true)
      exports.blrp_doors:OverrideByUid('bnkr_clearance_13_b', true)
    end
  })
end)

local mulderite_spots = {
  ['crashsite1'] = vector3(-3226.105, 2318.681, -55.36375),
  ['crashsite2'] = vector3(-3223.548, 2347.796, -62.509),
  ['crashsite3'] = vector3(-3220.522, 2353.772, -72.86331),
}

local mulderite_harvest_timeouts = {}

RegisterNetEvent('camp_morningwood:server:harvestMetal', function(_, event_data)
  local character = exports.blrp_core:character(source)
  local character_id = tonumber(character.get('id'))
  local coords = character.getCoordinates()

  local location_key = nil

  for key, loc_coords in pairs(mulderite_spots) do
    if #(coords - loc_coords) < 3.0 then
      location_key = key
      break
    end
  end

  if not location_key then
    character.notify("There's nothing to extract here.")
    return
  end

  if not character.hasItemQuantity('cm25_xtool', 1, false, true) then
    character.notify("You don't have the right tool for this.")
    return
  end

  local timeout_key = location_key .. "_" .. character_id
  local today = os.date('%Y-%m-%d')

  if mulderite_harvest_timeouts[timeout_key] == today then
    character.notify("You already extracted material from this spot today.")
    return
  end

  local playSound = os.time()
  Citizen.CreateThread(function()
    while playSound and playSound + 12 > os.time() do
      character.playSoundAround(5, 'grinder', 2)
      Citizen.Wait(5000)
    end
  end)

  local success = character.progressPromise('Cutting Strange Metal', 15.5, {
    animation = {
      animDict = "anim@heists@fleeca_bank@drilling",
      anim = "drill_straight_end",
      flags = 49
    },
    prop = {
      model = 'prop_tool_consaw',
      bone = 28422,
      coords = { x = 0.0, y = 0.09, z = 0.05 },
      rotation = { x = -70.30098, y = 71.0092, z = 83.75942 },
    }
  })
  if not success then return end

  character.give('cm25_xmetal', 1)
  character.client('blrp_inventory:hide')
  character.log('CM25-QUEST', 'Collected Close Encounters quest item', {
  item = 'cm25_xmetal',
  location = location_key,
  })

  tQuests.runDialogue(character.source, {{
    { 'item:' .. 'cm25_xmetal', false, "You cut off a chunk of the strange metal." },
  }})

  mulderite_harvest_timeouts[timeout_key] = today
end)

RegisterNetEvent('camp_morningwood:server:unlockBunkerGarage', function(_, event_data)
  local character = exports.blrp_core:character(source)

  if not event_data or not event_data.position or not event_data.entity_hash then
    return
  end

  local input = character.prompt("Enter the code to unlock the door.")

  if not input or input ~= '6759' then
    character.notifyError("Incorrect code.")
    return
  end

  exports.blrp_doors:OverrideByUid('bunker_gar_r', false)

  character.notify("Access granted.")

  Citizen.SetTimeout(3000, function()
    exports.blrp_doors:OverrideByUid('bunker_gar_r', true)
  end)
end)

RegisterNetEvent('camp_morningwood:server:releaseBunkerGarage', function(_, event_data)
  local character = exports.blrp_core:character(source)

  if not event_data or not event_data.position or not event_data.entity_hash then
    return
  end

  exports.blrp_doors:OverrideByUid('bunker_gar_r', false)

  character.notify("Door Opening...")

  Citizen.SetTimeout(3000, function()
    exports.blrp_doors:OverrideByUid('bunker_gar_r', true)
  end)
end)

RegisterNetEvent('camp_morningwood:server:dwightOpenDoor', function(_, event_data)
    local character = exports.blrp_core:character(source)
    local hasKeycard = character.hasItemQuantity('cm25_dbunkercard', 1, false, true)
    local givenKeycard = character.getUserData('cm25:has_dwight_keycard', false)

    local function DoorFunction()
        Citizen.SetTimeout(2500, function()
            exports.blrp_doors:OverrideByUid('bunker_door_1', false)
            character.notify("Dwight opens the door...")
            Citizen.SetTimeout(5000, function()
                exports.blrp_doors:OverrideByUid('bunker_door_1', true)
            end)
        end)
    end

    if not event_data or not event_data.position or not event_data.entity_hash then
        return
    end

    if hasKeycard then
        character.dpEmote('knock')
        Citizen.Wait(3000)
        character.client('vrp:client:stopAnimation', true)
        tQuests.runDialogue(character.source, {{
            { 'CM25DWIGHT', false, "Use the keycard I gave you! I'm extremely busy." },
        }})
    elseif givenKeycard then
        character.dpEmote('knock')
        Citizen.Wait(3000)
        character.client('vrp:client:stopAnimation', true)
        tQuests.runDialogue(character.source, {{
            { 'CM25DWIGHT', false, "Use the keycard I gave you! I'm extremely busy." },
            { false, 'SELF', "I don't have it." },
            { 'CM25DWIGHT', false, "Well you better go and fetch it then! I don't have time for this." },
        }})
    else
        character.dpEmote('knock')
        Citizen.Wait(3000)
        character.client('vrp:client:stopAnimation', true)
        tQuests.runDialogue(character.source, {{
            { 'CM25DWIGHT', false, "I'm coming! Quit pounding at my door!" },
        }})
        DoorFunction()
    end
end)

local goo_collect_timeouts = {}

RegisterNetEvent('camp_morningwood:server:collectGoo', function(_, event_data)
  local character = exports.blrp_core:character(source)

  local character_id = tonumber(character.get('id'))
  local today = os.date('%Y-%m-%d')
  if goo_collect_timeouts[character_id] == today then
    character.notify("You've already collected a sample of this today.")
    return
  end

  if not character.hasItemQuantity('empty_vial', 1) then
    character.notify("You don't have anything to collect this with.")
    return
  end

  if not event_data or not event_data.position then
    return
  end

  local success = character.progressPromise('Collecting Goo Sample', 8, {
    animation = { animDict = "mp_arresting", anim = 'a_uncuff', flags = 49 }
  })
  if not success then return end

  character.take('empty_vial', 1, false)
  character.give('cm25_xgoo', 1, false)
  character.client('blrp_inventory:hide')
  character.log('CM25-QUEST', 'Collected Close Encounters quest item', {
  item = item_id,
  })

  tQuests.runDialogue(character.source, {{
    { 'item:' .. 'cm25_xgoo', false, "You scoop up a sample of the goo into the vial." },
  }})

  goo_collect_timeouts[character_id] = today
end)

RegisterNetEvent('blrp_radiation:tooRadiated', function()
  local character = exports.blrp_core:character(source)
  local damage = -10

  character.varyHealth(damage)
  character.log('HEALTH', 'Received ' .. damage .. ' damage from radiation exposure')
end)

RegisterNetEvent('blrp_radiation:damageHazmatSuit')
AddEventHandler('blrp_radiation:damageHazmatSuit', function(damage)
  local src = source
  local character = exports.blrp_core:character(src)
  local ped = GetPlayerPed(character.source)
  if not character then return end

  local meta, item_id = character.hasGetItemMetaWithProperty('clth_hazmat_suit', function(meta)
    return meta and tonumber(meta.dur_cur or 0) > 0
  end)

  if item_id and meta then
    local cur = tonumber(meta.dur_cur or 180)
    local new_dur = math.max(cur - damage, 0)

    exports.blrp_core:ModifyItemMeta(src, item_id, 'dur_cur', new_dur)

      if new_dur == 50 then
        character.notify("Your mask cracked, it won't be useful for much longer.")
      end

      if new_dur <= 0 then
        character.notify("Your mask broke and you begin to feel the radiation taking effect!")
        character.log('CM25-QUEST', 'Hazmat suit broke in radiated zone', {
        suit = item_id,
        suit_health = new_dur,
        })
        SetPedComponentVariation(ped, 1, 0, 0, 0)
      end
  end
end)

RegisterNetEvent('camp_morningwood:server:boilCanteen')
AddEventHandler('camp_morningwood:server:boilCanteen', function()
  local src = source
  local character = exports.blrp_core:character(src)
  if not character then return end

  local meta, item_id = character.hasGetItemMetaWithProperty('cm25_canteen', function(m)
    return (tonumber(m.dur_cur or 0) > 0)
  end)

  if not item_id or not meta then
    character.notify("You don't have anything to boil.")
    return
  end

  local boilSuccess = character.progressPromise('Boiling Water', 20, {
    animation = {
      animDict = 'bzzz@animation@camp',
      anim     = 'bzzz_camp',
      flags    = 49,
    },
    prop = {
      model = 'bzzz_prop_military_canteen_a',
      bone  = 57005,
      coords = { x = 0.12, y = 0.05,  z = -0.02 },
      rotation = { x = -63.0, y = 15.0, z = -2.0 },
    }
  })

  if not boilSuccess then
    character.notify("You pulled the canteen away before the water boiled.")
    return
  end

  exports.blrp_core:ModifyItemMeta(src, item_id, 'is_boiled', true, true)
  exports.blrp_core:ModifyItemMeta(src, item_id, 'label', 'Clean Water')

  character.notify("You boiled the water in the canteen. It's now safe to drink.")
end)

local time_out_boat = 0
local time_out_bunker = 0
local time_out_radiate = 0

RegisterNetEvent('camp_morningwood:server:zancudoboat')
AddEventHandler('camp_morningwood:server:zancudoboat', function()
  local src = source
  local character = exports.blrp_core:character(src)

  -- Validate character
  if not character then
    print('[camp_morningwood:server:zancudoboat] Error: No character found for source ' .. src)
    return
  end

  if time_out_boat > os.time() then
    return
  end

  -- Set cooldown (10 mins)
  time_out_boat = os.time() + 600

  exports.blrp_core:group('Fort Zancudo').alert({
    coords = vector3(-5821.972, 1326.022, 17.800),
    location_override = 'Boat',
    badge = 'Zancudo',
    badge_style = 'danger',
    msg = 'Unauthorized access to Fort Zancudo patrol boat detected!',
    icon = 'fa-regular fa-triangle-exclamation',
    sound = 'ToneP3',
  })

  -- Play alarm sound at boat location after random delay (5-10 minutes)
  SetTimeout(math.random(5, 10) * 60 * 1000, function()
    TriggerClientEvent('InteractSound_CL:PlayWithincordinateDistance', -1, vector3(-5819.758, 1335.79, 16.80515), 90.0, 'red_alert_alarm', 0.03)
  end)
end)

RegisterNetEvent('camp_morningwood:server:zancudobunker')
AddEventHandler('camp_morningwood:server:zancudobunker', function()
  local src = source
  local character = exports.blrp_core:character(src)

  -- Validate character
  if not character then
    print('[camp_morningwood:server:zancudoboat] Error: No character found for source ' .. src)
    return
  end

  if time_out_bunker > os.time() then
    return
  end

  -- Set cooldown (10 mins)
  time_out_bunker = os.time() + 600

  exports.blrp_core:group('Fort Zancudo').alert({
    coords = vector3(-2054.124, 3241.643, 31.497),
    location_override = 'Bunker',
    badge = 'Zancudo',
    badge_style = 'danger',
    msg = 'Unauthorized access to Fort Zancudo bunker detected!',
    icon = 'fa-regular fa-triangle-exclamation',
    sound = 'ToneP3',
  })
end)

RegisterNetEvent('camp_morningwood:server:radiatedwarn')
AddEventHandler('camp_morningwood:server:radiatedwarn', function()
  local src = source
  local character = exports.blrp_core:character(src)

  if not character then return end

  character.notifyError('[WARNING] Any unauthorised personnel beyond this point will be opened fire upon!', 20000)

  if time_out_radiate > os.time() then return end
    exports.blrp_core:group('Fort Zancudo').alert({
      flash = true,
      coords = vector3(-2052.836, 3219.552, -45.453),
      location_override = 'Redacted',
      badge = 'Zancudo',
      badge_style = 'danger',
      title = 'CONTAINMENT BREACH',
      msg = 'Unauthorised access to Zancudo containment zone detected!',
      icon = 'fa-regular fa-triangle-exclamation',
      sound = 'ToneP3',
    })

  -- exports.blrp_core:SpawnZancudoMilitary()

  time_out_radiate = os.time() + 600

  TriggerClientEvent('InteractSound_CL:PlayWithincordinateDistance', -1, vector3(-2052.957, 3220.007, -43.74461), 10.0, 'allarm', 0.15)
end)