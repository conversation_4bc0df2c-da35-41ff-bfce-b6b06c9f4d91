tRadiation = {}

local radiation_percent = 0
local radiation_tick_time = 0
local radiation_tick_percent = 4.44
local decontaminate_until = nil
local decontaminate_power = 1.0
local radiation_effect_playing = false
local last_radiated_state = nil

local radiated_room_hashes = {
  [-277329710] = true,
  [866094678] = true,
  [-1280160856] = true,
  [-1579894475] = true,
  [499522575] = true,
  [671166597] = true,
  [1714842308] = true,
  [-315279172] = true,
  [-117021936] = true,
  [1187976589] = true,
  [-1738790462] = true,
  [-1639187673] = true,
  [376479556] = true,
  [-1458813831] = true,
  [-2099785631] = true,
  [770934098] = true,
  [-1116396409] = true,
  [-167870369] = true,
  [-335099887] = true,
  [-537969462] = true,
  [-2124638892] = true,
  [-745093006] = true,
}

local function isWearingHazmatSuit()
  local ped = PlayerPedId()
  local drawable = GetPedDrawableVariation(ped, 1)
  local texture = GetPedTextureVariation(ped, 1)
  local model = GetEntityModel(ped)

  return model and drawable == 175 and texture == 1
end

tRadiation.decontaminateByPercent = function(percent)
  radiation_percent = math.max(0, radiation_percent - percent)
end

tRadiation.startDecontamination = function(duration, power)
  decontaminate_until = GetGameTimer() + (duration * 1000) + math.random(-60000, 60000)
  decontaminate_power = power or 1.0
end

function damageHazmatSuit()
  TriggerServerEvent('blrp_radiation:damageHazmatSuit', 1)
end

function radiationTick()
  local player_ped = PlayerPedId()
  local me = exports.blrp_core:me()
  local character_selected = exports.blrp_characterselect:isCharacterSelected()
  local room_hash = GetRoomKeyFromEntity(player_ped)
  local in_radiation_room = radiated_room_hashes[room_hash] == true
  local MilPersonel = exports.blrp_core:me().hasBusinessPerm('Fort Zancudo', 'extra_a')
  local IsDead = exports.blrp_core:me().isInComa()

  if not character_selected or me.get('god_active') or MilPersonel then
    radiation_percent = 0
    return false
  end

    local is_in_radiated_room = in_radiation_room
  if last_radiated_state ~= is_in_radiated_room then
    TriggerServerEvent('blrp_radiation:setRadiatedState', is_in_radiated_room)
    last_radiated_state = is_in_radiated_room
  end

  local is_decontaminating = false

  if decontaminate_until and decontaminate_until > GetGameTimer() then
    is_decontaminating = true
  else
    decontaminate_power = 1.0
  end

  if radiation_tick_time < GetGameTimer() - 3000 then
    local is_wearing_suit = isWearingHazmatSuit()

    if in_radiation_room and is_wearing_suit then
      damageHazmatSuit()
    end

    if in_radiation_room and not isWearingHazmatSuit() then
      if is_decontaminating then
        radiation_percent = math.max(0, radiation_percent - (radiation_tick_percent * decontaminate_power))
      else
        radiation_percent = math.min(100, radiation_percent + radiation_tick_percent)
      end
    else
      radiation_percent = math.max(0, radiation_percent - radiation_tick_percent)
    end

    radiation_tick_time = GetGameTimer()
  end

  if radiation_percent >= 20 and not radiation_effect_playing then
    radiation_effect_playing = true
    AnimpostfxPlay('ChopVision', 0, true)
  elseif radiation_percent < 20 and radiation_effect_playing then
    radiation_effect_playing = false
    AnimpostfxStop('ChopVision')
  end

  if not IsDead and in_radiation_room and radiation_percent >= 100 then
    TriggerServerEvent('blrp_radiation:tooRadiated')
  end

  return is_decontaminating
end

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(3000)

    local is_decontaminating = radiationTick()
    local room_hash = GetRoomKeyFromEntity(PlayerPedId())
    local should_show = radiated_room_hashes[room_hash] or radiation_percent > 0

    pcall(function()
      exports.blrp_hud:SetRadiationData({
        visible = should_show,
        radiation = radiation_percent,
        decontaminating = is_decontaminating,
      })
    end)
  end
end)