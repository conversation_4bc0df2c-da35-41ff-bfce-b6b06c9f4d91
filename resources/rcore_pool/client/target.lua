function GetTargetFunction()
    return Config.TargetResourceName
end

Citizen.CreateThread(function()
    if not GetTargetFunction() then
        return
    end
    exports[GetTargetFunction()]:AddTargetModel(POOL_RACKS, {
        options = {
            {
                event_client = "rcore_pool:target:takeCue",
                icon = "fas fa-arrow-right",
                label = Config.Text.HINT_TAKE_CUE,
                filter = function()
                    return not HasPoolCueInHand
                end
            },
            {
                event_client = "rcore_pool:target:returnCue",
                icon = "fas fa-arrow-left",
                label = Config.Text.HINT_RETURN_CUE,
                filter = function()
                    return HasPoolCueInHand
                end
            },
        },
        distance = 2
    })
    
    local costSuffix = ''

    if Config.PayForSettingBalls and Config.BallSetupCost then
        costSuffix = ' ($' .. Config.BallSetupCost .. ')'
    end

    exports[GetTargetFunction()]:AddTargetModel(ALLOWED_MODELS, {
        options = {
            {
                event_client = "rcore_pool:target:playTable",
                icon = "fas fa-arrow-right",
                label = Config.Text.HINT_PLAY,
                filter = function()
                    return ClosestTableAddress and CurrentState == STATE_NONE and HasPoolCueInHand and not TableData[ClosestTableAddress].player and #TableData[ClosestTableAddress].balls > 0
                end,
                num = 1,
            },
            {
                event_client = "rcore_pool:target:setupStraightPool",
                icon = "fas fa-arrow-left",
                label = (Config.Text.POOL_SETUP or 'Setup: ') .. Config.Text.TYPE_STRAIGHT .. costSuffix,
                filter = function()
                    return ClosestTableAddress and CurrentState == STATE_NONE and HasPoolCueInHand and not TableData[ClosestTableAddress].player
                end,
                num = 2,
            },
            {
                event_client = "rcore_pool:target:setup8ball",
                icon = "fas fa-arrow-left",
                label = (Config.Text.POOL_SETUP or 'Setup: ') .. Config.Text.TYPE_9_BALL .. costSuffix,
                filter = function()
                    return ClosestTableAddress and CurrentState == STATE_NONE and HasPoolCueInHand and not TableData[ClosestTableAddress].player
                end,
                num = 3,
            },
            {
                event_client = "rcore_pool:target:noop",
                icon = "fas fa-arrow-left",
                label = Config.Text.HINT_HINT_TAKE_CUE,
                filter = function()
                    return not HasPoolCueInHand
                end,
                num = 4,
            },
        },
        distance = 2
    })
end)

AddEventHandler('rcore_pool:target:playTable', function()
    if ClosestTableAddress then
        RequestPlayTable(ClosestTableAddress)
    else
        print("ERROR: NO CLOSEST TABLE")
    end
end)
AddEventHandler('rcore_pool:target:setupStraightPool', function()
    TriggerEvent('rcore_pool:setupTable', 'BALL_SETUP_STRAIGHT_POOL')
end)
AddEventHandler('rcore_pool:target:setup8ball', function()
    TriggerEvent('rcore_pool:setupTable', 'BALL_SETUP_9_BALL')
end)

AddEventHandler('rcore_pool:target:takeCue', function()
    TriggerServerEvent('rcore_pool:requestPoolCue')
end)

AddEventHandler('rcore_pool:target:returnCue', function()
    TriggerServerEvent('rcore_pool:requestRemoveCue')
end)