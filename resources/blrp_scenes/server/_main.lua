local all_scenes = {}
local scene_counter = 0

Citizen.CreateThread(function()
  local rows = MySQL.query.await('SELECT * FROM core_persistent_scenes')

  for _, row in pairs(rows) do
    scene_counter = scene_counter + 1

    local scene_id = scene_counter
    local _coords = json.decode(row.coords)
    local bg_color = row.bg_color

    if bg_color and string.find(bg_color, ',') then
      bg_color = json.decode(bg_color)
    end

    if _coords.x and _coords.y and _coords.z then
      all_scenes[scene_id] = {
        id = scene_id,
        message = row.message,
        color = json.decode(row.color),
        distance = row.distance,
        coords = vector3(_coords.x, _coords.y, _coords.z),
        persistent = true,
        db_id = row.id,
        font_size = row.font_size,
        bg_color = bg_color,
        emote = row.emote,
        font_style = row.font_style or 0,
        instance = row.instance or 'global',
        background_type = row.background_type or nil,
      }
    end
  end

  -- Allows dev rebooting
  if GlobalState.is_dev then
    Citizen.Wait(1000)

    tScenes.setScenes(-1, { all_scenes })
  end


  print('^3[blrp_scenes] Loaded ' .. scene_counter .. ' persistent scenes^7')
end)

pScenes = {}
P.bindInstance('scenes', pScenes)
tScenes = T.getInstance('blrp_scenes', 'scenes')

pScenes.getAll = function()
  return all_scenes
end

pScenes.add = function(packet)
  local coords = packet.coords
  local message = packet.message
  local color = packet.color
  local distance = packet.distance
  local persistent = packet.persistent
  local bg_color = packet.bg_color
  local font_size = packet.font_size
  local emote = packet.emote
  local font_style = packet.font_style
  local instance = packet.instance
  local bg_type = packet.background_type
  local mysql_bg_color = bg_color

  local character = exports.blrp_core:character(source)

  if type(bg_color) == 'table' then
    mysql_bg_color = json.encode(bg_color)
  end

  -- Blacklist for inappropriate messages
  local blacklist = {
    "cum", "jizz", "dick", "pussy", "sex", "cock", "orgasm", "ejaculate", "retard", "splooge", "man juice", "creampie", "baby gravy", "squirt juice"
  }

  -- Convert message to lowercase and check against the blacklist
  local lower_message = string.lower(message)
  for _, word in ipairs(blacklist) do
    if string.find(lower_message, word) then
      character.notify("Your scene message contains inappropriate content and cannot be created.")
      return
    end
  end


  scene_counter = scene_counter + 1

  local scene_id = scene_counter

  if exports.blrp_core:ScanInputForBadWords(character.source, 'scenes', message) then
    return
  end

  local insert_id = nil

  if
    persistent and
    string.lower(persistent) == 'yes' and
    character.hasPermissionCore('scene.persist') and
    character.request('Post persistent scene for $500?') and
    character.tryPayment(500)
  then
    insert_id = MySQL.insert.await('INSERT INTO core_persistent_scenes (user_id, character_id, message, coords, distance, color, bg_color, font_size, instance, emote, font_style, created_at, background_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', {
      character.get('identifier'),
      character.get('id'),
      message,
      json.encode(coords),
      distance,
      json.encode(color),
      mysql_bg_color,
      font_size,
      instance,
      emote,
      font_style,
      os.date('%Y-%m-%d %H:%M:%S'),
      bg_type,
    })

    persistent = true
  else
    persistent = false
  end

  local scene = {
    id = scene_id,
    message = message,
    color = color,
    distance = distance,
    coords = coords,
    persistent = persistent,
    db_id = insert_id,
    bg_color = bg_color,
    font_size = font_size,
    emote = emote,
    font_style = font_style,
    instance = character.getInstance(),
    background_type = bg_type,
  }

  all_scenes[scene_id] = scene

  tScenes.add(-1, { scene })

  exports.blrp_core:LogDiscord('chat', '**SCENE**: **' .. character.getStringIdentifierVrpOnly() .. '** message = ' .. message .. ' / coords = ' .. coords .. ' / persistent = ' .. tostring(persistent), character.getStringIdentifierVrpOnly())
  character.notify('Scene Created')
  character.log('ACTION', 'Created Scene / message = ' .. message .. ' / coords = ' .. coords .. ' / persistent = ' .. tostring(persistent))
end

pScenes.remove = function(scene_id)
  local character = exports.blrp_core:character(source)

  local scene = all_scenes[scene_id]

  if not scene then
    return
  end

  if scene.persistent and not character.hasPermissionCore('scene.persist') then
    character.notify('You cannot delete this scene')
    return
  end

  if scene.persistent and not character.request('The scene you are trying to delete is persistent. Are you sure you wish to delete it?', 20) then
    return
  end

  if scene.persistent and scene.db_id then
    MySQL.Async.execute('DELETE FROM core_persistent_scenes WHERE id = @id LIMIT 1', {
      id = scene.db_id
    })
    character.log('ACTION', 'Deleted persistent scene / message = ' .. scene.message .. ' / coords = ' .. scene.coords)
  end

  all_scenes[scene_id] = nil

  tScenes.remove(-1, { scene_id })

  character.notify('Scene Deleted')
end

AddEventHandler('core:server:registerSelectedPlayer', function(player, character)
  tScenes.setScenes(player, { all_scenes })
end)
