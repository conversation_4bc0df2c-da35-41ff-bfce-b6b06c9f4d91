Config = {}
Config.Locale = 'en' --html locale you can change at client/html/script.js
-- Only debug function
Config.Debug = false
Config.DebugLevel = {
  'CRITICAL',
  'ERROR',
}

-- Standalone = 1
-- ESX = 2
-- QBCore = 3
Config.FrameWork = "1"

Config.GetQBCoreObject = function()
  -- Choose your objectType or made here your own.
  local objectType = "1"

  if objectType == "1" then
    return exports['qb-core']:GetCoreObject()
  end

  if objectType == "2" then
    return exports['qb-core']:GetSharedObject()
  end

  if objectType == "3" then
    local QBCore = nil
    local breakPoint = 0
    while not QBCore do
      Wait(100)
      TriggerEvent("QBCore:GetObject", function(obj)
        QBCore = obj
      end)

      breakPoint = breakPoint + 1
      if breakPoint == 25 then
        print(string.format("^1[%s]^7 Could not load the sharedobject, are you sure it is called ^1˙QBCore:GetObject˙^7?", GetCurrentResourceName()))
        break
      end
    end

    return QBCore
  end
end

Config.DebugDraw = false
Config.DebugDrawOnlyShots = false
--
Config.Payment = 0 --Global payment price
--If you have different calls
--Rcore notification can be found at our discord free to download
Config.UseRcoreNotification = false --You have to install script rcore_notification first

---IF YOU DONT USE OWN SOLUTION USE THIS (check server/framework/events.lua)
Config.RemoveAndReturnWeapon = false --Will remove weapons and add rented weapons, after leaving line it will return player guns

--Marker setup
Config.NearObjectDistance = 50.0
Config.CheckPlayerPosition = 500

Config.InRadius = 0.3 --In marker radius
Config.RenderDistance = 25.0 --Marker render

--Do not change this if you dont know what are u doing
Config.DefaultBlipOptions = {
  scale = 1.0,
  shortRange = true,
  type = 4,
  color = 55,
}
Config.Offsets = {
  center = {
    x = 0,
    y = 0,
    z = 0.52
  },
  head = {
    x = 0,
    y = 0,
    z = 1.05
  }
}

local guns = { --You can use guns variable for all guns or you can setup specifically every target line
  {
    hash = 'WEAPON_PISTOL',
    label = 'Use your own weapon',
    ammo = 0,
    rentPrice = 0,
  },
}

local sandy_target_short = {
  { --Target menu index 1
    pos = vector3(952.585, 3584.289, 33.077),
    model = GetHashKey('gr_prop_gr_target_05b'),
    heading = 178.882,

    -- if the shooting points dont go upwards/side ways
    -- 1 = y, z are used
    -- 2 = x, z are used
    -- 3 = z, y are used as a render coords.
    SwitchAxis = "2",

    -- if the position of point need correction. Make it here
    VectorXoffset = 0.0,
    VectorYoffset = 0.2,

    -- if you shoot lets say 7 on right side and it will
    -- display it on left side. Switch one of these
    -- Axis to true so it will correct the position of point.
    ReverseAxisX = true,
    ReverseAxisY = false,
    ReverseAxisZ = false,
  },
}
local sandy_target_mid = {
  { --Target menu index 2
    pos = vector3(959.793, 3589.256, 34.223),
    model = GetHashKey('gr_prop_gr_target_05b'),
    heading = 178.882,

    -- if the shooting points dont go upwards/side ways
    -- 1 = y, z are used
    -- 2 = x, z are used
    -- 3 = z, y are used as a render coords.
    SwitchAxis = "2",

    -- if the position of point need correction. Make it here
    VectorXoffset = 0.0,
    VectorYoffset = 0.2,

    -- if you shoot lets say 7 on right side and it will
    -- display it on left side. Switch one of these
    -- Axis to true so it will correct the position of point.
    ReverseAxisX = true,
    ReverseAxisY = false,
    ReverseAxisZ = false,
  },
}
local sandy_target_long = {
  { --Target menu index 3
    pos = vector3(965.667, 3596.656, 34.277),
    model = GetHashKey('gr_prop_gr_target_05b'),
    heading = 178.882,

    -- if the shooting points dont go upwards/side ways
    -- 1 = y, z are used
    -- 2 = x, z are used
    -- 3 = z, y are used as a render coords.
    SwitchAxis = "2",

    -- if the position of point need correction. Make it here
    VectorXoffset = 0.0,
    VectorYoffset = 0.2,

    -- if you shoot lets say 7 on right side and it will
    -- display it on left side. Switch one of these
    -- Axis to true so it will correct the position of point.
    ReverseAxisX = true,
    ReverseAxisY = false,
    ReverseAxisZ = false,
  },
}

Config.Gunranges = {
  {
    menu = {
      {
        valueIndex = 1,
        label = '<span style="font-size: 20px;">Distance</span><div style="color:green;">Renting price: ' .. Config.Payment .. '$</div>',
      },
    },
    targets = {
      [1] = {
        guns = guns,
        marker = {
          type = 29,
          color = {
            r = 50,
            g = 255,
            b = 50, a = 255
          },
          pos = vector3(952.601, 3573.128, 34.493),
        },
        targets = sandy_target_short
      },
      [2] = {
        guns = guns,
        marker = {
          type = 29,
          color = {
            r = 50,
            g = 255,
            b = 50, a = 255
          },
          pos = vector3(959.286, 3573.215, 34.493),
        },
        targets = sandy_target_mid
      },
      [3] = {
        guns = guns,
        marker = {
          type = 29,
          color = {
            r = 50,
            g = 255,
            b = 50, a = 255
          },
          pos = vector3(966.753, 3573.295, 34.493),
        },
        targets = sandy_target_long
      },
    }
  }
}
