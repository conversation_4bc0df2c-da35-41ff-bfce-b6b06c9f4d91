local savedPlayerWeapons = {}

RegisterNetEvent(triggerName('leaveLine'))
AddEventHandler(triggerName('leaveLine'), function(bullets, gunrangeIndex, boxIndex, targetIndex)
    local _source = source
    local points = 0;
    for i, v in pairs(bullets) do
        points = points + tonumber(v.hitPoints)
    end

    print(string.format('Player %s leave line with points %s and target distance %s  - YOU CAN EDIT THIS AT server/framework/events.lua', GetPlayerName(_source), points, targetIndex))

    --Give back weapons to player HERE
    if savedPlayerWeapons[source] then
        local xPlayer = ESX.GetPlayerFromId(source)
        for i, v in pairs(savedPlayerWeapons[source]) do
            xPlayer.addWeapon(v.name, v.ammo)
        end
        savedPlayerWeapons[source] = nil
    end
end)

AddEventHandler(triggerName('joinLine'), function(source, gunrangeIndex, boxIndex, distanceIndex, rentPrice, gunHash, gunAmmo)
    savedPlayerWeapons[source] = nil
    --For example add discord notifiaction or anything
    print(string.format('Player %s enter line and start shooting - YOU CAN EDIT THIS AT server/framework/events.lua', GetPlayerName(source)))
end)

AddEventHandler('esx:playerLoaded', function(playerId)
    if savedPlayerWeapons[playerId] then
        local xPlayer = ESX.GetPlayerFromId(playerId)
        for _, w in pairs(savedPlayerWeapons[playerId]) do
            xPlayer.addWeapon(w.name, w.ammo)
        end
        savedPlayerWeapons[playerId] = nil
    end
end)

RegisterNetEvent(triggerName("ClearPedWeapons"), function()
    if Config.FrameWork == "3" then -- qbcore

    end

    if Config.FrameWork == "2" then -- esx
        local xPlayer = ESX.GetPlayerFromId(source)
        savedPlayerWeapons[source] = xPlayer.getLoadout()

        for _, weapon in pairs(savedPlayerWeapons[source]) do
            if weapon.name == weaponName then
                xPlayer.removeWeapon(weaponName)
            end
        end
    end

    if Config.FrameWork == "1" then -- standalone

    end
end)

RegisterNetEvent(triggerName("GiveWeaponToPed"), function(weaponName, ammo, components)
    if Config.FrameWork == "3" then -- qbcore

    end

    if Config.FrameWork == "2" then -- esx
        local xPlayer = ESX.GetPlayerFromId(source)
        xPlayer.addWeapon(weaponName, ammo)
    end

    if Config.FrameWork == "1" then -- standalone

    end
end)
