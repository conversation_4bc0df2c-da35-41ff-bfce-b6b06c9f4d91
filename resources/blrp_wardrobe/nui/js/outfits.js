// Outfit management functions
const WardrobeOutfits = {
    // Filter and sort outfits
    getFilteredOutfits() {
        let outfits = [...currentOutfits];
        
        // Filter by search query
        if (searchQuery) {
            outfits = outfits.filter(outfit => 
                outfit.name.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }
        
        // Filter by folder
        if (selectedFolder !== 'all') {
            if (selectedFolder === 'unassigned') {
                outfits = outfits.filter(outfit => !outfit.folder_id);
            } else {
                outfits = outfits.filter(outfit => outfit.folder_id == selectedFolder);
            }
        }
        
        // Sort outfits - always show favorites first
        outfits.sort((a, b) => {
            // First, sort by favorite status (favorites always on top)
            if (a.favorite && !b.favorite) return -1;
            if (!a.favorite && b.favorite) return 1;

            // Then apply the selected sorting within each group (favorites/non-favorites)
            switch (sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'name_desc':
                    return b.name.localeCompare(a.name);
                case 'date':
                    return (a.created_at || 0) - (b.created_at || 0);
                case 'date_desc':
                    return (b.created_at || 0) - (a.created_at || 0);
                case 'favorite':
                    // For favorite sorting, just sort by name within each group
                    return a.name.localeCompare(b.name);
                default:
                    return a.name.localeCompare(b.name);
            }
        });
        
        return outfits;
    },

    // Generate folder options for outfit folder dropdown
    generateFolderOptions(outfitName) {
        let options = '';
        currentFolders.forEach(folder => {
            options += `
                <div class="folder-option" data-outfit="${outfitName}" data-folder="${folder.id}">
                    <i data-lucide="folder" style="color: ${folder.color};"></i>
                    ${folder.name}
                </div>
            `;
        });
        return options;
    },

    // Render outfits
    render() {
        const grid = $('#outfits-grid');
        const noOutfits = $('#no-outfits');
        const outfits = this.getFilteredOutfits();
        
        if (outfits.length === 0) {
            grid.hide();
            noOutfits.show();
            return;
        }
        
        noOutfits.hide();
        grid.show();

        // Just list all outfits together without folder categorization
        let html = '';
        outfits.forEach((outfit, index) => {
            html += this.renderOutfitCard(outfit, index);
        });
        
        grid.html(html);
        
        // Initialize Lucide icons for new content
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
        
        this.bindOutfitEvents();
    },

    // Render individual outfit card
    renderOutfitCard(outfit, index = -1) {
        const isOutdated = (outfit.version || 0) < wardrobeData.current_version;
        const isFavorite = outfit.favorite || false;
        const emoji = outfit.emoji || '';

        // Get folder color for this outfit
        let folderColor = 'var(--text-muted)'; // Default color
        let folderIcon = 'folder-x'; // Default icon for unassigned

        if (outfit.folder_id) {
            const folder = currentFolders.find(f => f.id == outfit.folder_id);
            if (folder) {
                folderColor = folder.color;
                folderIcon = 'folder';
            }
        }

        // Escape HTML attributes to prevent issues with special characters
        const escapedName = outfit.name.replace(/"/g, '&quot;').replace(/'/g, '&#39;');

        return `
            <div class="outfit-card ${isOutdated ? 'outdated' : ''}"
                 data-outfit-name="${escapedName}">
                ${isFavorite ? '<div class="favorite-star">⭐</div>' : ''}
                <div class="outfit-name">
                    ${emoji ? `<span class="outfit-emoji">${emoji}</span>` : ''}${outfit.name}
                </div>
                <div class="outfit-actions">
                    ${!isOutdated && wardrobeData.allow_dress !== false ? `<button class="btn btn-apply btn-sm apply-outfit" data-outfit="${escapedName}" title="Apply outfit">
                        ✓
                    </button>` : ''}
                    <div class="outfit-folder-container">
                        <button class="outfit-folder-btn" data-outfit="${escapedName}" title="Move to folder">
                            <i data-lucide="${folderIcon}" style="color: ${folderColor};"></i>
                        </button>
                        <div class="outfit-folder-menu hidden">
                            <div class="folder-option" data-outfit="${escapedName}" data-folder="">
                                <i data-lucide="folder-x"></i>
                                Unassigned
                            </div>
                            ${this.generateFolderOptions(escapedName)}
                        </div>
                    </div>
                    <div class="outfit-menu-container">
                        <button class="outfit-menu-btn" data-outfit="${escapedName}" title="More actions">
                            ${isOutdated ? '⚠️' : '⚙️'}
                        </button>
                        <div class="outfit-menu hidden">
                            <button class="toggle-favorite" data-outfit="${escapedName}">
                                ${isFavorite ? '⭐ Remove from favorites' : '⭐ Add to favorites'}
                            </button>
                            <hr>
                            <button class="edit-outfit" data-outfit="${escapedName}">
                                ✏️ Edit
                            </button>
                            <button class="share-outfit" data-outfit="${escapedName}">
                                📤 Share
                            </button>
                            <button class="replace-outfit" data-outfit="${escapedName}">
                                🔄 Replace with current
                            </button>
                            <button class="export-outfit" data-outfit="${escapedName}">
                                📋 Export
                            </button>
                            ${isOutdated ? `
                            <button class="upgrade-outfit text-warning" data-outfit="${escapedName}">
                                ⬆️ Upgrade
                            </button>
                            <hr>
                            ` : ''}
                            <button class="delete-outfit text-danger" data-outfit="${escapedName}">
                                🗑️ Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // Bind outfit events (now using event delegation in events.js)
    bindOutfitEvents() {
        // All outfit events are now handled by event delegation in events.js
        // This ensures they work for dynamically generated content including:
        // - Apply outfit
        // - Toggle favorite
        // - Edit outfit
        // - Share outfit
        // - Upgrade outfit
        // - Delete outfit
        // - Menu toggles
        // - Folder assignments
    },

    // All menu and folder events are now handled by event delegation in events.js



    // Show save outfit modal
    showSaveModal() {
        const modal = $('#save-outfit-modal');
        if (modal.is(':visible')) return;

        // Clear form
        $('#outfit-name-input').val('');
        $('#save-hair-checkbox').prop('checked', false);
        $('#save-hair-info').hide();
        $('#save-selected-emoji').text('').data('emoji', '');

        // Clear emoji search and reset visibility
        $('#save-emoji-search').val('');
        $('#save-emoji-grid .emoji-option').show();

        // Auto-populate folder based on current selection
        const folderSelect = $('#outfit-folder-select');
        const modalTitle = $('#save-modal-title');

        if (selectedFolder && selectedFolder !== 'all' && selectedFolder !== 'unassigned') {
            // If a specific folder is selected, pre-select it
            folderSelect.val(selectedFolder);

            // Find folder name for title
            const folder = currentFolders.find(f => f.id == selectedFolder);
            const folderName = folder ? folder.name : 'Selected Folder';
            modalTitle.text(`Save to "${folderName}"`);
        } else {
            // Default to unassigned
            folderSelect.val('');
            modalTitle.text('Save Current Outfit');
        }

        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);
        setTimeout(() => $('#outfit-name-input').focus(), 200);
    },

    // Hide save outfit modal
    hideSaveModal() {
        const modal = $('#save-outfit-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide();
        });
    },

    // Save outfit
    save() {
        const name = $('#outfit-name-input').val().trim();
        const folderId = $('#outfit-folder-select').val();
        const saveHair = $('#save-hair-checkbox').is(':checked');
        const emoji = $('#save-selected-emoji').data('emoji') || '';

        if (!name || name.length > WardrobeConfig.limits.outfitNameLength) {
            alert(`Please enter a valid outfit name (1-${WardrobeConfig.limits.outfitNameLength} characters)`);
            return;
        }

        WardrobeAPI.saveOutfit(name, folderId, saveHair, emoji);
        this.hideSaveModal();
    },

    // Show import outfit modal
    showImportModal() {
        const modal = $('#import-outfit-modal');
        if (modal.is(':visible')) return;

        // Clear form
        $('#import-outfit-name').val('');
        $('#import-outfit-data').val('');

        // Populate folder options
        const folderSelect = $('#import-outfit-folder');
        folderSelect.empty();
        folderSelect.append('<option value="">Unassigned</option>');

        currentFolders.forEach(folder => {
            folderSelect.append(`<option value="${folder.id}">${folder.name}</option>`);
        });

        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);
        setTimeout(() => $('#import-outfit-name').focus(), 200);
    },

    // Hide import outfit modal
    hideImportModal() {
        const modal = $('#import-outfit-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide();
        });
    },

    // Import outfit
    importOutfit() {
        const name = $('#import-outfit-name').val().trim();
        const data = $('#import-outfit-data').val().trim();
        const folderId = $('#import-outfit-folder').val();

        if (!name || name.length > WardrobeConfig.limits.outfitNameLength) {
            alert(`Please enter a valid outfit name (1-${WardrobeConfig.limits.outfitNameLength} characters)`);
            return;
        }

        if (!data) {
            alert('Please paste outfit data');
            return;
        }

        WardrobeAPI.importOutfit(name, data, folderId);
        this.hideImportModal();
    },

    // Show export outfit modal
    showExportModal(outfitName) {
        const modal = $('#export-outfit-modal');
        if (modal.is(':visible')) return;

        // Set title
        $('#export-modal-title').text(`Export "${outfitName}"`);

        // Clear data initially
        $('#export-outfit-data').val('Loading...');

        // Reset to compact view
        this.currentExportFormat = 'compact';
        $('#compact-export-tab').addClass('active');
        $('#expanded-export-tab').removeClass('active');

        // Show modal
        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);

        // Fetch outfit data
        WardrobeAPI.exportOutfit(outfitName)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Store both formats
                    this.exportedData = {
                        expanded: data.data,
                        compact: this.convertToCompactFormat(data.data)
                    };

                    // Show compact format by default
                    $('#export-outfit-data').val(this.exportedData.compact);
                } else {
                    $('#export-outfit-data').val('Error: ' + (data.message || 'Failed to export outfit'));
                }
            })
            .catch(error => {
                $('#export-outfit-data').val('Error: Failed to fetch outfit data');
                console.error('Export error:', error);
            });
    },

    // Convert expanded format to compact format
    convertToCompactFormat(expandedData) {
        // Remove all whitespace and newlines, keep it as a single line
        return expandedData
            .replace(/\n/g, '')
            .replace(/\s+/g, ' ')
            .replace(/,\s*}/g, '}')
            .replace(/{\s*/g, '{')
            .replace(/\s*}/g, '}')
            .trim();
    },

    // Switch export format
    switchExportFormat(format) {
        if (!this.exportedData) return;

        this.currentExportFormat = format;

        // Update tab states
        $('#compact-export-tab').toggleClass('active', format === 'compact');
        $('#expanded-export-tab').toggleClass('active', format === 'expanded');

        // Update textarea content
        $('#export-outfit-data').val(this.exportedData[format]);
    },

    // Hide export outfit modal
    hideExportModal() {
        const modal = $('#export-outfit-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide();
        });

        // Clear export data
        this.exportedData = null;
        this.currentExportFormat = 'compact';
    },

    // Copy export data to clipboard
    copyExportData() {
        const textarea = $('#export-outfit-data')[0];
        textarea.select();
        textarea.setSelectionRange(0, 99999); // For mobile devices

        try {
            document.execCommand('copy');
            // Temporarily change button text to show success
            const button = $('#copy-export-data');
            const originalText = button.text();
            button.text('Copied!');
            setTimeout(() => {
                button.text(originalText);
            }, 2000);
        } catch (err) {
            alert('Failed to copy to clipboard. Please select and copy manually.');
        }
    },

    // Show edit outfit modal
    showEditModal(outfitName) {
        const modal = $('#edit-outfit-modal');
        if (modal.is(':visible')) return;

        // Store the outfit name for later use
        modal.data('outfit-name', outfitName);

        // Find the outfit to get current emoji
        const outfit = currentOutfits.find(o => o.name === outfitName);
        const currentEmoji = outfit ? outfit.emoji || '' : '';

        // Pre-fill with current name and emoji
        $('#edit-outfit-input').val(outfitName);
        $('#selected-emoji').text(currentEmoji);
        $('#selected-emoji').data('emoji', currentEmoji);

        // Clear emoji search and reset visibility
        $('#emoji-search').val('');
        $('#emoji-grid .emoji-option').show();

        // Highlight current emoji if it exists
        if (currentEmoji) {
            setTimeout(() => {
                $(`.emoji-option[data-emoji="${currentEmoji}"]`).addClass('selected');
            }, 100);
        }

        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);
        setTimeout(() => {
            $('#edit-outfit-input').focus().select();
        }, 200);
    },

    // Hide edit outfit modal
    hideEditModal() {
        const modal = $('#edit-outfit-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide().removeData('outfit-name');
        });
    },

    // Edit outfit
    editOutfit() {
        const modal = $('#edit-outfit-modal');
        const oldName = modal.data('outfit-name');
        const newName = $('#edit-outfit-input').val().trim();
        const emoji = $('#selected-emoji').data('emoji') || '';

        if (!newName || newName.length > WardrobeConfig.limits.outfitNameLength) {
            alert(`Please enter a valid outfit name (1-${WardrobeConfig.limits.outfitNameLength} characters)`);
            return;
        }

        // Check if anything changed
        const outfit = currentOutfits.find(o => o.name === oldName);
        const currentEmoji = outfit ? outfit.emoji || '' : '';

        if (newName === oldName && emoji === currentEmoji) {
            this.hideEditModal();
            return;
        }

        WardrobeAPI.editOutfit(oldName, newName, emoji);
        this.hideEditModal();
    },

    // Show delete outfit modal
    showDeleteModal(outfitName) {
        const modal = $('#delete-outfit-modal');
        if (modal.is(':visible')) return;

        // Store the outfit name for later use
        modal.data('outfit-name', outfitName);

        // Update the outfit name in the modal
        $('#delete-outfit-name').text(outfitName);

        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);
    },

    // Hide delete outfit modal
    hideDeleteModal() {
        const modal = $('#delete-outfit-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide().removeData('outfit-name');
        });
    },

    // Delete outfit
    deleteOutfit() {
        const modal = $('#delete-outfit-modal');
        const outfitName = modal.data('outfit-name');

        if (!outfitName) {
            this.hideDeleteModal();
            return;
        }

        WardrobeAPI.deleteOutfit(outfitName);
        this.hideDeleteModal();
    }
};
