#app {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

#scratcher {
  width: 800px;
  height: 600px;
  margin-left: 950px;
  position: relative;
}

#scratch-overlay {
  position: absolute;
  top: 254px;
  left: 27px;
  width: 498px;
  height: 283px;
  margin: 0 auto;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  background-position-x: -27px;
  background-position-y: -254px;
}

#scratch-canvas {
  position: absolute;
  top: 0;
}

/* Drawable styles */

#drawable {
  user-select: none;
  margin-left: 1090px;
  position: relative;
  font-family: "Times New Roman", Times, serif;
}

#drawable * {
  user-select: none;
}

#drawable-bg {
  display: flex;
  flex-direction: column;
}

#drawable-numbers {
  position: absolute;
  left: 0;
  top: 150px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1.4;
}

.drawable-number-row {
  margin-bottom: 2px;
}

.drawable-number {
  margin-right: 5px;
  padding: 4px;
}

.drawable-number-circled {
  margin-right: 5px;
  padding: 2px;
  border: 2px solid #566be9;
  border-radius: 10px;
}
