function vRP.selectCharacter(player, user_id, char_id, isNew<PERSON><PERSON>)
  if not player then
    print('error on vRP.select<PERSON>haracter - no player passed', player, user_id, char_id, isNew<PERSON>har)
    return
  end

  local character = MySQL.single.await('SELECT * FROM characters WHERE id = ? AND identifier = ? AND deleted_at IS NULL', {
    char_id, user_id
  })

  if not character then
    return
  end

  -- Set selected character active and all other characters inactive
  MySQL.update.await('UPDATE characters SET active = IF(id = ?, 1, 0) WHERE identifier = ?', {
    char_id, user_id
  })

  vRP.user_tables[user_id] = {}

	vRP.user_tmp_tables[user_id] = {}
  vRP.user_tmp_tables[user_id].spawns = 0
  vRP.user_tmp_tables[user_id].wallet = character.wallet
  vRP.user_tmp_tables[user_id].bank = character.bank
  vRP.user_characters[user_id] = character.id

  -- load user data table
  local datatable = MySQL.scalar.await('SELECT dvalue FROM vrp_user_data WHERE user_id = ? AND dkey = ?', {
    user_id, 'vRP:datatable' .. character.id
  })

  if datatable and datatable ~= '' then
    datatable = json.decode(datatable)

    if type(datatable) == 'table' then
      vRP.user_tables[user_id] = datatable
    end
  end

  if not datatable then
    print('No datatable found, using default', user_id, 'vRP:datatable' .. character.id)

    datatable = vRP.user_tables[user_id]
  end

  vRPclient.setCharNumber(player, { character.id })
  TriggerEvent("vRPcli:playerSpawned", false, character.sex, player, isNewChar)

  -- cache character info for blrp core
  character.steam_name = GetPlayerName(player)
  TriggerEvent('core:server:initSelectedPlayer', player, character, datatable.groups or {})

  -- Set last used walk
  TriggerEvent("core:server:reqLocalConfigValue", player, 'walk_style', function(value)
    if not value then return end
    SetTimeout(5000, function() -- wait for char to init
      TriggerClientEvent("dpemotes:WalkCommandStart", player, { nil, value })
    end)
  end)
end

RegisterNetEvent('vrp:updateIdentity', function(char_id)
  if not char_id then
    return
  end

  local player = source
  local user_id = vRP.getUserId(player)

  vRP.selectCharacter(player, user_id, char_id)
end)

local function trim(s)
  return string.match(s,'^()%s*$') and '' or string.match(s,'^%s*(.*%S)')
end

local characterCooldowns = {}
local userWarned = {}

pVRP.createCharacter = function(data)
  local player = source
  local user_id = vRP.getUserId(player)

  local character_count = MySQL.scalar.await('SELECT COUNT(*) FROM characters WHERE identifier = ? AND deleted_at IS NULL', {
    user_id
  })

  local allowed_characters = exports.blrp_core:GetMaxCharacterSlots(user_id)

  if character_count >= allowed_characters then
    return false, 'You may only have a maximum of ' .. allowed_characters .. ' characters. Purchase more slots at https://store.badlandsrp.com'
  end

  data = data.character

  local currentTime = os.time()
  if characterCooldowns[user_id] and currentTime - characterCooldowns[user_id] < 60 then
    if not userWarned[user_id] or currentTime - userWarned[user_id] >= 60 then
      sendToDiscordCharacterChannel(user_id, '**' .. user_id .. '** Attempted to create multiple characters too quickly: '
        .. data.firstname .. ' ' .. data.lastname .. ' :warning: **Potential Exploiter Alert!**')
      userWarned[user_id] = currentTime
    end
    return false, 'Character created too recently'
  end

  if not data.firstname or data.firstname == '' or not data.lastname or data.lastname == '' then
    return false, 'First and last name must be specified'
  end

  -- Force uppercase first letter of first and last name
  data.firstname = trim(string.gsub(data.firstname, '^%l', string.upper))
	data.lastname = trim(string.gsub(data.lastname, '^%l', string.upper))

  -- Check for profanity / stupid names
  local valid_name, name_message = exports.blrp_characterselect:ValidateName(player, data.firstname, data.lastname)

  if not valid_name then
    return false, name_message
  end

  local previous_lastname_count = MySQL.scalar.await('SELECT COUNT(*) FROM characters WHERE identifier = ? AND lastname = ?', {
    user_id, data.lastname
  })

  if previous_lastname_count > 0 then
    return false, 'You have used this last name on another character. Per server rules, your characters cannot be related.'
  end

  local height = tonumber(data.height)

  if not height or height < 140 or height > 200 then
    return false, 'Invalid height'
  end

  data.day = tonumber(data.day)
  data.month = tonumber(data.month)
  data.year = tonumber(data.year)

  if not data.day or not data.month or not data.year then
    return false, 'Invalid date of birth'
  end

  local validDays = 31

  if data.month == 2 then
    if (data.year % 4 == 0 and data.year % 100) or data.year % 400 == 0 then
      validDays = 29
    else
      validDays = 28
    end
  elseif data.month == 4 or data.month == 6 or data.month == 9 or data.month == 11 then
    validDays = 30
  end

  if data.month < 1 or data.month > 12 or data.day < 1 or data.day > validDays then
    return false, 'Invalid date of birth'
  end

  if data.sex ~= 'm' and data.sex ~= 'f' then
    return false, 'Invalid gender'
  end

  if data.home ~= 'lapuerta' and data.home ~= 'sandy' and data.home ~= 'paleto' and data.home ~= 'billingsgate' and data.home ~= 'perrera' then
    return false, 'Invalid home location'
  end

  if data.day < 10 then
    data.day = '0' .. data.day
  end

  if data.month < 10 then
    data.month = '0' .. data.month
  end

  data.dateofbirth = data.year .. '-' .. data.month .. '-' .. data.day

  characterCooldowns[user_id] = currentTime

  sendToDiscordCharacterChannel(user_id, '**' .. user_id .. '** Created character: ' .. data.firstname .. ' ' .. data.lastname .. ' / home = ' .. data.home)

  local character_data = exports.blrp_core:GenerateCharacterData()

  local character_id = MySQL.insert.await([[
    INSERT INTO characters
      (identifier, firstname, lastname, dateofbirth, sex, height, phone, dlnumber, dna, home, created_at) VALUES
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
  ]], {
    user_id,
    data.firstname,
    data.lastname,
    data.dateofbirth,
    data.sex,
    data.height,
    character_data.phone,
    character_data.dlnumber,
    character_data.dna,
    data.home
  })

  vRP.selectCharacter(player, user_id, character_id, true)

  return true, 'Successful validation'
end
