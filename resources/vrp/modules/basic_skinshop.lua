AddEventHandler('chatMessage', function(from, name, message)
  if (string.sub(message, 1, 1) == "/") then
    local args = splitString(message)
    local cmd = string.lower(args[1])
    if cmd == "/helmet" then
      CancelEvent()
      vRPclient.removeHelmet(from, {})
    elseif cmd == "/glasses" then
      CancelEvent()
      vRPclient.removeGlasses(from, {})
    elseif cmd == "/pants" then
      CancelEvent()
      vRPclient.removePants(from, {})
    elseif cmd == "/vest" then
      CancelEvent()
      vRPclient.removeVest(from, {})
    elseif cmd == "/hathair" then
      CancelEvent()
      vRPclient.removeHair(from, {})
    elseif cmd == "/shirt" then
      CancelEvent()
      vRPclient.removeShirt(from, {})
    elseif cmd == "/decals" then
      CancelEvent()
      vRPclient.removeDecals(from, {})
    elseif cmd == "/shoes" then
      CancelEvent()
      vRPclient.removeShoes(from, {})
    elseif cmd == "/mask" then
      CancelEvent()
      local value = (tonumber(args[2]))
      if value ~= nil then
        if value == 1 then
          vRPclient.reapplyMask(from, {})
        elseif value == 0 then
          vRPclient.removeMask(from, { false })
        else
          tUi.notify(from, { "Invalid input" })
        end
      end
    elseif cmd == "/regear" then
      CancelEvent()
      local user_id = vRP.getUserId(from)
      local data = vRP.getUserDataTable(user_id)
      vRPclient.reapplyShirt(from, {})
      vRPclient.reapplyVest(from, {})
      vRPclient.reapplyShirt(from)
      vRPclient.reapplyShoes(from)
      vRPclient.reapplyPants(from, {})
      vRPclient.reapplyProps(from, { data.customization })
    elseif cmd == "/removemask" then
      CancelEvent()
      vRPclient.removeTargetMask_cl(from, {})
    end
  end
end)
