<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>streamer216x</modelName>
      <txdName>streamer216x</txdName>
      <handlingId>STREAMER216</handlingId>
      <gameName>STREAMER</gameName>
      <vehicleMakeName>MAMMOTH</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_sm_tula</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_PLANE_216X</layout>
      <coverBoundOffsets>STANDARD_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_PLANE2_CAMERA</cameraName>
      <aimCameraName>PLANE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>PLANE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>PLANE_POV_CAMERA</povCameraName>
      <povTurretCameraName>POV_TURRET_CAMERA_MOGUL</povTurretCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.025000" y="-0.063000" z="-0.058000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.206000" y="0.396000" z="0.411000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.211000" y="0.338000" z="0.313000" />
      <PovCameraOffset x="0.000000" y="-0.115000" z="0.710000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.020000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.200000" z="0.150000" />
      <vfxInfoName>VFXVEHICLEINFO_PLANE_CUBAN800</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.225000" />
      <wheelScaleRear value="0.255000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.400000" />
      <envEffScaleMax value="0.400000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.700000" />
      <damageMapScale value="0.100000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        30.000000
        80.000000
        160.000000
        1000.000000
        1000.000000
      </lodDistances>
      <minSeatHeight value="0.933" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.100000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="999" />
      <flags>FLAG_NO_BOOT FLAG_DRIVER_NO_DRIVE_BY FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_DONT_TIMESLICE_WHEELS FLAG_USE_STANDARD_FLIGHT_HELMET FLAG_HAS_JATO_BOOST_MOD FLAG_IGNORE_TRAPPED_HULL_CHECK FLAG_HOLD_TO_SHUFFLE FLAG_USE_ROOT_AS_BASE_LOCKON_POS FLAG_HAS_LIVERY FLAG_USE_FULL_ANIMS_FOR_MP_WARP_ENTRY_POINTS</flags>
      <type>VEHICLE_TYPE_PLANE</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_MAVERICK</dashboardType>
      <vehicleClass>VC_PLANE</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WINGTIP_LEFT_CAMERA</Item>
        <Item>WINGTIP_RIGHT_CAMERA</Item>
        <Item>CUBAN_TAIL_LEFT_CAMERA</Item>
        <Item>CUBAN_TAIL_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	    <lockOnPositionOffset x="0.000000" y="0.000000" z="-0.700000" />
      <firstPersonDrivebyData>
        <Item>STD_FRONT_LEFT</Item>
        <Item>PLANE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
  	<Item>
      <parent>vehicles_flyer_interior</parent>
      <child>streamer216x</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
