<?xml version="1.0" encoding="UTF-8"?>
<CHandlingDataMgr>
	<HandlingData>
    <Item type="CHandlingData">
			<handlingName>JUBILEE</handlingName>
			<fMass value="2660.000000" />
			<fInitialDragCoeff value="11.200000" />
			<fPercentSubmerged value="85.000000" />
			<vecCentreOfMassOffset x="0.000000" y="0.00000" z="0.06500" />
			<vecInertiaMultiplier x="1.200000" y="1.350000" z="1.700000" />
			<fDriveBiasFront value="0.350000" />
			<nInitialDriveGears value="8" />
			<fInitialDriveForce value="0.297500" />
			<fDriveInertia value="1.100000" />
			<fClutchChangeRateScaleUpShift value="1.950000" />
			<fClutchChangeRateScaleDownShift value="1.950000" />
			<fInitialDriveMaxFlatVel value="151.000000" />
			<fBrakeForce value="0.750000" />
			<fBrakeBiasFront value="0.600000" />
			<fHandBrakeForce value="0.800000" />
			<fSteeringLock value="40.00000" />
			<fTractionCurveMax value="2.285000" />
			<fTractionCurveMin value="2.060000" />
			<fTractionCurveLateral value="22.500000" />
			<fTractionSpringDeltaMax value="0.150000" />
			<fLowSpeedTractionLossMult value="1.10000" />
			<fCamberStiffnesss value="0.000000" />
			<fTractionBiasFront value="0.483000" />
			<fTractionLossMult value="1.000000" />
			<fSuspensionForce value="2.150000" />
			<fSuspensionCompDamp value="1.780000" />
			<fSuspensionReboundDamp value="2.750000" />
			<fSuspensionUpperLimit value="0.130000" />
			<fSuspensionLowerLimit value="-0.120000" />
			<fSuspensionRaise value="0.000000" />
			<fSuspensionBiasFront value="0.500000" />
			<fAntiRollBarForce value="0.60000" />
			<fAntiRollBarBiasFront value="0.545000" />
			<fRollCentreHeightFront value="0.240000" />
			<fRollCentreHeightRear value="0.220000" />
			<fCollisionDamageMult value="0.700000" />
			<fWeaponDamageMult value="1.000000" />
			<fDeformationDamageMult value="0.700000" />
			<fEngineDamageMult value="1.500000" />
			<fPetrolTankVolume value="65.000000" />
			<fOilVolume value="5.000000" />
			<fSeatOffsetDistX value="0.050000" />
			<fSeatOffsetDistY value="0.000000" />
			<fSeatOffsetDistZ value="0.000000" />
			<nMonetaryValue value="25000" />
			<strModelFlags>440010</strModelFlags>
			<strHandlingFlags>0</strHandlingFlags>
			<strDamageFlags>0</strDamageFlags>
			<AIHandling>AVERAGE</AIHandling>
			<SubHandlingData>
				<Item type="CCarHandlingData">
					<fBackEndPopUpCarImpulseMult value="0.100000" />
					<fBackEndPopUpBuildingImpulseMult value="0.030000" />
					<fBackEndPopUpMaxDeltaSpeed value="0.600000" />
					<strAdvancedFlags>14000000</strAdvancedFlags>
				</Item>
				<Item type="CVehicleWeaponHandlingData">
					<uWeaponHash>
						<Item>VEHICLE_WEAPON_GRANGER2_MG</Item>
						<Item />
						<Item />
					</uWeaponHash>
					<WeaponSeats content="int_array">
				  0
				  0
				  0
				</WeaponSeats>
					<WeaponVehicleModType>
						<Item>VMT_CHASSIS5</Item>
						<Item />
						<Item />
					</WeaponVehicleModType>
					<fTurretSpeed content="float_array">
				  0.000000
				  0.000000
				</fTurretSpeed>
					<fTurretPitchMin content="float_array">
				  0.000000
				  0.000000
				</fTurretPitchMin>
					<fTurretPitchMax content="float_array">
				  0.000000
				  0.000000
				</fTurretPitchMax>
					<fTurretCamPitchMin content="float_array">
				  0.000000
				  0.000000
				</fTurretCamPitchMin>
					<fTurretCamPitchMax content="float_array">
				  0.000000
				  0.000000
				</fTurretCamPitchMax>
					<fBulletVelocityForGravity content="float_array">
				  0.000000
				  0.000000
				</fBulletVelocityForGravity>
					<fTurretPitchForwardMin content="float_array">
				  0.000000
				  0.000000
				</fTurretPitchForwardMin>
					<fUvAnimationMult value="0.000000" />
					<fMiscGadgetVar value="0.000000" />
					<fWheelImpactOffset value="0.000000" />
				</Item>
				<Item type="NULL" />
				<Item type="NULL" />
			</SubHandlingData>
		   </Item>
    </HandlingData>
</CHandlingDataMgr>