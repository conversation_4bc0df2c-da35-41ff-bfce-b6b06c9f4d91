<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>brisket</modelName>
      <txdName>brisket</txdName>
      <handlingId>BRISKET</handlingId>
      <gameName>brisket</gameName>
      <vehicleMakeName>WEENY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>mamba</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>JESTER3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.090000" y="-0.130000" z="-0.070000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.070000" y="-0.095000" z="-0.070000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.130000" z="-0.070000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.020000" y="-0.130000" z="-0.070000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.015000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.080000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.160000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.225000" y="0.140000" z="0.440000" />
	    <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.200000" />
      <wheelScaleRear value="0.200000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.300000" />
      <envEffScaleMax value="0.400000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.00000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1" />
      <flags>FLAG_PEDS_INSIDE_CAN_BE_SET_ON_FIRE_MP FLAG_ALLOW_HATS_NO_ROOF FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_CONVERTIBLE FLAG_NO_HEAVY_BRAKE_ANIMATION FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_RICH_CAR FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_USE_PILOT_HELMET FLAG_FORCE_ENABLE_CHASSIS_COLLISION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	    <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	    <firstPersonDrivebyData>
        <Item>STD_JESTER3_FRONT_LEFT</Item>
        <Item>STD_JESTER3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
	  <Item>
      <modelName>brisket2</modelName>
      <txdName>brisket</txdName>
      <handlingId>BRISKET</handlingId>
      <gameName>brisket2</gameName>
      <vehicleMakeName>WEENY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>mamba</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>JESTER3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.090000" y="-0.130000" z="-0.070000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.070000" y="-0.095000" z="-0.070000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.130000" z="-0.070000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.020000" y="-0.130000" z="-0.070000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.015000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.080000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.160000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.225000" y="0.140000" z="0.440000" />
	    <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.200000" />
      <wheelScaleRear value="0.200000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.300000" />
      <envEffScaleMax value="0.400000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.00000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1" />
      <flags>FLAG_PEDS_INSIDE_CAN_BE_SET_ON_FIRE_MP FLAG_ALLOW_HATS_NO_ROOF FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_CONVERTIBLE FLAG_NO_HEAVY_BRAKE_ANIMATION FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_RICH_CAR FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_USE_PILOT_HELMET FLAG_FORCE_ENABLE_CHASSIS_COLLISION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	    <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_SPORT_CLASSIC</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	    <firstPersonDrivebyData>
        <Item>STD_JESTER3_FRONT_LEFT</Item>
        <Item>STD_JESTER3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
	  <Item>
      <modelName>brisket3</modelName>
      <txdName>brisket</txdName>
      <handlingId>BRISKET3</handlingId>
      <gameName>brisket3</gameName>
      <vehicleMakeName>WEENY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>RATLOADER2</audioNameHash>
      <layout>LAYOUT_LOW_RESTRICTED</layout>
      <coverBoundOffsets>JESTER3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.090000" y="-0.130000" z="-0.070000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.070000" y="-0.095000" z="-0.070000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.130000" z="-0.070000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.020000" y="-0.130000" z="-0.070000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.015000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.020000" y="-0.080000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.020000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.125000" y="0.160000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.225000" y="0.140000" z="0.440000" />
	    <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.190000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.211189" />
      <wheelScaleRear value="0.199783" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.300000" />
      <envEffScaleMax value="0.400000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.00000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.988" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="5" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1" />
      <flags>FLAG_PEDS_INSIDE_CAN_BE_SET_ON_FIRE_MP FLAG_DONT_TIMESLICE_WHEELS FLAG_HAS_SUPERCHARGER FLAG_ALLOW_HATS_NO_ROOF FLAG_SPOILER_MOD_DOESNT_INCREASE_GRIP FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_CONVERTIBLE FLAG_NO_HEAVY_BRAKE_ANIMATION FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_RICH_CAR FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_USE_PILOT_HELMET FLAG_FORCE_ENABLE_CHASSIS_COLLISION</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
	    <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_MUSCLE</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	    <firstPersonDrivebyData>
        <Item>STD_JESTER3_FRONT_LEFT</Item>
        <Item>STD_JESTER3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
	  <numSeatsOverride value="2" />
    </Item>
  </InitDatas>
  <txdRelationships>
 	  <Item>
      <parent>vehshare</parent>
      <child>vehicles_brisket_interior</child>
    </Item>
    <Item>
      <parent>vehicles_brisket_interior</parent>
      <child>brisket</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
