<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVarGlobal>
    <Sirens>
        <Item>
            <id value="44"/>
            <name>UNMARKED</name>
            <timeMultiplier value="1.00000000"/>
            <lightFalloffMax value="40.00000000"/>
            <lightFalloffExponent value="40.00000000"/>
            <lightInnerConeAngle value="2.29061000"/>
            <lightOuterConeAngle value="50.00000000"/>
            <lightOffset value="0.00000000"/>
            <textureName>VehicleLight_sirenlight</textureName>
            <sequencerBpm value="260"/>
            <leftHeadLight>
                <sequencer value="0"/>
            </leftHeadLight>
            <rightHeadLight>
                <sequencer value="0"/>
            </rightHeadLight>
            <leftTailLight>
                <sequencer value="1431655765"/>
            </leftTailLight>
            <rightTailLight>
                <sequencer value="2863311530"/>
            </rightTailLight>
            <leftHeadLightMultiples value="1"/>
            <rightHeadLightMultiples value="1"/>
            <leftTailLightMultiples value="2"/>
            <rightTailLightMultiples value="2"/>
            <useRealLights value="true"/>
            <sirens>
                <!-- siren1 -->
                <!-- siren2 -->
                <!-- siren3 -->
                <!-- siren4 -->
                <!-- siren5 -->
                <!-- siren6 -->
                <!-- siren7 -->
                <!-- siren8 -->
                <!-- siren9 -->
                <!-- siren10 -->
                <!-- siren11 -->
                <!-- siren12 -->
                <!-- siren13 -->
                <!-- siren14 -->
                <!--Siren 15 : Blue : Front-->
                <!-- Siren 16 -->
                <!-- siren17 -->
                <!-- siren18 -->
                <!-- siren19 -->
                <!-- siren20 -->
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="70.00000000"/>
                        <size value="2.50000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="true"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="70.00000000"/>
                        <size value="2.50000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="true"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2862752597"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="4.71238900"/>
                        <speed value="1.00000000"/>
                        <sequencer value="1432494762"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2861913258"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1430816938"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.09400900"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="2862853818"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="60.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="3"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="1432188375"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="1432188375"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="60.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF0000FF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="3"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.50000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.50000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="1.57079600"/>
                        <start value="4.71238900"/>
                        <speed value="1.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="4.71238900"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
            </sirens>
        </Item>
        <Item>
            <id value="50"/>
            <name>nkcoquette</name>
            <timeMultiplier value="1.00000000"/>
            <lightFalloffMax value="100.00000000"/>
            <lightFalloffExponent value="100.00000000"/>
            <lightInnerConeAngle value="2.29061000"/>
            <lightOuterConeAngle value="70.00000000"/>
            <lightOffset value="0.00000000"/>
            <textureName>VehicleLight_sirenlight</textureName>
            <sequencerBpm value="220"/>
            <leftHeadLight>
                <sequencer value="0"/>
            </leftHeadLight>
            <rightHeadLight>
                <sequencer value="0"/>
            </rightHeadLight>
            <leftTailLight>
                <sequencer value="1431655765"/>
            </leftTailLight>
            <rightTailLight>
                <sequencer value="2863311530"/>
            </rightTailLight>
            <leftHeadLightMultiples value="1"/>
            <rightHeadLightMultiples value="1"/>
            <leftTailLightMultiples value="2"/>
            <rightTailLightMultiples value="2"/>
            <useRealLights value="true"/>
            <sirens>
                <!-- Siren 1 -->
                <!-- Siren 2 -->
                <!-- Siren 3 -->
                <!-- Siren 4 -->
                <!-- Siren 5 -->
                <!-- Siren 6 -->
                <!-- Siren 7 -->
                <!-- Siren 8 -->
                <!-- Siren 9 -->
                <!-- Siren 10 -->
                <!-- Siren 11 -->
                <!-- Siren 12 -->
                <!-- Siren 13 -->
                <!-- Siren 14 -->
                <!-- Siren 15 -->
                <!-- Siren 16 -->
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="1.57079600"/>
                        <start value="4.71238900"/>
                        <speed value="1.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863486250"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431655723"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311541"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1432005205"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="4.71238900"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431666005"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.09400900"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF0A0AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431666005"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159300"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000001"/>
                        <size value="0.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF0A0AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
            </sirens>
        </Item>
        <Item>
            <id value="51"/>
            <name>LSPD_Aero clone clone</name>
            <timeMultiplier value="1"/>
            <lightFalloffMax value="100"/>
            <lightFalloffExponent value="150000"/>
            <lightInnerConeAngle value="125"/>
            <lightOuterConeAngle value="45"/>
            <lightOffset value="0"/>
            <textureName>0xF2534FF3</textureName>
            <sequencerBpm value="85"/>
            <leftHeadLight>
                <sequencer value="0"/>
            </leftHeadLight>
            <rightHeadLight>
                <sequencer value="0"/>
            </rightHeadLight>
            <leftTailLight>
                <sequencer value="0"/>
            </leftTailLight>
            <rightTailLight>
                <sequencer value="0"/>
            </rightTailLight>
            <leftHeadLightMultiples value="1"/>
            <rightHeadLightMultiples value="1"/>
            <leftTailLightMultiples value="1"/>
            <rightTailLightMultiples value="1"/>
            <useRealLights value="true"/>
            <sirens>
                <!-- Siren 1 -->
                <!-- Siren 2 -->
                <!-- Siren 3 -->
                <!-- Siren 4 -->
                <!-- Siren 5 -->
                <!-- Siren 6 -->
                <!-- Siren 7 -->
                <!-- Siren 8 -->
                <!-- Siren 9 -->
                <!-- Siren 10 -->
                <!-- Siren 11 -->
                <!-- Siren 12 -->
                <!-- Siren 13 -->
                <!-- Siren 14 -->
                <!-- Siren 15 -->
                <!-- Siren 16 -->
                <!-- Siren 17 -->
                <!-- Siren 18 -->
                <!-- Siren 19 -->
                <!-- Siren 20 -->
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="3.92699075"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="1"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF2130FF"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="3.14159274"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="1"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF2130FF"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="1.57079637"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="1"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF1405"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="5.497787"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="1"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF1405"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="3.14159274"/>
                        <speed value="45"/>
                        <sequencer value="1431655765"/>
                        <multiples value="0"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF2130FF"/>
                    <intensity value="4"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="2.3561945"/>
                        <speed value="55"/>
                        <sequencer value="1431655765"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="2"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="0"/>
                        <speed value="55"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="2"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF1405"/>
                    <intensity value="4"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="0"/>
                        <speed value="25"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="2"/>
                        <pull value="1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="3.14159274"/>
                        <speed value="15"/>
                        <sequencer value="1431655765"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="2"/>
                        <pull value="1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="2.3561945"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="15"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="1"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF1405"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="1.57079637"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="1"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF2130FF"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="5.497787"/>
                        <speed value="0"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="1"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="4"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="5.497787"/>
                        <speed value="0"/>
                        <sequencer value="1431655765"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="1"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="4"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="5.497787"/>
                        <speed value="55"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="2"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="4"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.0174532924"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="2"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF2130FF"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="5.497787"/>
                        <start value="0"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="35"/>
                        <size value="2"/>
                        <pull value="2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF1405"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="1"/>
                        <sequencer value="0"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="3"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF1405"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="20"/>
                        <sequencer value="1431655765"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1"/>
                        <pull value="0.2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFFFCC"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="20"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1"/>
                        <pull value="0.2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFFFCC"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="true"/>
                </Item>
            </sirens>
        </Item>
        <Item>
            <id value="52"/>
            <name>SAHP_BRIGHAM</name>
            <timeMultiplier value="1"/>
            <lightFalloffMax value="100"/>
            <lightFalloffExponent value="150000"/>
            <lightInnerConeAngle value="125"/>
            <lightOuterConeAngle value="45"/>
            <lightOffset value="0"/>
            <textureName>VehicleLight_misc_searchlight</textureName>
            <sequencerBpm value="150"/>
            <leftHeadLight>
                <sequencer value="0"/>
            </leftHeadLight>
            <rightHeadLight>
                <sequencer value="0"/>
            </rightHeadLight>
            <leftTailLight>
                <sequencer value="0"/>
            </leftTailLight>
            <rightTailLight>
                <sequencer value="0"/>
            </rightTailLight>
            <leftHeadLightMultiples value="1"/>
            <rightHeadLightMultiples value="1"/>
            <leftTailLightMultiples value="1"/>
            <rightTailLightMultiples value="1"/>
            <useRealLights value="true"/>
            <sirens>
                <!-- Siren 1 -->
                <!-- Siren 2 -->
                <!-- Siren 3 -->
                <!-- Siren 4 -->
                <!-- Siren 5 -->
                <!-- Siren 6 -->
                <!-- Siren 7 -->
                <!-- Siren 8 -->
                <!-- Siren 9 -->
                <!-- Siren 10 -->
                <!-- Siren 11 -->
                <!-- Siren 12 -->
                <!-- Siren 13 -->
                <!-- Siren 14 -->
                <!-- Siren 15 -->
                <!-- Siren 16 -->
                <!-- Siren 17 -->
                <!-- Siren 18 -->
                <!-- Siren 19 -->
                <!-- Siren 20 -->
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="3.14159274"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="100"/>
                        <size value="1"/>
                        <pull value="0.05"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF1405"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="3.14159274"/>
                        <speed value="33"/>
                        <sequencer value="1431655765"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="150"/>
                        <size value="1"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF2130FF"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="0"/>
                        <speed value="33"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="150"/>
                        <size value="1"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="3.14159274"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="2"/>
                        <pull value="0.05"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF1405"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="5.497787"/>
                        <speed value="20"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1"/>
                        <pull value="0.05"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF2130FF"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="3.92699075"/>
                        <speed value="13"/>
                        <sequencer value="2290649224"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1"/>
                        <pull value="0.05"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="2.3561945"/>
                        <speed value="0.2"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="5.497787"/>
                        <speed value="20"/>
                        <sequencer value="1431655765"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1"/>
                        <pull value="0.05"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF1405"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="3.14159274"/>
                        <start value="0"/>
                        <speed value="1"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.5"/>
                        <pull value="0.25"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF2130FF"/>
                    <intensity value="40"/>
                    <lightGroup value="0"/>
                    <rotate value="true"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="3435973836"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="25"/>
                        <size value="0.25"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="2"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="3.14159274"/>
                        <speed value="0"/>
                        <sequencer value="3435973836"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="25"/>
                        <size value="0.25"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="2"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2290657962"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="3"/>
                        <pull value="0.02"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="2"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="572675413"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="3"/>
                        <pull value="0.02"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="2"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="0.7853982"/>
                        <speed value="0"/>
                        <sequencer value="2290657962"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="3"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="2"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="20"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="3.92699075"/>
                        <speed value="0"/>
                        <sequencer value="572675413"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="3"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="2"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="20"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="858993459"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="20"/>
                        <size value="6"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="2"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="4.18879"/>
                        <speed value="0.5"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="3435973836"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="20"/>
                        <size value="6"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="2"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="5.235988"/>
                        <speed value="0.5"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="3435973836"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="20"/>
                        <size value="6"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="2"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="4.18879"/>
                        <speed value="0.5"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159274"/>
                        <start value="3.14159274"/>
                        <speed value="0"/>
                        <sequencer value="858993459"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="20"/>
                        <size value="6"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="2"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="5.497787"/>
                        <speed value="0.5"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2576980377"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1.5"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF606C87"/>
                    <intensity value="2"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0"/>
                        <start value="3.141593"/>
                        <speed value="0.5"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="1717986918"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1.5"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF606C87"/>
                    <intensity value="2"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
            </sirens>
        </Item>
        <Item>
            <id value="53"/>
            <name>Unmarked</name>
            <timeMultiplier value="1.00000000"/>
            <lightFalloffMax value="40.00000000"/>
            <lightFalloffExponent value="40.00000000"/>
            <lightInnerConeAngle value="2.29061000"/>
            <lightOuterConeAngle value="50.00000000"/>
            <lightOffset value="0.00000000"/>
            <textureName>VehicleLight_misc_searchlight</textureName>
            <sequencerBpm value="625"/>
            <leftHeadLight>
                <sequencer value="0"/>
            </leftHeadLight>
            <rightHeadLight>
                <sequencer value="0"/>
            </rightHeadLight>
            <leftTailLight>
                <sequencer value="0"/>
            </leftTailLight>
            <rightTailLight>
                <sequencer value="0"/>
            </rightTailLight>
            <leftHeadLightMultiples value="1"/>
            <rightHeadLightMultiples value="1"/>
            <leftTailLightMultiples value="2"/>
            <rightTailLightMultiples value="2"/>
            <useRealLights value="true"/>
            <sirens>
                <!-- siren 1 -->
                <!-- Siren 2 -->
                <!-- Siren 3 -->
                <!-- Siren 4 -->
                <!-- siren 5 -->
                <!-- Siren 6 -->
                <!-- Siren 7 -->
                <!-- Siren 8 -->
                <!-- siren 9 -->
                <!-- siren 10 -->
                <!-- siren11 -->
                <!-- Siren 12 -->
                <!-- Siren 13 -->
                <!-- Siren 14 -->
                <!-- Siren 15 -->
                <!-- Siren 16 -->
                <!-- Siren 17 -->
                <!-- Siren 18 -->
                <!-- Siren 19 -->
                <!-- Siren 20 -->
                <Item>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="168430090"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="168468640"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2694842890"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2694881440"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2964369584"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="85"/>
                        <size value="1.1"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="185273099"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="85"/>
                        <size value="1.1"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2964369584"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="185273099"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1717986918"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="1717986918"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2964369584"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="1"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0x00EEEEEE"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="false"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2964369584"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="1"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0x00EEEEEE"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="false"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2964369584"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0x00F7F7F7"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="185273099"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0x00F7F7F7"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="185273099"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="1"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0x00EEEEEE"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="false"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="185273099"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="1"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0x00EEEEEE"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="false"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="3435973836"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="-1.57079633"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="185273099"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="90.00000000"/>
                        <size value="0.00000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="20"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="185273099"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2964369584"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
            </sirens>
        </Item>
        <Item>
            <id value="54"/>
            <name>polalamop2a</name>
            <timeMultiplier value="1.00000000"/>
            <lightFalloffMax value="100.00000000"/>
            <lightFalloffExponent value="55.00000000"/>
            <lightInnerConeAngle value="2.29061000"/>
            <lightOuterConeAngle value="60.00000000"/>
            <lightOffset value="0.00000000"/>
            <textureName>VehicleLight_misc_searchlight</textureName>
            <sequencerBpm value="550"/>
            <leftHeadLight>
                <sequencer value="0"/>
            </leftHeadLight>
            <rightHeadLight>
                <sequencer value="0"/>
            </rightHeadLight>
            <leftTailLight>
                <sequencer value="0"/>
            </leftTailLight>
            <rightTailLight>
                <sequencer value="0"/>
            </rightTailLight>
            <leftHeadLightMultiples value="1"/>
            <rightHeadLightMultiples value="1"/>
            <leftTailLightMultiples value="1"/>
            <rightTailLightMultiples value="1"/>
            <useRealLights value="true"/>
            <sirens>
                <!-- Siren 1 -->
                <!-- Siren 2 -->
                <!-- Siren 3 -->
                <!-- Siren 4 -->
                <!-- Siren 5 -->
                <!-- Siren 6 -->
                <!-- Siren 7 -->
                <!-- Siren 8 -->
                <!-- Siren 9 -->
                <!-- Siren 10 -->
                <!-- Siren 11 -->
                <!-- Siren 12 -->
                <!-- Siren 13 -->
                <!-- Siren 14 -->
                <!-- Siren 15 -->
                <!-- Siren 16 -->
                <!-- Siren 17 -->
                <!-- Siren 18 -->
                <!-- Siren 19 -->
                <!-- Siren 20 -->
                <Item>
                    <flashiness>
                        <delta value="1.57079633"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881930"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="4"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="-1.57079633"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168469130"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="4"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881930"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168469130"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFFFFF"/>
                    <intensity value="0"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFFFFF"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881930"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168469130"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.25"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694842890"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.25"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168468640"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="1.57079633"/>
                        <start value="0"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2694881440"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="-1.57079633"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="168430090"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2694842890"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="168468640"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFCC700"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
            </sirens>
        </Item>
        <Item>
            <id value="55"/>
            <name>pbp</name>
            <timeMultiplier value="1.00000000"/>
            <lightFalloffMax value="100.00000000"/>
            <lightFalloffExponent value="55.00000000"/>
            <lightInnerConeAngle value="2.29061000"/>
            <lightOuterConeAngle value="70.00000000"/>
            <lightOffset value="0.00000000"/>
            <textureName>VehicleLight_misc_searchlight</textureName>
            <sequencerBpm value="550"/>
            <leftHeadLight>
                <sequencer value="0"/>
            </leftHeadLight>
            <rightHeadLight>
                <sequencer value="0"/>
            </rightHeadLight>
            <leftTailLight>
                <sequencer value="4042322160"/>
            </leftTailLight>
            <rightTailLight>
                <sequencer value="252645135"/>
            </rightTailLight>
            <leftHeadLightMultiples value="1"/>
            <rightHeadLightMultiples value="1"/>
            <leftTailLightMultiples value="1"/>
            <rightTailLightMultiples value="1"/>
            <useRealLights value="true"/>
            <sirens>
                <!-- Siren 1 - Lightbar -->
                <!-- Siren 2 - Lightbar -->
                <!-- Siren 3 - Lightbar -->
                <!-- Siren 4 - Lightbar -->
                <!-- Siren 5 - Lightbar -->
                <!-- Siren 6 - Lightbar -->
                <!-- Siren 7 - Lightbar -->
                <!-- Siren 8 - Lightbar -->
                <!-- Siren 9 -->
                <!-- Siren 10 -->
                <!-- Siren 11 - Front grill -->
                <!-- Siren 12 - Front grill -->
                <!-- Siren 13 - Front grill -->
                <!-- Siren 14 - Front grill -->
                <!-- Siren 15 - Front left pushbumper -->
                <!-- Siren 16 - Front right pushbumper -->
                <!-- Siren 17 - Rear window left -->
                <!-- Siren 18 - Rear window right -->
                <!-- Siren 19 - Rear plate left -->
                <!-- Siren 20 - Rear plate right -->
                <Item>
                    <flashiness>
                        <delta value="1.57079633"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881930"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="4"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="-1.57079633"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168469130"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="4"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881930"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168469130"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFFFFF"/>
                    <intensity value="0"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFFFFF"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881930"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168469130"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="70"/>
                        <size value="1"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.0"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="3"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168468640"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="70.00000000"/>
                        <size value="1.00000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="3"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.25"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694842890"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.25"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168468640"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="1.57079633"/>
                        <start value="0"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2694881440"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="-1.57079633"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="168430090"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2694842890"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="168468640"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
            </sirens>
        </Item>
        <!-- nkcoquette -->
    </Sirens>
    <Lights>
        <Item>
            <Item>
                <id value="230"/>
                <indicator>
                    <intensity value="0.30000000"/>
                    <falloffMax value="2.50000000"/>
                    <falloffExponent value="8.00000000"/>
                    <innerConeAngle value="20.00000000"/>
                    <outerConeAngle value="50.00000000"/>
                    <emmissiveBoost value="false"/>
                    <color value="0xFFFF8000"/>
                    <textureName/>
                    <mirrorTexture value="true"/>
                </indicator>
                <rearIndicatorCorona>
                    <size value="0.50000000"/>
                    <size_far value="0.08000000"/>
                    <intensity value="1.00000000"/>
                    <intensity_far value="1.00000000"/>
                    <color value="0xFFFF6435"/>
                    <numCoronas value="1"/>
                    <distBetweenCoronas value="128"/>
                    <distBetweenCoronas_far value="255"/>
                    <xRotation value="0.00000000"/>
                    <yRotation value="0.00000000"/>
                    <zRotation value="0.00000000"/>
                    <zBias value="0.25000000"/>
                    <pullCoronaIn value="false"/>
                </rearIndicatorCorona>
                <frontIndicatorCorona>
                    <size value="0.50000000"/>
                    <size_far value="0.08000000"/>
                    <intensity value="1.00000000"/>
                    <intensity_far value="1.00000000"/>
                    <color value="0xFFFF6435"/>
                    <numCoronas value="1"/>
                    <distBetweenCoronas value="128"/>
                    <distBetweenCoronas_far value="255"/>
                    <xRotation value="0.00000000"/>
                    <yRotation value="0.00000000"/>
                    <zRotation value="0.00000000"/>
                    <zBias value="0.25000000"/>
                    <pullCoronaIn value="false"/>
                </frontIndicatorCorona>
                <tailLight>
                    <intensity value="0.25000000"/>
                    <falloffMax value="4.00000000"/>
                    <falloffExponent value="16.00000000"/>
                    <innerConeAngle value="45.00000000"/>
                    <outerConeAngle value="90.00000000"/>
                    <emmissiveBoost value="false"/>
                    <color value="0xFFE50902"/>
                    <textureName/>
                    <mirrorTexture value="true"/>
                </tailLight>
                <tailLightCorona>
                    <size value="0.00000000"/>
                    <size_far value="8.00000000"/>
                    <intensity value="0.00000000"/>
                    <intensity_far value="1.00000000"/>
                    <color value="0xFFFF2D05"/>
                    <numCoronas value="1"/>
                    <distBetweenCoronas value="128"/>
                    <distBetweenCoronas_far value="255"/>
                    <xRotation value="0.00000000"/>
                    <yRotation value="0.00000000"/>
                    <zRotation value="0.00000000"/>
                    <zBias value="0.25000000"/>
                    <pullCoronaIn value="false"/>
                </tailLightCorona>
                <tailLightMiddleCorona>
                    <size value="0.00000000"/>
                    <size_far value="0.00000000"/>
                    <intensity value="0.00000000"/>
                    <intensity_far value="0.00000000"/>
                    <color value="0x00000000"/>
                    <numCoronas value="1"/>
                    <distBetweenCoronas value="128"/>
                    <distBetweenCoronas_far value="255"/>
                    <xRotation value="0.00000000"/>
                    <yRotation value="0.00000000"/>
                    <zRotation value="0.00000000"/>
                    <zBias value="0.25000000"/>
                    <pullCoronaIn value="false"/>
                </tailLightMiddleCorona>
                <headLight>
                    <intensity value="1.00000000"/>
                    <falloffMax value="35.00000000"/>
                    <falloffExponent value="16.00000000"/>
                    <innerConeAngle value="0.00000000"/>
                    <outerConeAngle value="60.00000000"/>
                    <emmissiveBoost value="false"/>
                    <color value="0xFFFFF5DD"/>
                    <textureName>VehicleLight_car_oldsquare</textureName>
                    <mirrorTexture value="false"/>
                </headLight>
                <headLightCorona>
                    <size value="0.10000000"/>
                    <size_far value="10.00000000"/>
                    <intensity value="5.00000000"/>
                    <intensity_far value="1.00000000"/>
                    <color value="0xFFFFF5DD"/>
                    <numCoronas value="1"/>
                    <distBetweenCoronas value="128"/>
                    <distBetweenCoronas_far value="255"/>
                    <xRotation value="0.00000000"/>
                    <yRotation value="0.00000000"/>
                    <zRotation value="0.00000000"/>
                    <zBias value="0.25000000"/>
                    <pullCoronaIn value="false"/>
                </headLightCorona>
                <reversingLight>
                    <intensity value="0.35000000"/>
                    <falloffMax value="20.00000000"/>
                    <falloffExponent value="32.00000000"/>
                    <innerConeAngle value="20.00000000"/>
                    <outerConeAngle value="90.00000000"/>
                    <emmissiveBoost value="false"/>
                    <color value="0xFFF7F7F7"/>
                    <textureName/>
                    <mirrorTexture value="true"/>
                </reversingLight>
                <reversingLightCorona>
                    <size value="0.80000000"/>
                    <size_far value="2.00000000"/>
                    <intensity value="1.50000000"/>
                    <intensity_far value="1.00000000"/>
                    <color value="0x00F7F7F7"/>
                    <numCoronas value="1"/>
                    <distBetweenCoronas value="128"/>
                    <distBetweenCoronas_far value="255"/>
                    <xRotation value="0.00000000"/>
                    <yRotation value="0.00000000"/>
                    <zRotation value="0.00000000"/>
                    <zBias value="0.25000000"/>
                    <pullCoronaIn value="false"/>
                </reversingLightCorona>
                <name>brigham</name>
            </Item>
            <id value="450"/>
            <indicator>
                <intensity value="0.37500000"/>
                <falloffMax value="2.50000000"/>
                <falloffExponent value="8.00000000"/>
                <innerConeAngle value="20.00000000"/>
                <outerConeAngle value="45.00000000"/>
                <emmissiveBoost value="false"/>
                <color value="0xFFFF7300"/>
                <textureName/>
                <mirrorTexture value="true"/>
            </indicator>
            <rearIndicatorCorona>
                <size value="1.00000000"/>
                <size_far value="2.500000"/>
                <intensity value="5.00000000"/>
                <intensity_far value="1.00000000"/>
                <color value="0xFFFF7300"/>
                <numCoronas value="1"/>
                <distBetweenCoronas value="128"/>
                <distBetweenCoronas_far value="255"/>
                <xRotation value="0.00000000"/>
                <yRotation value="0.00000000"/>
                <zRotation value="0.00000000"/>
                <zBias value="0.25000000"/>
                <pullCoronaIn value="false"/>
            </rearIndicatorCorona>
            <frontIndicatorCorona>
                <size value="1.00000000"/>
                <size_far value="2.500000"/>
                <intensity value="5.00000000"/>
                <intensity_far value="1.00000000"/>
                <color value="0xFFFF7300"/>
                <numCoronas value="1"/>
                <distBetweenCoronas value="128"/>
                <distBetweenCoronas_far value="255"/>
                <xRotation value="0.00000000"/>
                <yRotation value="0.00000000"/>
                <zRotation value="0.00000000"/>
                <zBias value="0.25000000"/>
                <pullCoronaIn value="false"/>
            </frontIndicatorCorona>
            <tailLight>
                <intensity value="0.25000000"/>
                <falloffMax value="3.00000000"/>
                <falloffExponent value="8.00000000"/>
                <innerConeAngle value="45.00000000"/>
                <outerConeAngle value="70.00000000"/>
                <emmissiveBoost value="false"/>
                <color value="0xFFFF0000"/>
                <textureName/>
                <mirrorTexture value="true"/>
            </tailLight>
            <tailLightCorona>
                <size value="0.00000000"/>
                <size_far value="4.000000"/>
                <intensity value="5.00000000"/>
                <intensity_far value="1.00000000"/>
                <color value="0xFFFF0F05"/>
                <numCoronas value="1"/>
                <distBetweenCoronas value="128"/>
                <distBetweenCoronas_far value="255"/>
                <xRotation value="0.00000000"/>
                <yRotation value="0.00000000"/>
                <zRotation value="0.00000000"/>
                <zBias value="0.25000000"/>
                <pullCoronaIn value="false"/>
            </tailLightCorona>
            <tailLightMiddleCorona>
                <size value="0.00000000"/>
                <size_far value="2.000000"/>
                <intensity value="0.00000000"/>
                <intensity_far value="0.00000000"/>
                <color value="0x00000000"/>
                <numCoronas value="1"/>
                <distBetweenCoronas value="128"/>
                <distBetweenCoronas_far value="255"/>
                <xRotation value="0.00000000"/>
                <yRotation value="0.00000000"/>
                <zRotation value="0.00000000"/>
                <zBias value="0.25000000"/>
                <pullCoronaIn value="false"/>
            </tailLightMiddleCorona>
            <headLight>
                <intensity value="1.40000000"/>
                <falloffMax value="35.00000000"/>
                <falloffExponent value="16.00000000"/>
                <innerConeAngle value="0.00000000"/>
                <outerConeAngle value="55.00000000"/>
                <emmissiveBoost value="true"/>
                <color value="0xFF7FA7E3"/>
                <textureName>VehicleLight_car_standardmodern</textureName>
                <mirrorTexture value="false"/>
            </headLight>
            <headLightCorona>
                <size value="1.00000000"/>
                <size_far value="6.000000"/>
                <intensity value="7.00000000"/>
                <intensity_far value="5.00000000"/>
                <color value="0xFF61A5FF"/>
                <numCoronas value="1"/>
                <distBetweenCoronas value="0"/>
                <distBetweenCoronas_far value="255"/>
                <xRotation value="0.00000000"/>
                <yRotation value="0.00000000"/>
                <zRotation value="0.00000000"/>
                <zBias value="0.25000000"/>
                <pullCoronaIn value="false"/>
            </headLightCorona>
            <reversingLight>
                <intensity value="0.50000000"/>
                <falloffMax value="4.20000000"/>
                <falloffExponent value="12.00000000"/>
                <innerConeAngle value="20.00000000"/>
                <outerConeAngle value="60.00000000"/>
                <emmissiveBoost value="false"/>
                <color value="0xFFFFFFFF"/>
                <textureName/>
                <mirrorTexture value="true"/>
            </reversingLight>
            <reversingLightCorona>
                <size value="0.05000000"/>
                <size_far value="1.500000"/>
                <intensity value="0.50000000"/>
                <intensity_far value="1.00000000"/>
                <color value="0x00000000"/>
                <numCoronas value="1"/>
                <distBetweenCoronas value="128"/>
                <distBetweenCoronas_far value="255"/>
                <xRotation value="0.00000000"/>
                <yRotation value="0.00000000"/>
                <zRotation value="0.00000000"/>
                <zBias value="0.25000000"/>
                <pullCoronaIn value="false"/>
            </reversingLightCorona>
            <name>cara</name>
        </Item>
    </Lights>
    <Kits>
        <Item>
            <kitName>3164_nkcoquette_modkit</kitName>
            <id value="3164"/>
            <kitType>MKT_SPORT</kitType>
            <visibleMods>
                <!-- CallSign 0 Left -->
                <!-- CallSign 1 Left -->
                <!-- CallSign 2 Left -->
                <!-- CallSign 3 Left -->
                <!-- CallSign 4 Left -->
                <!-- CallSign 5 Left -->
                <!-- CallSign 6 Left -->
                <!-- CallSign 7 Left -->
                <!-- CallSign 8 Left -->
                <!-- CallSign 9 Left -->
                <!-- CallSign 0 Center -->
                <!-- CallSign 1 Center -->
                <!-- CallSign 2 Center -->
                <!-- CallSign 3 Center -->
                <!-- CallSign 4 Center -->
                <!-- CallSign 5 Center -->
                <!-- CallSign 6 Center -->
                <!-- CallSign 7 Center -->
                <!-- CallSign 8 Center -->
                <!-- CallSign 9 Center -->
                <!-- CallSign 0 Right -->
                <!-- CallSign 1 Right -->
                <!-- CallSign 2 Right -->
                <!-- CallSign 3 Right -->
                <!-- CallSign 4 Right -->
                <!-- CallSign 5 Right -->
                <!-- CallSign 6 Right -->
                <!-- CallSign 7 Right -->
                <!-- CallSign 8 Right -->
                <!-- CallSign 9 Right -->
                <Item>
                    <modelName>nkcoquette_csign_a0</modelName>
                    <modShopLabel>NKCOQ_CSIGN_A0</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_L</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_a1</modelName>
                    <modShopLabel>NKCOQ_CSIGN_A1</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_L</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_a2</modelName>
                    <modShopLabel>NKCOQ_CSIGN_A2</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_L</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_a3</modelName>
                    <modShopLabel>NKCOQ_CSIGN_A3</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_L</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_a4</modelName>
                    <modShopLabel>NKCOQ_CSIGN_A4</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_L</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_a5</modelName>
                    <modShopLabel>NKCOQ_CSIGN_A5</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_L</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_a6</modelName>
                    <modShopLabel>NKCOQ_CSIGN_A6</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_L</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_a7</modelName>
                    <modShopLabel>NKCOQ_CSIGN_A7</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_L</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_a8</modelName>
                    <modShopLabel>NKCOQ_CSIGN_A8</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_L</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_a9</modelName>
                    <modShopLabel>NKCOQ_CSIGN_A9</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_L</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_b0</modelName>
                    <modShopLabel>NKCOQ_CSIGN_B0</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_R</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_b1</modelName>
                    <modShopLabel>NKCOQ_CSIGN_B1</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_R</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_b2</modelName>
                    <modShopLabel>NKCOQ_CSIGN_B2</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_R</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_b3</modelName>
                    <modShopLabel>NKCOQ_CSIGN_B3</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_R</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_b4</modelName>
                    <modShopLabel>NKCOQ_CSIGN_B4</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_R</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_b5</modelName>
                    <modShopLabel>NKCOQ_CSIGN_B5</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_R</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_b6</modelName>
                    <modShopLabel>NKCOQ_CSIGN_B6</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_R</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_b7</modelName>
                    <modShopLabel>NKCOQ_CSIGN_B7</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_R</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_b8</modelName>
                    <modShopLabel>NKCOQ_CSIGN_B8</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_R</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_b9</modelName>
                    <modShopLabel>NKCOQ_CSIGN_B9</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_WING_R</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_c0</modelName>
                    <modShopLabel>NKCOQ_CSIGN_C0</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_c1</modelName>
                    <modShopLabel>NKCOQ_CSIGN_C1</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_c2</modelName>
                    <modShopLabel>NKCOQ_CSIGN_C2</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_c3</modelName>
                    <modShopLabel>NKCOQ_CSIGN_C3</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_c4</modelName>
                    <modShopLabel>NKCOQ_CSIGN_C4</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_c5</modelName>
                    <modShopLabel>NKCOQ_CSIGN_C5</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_c6</modelName>
                    <modShopLabel>NKCOQ_CSIGN_C6</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_c7</modelName>
                    <modShopLabel>NKCOQ_CSIGN_</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_c8</modelName>
                    <modShopLabel>NKCOQ_CSIGN_C8</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_csign_c9</modelName>
                    <modShopLabel>NKCOQ_CSIGN_C9</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_spoiler_1</modelName>
                    <modShopLabel>NKCOQ_SPOILER_1</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SPOILER</type>
                    <bone>chassis</bone>
                    <collisionBone>mod_col_1</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_spoiler_2</modelName>
                    <modShopLabel>NKCOQ_SPOILER_2</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SPOILER</type>
                    <bone>chassis</bone>
                    <collisionBone>mod_col_2</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_skirt_1</modelName>
                    <modShopLabel>NKCOQ_SKIRT_1</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SKIRT</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_bumper_1</modelName>
                    <modShopLabel>NKCOQ_BUMPER_1</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_BUMPER_R</type>
                    <bone>bumper_r</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_bonnet_1</modelName>
                    <modShopLabel>NKCOQ_BONNET_1</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>bonnet</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>bonnet</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>nkcoquette_bonnet_2</modelName>
                    <modShopLabel>NKCOQ_BONNET_2</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>bonnet</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>bonnet</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>nkcoqantennas_main_1a</modelName>
                    <modShopLabel>NKCOQANT_MAIN_1A</modShopLabel>
                    <linkedModels>
                        <Item>nkcoqantennas_main_1b</Item>
                    </linkedModels>
                    <turnOffBones/>
                    <type>VMT_CHASSIS3</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>coq_split1</modelName>
                    <modShopLabel>MNU_BUMF1</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_BUMPER_F</type>
                    <bone>bumper_f</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>coq_exh1</modelName>
                    <modShopLabel>MNU_EXH12</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_a</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>coq_exh2</modelName>
                    <modShopLabel>MNU_EXH1</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_a</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>coq_exh3</modelName>
                    <modShopLabel>MNU_EXH2</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_a</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
            </visibleMods>
            <linkMods>
                <Item>
                    <modelName>nkcoqantennas_main_1b</modelName>
                    <bone>bobble_head</bone>
                    <turnOffExtra value="false"/>
                </Item>
            </linkMods>
            <statMods>
                <Item>
                    <identifier/>
                    <modifier value="25"/>
                    <audioApply value="1.00000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="50"/>
                    <audioApply value="1.00000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="75"/>
                    <audioApply value="1.00000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.00000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="25"/>
                    <audioApply value="1.00000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="50"/>
                    <audioApply value="1.00000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.00000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="25"/>
                    <audioApply value="1.00000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="50"/>
                    <audioApply value="1.00000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.00000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="20"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="40"/>
                    <audioApply value="1.00000000"/>
                    <weight value="10"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="60"/>
                    <audioApply value="1.00000000"/>
                    <weight value="20"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="80"/>
                    <audioApply value="1.00000000"/>
                    <weight value="30"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.00000000"/>
                    <weight value="40"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier>HORN_TRUCK</identifier>
                    <modifier value="1766676233"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_COP</identifier>
                    <modifier value="2904189469"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_CLOWN</identifier>
                    <modifier value="2543206147"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_1</identifier>
                    <modifier value="1732399718"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_2</identifier>
                    <modifier value="2046162893"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_3</identifier>
                    <modifier value="2194999691"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_4</identifier>
                    <modifier value="2508304100"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_5</identifier>
                    <modifier value="3707223535"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_SAD_TROMBONE</identifier>
                    <modifier value="632950117"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="5"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="10"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="15"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="20"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
                    <modifier value="3628534289"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
                    <modifier value="3892554122"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
                    <modifier value="4112892878"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
                    <modifier value="116877169"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
                    <modifier value="2684983719"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
                    <modifier value="2982690084"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
                    <modifier value="3203290992"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
                    <modifier value="771284519"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
                    <modifier value="2586621229"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
                    <modifier value="283386134"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
                    <modifier value="3884502400"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
                    <modifier value="265723083"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
                    <modifier value="1746883687"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
                    <modifier value="1919870950"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
                    <modifier value="1085277077"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_1</identifier>
                    <modifier value="444549672"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_2</identifier>
                    <modifier value="1603064898"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_3</identifier>
                    <modifier value="240366033"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_4</identifier>
                    <modifier value="960137118"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_1</identifier>
                    <modifier value="3572144790"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_2</identifier>
                    <modifier value="3801396714"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_3</identifier>
                    <modifier value="2843657151"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_4</identifier>
                    <modifier value="3341811489"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_1</identifier>
                    <modifier value="3199657341"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_2</identifier>
                    <modifier value="2900378064"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_3</identifier>
                    <modifier value="3956195248"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXORY_HORN_1</identifier>
                    <modifier value="676333254"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXURY_HORN_2</identifier>
                    <modifier value="2099578296"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXURY_HORN_3</identifier>
                    <modifier value="1373384483"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_01</identifier>
                    <modifier value="2916775806"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
                    <modifier value="3714706952"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_02</identifier>
                    <modifier value="2611860261"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
                    <modifier value="3206770359"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_1</identifier>
                    <modifier value="310529291"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
                    <modifier value="2965568987"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_2</identifier>
                    <modifier value="55291550"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
                    <modifier value="965054819"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_01</identifier>
                    <modifier value="55862314"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_01_PREVIEW</identifier>
                    <modifier value="2156743178"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_02</identifier>
                    <modifier value="400002352"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_02_PREVIEW</identifier>
                    <modifier value="897484282"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_03</identifier>
                    <modifier value="560832604"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_03_PREVIEW</identifier>
                    <modifier value="314232747"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_01</identifier>
                    <modifier value="3851180092"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_01_Preview</identifier>
                    <modifier value="246182814"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_02</identifier>
                    <modifier value="3412861948"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_02_Preview</identifier>
                    <modifier value="1804608241"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_03</identifier>
                    <modifier value="3374260066"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_03_Preview</identifier>
                    <modifier value="2798044638"/>
                    <audioApply value="1.00000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
            </statMods>
            <slotNames>
                <Item>
                    <slot>VMT_CHASSIS</slot>
                    <name>TOP_CAGE</name>
                </Item>
            </slotNames>
            <liveryNames/>
            <livery2Names/>
        </Item>
        <Item>
            <kitName>50000_bl_novak_modkit</kitName>
            <id value="50000"/>
            <kitType>MKT_SPECIAL</kitType>
            <visibleMods>
                <Item>
                    <modelName>novak_liv_1</modelName>
                    <modShopLabel>NOVA_LIV1</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_liv_2</modelName>
                    <modShopLabel>NOVA_LIV2</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_liv_3</modelName>
                    <modShopLabel>NOVA_LIV3</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_liv_4</modelName>
                    <modShopLabel>NOVA_LIV4</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_liv_5</modelName>
                    <modShopLabel>NOVA_LIV5</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_liv_6</modelName>
                    <modShopLabel>NOVA_LIV6</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_liv_7</modelName>
                    <modShopLabel>NOVA_LIV7</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_liv_8</modelName>
                    <modShopLabel>NOVA_LIV8</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_liv_9</modelName>
                    <modShopLabel>NOVA_LIV9</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_liv_10</modelName>
                    <modShopLabel>NOVA_LIV10</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_liv_11</modelName>
                    <modShopLabel>NOVA_LIV11</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_wing_a</modelName>
                    <modShopLabel>NOVA_WING_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_b</Item>
                    </turnOffBones>
                    <type>VMT_SPOILER</type>
                    <bone>boot</bone>
                    <collisionBone>mod_col_3</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_wing_b</modelName>
                    <modShopLabel>NOVA_WING_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_b</Item>
                    </turnOffBones>
                    <type>VMT_SPOILER</type>
                    <bone>boot</bone>
                    <collisionBone>mod_col_3</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_wing_c</modelName>
                    <modShopLabel>NOVA_WING_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_b</Item>
                    </turnOffBones>
                    <type>VMT_SPOILER</type>
                    <bone>boot</bone>
                    <collisionBone>mod_col_3</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_wing_d</modelName>
                    <modShopLabel>NOVA_WING_D</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_b</Item>
                    </turnOffBones>
                    <type>VMT_SPOILER</type>
                    <bone>boot</bone>
                    <collisionBone>mod_col_3</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_wing_e</modelName>
                    <modShopLabel>NOVA_WING_E</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_b</Item>
                    </turnOffBones>
                    <type>VMT_SPOILER</type>
                    <bone>boot</bone>
                    <collisionBone>mod_col_3</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_wing_f</modelName>
                    <modShopLabel>NOVA_WING_F</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_b</Item>
                    </turnOffBones>
                    <type>VMT_SPOILER</type>
                    <bone>boot</bone>
                    <collisionBone>mod_col_3</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_wing_g</modelName>
                    <modShopLabel>NOVA_WING_G</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_b</Item>
                    </turnOffBones>
                    <type>VMT_SPOILER</type>
                    <bone>boot</bone>
                    <collisionBone>mod_col_3</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_wing_h</modelName>
                    <modShopLabel>NOVA_WING_H</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_b</Item>
                    </turnOffBones>
                    <type>VMT_SPOILER</type>
                    <bone>boot</bone>
                    <collisionBone>mod_col_3</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_hood_a</modelName>
                    <modShopLabel>NOVA_HOOD_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>bonnet</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>bonnet</bone>
                    <collisionBone>bonnet</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_hood_b</modelName>
                    <modShopLabel>NOVA_HOOD_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>bonnet</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>bonnet</bone>
                    <collisionBone>bonnet</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_hood_c</modelName>
                    <modShopLabel>NOVA_HOOD_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>bonnet</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>bonnet</bone>
                    <collisionBone>bonnet</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_hood_d</modelName>
                    <modShopLabel>NOVA_HOOD_D</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>bonnet</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>bonnet</bone>
                    <collisionBone>bonnet</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_hood_e</modelName>
                    <modShopLabel>NOVA_HOOD_E</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>bonnet</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>bonnet</bone>
                    <collisionBone>bonnet</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_hood_f</modelName>
                    <modShopLabel>NOVA_HOOD_F</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>bonnet</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>bonnet</bone>
                    <collisionBone>bonnet</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_hood_g</modelName>
                    <modShopLabel>NOVA_HOOD_G</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>bonnet</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>bonnet</bone>
                    <collisionBone>mod_col_4</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_hood_h</modelName>
                    <modShopLabel>NOVA_HOOD_H</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>bonnet</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>bonnet</bone>
                    <collisionBone>mod_col_4</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_skirt_a</modelName>
                    <modShopLabel>NOVA_SKIRT_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SKIRT</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_skirt_b</modelName>
                    <modShopLabel>NOVA_SKIRT_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SKIRT</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_skirt_c</modelName>
                    <modShopLabel>NOVA_SKIRT_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SKIRT</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_skirt_d</modelName>
                    <modShopLabel>NOVA_SKIRT_D</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SKIRT</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_exh_a</modelName>
                    <modShopLabel>NOVA_EXH_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_a</Item>
                        <Item>exhaust</Item>
                        <Item>exhaust_2</Item>
                        <Item>exhaust_3</Item>
                        <Item>exhaust_4</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_exh_b</modelName>
                    <modShopLabel>NOVA_EXH_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_a</Item>
                        <Item>exhaust</Item>
                        <Item>exhaust_2</Item>
                        <Item>exhaust_3</Item>
                        <Item>exhaust_4</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_exh_c</modelName>
                    <modShopLabel>NOVA_EXH_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_a</Item>
                        <Item>exhaust</Item>
                        <Item>exhaust_2</Item>
                        <Item>exhaust_3</Item>
                        <Item>exhaust_4</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_exh_d</modelName>
                    <modShopLabel>NOVA_EXH_D</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_a</Item>
                        <Item>exhaust</Item>
                        <Item>exhaust_2</Item>
                        <Item>exhaust_3</Item>
                        <Item>exhaust_4</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_bumf_a</modelName>
                    <modShopLabel>NOVA_BUMPF_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_BUMPER_F</type>
                    <bone>misc_d</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_bumf_b</modelName>
                    <modShopLabel>NOVA_BUMPF_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_BUMPER_F</type>
                    <bone>misc_d</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_bumf_c</modelName>
                    <modShopLabel>NOVA_BUMPF_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_d</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_F</type>
                    <bone>misc_d</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_bumf_d</modelName>
                    <modShopLabel>NOVA_BUMPF_D</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_d</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_F</type>
                    <bone>misc_d</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_bumf_e</modelName>
                    <modShopLabel>NOVA_BUMPF_E</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_d</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_F</type>
                    <bone>misc_d</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_bumr_a</modelName>
                    <modShopLabel>NOVA_BUMPR_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_c</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_R</type>
                    <bone>misc_c</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_bumr_b</modelName>
                    <modShopLabel>NOVA_BUMPR_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_c</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_R</type>
                    <bone>misc_c</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_bumr_c</modelName>
                    <modShopLabel>NOVA_BUMPR_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_c</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_R</type>
                    <bone>misc_c</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_bumr_d</modelName>
                    <modShopLabel>NOVA_BUMPR_D</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_c</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_R</type>
                    <bone>misc_c</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                </Item>
                <Item>
                    <modelName>novak_roof_a</modelName>
                    <modShopLabel>NOVA_ROOF_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>mod_col_1</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>novak_roof_b</modelName>
                    <modShopLabel>NOVA_ROOF_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>mod_col_2</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
            </visibleMods>
            <linkMods/>
            <statMods>
                <Item>
                    <identifier/>
                    <modifier value="25"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="50"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="75"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="25"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="50"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="50"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="200"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="10"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="20"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="30"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="40"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="20"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="40"/>
                    <audioApply value="1.000000"/>
                    <weight value="10"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="60"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="80"/>
                    <audioApply value="1.000000"/>
                    <weight value="30"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="40"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier>HORN_TRUCK</identifier>
                    <modifier value="1766676233"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_COP</identifier>
                    <modifier value="2904189469"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_CLOWN</identifier>
                    <modifier value="2543206147"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_1</identifier>
                    <modifier value="1732399718"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_2</identifier>
                    <modifier value="2046162893"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_3</identifier>
                    <modifier value="2194999691"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_4</identifier>
                    <modifier value="2508304100"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_5</identifier>
                    <modifier value="3707223535"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_SAD_TROMBONE</identifier>
                    <modifier value="632950117"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
                    <modifier value="3628534289"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
                    <modifier value="3892554122"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
                    <modifier value="4112892878"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
                    <modifier value="116877169"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
                    <modifier value="2684983719"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
                    <modifier value="2982690084"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
                    <modifier value="3203290992"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
                    <modifier value="771284519"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
                    <modifier value="2586621229"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
                    <modifier value="283386134"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
                    <modifier value="3884502400"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
                    <modifier value="265723083"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
                    <modifier value="1746883687"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
                    <modifier value="1919870950"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
                    <modifier value="1085277077"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_1</identifier>
                    <modifier value="444549672"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_2</identifier>
                    <modifier value="1603064898"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_3</identifier>
                    <modifier value="240366033"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_4</identifier>
                    <modifier value="960137118"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_1</identifier>
                    <modifier value="3572144790"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_2</identifier>
                    <modifier value="3801396714"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_3</identifier>
                    <modifier value="2843657151"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_4</identifier>
                    <modifier value="3341811489"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_1</identifier>
                    <modifier value="3199657341"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_2</identifier>
                    <modifier value="2900378064"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_3</identifier>
                    <modifier value="3956195248"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXORY_HORN_1</identifier>
                    <modifier value="676333254"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXURY_HORN_2</identifier>
                    <modifier value="2099578296"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXURY_HORN_3</identifier>
                    <modifier value="1373384483"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_01</identifier>
                    <modifier value="2916775806"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
                    <modifier value="3714706952"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_02</identifier>
                    <modifier value="2611860261"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
                    <modifier value="3206770359"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_1</identifier>
                    <modifier value="310529291"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
                    <modifier value="2965568987"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_2</identifier>
                    <modifier value="55291550"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
                    <modifier value="965054819"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_01</identifier>
                    <modifier value="55862314"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_01_PREVIEW</identifier>
                    <modifier value="2156743178"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_02</identifier>
                    <modifier value="400002352"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_02_PREVIEW</identifier>
                    <modifier value="897484282"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_03</identifier>
                    <modifier value="560832604"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_03_PREVIEW</identifier>
                    <modifier value="314232747"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_01</identifier>
                    <modifier value="3851180092"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_01_Preview</identifier>
                    <modifier value="246182814"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_02</identifier>
                    <modifier value="3412861948"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_02_Preview</identifier>
                    <modifier value="1804608241"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_03</identifier>
                    <modifier value="3374260066"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_03_Preview</identifier>
                    <modifier value="2798044638"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
            </statMods>
            <slotNames>
                <Item>
                    <slot>VMT_CHASSIS</slot>
                    <name>TOP_CAGE</name>
                </Item>
                <Item>
                    <slot>VMT_GRILL</slot>
                    <name>TOP_SPLIT</name>
                </Item>
            </slotNames>
            <liveryNames/>
        </Item>
        <Item>
            <kitName>369_zorrusso_modkit</kitName>
            <id value="369"/>
            <kitType>MKT_SPECIAL</kitType>
            <visibleMods>
                <Item>
                    <modelName>zor_roof_a</modelName>
                    <modShopLabel>ZOR_ROOF_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_ROOF</type>
                    <bone>chassis</bone>
                    <collisionBone>mod_col_4</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_hood_a</modelName>
                    <modShopLabel>ZOR_HOOD_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_a</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_hood_b</modelName>
                    <modShopLabel>ZOR_HOOD_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_a</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_hood_c</modelName>
                    <modShopLabel>ZOR_HOOD_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_a</Item>
                    </turnOffBones>
                    <type>VMT_BONNET</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_bumf_a</modelName>
                    <modShopLabel>ZOR_BUMF_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_e</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_F</type>
                    <bone>misc_e</bone>
                    <collisionBone>misc_e</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_bumf_b</modelName>
                    <modShopLabel>ZOR_BUMF_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_e</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_F</type>
                    <bone>misc_e</bone>
                    <collisionBone>misc_e</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_bumf_c</modelName>
                    <modShopLabel>ZOR_BUMF_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_e</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_F</type>
                    <bone>misc_e</bone>
                    <collisionBone>misc_e</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_bumf_d</modelName>
                    <modShopLabel>ZOR_BUMF_D</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_e</Item>
                    </turnOffBones>
                    <type>VMT_BUMPER_F</type>
                    <bone>misc_e</bone>
                    <collisionBone>misc_e</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="false"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_wing_a</modelName>
                    <modShopLabel>ZOR_WING_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SPOILER</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_wing_b</modelName>
                    <modShopLabel>ZOR_WING_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SPOILER</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_wing_c</modelName>
                    <modShopLabel>ZOR_WING_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SPOILER</type>
                    <bone>chassis</bone>
                    <collisionBone>mod_col_1</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_wing_d</modelName>
                    <modShopLabel>ZOR_WING_D</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SPOILER</type>
                    <bone>chassis</bone>
                    <collisionBone>mod_col_2</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_wing_e</modelName>
                    <modShopLabel>ZOR_WING_E</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SPOILER</type>
                    <bone>chassis</bone>
                    <collisionBone>mod_col_3</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_grill_a</modelName>
                    <modShopLabel>ZOR_GRILL_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_b</Item>
                    </turnOffBones>
                    <type>VMT_GRILL</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_grill_b</modelName>
                    <modShopLabel>ZOR_GRILL_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_b</Item>
                    </turnOffBones>
                    <type>VMT_GRILL</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_exh_a</modelName>
                    <modShopLabel>ZOR_EXH_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_d</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_exh_b</modelName>
                    <modShopLabel>ZOR_EXH_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_d</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_exh_c</modelName>
                    <modShopLabel>ZOR_EXH_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_d</Item>
                        <Item>bumper_r</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_exh_d</modelName>
                    <modShopLabel>ZOR_EXH_D</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_d</Item>
                        <Item>bumper_r</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_exh_e</modelName>
                    <modShopLabel>ZOR_EXH_E</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_d</Item>
                        <Item>bumper_r</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_exh_f</modelName>
                    <modShopLabel>ZOR_EXH_F</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>exhaust</Item>
                        <Item>exhaust_2</Item>
                        <Item>misc_d</Item>
                        <Item>bumper_r</Item>
                    </turnOffBones>
                    <type>VMT_EXHAUST</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_livery_a</modelName>
                    <modShopLabel>ZOR_LIV_A</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_livery_b</modelName>
                    <modShopLabel>ZOR_LIV_B</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_livery_c</modelName>
                    <modShopLabel>ZOR_LIV_C</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_livery_d</modelName>
                    <modShopLabel>ZOR_LIV_D</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_livery_e</modelName>
                    <modShopLabel>ZOR_LIV_E</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_livery_f</modelName>
                    <modShopLabel>ZOR_LIV_F</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_livery_g</modelName>
                    <modShopLabel>ZOR_LIV_G</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_livery_h</modelName>
                    <modShopLabel>ZOR_LIV_H</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_livery_i</modelName>
                    <modShopLabel>ZOR_LIV_I</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
                <Item>
                    <modelName>zor_livery_j</modelName>
                    <modShopLabel>ZOR_LIV_J</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_LIVERY_MOD</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                    <weaponSlot value="-1"/>
                    <weaponSlotSecondary value="-1"/>
                    <disableProjectileDriveby value="false"/>
                    <disableDriveby value="false"/>
                    <disableDrivebySeat value="-1"/>
                    <disableDrivebySeatSecondary value="-1"/>
                </Item>
            </visibleMods>
            <linkMods/>
            <statMods>
                <Item>
                    <identifier/>
                    <modifier value="25"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="50"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="75"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="25"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="65"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="25"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="50"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="20"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="40"/>
                    <audioApply value="1.000000"/>
                    <weight value="10"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="60"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="80"/>
                    <audioApply value="1.000000"/>
                    <weight value="30"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="40"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier>HORN_TRUCK</identifier>
                    <modifier value="1766676233"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_COP</identifier>
                    <modifier value="2904189469"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_CLOWN</identifier>
                    <modifier value="2543206147"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_1</identifier>
                    <modifier value="1732399718"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_2</identifier>
                    <modifier value="2046162893"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_3</identifier>
                    <modifier value="2194999691"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_4</identifier>
                    <modifier value="2508304100"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_5</identifier>
                    <modifier value="3707223535"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_SAD_TROMBONE</identifier>
                    <modifier value="632950117"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
                    <modifier value="3628534289"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
                    <modifier value="3892554122"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
                    <modifier value="4112892878"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
                    <modifier value="116877169"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
                    <modifier value="2684983719"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
                    <modifier value="2982690084"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
                    <modifier value="3203290992"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
                    <modifier value="771284519"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
                    <modifier value="2586621229"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
                    <modifier value="283386134"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
                    <modifier value="3884502400"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
                    <modifier value="265723083"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
                    <modifier value="1746883687"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
                    <modifier value="1919870950"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
                    <modifier value="1085277077"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_1</identifier>
                    <modifier value="444549672"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_2</identifier>
                    <modifier value="1603064898"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_3</identifier>
                    <modifier value="240366033"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_4</identifier>
                    <modifier value="960137118"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_1</identifier>
                    <modifier value="3572144790"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_2</identifier>
                    <modifier value="3801396714"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_3</identifier>
                    <modifier value="2843657151"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_4</identifier>
                    <modifier value="3341811489"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_1</identifier>
                    <modifier value="3199657341"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_2</identifier>
                    <modifier value="2900378064"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_3</identifier>
                    <modifier value="3956195248"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXORY_HORN_1</identifier>
                    <modifier value="676333254"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXURY_HORN_2</identifier>
                    <modifier value="2099578296"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXURY_HORN_3</identifier>
                    <modifier value="1373384483"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_01</identifier>
                    <modifier value="2916775806"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
                    <modifier value="3714706952"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_02</identifier>
                    <modifier value="2611860261"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
                    <modifier value="3206770359"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_1</identifier>
                    <modifier value="310529291"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
                    <modifier value="2965568987"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_2</identifier>
                    <modifier value="55291550"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
                    <modifier value="965054819"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_01</identifier>
                    <modifier value="55862314"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_01_PREVIEW</identifier>
                    <modifier value="2156743178"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_02</identifier>
                    <modifier value="400002352"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_02_PREVIEW</identifier>
                    <modifier value="897484282"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_03</identifier>
                    <modifier value="560832604"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_03_PREVIEW</identifier>
                    <modifier value="314232747"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_01</identifier>
                    <modifier value="3851180092"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_01_Preview</identifier>
                    <modifier value="246182814"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_02</identifier>
                    <modifier value="3412861948"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_02_Preview</identifier>
                    <modifier value="1804608241"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_03</identifier>
                    <modifier value="3374260066"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_03_Preview</identifier>
                    <modifier value="2798044638"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
            </statMods>
            <slotNames/>
            <liveryNames/>
            <livery2Names/>
        </Item>
    </Kits>
</CVehicleModelInfoVarGlobal>