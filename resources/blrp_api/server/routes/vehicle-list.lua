routes['/vehicle-list'] = {
  method = 'GET',
  callback = function(request, api_account)
    local vehicles = {}

    for vehicle_name, vehicle_def in pairs(exports.blrp_vehicles:GetConfigVehiclesAll()) do
      vehicles[vehicle_name] = {
        vehicle_name,
        vehicle_def[1], -- Long name
        vehicle_def[2], -- Price
      }
    end

    return 200, { success = true, vehicles = vehicles }
  end
}
