routes['/logviadiscord'] = {
  method = 'GET',
  callback = function(request, api_account)
    local postdata = validateRequest(request, {
      'discord', 'log_type', 'log'
    })

    if not postdata then
      return 400, { }
    end

    local user_id = MySQL.scalar.await('SELECT user_id FROM vrp_user_ids WHERE identifier = ?', {
      'discord:' .. postdata.discord
    })

    if not user_id then
      return 404, { }
    end

    local context = postdata.context

    if context then
      context = json.decode(context)
    end

    exports.blrp_core:LogSplunkVrp(user_id, postdata.log_type, postdata.log, context)

    return 200, { success = true, user_id = user_id }
  end
}
