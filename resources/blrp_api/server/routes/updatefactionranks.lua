routes['/updatefactionranks'] = {
  method = 'POST',
  callback = function(request, api_account)
    local postdata = validateRequest(request, {
      'character_id',
    })

    if not postdata then
      return 400, {}
    end

    local character = exports.blrp_core:characterFromId(postdata.character_id)

    if character then
      local character_row = MySQL.Sync.fetchAll('SELECT * FROM characters WHERE id = @character_id', {
        character_id = character.get('id')
      })[1]

      for column, value in pairs(character_row) do
        if string.match(column, 'rank_') and tonumber(character.get(column)) ~= tonumber(value) then
          character.set(column, value)

          exports.blrp_core:ValidateOffDutyGroups(character.source)
        end
      end
    end

    return 200, {}
  end
}
