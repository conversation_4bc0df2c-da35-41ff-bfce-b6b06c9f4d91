<?xml version="1.0" encoding="UTF-8"?>
<CPedModelInfo__InitDataList>
   <InitDatas>
    <Item>
      <Name>a_c_capybara</Name>
      <PropsName>null</PropsName>
      <ClipDictionaryName>creatures@capybara@move</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName />
      <ExpressionDictionaryName>a_c_capybara</ExpressionDictionaryName>
      <ExpressionName>a_c_capybara</ExpressionName>
      <Pedtype>ANIMAL</Pedtype>
      <MovementClipSet>BOAR</MovementClipSet>
      <StrafeClipSet>move_ped_strafing</StrafeClipSet>
      <MovementToStrafeClipSet>move_ped_to_strafe</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>move_strafe_injured</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>dam_ko</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>dam_ad</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_M_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_male</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_M_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Boar</PoseMatcherName>
      <PoseMatcherProneName>null</PoseMatcherProneName>
      <GetupSetHash>NMBS_BOAR_GETUPS</GetupSetHash>
      <CreatureMetadataName>null</CreatureMetadataName>
      <DecisionMakerName>WildAnimal</DecisionMakerName>
      <MotionTaskDataSetName>Boar</MotionTaskDataSetName>
      <DefaultTaskDataSetName>ANIMAL</DefaultTaskDataSetName> 
      <PedCapsuleName>boar</PedCapsuleName>
      <PedLayoutName />
      <PedComponentSetName />
      <PedComponentClothName />
      <PedIKSettingsName>NO_IK</PedIKSettingsName>
      <TaskDataName>ANIMAL</TaskDataName>
      <IsStreamedGfx value="false" />
      <AmbulanceShouldRespondTo value="false" />
      <CanRideBikeWithNoHelmet value="false" />
      <CanSpawnInCar value="true" />
      <IsHeadBlendPed value="false" />
      <bOnlyBulkyItemVariations value="false" />
      <RelationshipGroup>WILD_ANIMAL</RelationshipGroup>
      <NavCapabilitiesName>WILD_ANIMAL</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_BOAR</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_BOAR</DefaultUnarmedWeapon>
      <Personality>BOAR</Personality>
      <CombatInfo>SCARED_ANIMAL</CombatInfo>
      <VfxInfoName>VFXPEDINFO_PIG_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_PUNK</Radio1>
      <Radio2>RADIO_GENRE_JAZZ</Radio2>
      <FUpOffset value="0.000000" />
      <RUpOffset value="0.000000" />
      <FFrontOffset value="0.000000" />
      <RFrontOffset value="0.147000" />
      <MinActivationImpulse value="20.000000" />
      <Stubble value="0.000000" />
      <HDDist value="6.000000" />
      <TargetingThreatModifier value="1.000000" />
      <KilledPerceptionRangeModifer value="-1.000000" />
      <Sexiness />
      <Age value="0" />
      <MaxPassengersInCar value="0" />
      <ExternallyDrivenDOFs />
      <PedVoiceGroup>MALE_BRAVE_PVG</PedVoiceGroup>
      <AnimalAudioObject />
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_COLD</ThermalBehaviour>
      <SuperlodType>SLOD_KEEP_LOWEST</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.000000" />
      <AllowCloseSpawning value="false" />
    </Item>
  	</InitDatas>
</CPedModelInfo__InitDataList>