local _promise = nil

blrp_phonehackCallback = {}
showHelp = false
helpTimer = 0
helpCycle = 4000

tPhonehack = {}
T.bindInstance('phonehack', tPhonehack)

tPhonehack.hack = function(solution_length, duration)
	SendNUIMessage({
		s = solution_length,
		d = duration,
		start = true,
		show = true
	})
	SetNuiFocus(true, false)
	showHelp = true

	_promise = promise:new()

	local result = Citizen.Await(_promise)

	TriggerEvent('blrp_phonehack:hide')

	return table.unpack(result)
end

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		if showHelp then
			if helpTimer > GetGameTimer() then
				showHelpText("Navigate with ~y~W,A,S,D~s~ and confirm with ~y~SPACE~s~ for the left code block.")
			elseif helpTimer > GetGameTimer()-helpCycle then
				showHelpText("Use the ~y~Arrow Keys~s~ and ~y~ENTER~s~ for the right code block")
			else
				helpTimer = GetGameTimer()+helpCycle
			end
			if IsEntityDead(PlayerPedId()) then
				nuiMsg = {}
				nuiMsg.fail = true
				SendNUIMessage(nuiMsg)
			end
		end
	end
end)

function showHelpText(s)
	SetTextComponentFormat("STRING")
	AddTextComponentString(s)
	EndTextCommandDisplayHelp(0,0,0,-1)
end

AddEventHandler('blrp_phonehack:show', function()
    nuiMsg = {}
	nuiMsg.show = true
	SendNUIMessage(nuiMsg)
	SetNuiFocus(true, false)
end)

AddEventHandler('blrp_phonehack:hide', function()
	SendNUIMessage({
		show = false
	})
	SetNuiFocus(false, false)
	showHelp = false
end)

AddEventHandler('menu:forceCloseMenu', function()
	TriggerEvent('blrp_phonehack:hide')
end)

AddEventHandler('blrp_phonehack:setmessage', function(msg)
    nuiMsg = {}
	nuiMsg.displayMsg = msg
	SendNUIMessage(nuiMsg)
end)

RegisterNUICallback('callback', function(data, cb)
	if not _promise then
		return
	end

	_promise:resolve({ data.success, data.remainingtime })
  cb('ok')
end)
