local last_queue_size = 0
local min_queue_size = 5 -- minimum queue size required for AFK kicker to activate

local tablet_open = false
local arcade_open = false


local afk_words = {
  -- Everyday objects
  "banana", "coffee", "dragon", "guitar", "jungle",
  "laptop", "pillow", "rocket", "sunshine", "toaster",
  "unicorn", "volcano", "whistle", "zebra", "yoyo",
  "cactus", "helmet", "noodle", "marble", "velcro",
  "taco", "bacon", "scooter", "syrup", "toffee",
  "kettle", "sneaker", "candle", "plunger", "daisy",

  -- GTA locations
  "losantos", "sandy", "paleto", "vinewood", "grapeseed",
  "mirrorpark", "vespucci", "delperro", "chumash", "blaine",
  "rockford", "murrieta", "gallilee", "elburro", "cyprus",

  -- GTA vehicles
  "banshee", "kuruma", "adder", "zentorno", "cavalcade",
  "faggio", "ruiner", "comet", "dominator", "sultan",
  "manchez", "reaper", "toreador", "buffalo", "blista",

  -- GTA characters
  "michael", "franklin", "trevor", "lester", "lamar",
  "ron", "tracy", "amanda", "devin", "dave",
  "martin", "brucie", "tony", "chop", "wade",

  -- GTA themes/slang
  "heist", "stash", "bling", "grind", "respawn",
  "copcar", "getaway", "stashhouse", "boosting", "lockpick",
  "pursuit", "towtruck", "safehouse", "griefer", "freemode",
  "dolla", "drift", "checkpoint", "sandbox"
}


function generateAfkPhrase()
  math.randomseed(GetGameTimer())
  local word1 = afk_words[math.random(1, #afk_words)]
  return word1
end

local afkBypassString = generateAfkPhrase()

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(60000)

    TriggerEvent('blrp_tablet:getTabletState', function(open)
      tablet_open = open
    end)

    arcade_open = exports['rcore_arcade']:isArcadeOpen()

    afkBypassString = generateAfkPhrase()
  end
end)

AddStateBagChangeHandler('queue_size', 'global', function(_, _, value, _, _)
  last_queue_size = value
end)

function drawTxt2(x, y, width, height, scale, text, r, g, b, a)
  SetTextFont(6)
  SetTextProportional(0)
  SetTextScale(scale, scale)
  SetTextColour(r, g, b, a)
  SetTextDropShadow(0, 0, 0, 0, 255)
  SetTextEdge(1, 0, 0, 0, 255)
  SetTextDropShadow()
  SetTextOutline()
  SetTextEntry("STRING")
  AddTextComponentString(text)
  DrawText(x - width / 2, y - height / 2 + 0.005)
end

local afk_last_pos = nil
local afk_ticks = 0
local afk_script_active = true
local afk_timeout = 900 -- AFK kick time in seconds (15 minutes)
local previous_timeout = afk_timeout

Citizen.CreateThread(function()
  Citizen.Wait(10000)
  if GlobalState.is_dev then
    afk_script_active = false
  end
end)

Citizen.CreateThread(function()
  while afk_script_active do
    Citizen.Wait(1000)

    local position = GetEntityCoords(PlayerPedId())
    local me = exports.blrp_core:me()

    -- Dynamically set timeout: 1800 if no queue, 900 if any queue
    if last_queue_size > min_queue_size then
      afk_timeout = 900
    else
      afk_timeout = 1800
    end

    -- Reset AFK ticks if timeout decreased
    if afk_timeout < previous_timeout and afk_ticks > 0 then
      afk_ticks = 0
    end

    previous_timeout = afk_timeout


    -- 45-minute timeout for new players under 3 hours
    if me.get('age_hours') < 3 and afk_timeout > 2700 then
      afk_timeout = 2700
    end

    -- Revert new players over 3 hours to regular timeout
    if me.get('age_hours') >= 3 and afk_timeout > 900 and afk_ticks == 0 then
      afk_timeout = (last_queue_size > 0) and 900 or 1800
    end

    -- Check AFK status (no more min_queue_size check)
    if
      not me.hasGroup('AFK Exempt') and
      not me.hasGroup('Dispatch') and
      not me.isStaff() and
      afk_last_pos and
      #(afk_last_pos - position) < 0.5
    then
      afk_ticks = afk_ticks + 1

      if afk_ticks >= afk_timeout then
        afk_ticks = 0
        TriggerServerEvent('vRP:dropSelf', '[Kicked] AFK for more than ' .. math.floor(afk_timeout / 60) .. ' minutes')
      end
    else
      afk_ticks = 0
    end

    afk_last_pos = position
  end
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    local y = 1.19

    if tablet_open then
      y = 1.4
    end

    if arcade_open then
      y = 1.4
    end

    if afk_script_active and (afk_ticks > afk_timeout - 180) then
      drawTxt2(0.9, y, 1.0, 1.0, 0.4, "You will be kicked due to inactivity in ~r~" .. (afk_timeout - afk_ticks) .. " seconds", 255, 255, 255, 255)
      drawTxt2(0.93, y+0.03, 1.0, 1.0, 0.4, "~w~Type ~g~/notafk "..afkBypassString.."~w~ to reset!", 255, 255, 255, 255)
    end
  end
end)

RegisterCommand('notafk', function(source, args)
 local CheckString = args[1]
  if CheckString == afkBypassString then
    afk_ticks = 0
    exports.blrp_core:me().notify('AFK Timer Reset')
    afkBypassString = generateAfkPhrase()
  else
    exports.blrp_core:me().notify('Wrong value entered, AFK Timer remains!')
  end
end)

RegisterNetEvent('blrp_dev:client:afkCommandCheck', function(checkValue)
  ExecuteCommand('notafk '..checkValue)
end)
