local pPostOffice = P.getInstance('blrp_postoffice', 'postoffice')

local houses = {}

RegisterNetEvent('core:client:syncHousing', function(data)
  SendNUIMessage({
    type = 'interface:updateData',
    house_data = data,
    office_locations = postoffice_config.locations,
    mail_types = postoffice_config.mail_types
  })
end)

RegisterNUICallback('next_step', function(data, cb)
  TriggerEvent('blrp_postoffice:client:hideInterface')
  TriggerServerEvent('blrp_postoffice:server:openDepositBox', data)
end)

RegisterNUICallback('escape', function(data, cb)
    TriggerEvent('blrp_postoffice:client:hideInterface')
    cb('ok')
end)

-- NUI related stuff

local guiEnabled = false

function focusNUI(shouldDisplay)
    guiEnabled = shouldDisplay
    SetNuiFocus(guiEnabled, guiEnabled)
end

RegisterNetEvent('blrp_postoffice:client:showInterface')
AddEventHandler('blrp_postoffice:client:showInterface', function(location)
  local data = pPostOffice.getHouses()

  SendNUIMessage({
    type = 'interface:updateData',
    house_data = data,
    office_locations = postoffice_config.locations,
    mail_types = postoffice_config.mail_types
  })

  focusNUI(true)
  SendNUIMessage({
      type = 'interface:show',
      location = location
  })
end)

RegisterNetEvent('blrp_postoffice:client:hideInterface')
AddEventHandler('blrp_postoffice:client:hideInterface', function()
    if guiEnabled then
        focusNUI(false)
        SendNUIMessage({
            type = 'interface:hide',
        })
    end
end)

Citizen.CreateThread(function()
    TriggerEvent('blrp_postoffice:client:hideInterface')
end)

AddEventHandler('menu:forceCloseMenu', function()
    if guiEnabled then
        TriggerEvent('blrp_postoffice:client:hideInterface')
    end
end)

if GlobalState.is_dev then
  Citizen.CreateThread(function()
    for _, location in ipairs(postoffice_config.locations) do
      if not location.hide_prompt then
        if not location.hotkey then location.hotkey = 'E' end
        if not location.distance then location.distance = 1 end
        if not location.lang then location.lang = 'Press {key} to use' end

        TriggerEvent('core:client:registerInteract', location)
      end
    end
  end)
end