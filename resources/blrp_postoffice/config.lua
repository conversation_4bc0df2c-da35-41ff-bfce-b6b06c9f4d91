postoffice_config = {
  shipment_hold_time = 10, -- Amount of time for a shipment to be held at a post office before moving to the central depot
  shipment_stagnate_time = 60, -- Amount of time before a shipment should be autodelivered

  mail_types = {
    ["letter"] = {
      type = "letter",
      cost_per_meter = 0.10,
      chest_weight = 0.4, -- Enough for one printed paper or signed document
      whitelisted_items = { 'printed_paper', 'm_document' }
    },

    ["parcel"] = {
      type = "parcel",
      cost_per_meter = 0.20,
      chest_weight = 4.5,
      whitelisted_items = false
    },

    ["freight"] = {
      type = "freight",
      cost_per_meter = 0.30,
      chest_weight = 19.5,
      whitelisted_items = false
    }
  },

  locations = {
    {
      coords = { x = -259.1716003418, y = -841.81060791016, z = 31.420108795166 },
      call = 'blrp_postoffice:server:sendMail',
      args = 'pillbox',
      lang = 'Press {key} to send mail',
      distance = 3,
    },
    {
      coords = { x = 1698.4916992188, y = 3782.2841796875, z = 34.766948699951 },
      call = 'blrp_postoffice:server:sendMail',
      args = 'sandy',
      lang = 'Press {key} to send mail',
      distance = 1.5,
      hide_prompt = true -- Accessible via blrp_target
    },
    {
      coords = { x = -423.01626586914, y = 6134.2983398438, z = 31.877340316772 },
      call = 'blrp_postoffice:server:sendMail',
      args = 'paleto',
      lang = 'Press {key} to send mail',
      distance = 3
    },
  },

  prohibited_items = {
    -- You can't mail mail...

    'gopostal_letter',
    'gopostal_parcel',
    'gopostal_freight',

    -- Illegal drugs and KNOWN illegal drug components

    'marijuana',
    'marijuana2',
    'weed1',
    'weed2',
    'meth',
    'cocaine1',
    'cocaine2',
    'lsd',
    'mdma',
    'heroin',
    'pcp',
    'cannabis_seed',
    'meth_kit',
    'coca_leaf',
    'coca_paste',
    'ephedrine',

    -- Vangelico heist related

    'bank_phone',

    'jewl_silver_bracelet',
    'jewl_silver_necklace',
    'jewl_gold_bracelet',
    'jewl_gold_necklace',
    'jewl_gold_rolex',
    'jewl_diamond_bracelet',
    'jewl_diamond_rolex',
    'jewl_diamond_necklace',

    -- Bank robbery related

    'laptop_h',
    'money_bag',
    'marked_bill',

    -- Weapon related

    'ammo',
    'wammo',
    'wbody',

    -- Other

    'speedbomb',
    'dynamite',
  }
}
