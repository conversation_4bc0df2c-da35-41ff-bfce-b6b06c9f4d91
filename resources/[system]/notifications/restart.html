<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <meta name="description" content="">
      <meta name="author" content="">
      <!-- Bootstrap Core CSS -->
      <link href="css/bootstrap.min.css" rel="stylesheet">
      <!-- Custom CSS -->
      <link href="css/homepage.css" rel="stylesheet">
      <script src="js/jquery.js" type="text/javascript"></script>
      <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
      <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
      <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
      <![endif]-->
   </head>
   <body>
      <img id="cursor" src="cursor.webp" style="position: absolute; display:none; z-index: 99;">
      <!-- Page Content -->
      <div class="container">
      <div class="row">
         <div class="col-md-12">
              <div class="jumbotron">
                 <h2><b>Hey there!</b></h2>
                 <p>This BadlandsRP server is about to restart. The process is painless and usually takes less than a minute, but you'll need to reconnect to continue where you left off. Just find us in the server list, and we'll see you when you get back!</p>
                 <p><button type="button" class="btn btn-danger btn-lg btn-block" id="close-modal">Got it, thanks!</button></p>
              </div>
         </div>
      </div>
      <!-- /.container -->
      <!-- jQuery -->
      <script src="js/jquery.js"></script>
      <!-- Bootstrap Core JavaScript -->
      <script src="js/bootstrap.min.js"></script>
      <script>
         var documentWidth = document.documentElement.clientWidth;
         var documentHeight = document.documentElement.clientHeight;

         var cursor = document.getElementById("cursor");
         var cursorX = documentWidth / 2;
         var cursorY = documentHeight / 2;

         function UpdateCursorPos() {
             $(cursor).css('left', cursorX + 1)
             $(cursor).css('top', cursorY + 1)
         }

         function Click(x, y) {
             var element = $(document.elementFromPoint(x - window.pageXOffset, y - window.pageYOffset));
             element.focus().click();
         }

         $(function() {
            $(cursor).css('display', "none")
            $("body").css('display', "none")
            $("body").css('background', 'none')

            window.addEventListener('message', function(event) {
                if (event.data.type == "enableui") {
                    $(cursor).css('display', event.data.enable ? "block" : "none")
                    $("body").css('display', event.data.enable ? "block" : "none")
                    //$("body").css('background', event.data.enable ? "white" : "none")
                }
            });

             $(document).mousemove(function(event) {
                 cursorX = event.pageX;
                 cursorY = event.pageY;
                 UpdateCursorPos();
             });

             document.onkeyup = function (data) {
                 if (data.which == 27) { // Escape key
                     $.post('http://notifications/escape', JSON.stringify({}));
                 }
             };

            $('#close-modal').click(function(){ $.post('http://notifications/escape', JSON.stringify({})); })
         });
      </script>
   </body>
</html>
