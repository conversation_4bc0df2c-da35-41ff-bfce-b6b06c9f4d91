quest_peds = {}

Citizen.CreateThread(function()
  local id = 'PilotYankton'
  local model = `s_m_m_pilot_01`
  local coords = vector4(5790.400, -5100.972, 80.856, 247.603)
  local dict = 'anim@heists@heist_corona@team_idles@male_a'
  local clip = 'idle'

  RequestModel(model)

  while not HasModelLoaded(model) do
    Citizen.Wait(1)
  end

  RequestAnimDict(dict)

  local start = GetGameTimer()

  while not HasAnimDictLoaded(dict) do
    Citizen.Wait(1)

    if GetGameTimer() - start > 10000 then
      exports.blrp_core:me().notify('Failed to load clip dict: ' .. dict)
      return
    end
  end

  local ped = CreatePed(4, model, coords.x, coords.y, coords.z - 0.97, coords.w, false, true)
  FreezeEntityPosition(ped, true)
  SetEntityInvincible(ped, true)
  SetBlockingOfNonTemporaryEvents(ped, true)
  TaskPlayAnim(ped, dict, clip, 8.0, 0, -1, 1, 0, 0, 0)

  exports.blrp_quests:RegisterPed(id, ped)
  quest_peds[id] = ped
  Entity(ped).state.pilot_id = id
end)

Citizen.CreateThread(function()
  local id = 'PilotLSIA'
  local model = `s_m_m_pilot_01`
  local coords = vector4(-1029.483, -2486.322, 20.169, 241.010)
  local dict = 'anim@heists@heist_corona@team_idles@male_a'
  local clip = 'idle'

  RequestModel(model)

  while not HasModelLoaded(model) do
    Citizen.Wait(1)
  end

  RequestAnimDict(dict)

  local start = GetGameTimer()

  while not HasAnimDictLoaded(dict) do
    Citizen.Wait(1)

    if GetGameTimer() - start > 10000 then
      exports.blrp_core:me().notify('Failed to load clip dict: ' .. dict)
      return
    end
  end

  local ped = CreatePed(4, model, coords.x, coords.y, coords.z - 0.97, coords.w, false, true)
  FreezeEntityPosition(ped, true)
  SetEntityInvincible(ped, true)
  SetBlockingOfNonTemporaryEvents(ped, true)
  TaskPlayAnim(ped, dict, clip, 8.0, 0, -1, 1, 0, 0, 0)

  exports.blrp_quests:RegisterPed(id, ped)
  quest_peds[id] = ped
  Entity(ped).state.pilot_id = id
end)

Citizen.CreateThread(function()
  local id = 'PilotSandy'
  local model = `s_m_m_pilot_01`
  local coords = vector4(1688.641, 3287.362, 41.147, 230.139)
  local dict = 'anim@heists@heist_corona@team_idles@male_a'
  local clip = 'idle'

  RequestModel(model)

  while not HasModelLoaded(model) do
    Citizen.Wait(1)
  end

  RequestAnimDict(dict)

  local start = GetGameTimer()

  while not HasAnimDictLoaded(dict) do
    Citizen.Wait(1)

    if GetGameTimer() - start > 10000 then
      exports.blrp_core:me().notify('Failed to load clip dict: ' .. dict)
      return
    end
  end

  local ped = CreatePed(4, model, coords.x, coords.y, coords.z - 0.97, coords.w, false, true)
  FreezeEntityPosition(ped, true)
  SetEntityInvincible(ped, true)
  SetBlockingOfNonTemporaryEvents(ped, true)
  TaskPlayAnim(ped, dict, clip, 8.0, 0, -1, 1, 0, 0, 0)

  exports.blrp_quests:RegisterPed(id, ped)
  quest_peds[id] = ped
  Entity(ped).state.pilot_id = id
end)

Citizen.CreateThread(function()
  local id = 'NYGallery'
  local model = `s_m_o_busker_01`
  local coords = vector4(6098.539, -5280.885, 85.601, 75.617)
  local dict = 'anim@heists@heist_corona@team_idles@male_a'
  local clip = 'idle'

  RequestModel(model)

  while not HasModelLoaded(model) do
    Citizen.Wait(1)
  end

  RequestAnimDict(dict)

  local start = GetGameTimer()

  while not HasAnimDictLoaded(dict) do
    Citizen.Wait(1)

    if GetGameTimer() - start > 10000 then
      exports.blrp_core:me().notify('Failed to load clip dict: ' .. dict)
      return
    end
  end

  local ped = CreatePed(4, model, coords.x, coords.y, coords.z - 0.97, coords.w, false, true)
  FreezeEntityPosition(ped, true)
  SetEntityInvincible(ped, true)
  SetBlockingOfNonTemporaryEvents(ped, true)
  TaskPlayAnim(ped, dict, clip, 8.0, 0, -1, 1, 0, 0, 0)

  SetPedComponentVariation(ped, 0, 1, 2, 0)
  SetPedComponentVariation(ped, 2, 2, 0, 0)
  SetPedComponentVariation(ped, 4, 1, 1, 0)
  SetPedComponentVariation(ped, 5, 1, 1, 0)
  SetPedComponentVariation(ped, 6, 2, 1, 0)
  SetPedComponentVariation(ped, 11, 2, 2, 0)

  ClearPedProp(ped, 0)

  exports.blrp_quests:RegisterPed(id, ped)
  quest_peds[id] = ped
  Entity(ped).state.quest_id = id
end)
