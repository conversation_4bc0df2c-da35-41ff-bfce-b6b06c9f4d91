local PauseSync = false
local PauseWeatherOnly = false

function ChangeWeather(weather)
  if PauseSync or PauseWeatherOnly then
    return
  end

  ClearOverrideWeather()
  ClearWeatherTypePersist()
  SetWeatherTypePersist(weather)
  SetWeatherTypeNow(weather)
  --SetWeatherTypeNowPersist(weather)
  SetWeatherTypeOvertimePersist(weather, 80.0)

  if weather == 'XMAS' then
    SetForceVehicleTrails(true)
    SetForcePedFootstepsTracks(true)
  else
    SetForceVehicleTrails(false)
    SetForcePedFootstepsTracks(false)
  end
end

exports('PauseSync', function(pause)
  PauseSync = pause

  if not PauseSync then
    ChangeWeather(GlobalState.weather_type)
  end
end)

exports('PauseWeatherOnly', function(pause)
  PauseWeatherOnly = pause
end)

Citizen.CreateThread(function()
  SetNuiFocus(false, false)

  Citizen.Wait(1500)

  ChangeWeather(GlobalState.weather_type)

  while true do
    if not PauseSync then
      NetworkOverrideClockTime(GlobalState.time_hours, GlobalState.time_minutes, 0)
    else
      NetworkOverrideClockTime(23, 0, 0)
    end

    Citizen.Wait(0)
  end
end)

AddStateBagChangeHandler('weather_type', 'global', function(_, _, value, _, _)
  ChangeWeather(value)
end)
