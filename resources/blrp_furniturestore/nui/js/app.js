const app = Vue.createApp({
  data() {
    return {
      mode: false,
      visible: false,
      search_term: '',
      active_category: null,
      categories: [],
      items: [],
      expensive_item: null,
      delete_item: null,
      delete_check_result: null,
      delete_settings: null,
      is_warehouse: false,

      recently_purchased_success: null,
      recently_purchased_model: null,
      recently_purchased_time: null,
      recently_purchased_time: null,

      recently_deleted_success: null,
      recently_deleted_model: null,
      recently_deleted_time: null,
    }
  },

  mounted() {
    window.addEventListener('message', (event) => {
      if(!event.data) return;

      if(event.data.action == 'open') {
        root.mode = event.data.mode;
        root.categories = event.data.categories;
        root.delete_settings = event.data.delete_settings;

        if(root.mode == 'store') {
          root.active_category = null;
          root.is_warehouse = event.data.is_warehouse;
        } else {
          root.active_category = root.categories[0];
          root.is_warehouse = false;
        }

        root.visible = true;
      }

      if(event.data.action == 'close') {
        root.visible = false;
      }
    });

    document.addEventListener('keydown', (event) => {
      if(event.key == 'Escape') {
        // Close any open confirmation modals first
        if(this.expensive_item) {
          this.popupDecline();
          return;
        }

        if(this.delete_item) {
          this.deleteCancel();
          return;
        }

        // If no modals open, close the UI
        this.close();
      }
    })
  },

  methods: {
    close() {
      fetch(`https://blrp_furniturestore/close`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8', },
        body: JSON.stringify({})
      });
    },

    previewClear() {
      fetch(`https://blrp_furniturestore/previewClear`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8', },
        body: JSON.stringify({})
      }).catch(error => {
        // Silently handle fetch errors (UI might be closing)
        console.debug('Preview clear fetch failed (UI likely closing):', error);
      });
    },

    selectItem(item) {
      fetch(`https://blrp_furniturestore/selectItem`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8', },
        body: JSON.stringify(item)
      }).then(r => r.json()).then(r => {
        if(root.mode == 'store') {
          root.recently_purchased_success = r.success;
          root.recently_purchased_model = item[1] + item[4] ?? '';

          root.recently_purchased_time = Date.now();

          let cooldown = 800;

          setTimeout(function() {
            if(root.recently_purchased_model == item[1] && Date.now() - root.recently_purchased_time > cooldown) {
              root.recently_purchased_model = null;
            }
          }, cooldown);
        }
      });
    },

    switchModeClicked() {
      fetch(`https://blrp_furniturestore/switchMode`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8', },
        body: JSON.stringify({})
      });
    },

    removeModeClicked() {
      fetch(`https://blrp_furniturestore/removeMode`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8', },
        body: JSON.stringify({})
      });
    },

    backClicked() {
      // If we have an active category, go back to category list
      if(this.active_category && this.mode !== 'remove') {
        this.active_category = null;

        fetch(`https://blrp_furniturestore/previewClear`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json; charset=UTF-8', },
          body: JSON.stringify({})
        }).catch(error => {
          console.debug('Preview clear fetch failed:', error);
        });
        return;
      }

      // For placement/removal modes, go back to placement menu
      fetch(`https://blrp_furniturestore/openPlacement`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8', },
        body: JSON.stringify({})
      }).catch(error => {
        console.debug('Open placement fetch failed:', error);
      });
    },

    clickedCategory(name) {
      root.categories.forEach((category) => {
        if(category.name == name) {
          root.active_category = category;
        }
      });
    },

    clickedItem(item) {
      if(this.expensive_item || this.delete_item) return;

      // Expensive item handling
      if(item[2] >= 50000 && this.mode == 'store') {
        this.expensive_item = item;
      } else {
        this.selectItem(item);
      }
    },

    hoverItem(item) {
      if(this.expensive_item || this.delete_item) return;

      fetch(`https://blrp_furniturestore/previewItem`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8', },
        body: JSON.stringify(item)
      }).catch(error => {
        // Silently handle fetch errors (UI might be closing)
        console.debug('Preview item fetch failed (UI likely closing):', error);
      });
    },

    hoverClear() {
      if(this.expensive_item || this.delete_item) return;

      this.previewClear();
    },

    popupAccept() {
      this.selectItem(this.expensive_item);
      this.previewClear();
      this.expensive_item = null;
    },

    popupDecline() {
      this.previewClear();
      this.expensive_item = null;
    },

    clickedDeleteItem(item) {
      if(this.delete_item || this.expensive_item) return;

      // Check if deletion is possible and get refund info
      fetch(`https://blrp_furniturestore/checkDeleteItem`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8', },
        body: JSON.stringify({ model: item[1], tex_var: item[3] })
      }).then(r => r.json()).then(r => {
        if (!r.can_delete) {
          // Show error message
          alert(r.reason || 'Cannot delete this item');
          return;
        }

        root.delete_check_result = r;

        // Show confirmation popup if required
        if(root.delete_settings && root.delete_settings.require_confirmation) {
          root.delete_item = item;
        } else {
          root.deleteItem(item);
        }
      });
    },

    deleteItem(item) {
      fetch(`https://blrp_furniturestore/deleteItem`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8', },
        body: JSON.stringify({ model: item[1], tex_var: item[3] })
      }).then(r => r.json()).then(r => {
        root.recently_deleted_success = r.success;
        root.recently_deleted_model = item[1] + (item[3] || '');
        root.recently_deleted_time = Date.now();

        let cooldown = 800;

        setTimeout(function() {
          if(root.recently_deleted_model == item[1] + (item[3] || '') && Date.now() - root.recently_deleted_time > cooldown) {
            root.recently_deleted_model = null;
          }
        }, cooldown);
      });
    },

    deleteConfirm() {
      this.deleteItem(this.delete_item);
      this.previewClear();
      this.delete_item = null;
      this.delete_check_result = null;
    },

    deleteCancel() {
      this.previewClear();
      this.delete_item = null;
      this.delete_check_result = null;
    },
  },

  computed: {
    visibleCategories() {
      return this.categories.filter((category) => {
        if(this.search_term == '') {
          if(category.warehouse_only && !this.is_warehouse) {
            return false;
          }

          if(category.residential_only && this.is_warehouse) {
            return false
          }

          return true;
        }

        let term_split = this.search_term.toLowerCase().split(' ');
        let category_match = (!category.warehouse_only || this.is_warehouse) && (!category.residential_only || !this.is_warehouse) && term_split.every(v => category.name.toLowerCase().includes(v));

        let item_match = false;

        category.items.forEach((item) => {
          term_split.every(v => {
            if(item[0].toLowerCase().includes(v) || item[1].toLowerCase().includes(v)) {
              item_match = true;
            }
          });
        });

        if(category.warehouse_only && !this.is_warehouse) {
          item_match = false;
        }

        if(category.residential_only && this.is_warehouse) {
          item_match = false;
        }

        return category_match || item_match;
      });
    },

    visibleItems() {
      let items = [];

      this.categories.forEach((category) => {
        if(this.active_category && this.active_category.name == category.name) {
          category.items.forEach((item) => {
            this.search_term.toLowerCase().split(' ').every(v => {
              if(item[0].toLowerCase().includes(v) || item[1].toLowerCase().includes(v)) {
                items.push(item);
              }
            });
          });
        }
      });

      return items;
    }
  }
});

const root = app.mount('#container');
