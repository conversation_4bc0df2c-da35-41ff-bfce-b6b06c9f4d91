* {
  font-family: "Roboto Mono", sans-serif;
  text-shadow: 0px 0px 6px rgb(9, 10, 26);
}

::-webkit-scrollbar {
    width: 0.3vw;
}

::-webkit-scrollbar-thumb {
    background: deepskyblue
}

#container {
  height: 100% !important;
  /* display: none; */
  justify-content: flex-end;
  align-items: flex-start;

  width: 100%;
  height: 100%;
}

#wrapper {
  width: 450px;
  height: 90%;
  max-height: 90%;
  overflow: hidden;

  padding: 10px;

  margin-top: 25px;
  margin-right: 25px;

  background: rgba(36, 38, 60, 0.8);
  box-shadow: 0px 0px 10px deepskyblue;
  border: 1px solid deepskyblue;
  border-radius: 25px;

  color: white;

  position: absolute;
  top: 0;
  right: 0;
}

.options {
  display: flex;
  flex-direction: column;

  max-height: 85%;
  overflow-y: scroll;
  overflow-x: hidden;
}

i.fa-fw {
  padding-top: 2px;
}

h2, h5 {
  margin-block: 0;
  text-align: center;
}

h2 {
  margin-top: 20px;
  margin-bottom: 5px;
}

h5 {
  margin-bottom: 20px;
}

.category-header {
  background: rgb(20, 21, 34);
  padding: 5px;

}

.category-body {
  background: rgba(0, 0, 0, 0.5);
  padding: 10px 5px 5px 5px;

  display: flex;
  justify-content: space-evenly;
}

.selector-group {
  display: flex;
  flex-direction: column;

}

.selector-options {
  display: flex;
  align-items: center;

}

.selector-title {
  text-align: center;
  font-size: 0.9rem;
}

.number-input {
  background-color: rgba(0, 0, 0, 0);
  width: 75px;
  height: 80%;
  text-align: center;
  border: 0.02vw solid deepskyblue;
  border-top: none;
  border-left: none;
  border-right:none;
  color: white;
}

.button {
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  padding: 10px 16px;
  text-align: center;
  text-decoration: none;
  font-size: 20px;
  margin: 4px 2px;
  cursor: pointer;

  display: flex;
  justify-content: space-between;
}

.button:hover {
  color: deepskyblue;
  outline: 1px solid deepskyblue;
  box-shadow: 0px 0px 4px deepskyblue;
}

input {
  border: none;
  padding: 10px 16px;
  font-size: 20px;
  margin: 4px 2px;
  text-shadow: none;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  color: white;

}

input:focus {
  outline: 1px solid deepskyblue;
  box-shadow: 0px 0px 4px deepskyblue;
}

*:not(input) {
  user-select: none;
}

#header {
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
}

/*  */

.popup {
    /* display: none; */
    position: absolute;
    font-size: 1.5vh;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 480px;
    height: 180px;

    background: rgba(36, 38, 60, 0.8);
    box-shadow: 0px 0px 10px deepskyblue;
    border: 1px solid deepskyblue;
    border-radius: 20px;

    color: white;

    text-align: center;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.popup-title {
  text-transform: uppercase;
  padding: 10px;
  font-size: 18px;
  font-weight: bold;
}

.popup-buttons {
  display: flex;
  justify-content: space-between;
  padding: 10px;
}

.popup-buttons .button {
  flex: 2;
}

/* Furniture item styling for removal mode */
.furniture-item {
  background: rgba(0, 0, 0, 0.5);
  margin: 4px 2px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.furniture-info {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  font-size: 20px;
}

.furniture-actions {
  display: flex;
  gap: 5px;
  padding: 5px;
  border-top: 1px solid rgba(135, 206, 235, 0.3);
}

.furniture-actions .button {
  flex: 1;
  margin: 0;
  padding: 8px 12px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.remove-button {
  background: rgba(0, 100, 0, 0.3);
  border: 1px solid rgba(0, 150, 0, 0.5);
}

.remove-button:hover {
  background: rgba(0, 150, 0, 0.4);
  color: lightgreen;
  outline: 1px solid lightgreen;
  box-shadow: 0px 0px 4px lightgreen;
}

.delete-button {
  background: rgba(150, 0, 0, 0.3);
  border: 1px solid rgba(200, 0, 0, 0.5);
}

.delete-button:hover {
  background: rgba(200, 0, 0, 0.4);
  color: #ff6b6b;
  outline: 1px solid #ff6b6b;
  box-shadow: 0px 0px 4px #ff6b6b;
}

.delete-confirm-button {
  background: rgba(150, 0, 0, 0.3);
  border: 1px solid rgba(200, 0, 0, 0.5);
}

.delete-confirm-button:hover {
  background: rgba(200, 0, 0, 0.4);
  color: #ff6b6b;
  outline: 1px solid #ff6b6b;
  box-shadow: 0px 0px 4px #ff6b6b;
}

.furniture-click-area {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: pointer;
}

.furniture-click-area:hover + .furniture-info,
.furniture-item:hover .furniture-info {
  color: deepskyblue;
}

.furniture-item:hover {
  outline: 1px solid deepskyblue;
  box-shadow: 0px 0px 4px deepskyblue;
}
