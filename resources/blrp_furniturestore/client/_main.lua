tFurnitureStore = {}
T.bindInstance('main', tFurnitureStore)

pFurnitureStore = P.getInstance('blrp_furniturestore', 'main')
pHousing = P.getInstance('housing', 'housing')

-- Values: store, place, remove
local current_mode = 'place'
local preview_handles = {}
local preview_timeout_handles = {}
local current_property_id = nil
local PREVIEW_TIMEOUT_SECONDS = 60

function previewClear(force)
  -- Clear any active timeout handles
  for handle_id, timeout_handle in pairs(preview_timeout_handles) do
    if timeout_handle then
      ClearTimeout(timeout_handle)
    end
  end
  preview_timeout_handles = {}

  -- Clear preview entities
  if #preview_handles > 0 then
    for _, preview_handle in pairs(preview_handles) do
      if DoesEntityExist(preview_handle) then
        if current_mode == 'remove' then
          SetEntityDrawOutline(preview_handle, false)
        else
          DeleteEntity(preview_handle)
        end
      end
    end

    preview_handles = {}
  end
end

RegisterNUICallback('switchMode', function(data, callback)
  -- Clear any previews when switching modes
  previewClear(true)

  if current_mode == 'store' then
    TriggerServerEvent('blrp_housing:server:openFurnitureMenu', current_property_id)
  else
    TriggerServerEvent('blrp_furniturestore:server:openStore', current_property_id)
  end

  callback({})
end)

RegisterNUICallback('removeMode', function(data, callback)
  -- Clear any previews when switching to remove mode
  previewClear(true)

  TriggerServerEvent('blrp_housing:server:openFurnitureRemovalMenu', current_property_id)

  callback({})
end)

RegisterNUICallback('checkDeleteItem', function(data, callback)
  local check_result = pFurnitureStore.checkDeleteFurniture({ current_property_id, data.model, data.tex_var })
  callback(check_result)
end)

RegisterNUICallback('deleteItem', function(data, callback)
  local success = pFurnitureStore.deleteFurniture({ current_property_id, data.model, data.tex_var })

  callback({ success = success })

  -- Refresh the placement menu after deletion
  if success then
    SetTimeout(500, function()
      TriggerServerEvent('blrp_housing:server:openFurnitureMenu', current_property_id)
    end)
  end
end)

RegisterNetEvent('blrp_furniturestore:client:openStore', function(property_id, is_warehouse)
  -- Clear any existing previews when opening store
  previewClear(true)

  current_property_id = property_id
  current_mode = 'store'

  SetNuiFocus(true, true)

  SendNUIMessage({
    action = 'open',
    categories = config.categories,
    mode = current_mode,
    is_warehouse = is_warehouse,
  })
end)

RegisterNUICallback('selectItem', function(data, callback)
  local success = false

  if current_mode == 'store' then
    success = pFurnitureStore.buyFurniture({ current_property_id, data[2], data[5] })
  elseif current_mode == 'place' then
    close()
    exports.housing:InitControlsPlaceFurniture(data[2], true, data[4])
  elseif current_mode == 'remove' then
    local success = pHousing.updateSingleFurniture({
      current_property_id,
      {
        id = data[3],
        placed = false,
        is_removing = true,
      }
    })

    TriggerServerEvent('blrp_housing:server:openFurnitureRemovalMenu', current_property_id)
  end

  callback({ success = success })
end)

local furniture_name_cache = {}

local function buildFurnitureNameCache()
  for _, category in ipairs(config.categories) do
    for _, item in ipairs(category.items) do
      local model = item[2]
      local display_name = item[1]
      furniture_name_cache[model] = display_name
    end
  end
end

-- Build the cache at script startup
buildFurnitureNameCache()

RegisterNetEvent('blrp_furniturestore:client:openPlacement', function(property_id, furnitures)
  -- Clear any existing previews when opening placement menu
  previewClear(true)

  current_property_id = property_id
  current_mode = 'place'

  local available_furniture = {}
  local model_furniture = {}

  for _, furniture in pairs(furnitures) do
    if not model_furniture[furniture.name_model .. (furniture.tex_var or '')] then
      local count = 0

      for _, _f in pairs(furnitures) do
        if
          _f.name_model == furniture.name_model and
          _f.tex_var == furniture.tex_var and
          not _f.placed
        then
          count = count + 1
        end
      end

      if count > 0 then
        -- Get the display name from the cache or use the one provided
        local display_name = furniture_name_cache[furniture.name_model] or furniture.name_display

        table.insert(available_furniture, {
          display_name,
          furniture.name_model,
          count,
          furniture.tex_var,
        })
      end

      model_furniture[furniture.name_model .. (furniture.tex_var or '')] = true
    end
  end

  SetNuiFocus(true, true)

  SendNUIMessage({
    action = 'open',
    categories = {
      {
        name = 'Available',
        items = available_furniture
      }
    },
    mode = current_mode,
    delete_settings = config.delete_settings,
  })
end)

RegisterNetEvent('blrp_furniturestore:client:openRemoval', function(property_id, furnitures)
  -- Clear any existing previews when opening removal menu
  previewClear(true)

  current_property_id = property_id
  current_mode = 'remove'

  local placed_furniture = {}

  for _, furniture in pairs(furnitures) do
    if furniture.placed then
      local display_name = furniture_name_cache[furniture.name_model] or furniture.name_display
      table.insert(placed_furniture, {
        display_name,
        furniture.name_model,
        furniture.id
      })
    end
  end

  SetNuiFocus(true, true)

  SendNUIMessage({
    action = 'open',
    categories = {
      {
        name = 'Placed',
        items = placed_furniture
      }
    },
    mode = current_mode,
  })
end)

function close()
  previewClear(true) -- Force clear with safety
  SetNuiFocus(false, false)
  SendNUIMessage({
    action = 'close',
  })
end

RegisterNUICallback('close', function(data, callback)
  close()

  callback({})
end)

RegisterNetEvent('blrp_furniturestore:client:closeStore', function()
  close()
end)

RegisterNUICallback('previewItem', function(data, callback)
  if current_mode == 'remove' then
    local spawned_furniture = exports.housing:GetSpawnedFurniture()

    if not spawned_furniture then
      return
    end

    for _, f in pairs(exports.housing:GetSpawnedFurniture() or {}) do
      if f.id == data[3] then
        table.insert(preview_handles, f.handle)

        SetEntityDrawOutline(f.handle, true)
        SetEntityDrawOutlineColor(255, 0, 255, 5)

        -- Set timeout for outline removal (safety)
        local timeout_handle = SetTimeout(PREVIEW_TIMEOUT_SECONDS * 1000, function()
          if DoesEntityExist(f.handle) then
            SetEntityDrawOutline(f.handle, false)
          end
          -- Remove from preview handles
          for i, handle in ipairs(preview_handles) do
            if handle == f.handle then
              table.remove(preview_handles, i)
              break
            end
          end
          preview_timeout_handles[f.handle] = nil
        end)
        preview_timeout_handles[f.handle] = timeout_handle

        break
      end
    end
  else
    local model = GetHashKey(data[2])

    while not HasModelLoaded(model) do
      RequestModel(model)
      Wait(0)
    end

    previewClear(true)

    local player_coords = GetEntityCoords(PlayerPedId())
    local spawn_coords = player_coords + (2.0 * GetEntityForwardVector(PlayerPedId()))

    local spawned_object = CreateObject(model, GetEntityCoords(PlayerPedId()) - vector3(0, 0, 25), false)

    table.insert(preview_handles, spawned_object)

    if current_mode == 'store' then
      SetEntityHeading(spawned_object, data[4] or GetEntityHeading(PlayerPedId()))
    end

    -- Tint palette (texture variation)
    local tex_var = (current_mode == 'store' and data[5]) or (current_mode == 'place' and data[4]) or nil

    if tex_var then
      SetObjectTextureVariation(spawned_object, tex_var)
    end

    SetEntityCoords(spawned_object, spawn_coords)
    SetEntityCompletelyDisableCollision(spawned_object, false, true)
    SetEntityDrawOutline(spawned_object, true)
    SetEntityDrawOutlineColor(255, 0, 255, 5)

    -- Set timeout for preview object deletion (safety)
    local timeout_handle = SetTimeout(PREVIEW_TIMEOUT_SECONDS * 1000, function()
      if DoesEntityExist(spawned_object) and GetEntityModel(spawned_object) == model then
        DeleteEntity(spawned_object)
      end
      -- Remove from preview handles
      for i, handle in ipairs(preview_handles) do
        if handle == spawned_object then
          table.remove(preview_handles, i)
          break
        end
      end
      preview_timeout_handles[spawned_object] = nil
    end)
    preview_timeout_handles[spawned_object] = timeout_handle
  end

  callback({})
end)

RegisterNUICallback('previewClear', function(data, callback)
  previewClear(true)
  callback({})
end)

RegisterNUICallback('openPlacement', function(data, callback)
  -- Clear previews and reopen the placement menu
  previewClear(true)
  TriggerServerEvent('blrp_housing:server:openFurnitureMenu', current_property_id)
  callback({})
end)

-- Safety cleanup when resource stops
AddEventHandler('onResourceStop', function(resourceName)
  if GetCurrentResourceName() == resourceName then
    previewClear(true)
  end
end)

-- Safety cleanup when player disconnects/leaves
AddEventHandler('onClientResourceStop', function(resourceName)
  if GetCurrentResourceName() == resourceName then
    previewClear(true)
  end
end)

-- Periodic safety check to clean up orphaned previews
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(30000) -- Check every 30 seconds

    -- Clean up any preview handles that no longer exist
    local cleaned_handles = {}
    for i, handle in ipairs(preview_handles) do
      if DoesEntityExist(handle) then
        table.insert(cleaned_handles, handle)
      else
        -- Clear timeout if entity no longer exists
        if preview_timeout_handles[handle] then
          ClearTimeout(preview_timeout_handles[handle])
          preview_timeout_handles[handle] = nil
        end
      end
    end
    preview_handles = cleaned_handles
  end
end)

-- Safety mechanism to detect when NUI focus is lost (ESC pressed)
local nui_focus_active = false
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(500) -- Check every 500ms

    local has_focus = HasNuiFocus()

    -- If we had focus but lost it, and we have preview items, clear them
    if nui_focus_active and not has_focus and #preview_handles > 0 then
      print("[FurnitureStore] NUI focus lost - clearing preview items for safety")
      previewClear(true)
    end

    nui_focus_active = has_focus
  end
end)

