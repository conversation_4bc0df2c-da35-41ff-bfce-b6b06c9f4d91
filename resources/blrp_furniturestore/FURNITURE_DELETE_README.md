# Furniture Delete Functionality

This document explains the new furniture deletion feature added to the furniture store system.

## Overview

The furniture delete functionality allows players to permanently delete **unplaced** furniture items from their properties, with optional refunds based on configurable settings.

## Configuration

The delete functionality is controlled by settings in `shared/config.lua`:

```lua
config = {
  -- Furniture deletion settings
  delete_settings = {
    enabled = true,              -- Enable/disable delete functionality
    refund_enabled = true,       -- Enable/disable refunds when deleting furniture
    refund_percentage = 50,      -- Refund percentage (0-100) of current item price
    require_confirmation = true, -- Require confirmation popup before deletion
  },
  -- ... rest of config
}
```

### Configuration Options

- **enabled**: Set to `true` to enable delete buttons, `false` to disable completely
- **refund_enabled**: Set to `true` to give refunds when deleting, `false` for no refunds
- **refund_percentage**: Percentage of current item price to refund (0-100)
- **require_confirmation**: Set to `true` to show confirmation popup, `false` for immediate deletion

## How It Works

### For Players

1. Open the furniture placement menu (shows unplaced furniture)
2. You'll see two buttons for each unplaced furniture item:
   - **Place**: Place the furniture in your property (existing functionality)
   - **Delete**: Permanently delete the furniture (new functionality)
3. Click the red "Delete" button to permanently delete an unplaced item
4. If confirmation is enabled, you'll see a popup explaining the action and refund amount
5. Confirm to permanently delete the item and receive any applicable refund

**Important**: You can only delete furniture that is NOT currently placed. To delete placed furniture, you must first remove it from placement using the removal menu, then delete it from the placement menu.

### For Administrators

The delete functionality respects all existing permission checks:
- Property owners can delete furniture from their properties
- Business members with warehouse access can delete from business properties
- Dynasty 8 realtors can delete from any property
- House designers can delete from properties with designer permissions

### Safety Features

- **Unplaced Only**: Can only delete furniture that is not currently placed
- **Permission Checks**: Same permission system as existing furniture management
- **Confirmation Popups**: Optional confirmation to prevent accidental deletions
- **Comprehensive Logging**: All deletions are logged with full details
- **Price Validation**: Items must exist in current config to calculate refunds

## Technical Details

### Database Changes

The delete functionality removes records from the `core_character_furniture` table permanently. This only affects unplaced furniture (where `placed = 0`).

### Refund Calculation

Refunds are calculated based on the current price in the config file. The formula is:

```
refund_amount = current_price * (refund_percentage / 100)
```

### UI Changes

- Delete buttons appear in the furniture placement menu (for unplaced items)
- Color-coded buttons (blue for place, red for delete)
- Confirmation popup with refund information
- Visual feedback for successful/failed operations

## Workflow

1. **Purchase furniture** → Item appears in placement menu as unplaced
2. **Place furniture** → Item moves to removal menu, no longer deletable
3. **Remove furniture** → Item returns to placement menu, now deletable again
4. **Delete furniture** → Item permanently removed from database

## Backwards Compatibility

The delete functionality is fully backwards compatible:
- Existing placement functionality unchanged
- Delete buttons only appear when enabled in config
- Default configuration has delete enabled
- No database schema changes required

## Troubleshooting

### Delete Button Not Showing
- Check that `delete_settings.enabled = true` in config
- Verify you're in the placement menu (not removal menu)
- Ensure the furniture is unplaced

### Cannot Delete Item
- Make sure the furniture is not currently placed
- If placed, use removal menu first, then try deleting from placement menu

### No Refund Given
- Check that `delete_settings.refund_enabled = true`
- Check that `delete_settings.refund_percentage > 0`
- Verify the item exists in the current config file
