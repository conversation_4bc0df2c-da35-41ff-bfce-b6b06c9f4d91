pFurnitureStore = {}
P.bindInstance('main', pFurnitureStore)

tFurnitureStore = T.getInstance('blrp_furniturestore', 'main')

-- Add known housing items to anticheat entity allowlist
Citizen.CreateThread(function()
  Citizen.Wait(2000)

  for _, category in pairs(config.categories) do
    for __, item in pairs(category.items) do
      exports.blrp_core:ACAddAllowedModel(item[2])
      --exports.blrp_core:ACAddAllowedModelNew(item[2])
    end
  end
end)

-- Check for permission to buy furniture
-- if so, return the computed target and if the target is a business
function furnitureStorePermissionCheck(house_data, character)
  local character_id = tonumber(character.get('id'))

  -- Owner, co-owner, realtor
  if
    not house_data.business_id and
    (
      character_id == tonumber(house_data.owner_character_id) or
      character_id == tonumber(house_data.coowner_character_id) or
      character.hasGroup('Dynasty 8')
    )
  then
    return house_data.owner_character_id, false
  end

  -- Warehouse
  if
    house_data.business_id and
    (
      exports.blrp_core:CheckUserCanAccess(character, house_data.business_id, 'warehouse') or
      character.hasGroup('Dynasty 8')
    )
  then
    return house_data.business_id, true
  end

  -- Allowed designer on residential property
  if
    house_data.flags & PRF_ALLOW_DESIGNER ~= 0 and
    character.isHouseDesigner()
  then
    return house_data.owner_character_id, false
  end

  return false, false
end

RegisterNetEvent('blrp_furniturestore:server:openStore', function(property_id)
  local character = exports.blrp_core:character(source)
  local house_data = exports.blrp_core:GetHouse(property_id)

  local has_permission, warehouse = furnitureStorePermissionCheck(house_data, character)

  if not has_permission then
    character.notify('You do not have permission to buy furniture for this property')
    return
  end

  character.client('blrp_furniturestore:client:openStore', property_id, (house_data.business_id ~= nil))
end)

pFurnitureStore.buyFurniture = function(property_id, model, tex_var)
  local character = exports.blrp_core:character(source)
  local house_data = exports.blrp_core:GetHouse(property_id)
  local owner_character_id = tonumber(house_data.owner_character_id)
  local business_id = tonumber(house_data.business_id)
  local definition = nil
  local warehouse_only = false
  local residential_only = false

  for _, category in pairs(config.categories) do
    for __, item in pairs(category.items) do
      if item[2] == model and item[5] == tex_var then
        warehouse_only = category.warehouse_only
        residential_only = category.residential_only
        definition = item
        break
      end
    end

    if definition then
      break
    end
  end

  if not definition then
    character.notify('Error purchasing furniture, definition not found')
    return false
  end

  if residential_only and house_data.business_id then
    return false
  end

  if warehouse_only and not house_data.business_id then
    return false
  end

  -- Figure out target for purchase
  local furniture_target, warehouse = furnitureStorePermissionCheck(house_data, character)

  if not furniture_target then
    return false
  end

  -- Calculate price. TODO: Realtor discount?
  local price = definition[3]

  -- Take money
  if not character.tryPayment(price, false) then
    return false
  end

  -- Insert record
  local bindings = { furniture_target, definition[2], definition[1], definition[5] }
  local insert_query = business_id
    and 'INSERT INTO core_character_furniture (business_id, name_model, name_display, tex_var) VALUES (?, ?, ?, ?)'
    or 'INSERT INTO core_character_furniture (character_id, name_model, name_display, tex_var) VALUES (?, ?, ?, ?)'

  local insert_id = MySQL.insert.await(insert_query, bindings) -- Get last inserted ID

  -- Log the purchase
  character.log('FURNITURE', 'Purchased furniture', {
    model = definition[2],
    name = definition[1],
    price = price,
    target_id = furniture_target,
    target_business = business_id or nil,
    furniture_id = insert_id,
    -- discount = (gets_discount and target_character_id ~= character.get('id') and Config.DecoratorDiscount) or nil,
  })
  TriggerClientEvent('InteractSound_CL:PlayOnOne', character.source,'kaching', 0.03)
  return true
end

pFurnitureStore.deleteFurniture = function(property_id, furniture_id)
  local character = exports.blrp_core:character(source)

  -- Check if delete functionality is enabled
  if not config.delete_settings.enabled then
    character.notify('Furniture deletion is currently disabled')
    return false
  end

  local house_data = exports.blrp_core:GetHouse(property_id)

  -- Check permissions
  local has_permission, warehouse = furnitureStorePermissionCheck(house_data, character)
  if not has_permission then
    character.notify('You do not have permission to delete furniture from this property')
    return false
  end

  -- Get furniture data from database
  local furniture_data = MySQL.single.await('SELECT * FROM core_character_furniture WHERE id = ?', { furniture_id })
  if not furniture_data then
    character.notify('Furniture item not found')
    return false
  end

  -- Check if it's a storage item with contents (prevent deletion)
  --local model_hash = GetHashKey(furniture_data.name_model)
  --if exports.housing:canRemoveFurniture and not exports.housing:canRemoveFurniture(model_hash, furniture_id) then
  --  character.notify('Cannot delete storage furniture that contains items')
  --  return false
  --end

  local refund_amount = 0

  -- Calculate refund if enabled
  if config.delete_settings.refund_enabled and config.delete_settings.refund_percentage > 0 then
    -- Find the item definition in config to get current price
    local definition = nil
    for _, category in pairs(config.categories) do
      for __, item in pairs(category.items) do
        if item[2] == furniture_data.name_model and (item[5] or nil) == (furniture_data.tex_var or nil) then
          definition = item
          break
        end
      end
      if definition then
        break
      end
    end

    if definition then
      local base_price = definition[3]
      refund_amount = math.floor(base_price * (config.delete_settings.refund_percentage / 100))
    else
      character.notify('Cannot calculate refund - item not found in current catalog')
      return false
    end
  end

  -- Delete furniture from database
  local affected_rows = MySQL.update.await('DELETE FROM core_character_furniture WHERE id = ? LIMIT 1', { furniture_id })

  if affected_rows == 0 then
    character.notify('Failed to delete furniture item')
    return false
  end

  -- Give refund if applicable
  if refund_amount > 0 then
    character.giveBankMoney(refund_amount, false)
    character.notify('Furniture deleted. Refund: $' .. refund_amount)
  else
    character.notify('Furniture deleted successfully')
  end

  -- Log the deletion
  character.log('FURNITURE', 'Deleted furniture', {
    furniture_id = furniture_id,
    model = furniture_data.name_model,
    name = furniture_data.name_display,
    refund_amount = refund_amount,
    property_id = property_id,
  })

  return true
end
