config = {
  -- Furniture deletion settings
  delete_settings = {
    enabled = true,              -- Enable/disable delete functionality
    refund_enabled = false,       -- Enable/disable refunds when deleting furniture
    refund_percentage = 50,      -- Refund percentage (0-100) of current item price
    require_confirmation = true, -- Require confirmation popup before deletion
  },

  categories = {
    {
      name = 'Warehouse Tools',
      warehouse_only = true,
      items = {
        { 'Drill', 'imp_prop_drill_01a', 500 },
        { 'Grinder', 'imp_prop_grinder_01a', 500 },
        { 'Impact Driver', 'imp_prop_impact_driver_01a', 500 },
        { 'Torque Wrench', 'imp_prop_torque_wrench_01a', 500 },
        { 'Socket Set 1', 'imp_prop_socket_set_01a', 500 },
        { 'Socket Set 2', 'imp_prop_socket_set_01b', 500 },
        { 'Car Creeper', 'gr_prop_gr_carcreeper', 500 },
        { 'Bench Grinder', 'imp_prop_bench_grinder_01a', 500 },
        { 'Air Compressor', 'imp_prop_air_compressor_01a', 500 },
        { 'Vice', 'imp_prop_bench_vice_01a', 500 },
        { 'Engine Hoist', 'imp_prop_engine_hoist_02a', 500 },
        -- TODO: hammer, screwdriver, stuff
      },
    },

    {
      name = 'Warehouse Storage',
      warehouse_only = true,
      items = {
        { 'Storage Shelves (1500kg)', 'prop_bl_wh_shelf_01', 250000 },
        { 'Sea Can (2000kg)', 'prop_container_05a', 325000 },
        { 'Smuggler Crate (5000kg)', 'sm_prop_smug_crate_l_fake', 500000 },
        { 'Tool Drawer 1 (5kg)', 'imp_prop_tool_draw_01a', 2100 },
        { 'Tool Drawer 2 (10kg)', 'imp_prop_tool_draw_01b', 3200 },
        { 'Tool Drawer 3 (15kg)', 'imp_prop_tool_draw_01c', 4500 },
        { 'Tool Drawer 4 (20kg)', 'imp_prop_tool_draw_01d', 6300 },
        { 'Tool Drawer 5 (25kg)', 'imp_prop_tool_draw_01e', 6500 },
      }
    },

    {
      name = 'Warehouse Only',
      warehouse_only = true,
      items = {
        { 'Business Management Laptop', 'xm_prop_x17_laptop_avon', 10000 },
        { 'Warehouse Shelf 1', 'v_ret_fh_shelf_01', 2200 },
        { 'Warehouse Shelf 2', 'v_ret_fh_shelf_02', 2200 },
        { 'Warehouse Shelf 3', 'v_ret_fh_shelf_03', 2200 },
        { 'Warehouse Shelf 4', 'v_ret_fh_shelf_04', 2200 },
        { 'Stairs', 'prop_air_stair_02', 7500, },
      }
    },

    {
      name = 'Residential Only',
      residential_only = true,
      items = {
        { 'Residential Management Laptop', 'xm_prop_x17_laptop_mrsr', 2500 },
      }
    },

    {
      name = 'Christmas 🎄',

      items = {
        {'Christmas Candy Cane Jar', 'pata_candyjar', 2000},
        {'Christmas Decor 1', 'pata_christmas1', 1500},
        {'Christmas Decor 2', 'pata_christmas2', 1500},
        {'Christmas Decor 3', 'pata_christmas6', 3500},
        {'Christmas Decor 4', 'pata_christmas8', 2000},
        {'Christmas Decor 5', 'pata_christmas9', 3000},
        {'Christmas Decor 6', 'pata_lightdeco', 3000},
        {'Christmas Decor 7', 'pata_lightdeco2', 3000},
        {'Christmas Decor 8', 'pata_lightdeco3', 3000},
        {'Christmas Decor 9', 'pata_lightdeco4', 3000},
        {'Christmas Decor 10', 'pata_renne', 3000},
        {'Christmas Tree Rug', 'pata_christmas3', 2500},
        {'Christmas Garland Greenery w/ Bell', 'pata_christmas4', 3500},
        {'Christmas Stocking', 'pata_christmas5', 3000},
        {'Christmas Bed', 'pata_christmasbed', 7500},
        {'Christmas Sofa', 'pata_sofamas', 7500},
        {'Christmas Chair', 'pata_christmaschair', 3500},
        {'Christmas Rug', 'pata_christmasrug', 1500},
        {'Christmas Stocking(s)', 'pata_christmassocks', 7500},
        {'Christmas Star Decor', 'pata_star', 5000},
        {'Christmas Table', 'pata_christmastable', 3500},
        {'Christmas Tree (Storage)', 'pata_christmastree', 25000},
        {'Christmas Tree 2', 'pata_christmastree2', 3500},
        {'Christmas Tree 3', 'pata_christmastree3', 3500},
        {'Christmas Gold Wreath', 'pata_crown', 3500},
        {'Christmas Wreath', 'pata_crown2', 2500},
        {'Christmas Present', 'pata_present', 5000},
        {'Christmas Deer', 'pata_renne2', 20000},
        {'Christmas Snowman', 'pata_snowman', 15000},

      }
    },


    {
      name = 'Wall Art',

      items = {
        { 'Cat Flower', 'prop_bl_houseposter_01_s', 1500 },
        { 'Cat Rainbow', 'prop_bl_houseposter_02_s', 1500 },
        { 'Raton Canyon Print', 'prop_bl_houseposter_03_l', 5000 },
        { 'Mirror Park Print', 'prop_bl_houseposter_04_l', 5000 },
        { 'Los Santos Print', 'prop_bl_houseposter_05_l', 5000 },
        { 'Liberator Print', 'prop_bl_houseposter_06_l', 2500 },
        { 'Del Perro Print', 'prop_bl_houseposter_07_l', 6500 },
        { 'Death Car Print', 'prop_bl_houseposter_08_l', 2500 },
        { 'Watercolor Wolf', 'prop_bl_houseposter_09_p', 4000 },
        { 'Watercolor Deer', 'prop_bl_houseposter_10_p', 4000 },
        { 'Watercolor Bear', 'prop_bl_houseposter_11_p', 4000 },
        { 'Vaporwave Icosahedron', 'prop_bl_houseposter_12_s', 2000 },
        { 'Vaporwave Heart', 'prop_bl_houseposter_13_s', 2000 },
        { 'Collage Floral', 'prop_bl_houseposter_14_l', 8500 },
        { 'Collage Grasshopper', 'prop_bl_houseposter_24_s', 6500 },
        { 'Collage Beetle', 'prop_bl_houseposter_25_s', 6500 },
        { 'Collage Woman #1', 'prop_bl_houseposter_18_p', 5000 },
        { 'Collage Woman #2', 'prop_bl_houseposter_19_p', 5000 },
        { 'Block Print Red', 'prop_bl_houseposter_15_l', 8500 },
        { 'Block Print Purple', 'prop_bl_houseposter_16_l', 8500 },
        { 'Block Print Orange', 'prop_bl_houseposter_17_l', 8500 },
        { 'Graffiti Skull', 'prop_bl_houseposter_26_p', 3500 },
        { 'Graffiti Bunny', 'prop_bl_houseposter_27_p', 3500 },
        { 'Graffiti Baby Bear', 'prop_bl_houseposter_28_p', 3500 },
        { 'Kawaii Peach', 'prop_bl_houseposter_29_p', 2500 },
        { 'Kawaii Froggie', 'prop_bl_houseposter_30_p', 2500 },
        { 'Kawaii Bears', 'prop_bl_houseposter_31_p', 2500 },
        { 'Cannabis Cutie', 'prop_bl_houseposter_38_p', 3500 },
        { 'Shrooms', 'prop_bl_houseposter_32_s', 12500 },
        { 'Hibiscus', 'prop_bl_houseposter_33_s', 6500 },
        { 'Fuego', 'prop_bl_houseposter_34_s', 3500 },
        { 'Flores', 'prop_bl_houseposter_35_s', 2500 },
        { 'Big Dash of Saucy Mirth', 'prop_bl_houseposter_36_p', 20000 },
        { 'Travel Grapeseed', 'prop_bl_houseposter_37_p', 2500 },
        { 'Travel Paleto', 'prop_bl_houseposter_39_p', 2500 },
        { 'Travel Sandy Shores', 'prop_bl_houseposter_40_p', 2500 },
        { 'Stanced', 'prop_bl_houseposter_42_l', 1200 },
        { 'D.A.B.S.', 'prop_bl_houseposter_43_p', 1200 },
        { 'Chains and Cash and Cars', 'prop_bl_houseposter_44_l', 1200 },
        { "Benny's Poster", 'prop_bl_houseposter_45_p', 1200 },
      },
    },

    {
      name = 'Wall Art (Tintable)',

      items = {

      },
    },

    {
      name = 'Neon Signs (Tintable)',

      items = {
        { 'Stan The Arcade Stand', 'bzzz_neons_stand', 250 },
        { 'Arcade Stand That Stands', 'bzzz_neons_stand_large', 500 },

        { 'Arcade Sign (Blue)', 'bzzz_neons_arcade_a', 7500 },
        { 'Arcade Sign (Red)', 'bzzz_neons_arcade_b', 7500 },
        { 'Arcade Sign (Green)', 'bzzz_neons_arcade_c', 7500 },
        { 'Arcade Sign (Yellow)', 'bzzz_neons_arcade_d', 7500 },
        { 'Arcade Sign (Purple)', 'bzzz_neons_arcade_e', 7500 },

        { 'Bar Sign (Blue)', 'bzzz_neons_bar_a', 7500 },
        { 'Bar Sign (Red)', 'bzzz_neons_bar_b', 7500 },
        { 'Bar Sign (Green)', 'bzzz_neons_bar_c', 7500 },
        { 'Bar Sign (Yellow)', 'bzzz_neons_bar_d', 7500 },
        { 'Bar Sign (Purple)', 'bzzz_neons_bar_e', 7500 },

        { 'Beer Sign (Blue)', 'bzzz_neons_beer_a', 7500 },
        { 'Beer Sign (Red)', 'bzzz_neons_beer_b', 7500 },
        { 'Beer Sign (Green)', 'bzzz_neons_beer_c', 7500 },
        { 'Beer Sign (Yellow)', 'bzzz_neons_beer_d', 7500 },
        { 'Beer Sign (Purple)', 'bzzz_neons_beer_e', 7500 },

        { 'Body Sign (Blue)', 'bzzz_neons_body_a', 7500 },
        { 'Body Sign (Red)', 'bzzz_neons_body_b', 7500 },
        { 'Body Sign (Green)', 'bzzz_neons_body_c', 7500 },
        { 'Body Sign (Yellow)', 'bzzz_neons_body_d', 7500 },
        { 'Body Sign (Purple)', 'bzzz_neons_body_e', 7500 },

        { 'Car Sign (Blue)', 'bzzz_neons_car_a', 7500 },
        { 'Car Sign (Red)', 'bzzz_neons_car_b', 7500 },
        { 'Car Sign (Green)', 'bzzz_neons_car_c', 7500 },
        { 'Car Sign (Yellow)', 'bzzz_neons_car_d', 7500 },
        { 'Car Sign (Purple)', 'bzzz_neons_car_e', 7500 },

        { 'Car Engine Sign (Blue)', 'bzzz_neons_car_engine_a', 7500 },
        { 'Car Engine Sign (Red)', 'bzzz_neons_car_engine_b', 7500 },
        { 'Car Engine Sign (Green)', 'bzzz_neons_car_engine_c', 7500 },
        { 'Car Engine Sign (Yellow)', 'bzzz_neons_car_engine_d', 7500 },
        { 'Car Engine Sign (Purple)', 'bzzz_neons_car_engine_e', 7500 },

        { 'Car Rent Sign (Blue)', 'bzzz_neons_car_rent_a', 7500 },
        { 'Car Rent Sign (Red)', 'bzzz_neons_car_rent_b', 7500 },
        { 'Car Rent Sign (Green)', 'bzzz_neons_car_rent_c', 7500 },
        { 'Car Rent Sign (Yellow)', 'bzzz_neons_car_rent_d', 7500 },
        { 'Car Rent Sign (Purple)', 'bzzz_neons_car_rent_e', 7500 },

        { 'Clothes Sign (Blue)', 'bzzz_neons_cloth_both_a', 7500 },
        { 'Clothes Sign (Red)', 'bzzz_neons_cloth_both_b', 7500 },
        { 'Clothes Sign (Green)', 'bzzz_neons_cloth_both_c', 7500 },
        { 'Clothes Sign (Yellow)', 'bzzz_neons_cloth_both_d', 7500 },
        { 'Clothes Sign (Purple)', 'bzzz_neons_cloth_both_e', 7500 },

        { 'T-shirt Sign (Blue)', 'bzzz_neons_cloth_tshirt_a', 7500 },
        { 'T-shirt Sign (Red)', 'bzzz_neons_cloth_tshirt_b', 7500 },
        { 'T-shirt Sign (Green)', 'bzzz_neons_cloth_tshirt_c', 7500 },
        { 'T-shirt Sign (Yellow)', 'bzzz_neons_cloth_tshirt_d', 7500 },
        { 'T-shirt Sign (Purple)', 'bzzz_neons_cloth_tshirt_e', 7500 },

        { 'Dress Sign (Blue)', 'bzzz_neons_cloth_woman_a', 7500 },
        { 'Dress Sign (Red)', 'bzzz_neons_cloth_woman_b', 7500 },
        { 'Dress Sign (Green)', 'bzzz_neons_cloth_woman_c', 7500 },
        { 'Dress Sign (Yellow)', 'bzzz_neons_cloth_woman_d', 7500 },
        { 'Dress Sign (Purple)', 'bzzz_neons_cloth_woman_e', 7500 },

        { 'Club Sign (Blue)', 'bzzz_neons_club_a', 7500 },
        { 'Club Sign (Red)', 'bzzz_neons_club_b', 7500 },
        { 'Club Sign (Green)', 'bzzz_neons_club_c', 7500 },
        { 'Club Sign (Yellow)', 'bzzz_neons_club_d', 7500 },
        { 'Club Sign (Purple)', 'bzzz_neons_club_e', 7500 },

        { 'Drugs Sign (Blue)', 'bzzz_neons_drugs_a', 7500 },
        { 'Drugs Sign (Red)', 'bzzz_neons_drugs_b', 7500 },
        { 'Drugs Sign (Green)', 'bzzz_neons_drugs_c', 7500 },
        { 'Drugs Sign (Yellow)', 'bzzz_neons_drugs_d', 7500 },
        { 'Drugs Sign (Purple)', 'bzzz_neons_drugs_e', 7500 },

        { 'Gas Sign (Blue)', 'bzzz_neons_gas_a', 7500 },
        { 'Gas Sign (Red)', 'bzzz_neons_gas_b', 7500 },
        { 'Gas Sign (Green)', 'bzzz_neons_gas_c', 7500 },
        { 'Gas Sign (Yellow)', 'bzzz_neons_gas_d', 7500 },
        { 'Gas Sign (Purple)', 'bzzz_neons_gas_e', 7500 },

        { 'Gas 2 Sign (Blue)', 'bzzz_neons_gas2_a', 7500 },
        { 'Gas 2 Sign (Red)', 'bzzz_neons_gas2_b', 7500 },
        { 'Gas 2 Sign (Green)', 'bzzz_neons_gas2_c', 7500 },
        { 'Gas 2 Sign (Yellow)', 'bzzz_neons_gas2_d', 7500 },
        { 'Gas 2 Sign (Purple)', 'bzzz_neons_gas2_e', 7500 },

        { 'Guitar Sign (Blue)', 'bzzz_neons_guitare_a', 7500 },
        { 'Guitar Sign (Red)', 'bzzz_neons_guitare_b', 7500 },
        { 'Guitar Sign (Green)', 'bzzz_neons_guitare_c', 7500 },
        { 'Guitar Sign (Yellow)', 'bzzz_neons_guitare_d', 7500 },
        { 'Guitar Sign (Purple)', 'bzzz_neons_guitare_e', 7500 },

        { 'Gun Sign (Blue)', 'bzzz_neons_gun_a', 7500 },
        { 'Gun Sign (Red)', 'bzzz_neons_gun_b', 7500 },
        { 'Gun Sign (Green)', 'bzzz_neons_gun_c', 7500 },
        { 'Gun Sign (Yellow)', 'bzzz_neons_gun_d', 7500 },
        { 'Gun Sign (Purple)', 'bzzz_neons_gun_e', 7500 },

        { 'Gun 2 Sign (Blue)', 'bzzz_neons_gun2_a', 7500 },
        { 'Gun 2 Sign (Red)', 'bzzz_neons_gun2_b', 7500 },
        { 'Gun 2 Sign (Green)', 'bzzz_neons_gun2_c', 7500 },
        { 'Gun 2 Sign (Yellow)', 'bzzz_neons_gun2_d', 7500 },
        { 'Gun 2 Sign (Purple)', 'bzzz_neons_gun2_e', 7500 },

        { 'Mechanic Sign (Blue)', 'bzzz_neons_mechanic_a', 7500 },
        { 'Mechanic Sign (Red)', 'bzzz_neons_mechanic_b', 7500 },
        { 'Mechanic Sign (Green)', 'bzzz_neons_mechanic_c', 7500 },
        { 'Mechanic Sign (Yellow)', 'bzzz_neons_mechanic_d', 7500 },
        { 'Mechanic Sign (Purple)', 'bzzz_neons_mechanic_e', 7500 },

        { 'Music Sign (Blue)', 'bzzz_neons_music_a', 7500 },
        { 'Music Sign (Red)', 'bzzz_neons_music_b', 7500 },
        { 'Music Sign (Green)', 'bzzz_neons_music_c', 7500 },
        { 'Music Sign (Yellow)', 'bzzz_neons_music_d', 7500 },
        { 'Music Sign (Purple)', 'bzzz_neons_music_e', 7500 },

        { 'Pipe Sign (Blue)', 'bzzz_neons_pipe_a', 7500 },
        { 'Pipe Sign (Red)', 'bzzz_neons_pipe_b', 7500 },
        { 'Pipe Sign (Green)', 'bzzz_neons_pipe_c', 7500 },
        { 'Pipe Sign (Yellow)', 'bzzz_neons_pipe_d', 7500 },
        { 'Pipe Sign (Purple)', 'bzzz_neons_pipe_e', 7500 },

        { 'Pizza Sign (Blue)', 'bzzz_neons_pizza_a', 7500 },
        { 'Pizza Sign (Red)', 'bzzz_neons_pizza_b', 7500 },
        { 'Pizza Sign (Green)', 'bzzz_neons_pizza_c', 7500 },
        { 'Pizza Sign (Yellow)', 'bzzz_neons_pizza_d', 7500 },
        { 'Pizza Sign (Purple)', 'bzzz_neons_pizza_e', 7500 },

        { 'Poker Sign (Blue)', 'bzzz_neons_poker_a', 7500 },
        { 'Poker Sign (Red)', 'bzzz_neons_poker_b', 7500 },
        { 'Poker Sign (Green)', 'bzzz_neons_poker_c', 7500 },
        { 'Poker Sign (Yellow)', 'bzzz_neons_poker_d', 7500 },
        { 'Poker Sign (Purple)', 'bzzz_neons_poker_e', 7500 },

        { 'Race Sign (Blue)', 'bzzz_neons_race_a', 7500 },
        { 'Race Sign (Red)', 'bzzz_neons_race_b', 7500 },
        { 'Race Sign (Green)', 'bzzz_neons_race_c', 7500 },
        { 'Race Sign (Yellow)', 'bzzz_neons_race_d', 7500 },
        { 'Race Sign (Purple)', 'bzzz_neons_race_e', 7500 },

        { 'Race 2 Sign (Blue)', 'bzzz_neons_race2_a', 7500 },
        { 'Race 2 Sign (Red)', 'bzzz_neons_race2_b', 7500 },
        { 'Race 2 Sign (Green)', 'bzzz_neons_race2_c', 7500 },
        { 'Race 2 Sign (Yellow)', 'bzzz_neons_race2_d', 7500 },
        { 'Race 2 Sign (Purple)', 'bzzz_neons_race2_e', 7500 },

        { 'Shisha Sign (Blue)', 'bzzz_neons_shisha_a', 7500 },
        { 'Shisha Sign (Red)', 'bzzz_neons_shisha_b', 7500 },
        { 'Shisha Sign (Green)', 'bzzz_neons_shisha_c', 7500 },
        { 'Shisha Sign (Yellow)', 'bzzz_neons_shisha_d', 7500 },
        { 'Shisha Sign (Purple)', 'bzzz_neons_shisha_e', 7500 },

        { 'Shop Sign (Blue)', 'bzzz_neons_shop_a', 7500 },
        { 'Shop Sign (Red)', 'bzzz_neons_shop_b', 7500 },
        { 'Shop Sign (Green)', 'bzzz_neons_shop_c', 7500 },
        { 'Shop Sign (Yellow)', 'bzzz_neons_shop_d', 7500 },
        { 'Shop Sign (Purple)', 'bzzz_neons_shop_e', 7500 },

        { 'Smoke Sign (Blue)', 'bzzz_neons_smoke_a', 7500 },
        { 'Smoke Sign (Red)', 'bzzz_neons_smoke_b', 7500 },
        { 'Smoke Sign (Green)', 'bzzz_neons_smoke_c', 7500 },
        { 'Smoke Sign (Yellow)', 'bzzz_neons_smoke_d', 7500 },
        { 'Smoke Sign (Purple)', 'bzzz_neons_smoke_e', 7500 },

        { 'Taxi Sign (Blue)', 'bzzz_neons_taxi_a', 7500 },
        { 'Taxi Sign (Red)', 'bzzz_neons_taxi_b', 7500 },
        { 'Taxi Sign (Green)', 'bzzz_neons_taxi_c', 7500 },
        { 'Taxi Sign (Yellow)', 'bzzz_neons_taxi_d', 7500 },
        { 'Taxi Sign (Purple)', 'bzzz_neons_taxi_e', 7500 },

        { 'Weed Sign (Blue)', 'bzzz_neons_weed_a', 7500 },
        { 'Weed Sign (Red)', 'bzzz_neons_weed_b', 7500 },
        { 'Weed Sign (Green)', 'bzzz_neons_weed_c', 7500 },
        { 'Weed Sign (Yellow)', 'bzzz_neons_weed_d', 7500 },
        { 'Weed Sign (Purple)', 'bzzz_neons_weed_e', 7500 },

        { 'Welcome Sign (Blue)', 'bzzz_neons_welcome_a', 7500 },
        { 'Welcome Sign (Red)', 'bzzz_neons_welcome_b', 7500 },
        { 'Welcome Sign (Green)', 'bzzz_neons_welcome_c', 7500 },
        { 'Welcome Sign (Yellow)', 'bzzz_neons_welcome_d', 7500 },
        { 'Welcome Sign (Purple)', 'bzzz_neons_welcome_e', 7500 },
      },

    },


    {
      name = 'Gaming Room',
      items = {
        { 'RGB Tower', 'pata_office1', 2500 },
        { 'Monitor + Keyboard', 'pata_office2', 1500 },
        { 'Gaming Chair Black', 'pata_office3', 2000 },
        { 'Gaming Chair Pink', 'pata_office6', 2000 },
        { 'Ghost', 'pata_office4', 2700 },
        { 'Speaker', 'pata_office5', 1000 },
        { 'Mounted Camera', 'pata_office7', 2000 },
        { 'Light', 'pata_office8', 750 },
        { 'Microphone', 'pata_office9', 1000 },
        { 'Desk', 'pata_office10', 1500 },
        { 'Game On Neon', 'pata_office12', 1500 },
        { 'RGB keyboard', 'dnxprops_tech_gamingkeyboard01_a_rgb1', 1500 },
        { 'RGB keyboard 2', 'dnxprops_tech_gamingkeyboard01_b_rgb1', 1500 },
        { 'Gaming Monitor', 'dnxprops_tech_gamingmonitor01_a_27_wp1', 1500 },
        { 'Gaming Monitor 2', 'dnxprops_tech_gamingmonitor01_a_27_wp2', 1500 },
        { 'Gaming Monitor 3', 'dnxprops_tech_gamingmonitor01_a_27_wpcustom1', 1500 },
        { 'Gaming Monitor 4', 'dnxprops_tech_gamingmonitor01_a_27_wpcustom2', 1500 },
        { 'RGB Mouse', 'dnxprops_tech_gamingmouse01_a_rgb1', 1500 },
        { 'RGB Mouse 2', 'dnxprops_tech_gamingmouse01_b_rgb2', 1500 },
        { 'RGB Mouse pad', 'dnxprops_tech_gamingmousepad01_a_rgb1', 1500 },
        { 'RGB Mouse Pad 2', 'dnxprops_tech_gamingmousepad01_a_rgb2', 1500 },
        { 'RGB PC', 'dnxprops_tech_gamingpc01_a_rgb1', 1500 },
      }
    },

    {
      name = 'Classic Office',
      items = {
        { 'Modern Office Desk', 'pata_officeclassic1', 2000 },
        { 'L Shaped Modern Desk', 'pata_officeclassic2', 2000 },
        { 'Drafting Table', 'pata_officeclassic3', 1500 },
        { 'Modern Filing Cabinet', 'pata_officeclassic4', 1000 },
        { 'Modern Cubbie', 'pata_officeclassic5', 1500 },
        { 'Black Modern Desk Chair', 'pata_officeclassic6', 1500 },
        { 'White Modern Dining Chair', 'pata_officeclassic7', 1500 },
        { 'White Wall Accent', 'pata_officeclassic8', 1000 },
        { 'Drafting Table Chair', 'pata_officeclassic9', 1500 },
        { 'Drafting Paper', 'pata_officeclassic10', 500 },
        { 'iFruitbook Pro', 'pata_officeclassic11', 2000 },
        { 'Modern Desk Accessory  ', 'pata_officeclassic12', 750 },
        { 'Modern Desk Fold Down ', 'pata_officeclassic13', 1200 },
        { 'Modern Drawer Cabinet', 'pata_officeclassic14', 1500 },
        { 'Plant Duo', 'pata_officeclassic15', 750 },
        { 'Notebooksa', 'pata_officeclassic16', 500 },
        { 'Modern Quad Cubbie', 'pata_officeclassic17', 1000 },
        { 'Modern Wall Organizer', 'pata_officeclassic18', 1000 },
        { 'Modern Wall File Sorter', 'pata_officeclassic19', 1000},
        { 'Modern Open Cabinet', 'pata_officeclassic20', 1500 },
        { 'Modern Leather Desk Chair', 'pata_officeclassic21', 2000},
        { 'Modern Short Cabinet', 'pata_officeclassic22', 1500 },
        { 'Scroll Holder', 'pata_officeclassic23', 750 },
        { 'Bulletin Board', 'dnxprops_buildings_bulletinboard01_a', 500 },
        { 'Bulletin Board Empty', 'dnxprops_buildings_bulletinboard01_empty', 200 },
        { 'Privacy Screen', 'dnxprops_buildings_privacyscreen01_a', 500 },
        { 'Privacy Screen plants', 'dnxprops_buildings_privacyscreen01_a_hedge', 1000 },
      }
    },

    {
      name = 'Pink Office',
      items = {
        { 'Kawaii Ifruit', 'pata_pinkoffice1', 2000 },
        { 'Kitty Desk Buddy', 'pata_pinkoffice2', 750 },
        { 'Flower Desk Accessory', 'pata_pinkoffice3', 750 },
        { 'Pink Speckled Potted Plant', 'pata_pinkoffice4', 750 },
        { 'Trenta Cool Beans', 'pata_pinkoffice5', 750 },
        { 'Modern White Drawer Desk', 'pata_pinkoffice6', 2000 },
        { 'Notebook Duo', 'pata_pinkoffice7', 500 },
        { 'Notebook Trio', 'pata_pinkoffice8', 500 },
        { 'Pink Cozy Chair', 'pata_pinkoffice9', 1500 },
        { 'Ifruit Pad Keyboard', 'pata_pinkoffice10', 1200 },
        { 'Cute Pen & Scissors Holder', 'pata_pinkoffice11', 500 },
        { 'Wire Picture Holder', 'pata_pinkoffice12', 750 },
        { 'White Desk', 'pata_pinkoffice13', 2000 },
        { 'White locker', 'pata_pinkoffice14', 3000 },
      }
    },

    {
      name = 'Laundry',
      items = {
        {'Laundry Decor', 'pata_laundry1', 400},
        {'Small Upper Cabinet', 'pata_laundry7', 800},
        {'Upper Cabinet', 'pata_laundry2', 1000},
        {'Round Basket', 'pata_laundry3', 800},
        {'Hand Cleaning Set', 'pata_laundry4', 700},
        {'Tall Cabinet', 'pata_laundry5', 1000},
        {'Tall Shelf', 'pata_laundry6', 400},
        {'Shelf Upper Cabinet', 'pata_laundry8', 1500},
        {'Table Hamper', 'pata_laundry9', 700},
        {'Small Shelf', 'pata_laundry10', 600},
        {'Shelf Cabinet', 'pata_laundry11', 400},
        {'Washer/Dryer Machine', 'pata_laundry12', 700},
        {'Drying Rack', 'pata_laundry13', 800},
        {'Ironing Board', 'pata_laundry14', 900},
        {'Folded Ironing Board', 'pata_laundry15', 900},
        {'Iron', 'pata_laundry16', 1000},
        {'Small Hamper', 'pata_laundry17', 650},
        {'Small Tree Plant', 'pata_laundry18', 700},
        {'BIG Sink', 'pata_laundry19', 1000},
        {'Detergent Set', 'pata_laundry20', 1599},
      }
    },

    {
      name = 'Living Room',
      items = {
        {'Wooden Table', 'pata_livingroom1', 1500},
        {'Wooden Coffee Table', 'pata_livingroom2', 1000},
        {'Wooden Chair', 'pata_livingroom3', 1200},
        {'Wooden Sofa', 'pata_livingroom4', 1500},
        {'Wooden Sofa Corner', 'pata_livingroom5', 1500},
        {'Wooden Sofa End', 'pata_livingroom6', 1500},
        {'Wooden Sofa End 2', 'pata_livingroom8', 1500},
        {'Wooden Sofa Middle', 'pata_livingroom7', 1500},
        {'Sofa Pillow', 'pata_livingroom9', 800},
        {'Wall Art 1', 'pata_livingroom10', 600},
        {'Wall Art 2', 'pata_livingroom11', 400},
        {'Candle Stands', 'pata_livingroom12', 700},
        {'Yellow Arm Chair', 'pata_livingroom13', 1000},
        {'Yellow Love Seat', 'pata_livingroom15', 1500},
        {'Yellow Sofa', 'pata_livingroom17', 1700},
        {'Stool Set', 'pata_livingroom14', 600},
        {'Fireplace', 'pata_livingroom16', 1500},
        {'Corner Shelf', 'pata_livingroom18', 800},
        {'Long Entertainment Stand', 'pata_livingroom19', 1000},
        {'Medium Entertainment Stand', 'pata_livingroom25', 600},
        {'Red Coffee Table', 'pata_livingroom20', 800},
        {'White Sofa Middle', 'pata_livingroom21', 1500},
        {'White Sofa Middle Small', 'pata_livingroom22', 1500},
        {'White Sofa Corner', 'pata_livingroom23', 1500},
        {'White Sofa Long Middle', 'pata_livingroom24', 1750},
        {'White Sofa Foot Rest', 'pata_livingroom26', 800},
        {'White Sofa', 'pata_livingroom27', 1500},
        {'White Love Seat', 'pata_livingroom28', 1200},
        {'White Arm Chair', 'pata_livingroom29', 1000},
        {'Pink Sofa', 'pata_livingroom30', 1500},
        {'Pink Love Seat', 'pata_livingroom31', 1200},
        {'Pink Arm Chair', 'pata_livingroom32', 1000},
        {'Beanbag Crate Chair', 'pata_livingroom33', 1000},
        {'Rocking Chair', 'pata_livingroom34', 1000},
        {'Circle Table', 'pata_livingroom35', 800},
        {'Circle Table 2', 'pata_livingroom36', 800},
        {'Small Shelf', 'pata_livingroom37', 600},
        {'Tall Shelf', 'pata_livingroom38', 850},
        {'Living Room Decor 1', 'pata_livingroom39', 450},
        {'Living Room Decor 2', 'pata_livingroom40', 400},
        {'Living Room Rug', 'pata_livingroom41', 700},
        {'Artsy Lamp', 'pata_livingroom42', 1000},
        {'Artsy Lamp 2', 'pata_livingroom43', 1000},
        {'Artsy Wall Art', 'pata_livingroom44', 1000},
        {'Book shelf', 'dnxprops_furniture_bookshelf01_a', 1000},
        {'Book shelf 2', 'dnxprops_furniture_bookshelf01_b', 1000},
        {'Book shelf 3', 'dnxprops_furniture_bookshelf01_c', 1000},
        {'Book shelf with books', 'dnxprops_furniture_bookshelf01_books', 10000},
        {'Large Book shelf', 'dnxprops_furniture_bookshelflarge01_a', 1500},
        {'Large Book shelf', 'dnxprops_furniture_bookshelflarge01_b', 1500},
        {'Large Book shelf', 'dnxprops_furniture_bookshelflarge01_c', 1500},
      }
    },



    {
      name = 'Bedroom Props',
      items = {
        { 'Cherry Blossom Print', 'pata_artwall', 5000 },
        { 'Landscape Prints', 'pata_artwall2', 5000 },
        { 'Butterfly Prints', 'pata_artwall3', 5000 },
        { 'Canopy Bed', 'pata_bedroom1', 5000 },
        { 'Modern Drawer Bed', 'pata_bedroom16', 5000 },
        { 'Modern Cozy Bed', 'pata_bedroom18', 5000 },
        { 'Pink & Red Bed', 'pata_bedroom22', 5000 },
        { 'Alternative Dresser', 'pata_bedroom2', 5000 },
        { 'Alternative Mesh Dresser (20kg)', 'pata_bedroom3', 5000 },
        { 'Alternative Nightstand', 'pata_bedroom4', 2000 },
        { 'Modern Coffee Table', 'pata_bedroom5', 1500 },
        { 'Modern Open Desk', 'pata_bedroom12', 2000 },
        { 'White Modern Nightstand', 'pata_bedroom19', 2000 },
        { 'Alternative Lamp', 'pata_bedroom6', 1500 },
        { 'Modern Dresser', 'pata_bedroom7', 5000 },
        { 'Alternative Folding Chair', 'pata_bedroom8', 1500 },
        { 'Blue Modern Desk Chair', 'pata_bedroom13', 2000 },
        { 'Radiator', 'pata_bedroom9', 750 },
        { 'Modern Deco Rug', 'pata_bedroom10', 5000 },
        { 'Modern Closet', 'pata_bedroom14', 5000 },
        { 'Modern Shelves', 'pata_bedroom15', 1500 },
        { 'Modern Drawer Shelves', 'pata_biblio3', 1500 },
        { 'Modern Single Shelf', 'pata_bedroom20', 1000 },
        { 'Modern Headboard', 'pata_bedroom17', 1500 },
        { 'Decorative Star Tree', 'pata_bedroom21', 2000 },
        { 'Makeup Table', 'pata_dressing2', 1000 },
        { 'Cat Mirror', 'pata_dressing3', 500 },
        { 'Shelf', 'pata_dressing4', 1500 },
        { 'Shelf 2', 'pata_dressing6', 1500 },
        { 'Shelf 3', 'pata_dressing5', 1500 },
        { 'Shelf 4', 'pata_dressing21', 1500 },
        { 'Shelf 5', 'pata_dressing22', 1500 },
        { 'Shelf 6', 'pata_dressing39', 1500 },
        { 'Shelf 7', 'pata_dressing40', 1500 },
        { 'Shelf 8', 'pata_dressing41', 1500 },
        { 'Shelf 9', 'pata_dressing42', 1500 },
        { 'Self 10', 'pata_dressing23', 1500 },
        { 'Self 11', 'dnxprops_furniture_shelf01_a', 1500 },
        { 'Self 12', 'dnxprops_furniture_shelf01_b', 1500 },
        { 'Bedroom Decor 1', 'pata_dressing7', 400 },
        { 'Bedroom Decor 2', 'pata_dressing8', 400 },
        { 'Bedroom Decor 3', 'pata_dressing9', 400 },
        { 'Bedroom Decor 4', 'pata_dressing10', 400 },
        { 'Lipstick', 'pata_dressing11', 400 },
        { 'Multiple Lipsticks', 'pata_dressing12', 500 },
        { 'Makeup Brushes', 'pata_dressing13', 400 },
        { 'Nail Polish Set', 'pata_dressing14', 400 },
        { 'Pink Flowers', 'pata_dressing15', 500 },
        { 'Flowers In A Vase', 'pata_dressing16', 500 },
        { 'Glasses Stand', 'pata_dressing17', 500 },
        { 'Marble Top Dresser', 'pata_dressing18', 1500 },
        { 'Wardrobe Closed', 'pata_dressing19', 1500 },
        { 'Dresser', 'pata_dressing20', 1500 },
        { 'Dresser 2', 'pata_dressing25', 1500 },
        { 'Wardrobe drawers (25kg)', 'dnxprops_furniture_wardrobedrawer01_a', 6500 }, -- Maked all useable but only small and large can do outfits
        { 'Wardrobe drawers 2 (25kg)', 'dnxprops_furniture_wardrobedrawer01_b', 6500 },
        { 'Wardrobe drawers 3 (25kg)', 'dnxprops_furniture_wardrobedrawer01_c', 6500 },
        { 'Wardrobe Small (50kg)', 'dnxprops_furniture_wardrobesmall01_a', 50000 },
        { 'Wardrobe Small 2 (50kg)', 'dnxprops_furniture_wardrobesmall01_b', 50000 },
        { 'Wardrobe Small 3 (50Kg)', 'dnxprops_furniture_wardrobesmall01_c', 50000 },
        { 'Wardrobe Large (200kg)', 'dnxprops_furniture_wardrobelarge01_a', 150000 },
        { 'Wardrobe Large 2 (200kg)', 'dnxprops_furniture_wardrobelarge01_b', 150000 },
        { 'Wardrobe Large 3 (200kg)', 'dnxprops_furniture_wardrobelarge01_c', 150000 },---
        { 'Bedroom Decor 4', 'pata_dressing24', 500 },
        { 'Soft Stool', 'pata_dressing26', 1500 },
        { 'Bedroom Table', 'pata_dressing27', 1200 },
        { 'Bedroom Decor 5', 'pata_dressing28', 400 },
        { 'Bedroom Decor 6', 'pata_dressing29', 450 },
        { 'Bedroom Decor 7', 'pata_dressing30', 450 },
        { 'Bedroom Decor 8', 'pata_dressing31', 450 },
        { 'Bedroom Decor 9', 'pata_dressing32', 450 },
        { 'Bedroom Decor 10', 'pata_dressing33', 450 },
        { 'Shoe Set', 'pata_dressing34', 800 },
        { 'Hanging Shirt 1', 'pata_dressing35', 450 },
        { 'Hanging Shirt 2', 'pata_dressing36', 500 },
        { 'Hanging Shirt 3', 'pata_dressing37', 400 },
        { 'Hanging Shirt 4', 'pata_dressing38', 650 },
        { 'Hanging Shirt 5', 'pata_dressing43', 450 },
      }
    },

    {
      name = 'Usable Storage',
      items = {
        {'Chest (5kg)', 'storage_chest_5kg', 2100},
        {'Crate (10kg)', 'prop_drop_crate_01', 3200},
        {'Ammo Crate (12kg)', 'prop_box_ammo04a', 3640},
        {'Crate (16kg)', 'prop_box_tea01a', 4520},
        {'Tool Chest (20kg)', 'xs_prop_x18_tool_draw_01a', 6300},
        {'Barrel (25kg)', 'vw_prop_vw_barrel_01a', 6500},
        {'Safe (35kg)', 'sf_prop_v_43_safe_s_gd_01a', 8700},
        {'Crate (40kg)', 'prop_box_wood05a', 12500},
        {'Gun Locker (50kg)', 'bkr_prop_gunlocker_01a', 15600},
        {'Safe (50kg)', 'h4_prop_h4_safe_01a', 15600},
        {'Crates (100kg)', 'prop_cratepile_07a', 31100},
        {'Crates (1000kg)', 'prop_lev_crate_01', 221000},
        {'White Wardrobe (50kg)', 'prop_bl_wardrobe', 50000}, -- Priced for usability
        {'Christmas Tree', 'prop_xmas_tree_int', 2500},
        {'Weed Tub (150kg)', 'prop_weed_tub_01', 37150},
        {'Green Crate (500kg)', 'prop_mil_crate_01', 110500},
        {'Electronics crate (2000kg)', 'ex_prop_crate_elec_bc', 325000},
      }
    },

    {
      name = 'Tables',

      items = {
        {'TV Table', 'prop_tv_cabinet_03', 5000},
        {'Coffee table', 'prop_fbi3_coffee_table', 3000},
        {'Coffee table 2', 'prop_t_coffe_table', 3000},
        {'Coffee table 2', 'prop_t_coffe_table_02', 3000},
        {'Tri table', 'prop_tri_table_01', 3000},
        {'Protest table', 'prop_protest_table_01', 3000},
        {'Desk 1', 'v_res_son_desk', 3000},
        {'Desk 2', 'v_res_mddesk', 3000},
        {'Coffee Table', 'v_res_fh_coftableb', 1500},
        {'Coffee Table 2', 'v_res_fh_coftablea', 1500},
        {'Table 2', 'hei_prop_yah_table_01', 1500},
        {'Table 3', 'hei_prop_yah_table_02', 1500},
        {'Table 4', 'hei_prop_yah_table_03', 1500},
        {'Table 5', 'prop_ld_farm_table01', 1500},
        {'Table 6', 'prop_ld_farm_table02', 1500},
        {'Table 7', 'prop_chateau_table_01', 1500},
        {'Table 8', 'prop_astro_table_01', 1500},
        {'Table 9', 'prop_patio_lounger1_table', 1500},
        {'Table 10', 'prop_picnictable_01', 1500},
        {'Table 11', 'prop_proxy_chateau_table', 1500},
        {'Table 12', 'prop_rub_table_01', 1500},
        {'Table 13', 'prop_rub_table_02', 1500},
        {'Table 14', 'prop_table_01', 1500},
        {'Table 15', 'prop_table_02', 1500},
        {'Table 16', 'prop_table_03', 1500},
        {'Table 17', 'prop_table_03b', 1500},
        {'Table 18', 'prop_table_04', 1500},
        {'Table 19', 'prop_table_05', 1500},
        {'Table 20', 'prop_table_06', 1500},
        {'Table 21', 'prop_table_07', 1500},
        {'Table 22', 'prop_table_08', 1500},
        {'Table 23', 'prop_table_08_chr', 1500},
        {'Table 24', 'prop_ven_market_table1', 1500},
        {'Table 25', 'v_ilev_liconftable_sml', 1500},
        {'Table 26', 'prop_table_tennis', 1500},
        {'Table 27', 'prop_astro_table_02', 1500},
        {'Dining Table', 'dnxprops_furniture_dinningtable01_a', 1500},
        {'Dining Table 2', 'dnxprops_furniture_dinningtable01_b', 1500},
        {'Dining Table 3', 'dnxprops_furniture_dinningtable02_a', 1500},
        {'Dining Table 4', 'dnxprops_furniture_dinningtable02_b', 1500},
        {'White Shelving', 'apa_mp_h_str_shelffreel_01', 7000},
        {'Bedroom Dresser', 'apa_mp_h_bed_chestdrawer_02', 5000},
        {'Glass Triangle Coffee Table', 'apa_mp_h_tab_coffee_07', 5000},
        {'Glass Table', 'apa_mp_h_tab_sidelrg_02', 5000},
		    { 'Computer Desk', 'v_ind_dc_desk02', 2500 },
      }
    },

    {
      name = 'Chairs',

      items = {
        {'Table chair', 'prop_table_02_chr', 500},
        {'Folding chair', 'prop_cs_folding_chair_01', 1500},
        {'Rock chair', 'prop_rock_chair_01', 1500},
        {'Leather office chair', 'p_clb_officechair_s', 1250},
        {'Gray office chair', 'prop_off_chair_04_s', 1500},
        {'Square office chair', 'prop_cs_office_chair', 1500},
        {'Blue office chair', 'prop_off_chair_04', 1500},
        {'Round office chair', 'prop_off_chair_03', 1500},
        {'Modern office chair', 'prop_off_chair_01', 1500},
        {'Sleek office chair', 'prop_off_chair_05', 1500},
        {'Plush office chair', 'v_club_officechair', 1500},
        {'Slatted Office chair', 'v_corp_offchair', 1500},
        {'Old wooden chair', 'prop_torture_ch_01', 1500},
        {'Diner chair', 'p_dinechair_01_s', 1500},
        {'Yacht recliner', 'prop_sol_chair', 1500},
        {'Wicker chair', 'prop_chair_02', 1500},
        {'Deck chair', 'prop_chair_01b', 1500},
        {'Modern wooden chair', 'prop_chair_04a', 1500},
        {'Modern white chair', 'prop_chair_04b', 1500},
        {'Cafeteria chair', 'prop_chair_06', 1500},
        {'Blue dining hair', 'prop_chair_07', 1500},
        {'Green wicker chair', 'prop_chair_09', 1500},
        {'Tea chair', 'prop_chateau_chair_01', 1500},
        {'Wooden chair', 'prop_clown_chair', 1500},
        {'Director chair', 'prop_direct_chair_01', 1500},
        {'Brown leather chair', 'prop_cs_office_chair', 1500},
        {'White plastic chair', 'prop_gc_chair02', 1500},
        {'Old armchair', 'prop_ld_farm_chair01', 1500},
        {'Blue plastic chair', 'v_ilev_chair02_ped', 1500},
        {'Red office chair', 'v_corp_cd_chair', 1500},
        {'Gray tall chair', 'v_corp_bk_chair3', 1500},
        {'Camping chair', 'prop_skid_chair_03', 1500},
        {'Kitchen chair (arms)', 'prop_table_01_chr_a', 1500},
        {'Kitchen chair (no arms)', 'prop_table_01_chr_b', 1500},
        {'Square green armchair', 'v_res_mp_stripchair', 1500},
        {'Green leather armchair', 'v_res_d_armchair', 1500},
        {'Old kitchen chair', 'prop_table_02_chr', 1500},
        {'White picnic chair', 'prop_table_03_chr', 1500},
        {'Plastic picnic chair', 'prop_table_03b_chr', 1500},
        {'White fabric chair', 'prop_table_04_chr', 1500},
        {'Wicker dining chair', 'prop_table_05_chr', 1500},
		    { 'Brown Leather Office Chair', 'v_ilev_leath_chr', 1000 },
        {'Pink Chair', 'pata_dressing1', 1500},
      }
    },

    {
      name = 'Decorations',

      items = {
        {'Bottle', 'apa_mp_h_acc_bottle_01', 1000},
        {'Candles', 'apa_mp_h_acc_candles_01', 1000},
        {'Mirror', 'p_int_jewel_mirror', 1000},
        {'Plate', 'apa_mp_h_acc_dec_plate_01', 1000},
        {'Vase', 'apa_mp_h_acc_vase_01', 1000},
        {'Flowers', 'apa_mp_h_acc_vase_flowers_01', 1000},
        {'Flowers 2', 'v_ret_flowers', 1000},
        {'Flowers 3', 'v_ret_ps_flowers_01', 1000},
        {'Flowers 4', 'v_ret_ps_flowers_02', 1000},
        {'Ashtray', 'ex_prop_ashtray_luxe_02', 1000},
        {'Newspaper', 'p_cs_newspaper_s', 1000},
        {'Pamphlet', 'p_cs_pamphlet_01_s', 1000},
        {'Bong', 'prop_bong_01', 1000},
        {'Rasberry', 'prop_mr_rasberryclean', 1000},
        {'Guitar', 'prop_acc_guitar_01', 1000},
        {'Board', 'p_planning_board_04', 1000},
        {'Clock 1', 'prop_hotel_clock_01', 1000},
        {'Clock 2', 'prop_big_clock_01', 1000},
        {'Clock 3', 'prop_egg_clock_01', 1000},
        {'Painting', 'v_ilev_ra_doorsafe', 5000},
        {'Exercise bike', 'prop_exercisebike', 3000},
        {'Laz', 'p_laz_j02_s', 3000},
        {'Landscape Painting', 'v_res_picture_frame', 10000},
        {'Blue Crystal', 'v_res_fa_crystal02', 5000},
        {'Green Crystal', 'v_res_fa_crystal03', 5000},
        {'Purple Crystal', 'v_res_fa_crystal01', 5000},
        {'Blue Candle', 'v_res_fa_candle01', 500},
        {'Red Candle', 'v_res_fa_candle02', 500},
        {'Green Candle', 'v_res_fa_candle03', 500},
        {'White Candle', 'v_res_fa_candle04', 500},
        {'Yoga For Beginners Book', 'v_res_fa_book01', 500},
        {'XXX Magazines', 'prop_porn_mag_03', 200},
        {'Flower Box', 'prop_windowbox_b', 500},
        {'Lady Mug', 'v_club_vu_coffeemug1', 100},
        {'Panther Statue', 'v_club_vu_statue', 1000},
        {'Disk Storage', 'v_res_cdstorage', 200},
        {'Desk Caddy', 'v_res_desktidy', 200},
        {'Kama Sutra Book', 'v_res_fa_book03', 500},
        {'Self Help Book', 'v_res_fa_book04', 500},
        {'Healing Crystals Book', 'v_res_fa_book02', 500},
        {'Red painting', 'apa_mp_h_acc_artwalll_01', 5000},
        {'Orange painting', 'apa_mp_h_acc_artwalll_02', 5000},
        {'Blue painting', 'apa_mp_h_acc_artwalll_03', 5000},
        {'Colorful painting1', 'apa_mp_h_acc_artwallm_02', 5000},
        {'Colorful painting2', 'apa_mp_h_acc_artwallm_03', 5000},
        {'FruitBowl', 'apa_mp_h_acc_fruitbowl_02', 1000},
        {'Gray Rug', 'apa_mp_h_acc_rugwooll_03', 5000},
        {'Light Rug', 'apa_mp_h_acc_rugwooll_04', 5000},
        {'Color Rug', 'apa_mp_h_acc_rugwoolm_01', 5000},
        {'Color Rug2', 'apa_mp_h_acc_rugwoolm_02', 5000},
        {'Geo Rug', 'apa_mp_h_acc_rugwools_01', 5000},
        {'WhiteBeige Rug', 'apa_mp_h_acc_rugwools_01', 5000},
        {'BlueBrowns Rug', 'apa_mp_h_acc_rugwools_03', 5000},
        {'WhiteBeige Rug', 'apa_mp_h_acc_rugwools_01', 5000},
        {'Bedroom Dresser', 'apa_mp_h_bed_chestdrawer_02', 5000},
        {'Framed Jersey', 'apa_p_h_acc_artwalls_04', 5000},
        {'Standing Fan', 'bkr_prop_weed_fan_floor_01a', 1000},
        {'Trophy', 'ex_prop_exec_award_silver', 1000},
        {'Gold Record', 'hei_heist_acc_artgolddisc_04', 10000},
        {'Impotent Rage Figurine', 'hei_prop_drug_statue_01', 1000},
        {'Bust', 'hei_prop_hei_bust_01', 20000},
        {'Wine Glass', 'p_wine_glass_s', 300},
        {'Teddy', 'p_mr_raspberry_01_s', 500},
        {'Bar Caddy', 'prop_bar_caddy', 500},
        {'Purse Rack', 'prop_beachbag_combo_02', 500},
        {'Cold Beer Neon', 'prop_beerneon', 10000},
        {'Baseball Hat Rack', 'prop_cap_row_02b', 1000},
        {'Baseball Hat Rack2', 'prop_cap_row_02', 1000},
        {'Desk Files', 'prop_cd_folder_pile1', 500},
        {'Desk Binders', 'prop_cd_folder_pile2', 500},
        {'Christmas Tree', 'prop_xmas_tree_int', 2000},
        {'Horse Painting', 'v_ilev_ra_doorsafe', 6000},
        {'Brown Shoes', 'v_ret_ps_shoe_01', 300},
        {'Left Shoe 1', 'v_res_fa_trainer01l', 150},
        {'Right Shoe 1', 'v_res_fa_trainer01r', 150},
        {'Left Shoe 2', 'v_res_fa_trainer02l', 150},
        {'Right Shoe 2', 'v_res_fa_trainer02r', 150},
        {'Left Shoe 3', 'v_res_fa_trainer03l', 150},
        {'Right Shoe 3', 'v_res_fa_trainer03r', 150},
        {'Left Shoe 4', 'v_res_fa_trainer04l', 150},
        {'Right Shoe 4', 'v_res_fa_trainer04r', 150},
        {'Pink Yoga Mat', 'v_res_fa_yogamat002', 500},
        {'Purple Yoga Mat', 'v_res_fa_yogamat1', 500},
        {'Red Glass Bottle', 'v_res_glasspot', 400},
        {'Bust Statue', 'v_res_m_statue', 1500},
        {'Laundry Hamper', 'v_res_mlaundry', 500},
        {'Tissue Box', 'v_res_tissues', 500},
        {'Tissue Box2', 'v_res_tt_tissues', 500},
        {'Newspaper 2', 'v_res_tabloidsa', 500},
        {'Newspaper 3', 'v_res_tabloidsb', 500},
        {'Whisker Basket', 'v_res_tre_basketmess', 900},
        {'Laundry Basket', 'v_res_tre_laundrybasket', 900},
        {'Plunger', 'v_res_tt_plunger', 600},
        {'9MM Ammo Box', 'v_ret_gc_ammo5', 500},
        {'Shotgun Shells', 'v_ret_gc_ammo3', 500},
        {'7.62mm Ammo Box', 'v_ret_gc_ammo4', 500},
        {'Rolodex', 'v_corp_bk_rolladex', 550 },
        {'Slightly Dated Filing Cabinet', 'v_ind_dc_filecab01', 1050 },
        {'Calculator', 'v_ret_gc_calc', 100 },
        {'Cologne', 'v_ret_ps_cologne', 400 },
        {'Liquid Soap 1', 'v_ret_ps_toiletry_01', 450},
        {'Liquid Soap 1', 'v_ret_ps_toiletry_02', 450},
        {'Laundry Detergent', 'v_ret_washpow2', 500},
        {'Blow Dryer', 'v_serv_bs_hairdryer', 1000},
        {'Empty Pizza Box', 'v_ret_fh_pizza01', 500},
        {'Paper Tray', 'prop_inout_tray_01', 1000},
        {'Pen Organizer', 'v_res_desktidy', 500},
        {'Unrolled Yoga Mat', 'prop_yoga_mat_02', 500},
        {'Unrolled Yoga Mat 2', 'prop_yoga_mat_03', 500},
        {'Unrolled Yoga Mat 3', 'prop_yoga_mat_01', 500},
        {'Rolling Papers', 'p_cs_papers_02', 750},
        {'Bongos', 'prop_bongos_01', 1000},
        {'Open CD Case', 'prop_cs_dvd_case', 250},
        {'Ironing Board', 'prop_cs_ironing_board', 500},
        {'Iron', 'prop_iron_01', 500},
        {'Sewing Machine', 'prop_sewing_machine', 1000},
        {'Manilla Folder', 'p_cs_script_s', 500},
        {'Blueprint', 'prop_tool_bluepnt', 500},
      }
    },

    {
      name = 'Electronics',

      items = {
        {'Flat tv', 'prop_tv_flat_02', 7500},
        {'Fat tv', 'prop_tv_06', 3500},
        {'Small fat tv', 'prop_tv_02', 2500},
        {'Radio', 'prop_portable_hifi_01', 1000},
        {'Monitor', 'prop_monitor_01c', 750},
        {'Monitor 2', 'prop_ld_monitor_01', 750},
        {'Monitor 3', 'prop_monitor_03b', 250},
		    {'Monitor 4', 'v_serv_ct_monitor03', 750},
		    {'Monitor 5', 'v_serv_ct_monitor05', 750},
        {'Phone', 'p_amb_phone_01', 750},
        {'Old phone', 'prop_v_m_phone_o1s', 750},
        {'Office Phone', 'prop_office_phone_tnt', 750},
		    {'Printer', 'prop_printer_02', 1500},
        {'CS Phone', 'prop_cs_phone_01', 750},
        {'Tablet', 'prop_cs_tablet_02', 750},
        {'LifeOS Tablet', 'prop_cs_tablet', 2500},
        {'Table mic', 'prop_table_mic_01', 750},
        {'Laptop', 'p_amb_lap_top_02', 2500},
        {'Laptop 2', 'p_cs_laptop_02', 2500},
        {'Laptop Closed', 'p_cs_laptop_02_w', 2500},
        {'Keypad', 'prop_ld_keypad_01b', 750},
        {'Keypad 2', 'prop_ld_keypad_01', 750},
        {'PC', 'prop_dyn_pc', 1500},
        {'PC 2', 'prop_dyn_pc_02', 1500},
        {'PC 3', 'prop_pc_01a', 1500},
        {'PC 4', 'hei_prop_heist_pc_01', 1500},
        {'PC 5', 'prop_pc_02a', 1500},
        {'Mouse', 'prop_cs_mouse_01', 750},
        {'Mouse 2', 'prop_mouse_01a', 750},
        {'Mousemat', 'v_res_mousemat', 750},
        {'Keyboard', 'hei_prop_hei_cs_keyboard', 750},
        {'Keyboard 2', 'prop_cs_keyboard_01', 750},
        {'Keyboard 3', 'prop_keyboard_01a', 750},
        {'Bedside clock', 'v_res_fh_bedsideclock', 750},
        {'Washing Machine', 'v_ret_fh_washmach', 1000},
        {'Dryer', 'v_ret_fh_dryer', 1000},
        {'Small Entertainment Unit', 'apa_mp_h_str_avunits_01', 10000},
        {'Medium Entertainment Unit', 'apa_mp_h_str_avunits_04', 15000},
        {'Entertainment Unit', 'apa_mp_h_str_avunitm_01', 20000},
        {'Flat Screen 1', 'prop_tv_flat_01_screen', 6000},
        {'Flat Screen 2', 'prop_tv_flat_02b', 6000},
        {'Flat Screen 3', 'prop_tv_flat_03', 6000},
        {'Flat Screen 4', 'prop_tv_flat_michael', 6000},
        {'Security Camera', 'v_serv_securitycam_03', 1500},
        {'TV Remote', 'prop_cs_remote_01', 500},
        {'Camera', 'prop_pap_camera_01', 3500},
        {'Controller', 'prop_controller_01', 500},
        {'Paper Shredder (Usable)', 'prop_shredder_01', 1200},
      }
    },

    {
      name = 'Beds',

      items = {
        {'Bed 2', 'p_lestersbed_s', 2500},
        {'Bed 5', 'v_res_msonbed_s', 5000},
        {'Bed 6', 'v_res_mbbed', 1500},
        {'Bed 7', 'v_res_d_bed', 1500},
        {'Bed 8', 'v_res_tre_bed1', 1500},
        {'Bed 9', 'v_res_tre_bed2', 1500},
        {'Bed 10', 'v_res_tt_bed', 5000},
        {'Bunker Bed', 'gr_prop_bunker_bed_01', 1500},
        {'Camp Bed', 'gr_prop_gr_campbed_01', 1000},
        {'Victorian Bed', 'p_mbbed_s', 5000},
        {'Gray Double Bed', 'p_v_res_tt_bed_s', 5000},
        {'Bed with Nighstands', 'apa_mp_h_bed_with_table_02', 5000},
        {'White and Gray Doublebed', 'apa_mp_h_bed_double_09', 5000},
        {'Yacht Bed', 'apa_mp_h_yacht_bed_01', 8000},
        {'Black Yacht Bed', 'apa_mp_h_yacht_bed_02', 5000},
        {'Day Bed', 'prop_t_sofa_02', 5000},
      }
    },

    {
      name = 'Sofas',

      items = {
        {'Sofa Cushion', 'v_ilev_m_sofacushion', 1000},
        {'Leather sofa', 'p_v_med_p_sofa_s', 5000},
        {'Used sofa', 'v_tre_sofa_mess_c_s', 1500},
        {'Fabric sofa', 'p_res_sofa_l_s', 5000},
        {'Couch', 'miss_rub_couch_01', 2250},
        {'Sofa 1', 'prop_ld_farm_couch02', 2000},
        {'Sofa 2', 'prop_t_sofa', 5000},
        {'Sofa 3', 'v_tre_sofa_mess_a_s', 1500},
        {'Sofa 4', 'v_tre_sofa_mess_b_s', 3500},
        {'Sofa 5', 'prop_ld_farm_couch01', 1500},
        {'Sofa 6', 'p_yacht_sofa_01_s', 2500},
        {'Sofa 7', 'v_ilev_m_sofa', 7000},
        {'Sofa 8', 'v_res_tre_sofa_s', 3500},
        {'Sofa 9', 'prop_rub_couch02', 1500},
        {'Sofa 10', 'v_res_m_h_sofa_sml', 1500},
        {'Sofa 11', 'v_res_mp_stripchair', 1500},
        {'Sofa 12', 'v_res_d_armchair', 1500},
        {'White 2 Seater', 'apa_mp_h_stn_sofa2seat_02', 5000},
        {'White Ottoman', 'apa_mp_h_stn_foot_stool_02', 3000},
        {'Psych Chair', 'apa_mp_h_stn_sofa_daybed_01', 5000},
        {'Green Sectional', 'apa_mp_h_stn_sofacorn_06', 8000},
        {'Blue Sectional', 'apa_mp_h_stn_sofacorn_07', 8000},
        {'Black Sectional', 'apa_mp_h_stn_sofacorn_09', 8000},
        {'Grey Sectional', 'apa_mp_h_stn_sofacorn_08', 8000},
        {'Long White Yacht Sofa', 'apa_mp_h_yacht_sofa_01', 8000},
        {'OffWhite Sofa', 'ex_mp_h_off_sofa_01', 5000},
        {'Grey Sofa', 'ex_mp_h_off_sofa_003', 5000},
        {'Black Leather Sofa', 'ex_mp_h_off_sofa_02', 5000},
        {'3 Seat Beige', 'hei_heist_stn_sofa3seat_02', 5000},
        {'Modern White Sofa', 'hei_heist_stn_sofa2seat_03', 5000},
        {'White ArmChair', 'p_armchair_01_s', 3000},
      }
    },

    {
      name = 'Plants',

      items = {
        {'Plant 1', 'p_int_jewel_plant_02', 1000},
        {'Plant 2', 'p_int_jewel_plant_01', 1000},
        {'Plant 3', 'prop_fbibombplant', 1000},
        {'Plant 4', 'prop_fib_plant_01', 1000},
        {'Plant 5', 'prop_fib_plant_02', 1000},
        {'Plant 6', 'prop_pot_plant_05a', 1000},
        {'Plant 7', 'prop_ld_planter2b', 1000},
        {'Plant 8', 'prop_plant_int_01a', 1000},
        {'Plant 9', 'prop_plant_int_01b', 1000},
        {'Plant 10', 'prop_plant_int_02a', 1000},
        {'Plant 11', 'prop_plant_int_02b', 1000},
        {'Plant 12', 'prop_plant_int_03a', 1000},
        {'Plant 13', 'prop_plant_int_03c', 1000},
        {'Plant 14', 'prop_plant_int_03b', 1000},
        {'Plant 15', 'prop_plant_int_04b', 1000},
        {'Plant 16', 'prop_plant_int_04a', 1000},
        {'Plant 17', 'prop_plant_int_04c', 1000},
        {'Plant 13', 'prop_plant_palm_01a', 1000},
        {'Plant 14', 'prop_plant_palm_01c', 1000},
        {'Plant 15', 'prop_pot_plant_01b', 1000},
        {'Plant 16', 'prop_pot_plant_01a', 1000},
        {'Plant 17', 'prop_pot_plant_01c', 1000},
        {'Plant 18', 'prop_pot_plant_01d', 1000},
        {'Plant 19', 'prop_pot_plant_03c', 1000},
        {'Plant 20', 'v_res_fa_plant01', 1000},
        {'Plant 21', 'v_res_m_vasedead', 1000},
        {'Plant 22', 'v_res_m_vasefresh', 1000},
        {'Plant 23', 'v_res_m_palmstairs', 1000},
        {'Plant 24', 'v_res_rosevasedead', 1000},
        {'Plant 25', 'v_res_rosevase', 1000},
        {'Plant 26', 'v_res_tre_tree', 1000},
        {'Plant 27', 'pata_2bathroom20', 100},
        {'Plant 28', 'pata_2bathroom21', 100},
        {'Plant 29', 'pata_2bathroom25', 100},
      }
    },

    {
      name = 'Valuables & Jewelry',

      items = {
        {'Cash Case', 'prop_cash_case_02', 250000, true},
        {'Cash Crate', 'prop_cash_crate_01', 500000, true},
        {'Cash Crate 2', 'hei_prop_cash_crate_half_full', 250000, true},
        {'Cash Note', 'prop_anim_cash_note_b', 5000},
        {'Cash Pile', 'prop_anim_cash_pile_01', 5000},
        {'Cash trolly', 'prop_cash_trolly', 250000, true},
        {'Cash trolly', 'prop_poly_bag_money', 500},
        {'Gold trolly full', 'prop_gold_trolly_full', 500000, true},
        {'Gold bar', 'prop_gold_bar', 20000, true},
        {'Large Gold', 'p_large_gold', 1000000, true},
        {'Gold Chest', 'prop_ld_gold_chest', 80000, true},
        {'Jewel 1', 'prop_jewel_03b', 5000},
        {'Jewel 2', 'prop_jewel_04b', 5000},
        {'Jewel 3', 'prop_jewel_02b', 5000},
        {'Jewel 4', 'prop_jewel_pickup_new_01', 5000},
      }
    },

    {
      name = 'Drugs',

      items = {
        {'Drug ', 'prop_weed_pallet', 15000},
        {'Drug 2', 'hei_prop_hei_drug_pack_01b', 1000},
        {'Drug 3', 'hei_prop_hei_drug_pack_01a', 1000},
        {'Drug 4', 'hei_prop_hei_drug_pack_02', 1000},
        {'Drug 5', 'hei_prop_heist_drug_tub_01', 1000},
        {'Drug 6', 'ng_proc_drug01a002', 1000},
        {'Drug 7', 'prop_drug_bottle', 1000},
        {'Drug 8', 'hei_prop_hei_drug_case', 5000},
        {'Drug 9', 'prop_drug_burner', 1000},
        {'Drug 10', 'prop_drug_erlenmeyer', 1000},
        {'Drug 11', 'prop_drug_package', 1000},
        {'Drug 12', 'prop_drug_package_02', 1000},
        {'Drug 13', 'prop_mp_drug_package', 1000},
        {'Drug 14', 'prop_mp_drug_pack_blue', 1000},
        {'Drug 15', 'prop_mp_drug_pack_red', 1000},
        {'Drug 16', 'prop_coke_block_01', 1000},
        {'Drug 17', 'prop_coke_block_half_a', 1500},
        {'Drug 18', 'p_meth_bag_01_s', 5000},
        {'Drug 19', 'prop_meth_bag_01', 1000},
        {'Drug 20', 'prop_meth_setup_01', 1000},
      }
    },

    {
      name = 'Light Switchs and Sockets',

      items = {
        {'Light Switch', 'dnxprops_electronics_lightbutton01_a', 250},
        {'Light Switch 2', 'dnxprops_electronics_lightbutton01_b', 250},
        {'Light Switch with plug', 'dnxprops_electronics_socketswitchus01_combo1_a', 250},
        {'Light Switch with plug 2', 'dnxprops_electronics_socketswitchus01_combo1_b', 250},
        {'Outlet plug', 'dnxprops_electronics_socketus01_double_a', 250},
        {'Outlet plug 2', 'dnxprops_electronics_socketus01_double_b', 250},
        {'Outlet plug 3', 'dnxprops_electronics_socketus01_single_a', 250},
        {'Outlet plug 4', 'dnxprops_electronics_socketus01_single_b', 250},
        {'Light Switch 3', 'dnxprops_electronics_switch01_double_a', 250},
        {'Light Switch 4', 'dnxprops_electronics_switch01_double_b', 250},
        {'Light Switch 5', 'dnxprops_electronics_switch01_single_a', 250},
        {'Light Switch 6', 'dnxprops_electronics_switch01_single_b', 250},
        {'Light Switch 7', 'dnxprops_electronics_switch01_triple_a', 250},
        {'Light Switch 7', 'dnxprops_electronics_switch01_triple_b', 250},
      }
    },

    {
      name = 'Food and Drinks',

      items = {
        {'Peanut bowl', 'prop_peanut_bowl_01', 500},
        {'Bowl', 'prop_cs_bowl_01', 500},
        {'BS Cup', 'prop_cs_bs_cup', 500},
        {'Coffee', 'p_ing_coffeecup_02', 500},
        {'Fruit Stand 1', 'prop_fruit_stand_03', 500},
        {'Fruit Stand 2', 'prop_fruit_stand_02', 500},
        {'Fruit Stand 3', 'prop_fruit_stand_01', 500},
        {'Beer box', 'prop_cs_beer_box', 500},
        {'Beer 2', 'beerrow_world', 500},
        {'Beer 3', 'prop_amb_beer_bottle', 500},
        {'Beer 4', 'prop_beer_blr', 500},
        {'Beer 5', 'prop_beer_logger', 500},
        {'Food', 'ng_proc_food_bag01a', 500},
        {'Food 2', 'prop_food_bs_burg1', 500},
        {'Food 3', 'prop_food_bs_burg3', 500},
        {'Food 4', 'prop_food_bs_chips', 500},
        {'Food 5', 'prop_food_bs_burger2', 500},
        {'Food 6', 'prop_food_bs_coffee', 500},
        {'Food 7', 'prop_food_cups1', 500},
        {'Food 8', 'prop_food_cb_cups01', 500},
        {'Food 9', 'prop_food_cb_cups02', 500},
        {'Food 10', 'prop_food_bs_cups02', 500},
        {'YJ Craft Case of Beer', 'v_ret_ml_beerbox', 500},
        {'Pisswasser Case of Beer', 'v_ret_ml_beerpis2', 500},
        {'Logger Case of Beer', 'v_ret_ml_beerlog1', 500},
        {'Patriot Case of Beer', 'v_ret_ml_beerpat2', 500},
        {'Rails Cereal Box', 'v_res_fa_cereal01', 100},
        {'Crackles Cereal Box', 'v_res_fa_cereal02', 100},
        {'Pizza Box', 'prop_pizza_box_03', 200},
        {'Food Jar 1', 'v_res_foodjarc', 500},
        {'Food Jar 2', 'v_res_foodjara', 500},
        {'Food Jar 3', 'v_res_foodjarb', 500},
        {'Bread Loaf', 'v_res_fa_bread02', 500},
        {'Bread Slice', 'v_res_fa_bread03', 500},
        {'Egg Carton', 'v_ret_247_eggs', 500},
        {'Ketchup Bottle', 'v_ret_247_ketchup2', 500},
        {'Container" of Noodles', 'v_ret_247_noodle1', 500},
        {'Bowl of Chips', 'prop_bowl_crisps', 500},
        {'Half of Sandwhich', 'prop_sandwich_01', 500},
	    }
    },

    {
      name = 'Lights',

      items = {
        {'Light ', 'prop_cd_lamp', 1500},
        {'Light 2', 'v_res_desklamp', 1500},
        {'Light 3', 'v_corp_cd_desklamp', 1500},
        {'Light 4', 'v_res_d_lampa', 1500},
        {'Light 5', 'v_res_fh_floorlamp', 1500},
        {'Light 6', 'v_res_fa_lamp1on', 1500},
        {'Light 7', 'v_res_j_tablelamp1', 1500},
        {'Light 8', 'v_res_j_tablelamp2', 1500},
        {'Light 9', 'v_res_mplanttongue', 1500},
        {'Light 10', 'v_res_m_lampstand', 1500},
        {'Light 11', 'v_res_m_lampstand2', 1500},
        {'Light 12', 'v_res_mtblelampmod', 1500},
        {'Light 13', 'v_res_m_lamptbl', 1500},
        {'Light 14', 'v_res_tre_lightfan', 1500},
        {'Light 15', 'v_res_tre_talllamp', 1500},
        {'Light 16', 'v_ret_fh_walllighton', 1500},
        {'Light 17', 'v_ret_gc_lamp', 1500},
        {'Light 18', 'prop_wall_light_05c', 1500},
        {'Light 19', 'hei_prop_hei_bnk_lamp_02', 1500},
        {'Light 20', 'prop_ld_cont_light_01', 1500},
        {'Light 21', 'hei_prop_hei_bnk_lamp_01', 1500},
        {'Light 22', 'prop_chall_lamp_02', 1500},
        {'Light 23', 'v_ilev_fh_lampa_on', 1500},
        {'Light 24', 'prop_construcionlamp_01', 1500},
        {'Light 25', 'hei_prop_bank_ornatelamp', 1500},
        {'Light 26', 'prop_kino_light_03', 1500},
        {'Light 27', 'prop_oldlight_01c', 1500},
        {'Light 28', 'prop_recycle_light', 1500},
        {'Light 29', 'prop_studio_light_01', 1500},
        {'Light 30', 'prop_studio_light_02', 1500},
        {'Light 31', 'prop_wall_light_02a', 1500},
        {'Light 32', 'prop_wall_light_03a', 1500},
        {'Light 33', 'prop_wall_light_04a', 1500},
        {'Light 34', 'prop_wall_light_05a', 1500},
        {'Light 35', 'v_res_m_lampstand', 1500},
        {'Light 36', 'v_res_m_lampstand2', 1500},
        {'Light 37', 'v_res_m_lamptbl', 1500},
      }
    },

    {
      name = 'Kitchen',

      items = {
        {'Coffee Machine', 'apa_mp_h_acc_coffeemachine_01', 1500},
        {'Scale', 'bkr_prop_coke_scale_01', 1500},
        {'Bin', 'hei_heist_kit_bin_01', 1500},
        {'Sideboard', 'hei_heist_str_sideboardl_03', 1500},
        {'Trolly', 'hei_prop_hei_cash_trolly_03', 1500},
        {'Bag', 'hei_prop_hei_paper_bag', 1500},
        {'Counter', 'p_new_j_counter_02', 1500},
        {'Fruit 1', 'apa_mp_h_acc_fruitbowl_01', 1500},
        {'Fruit 2', 'apa_mp_h_acc_fruitbowl_02', 1500},
        {'Fruit 3', 'prop_bar_fruit', 1500},
        {'Fruit 4', 'prop_bar_lemons', 1500},
        {'Fruit 5', 'prop_bar_measrjug', 1500},
        {'Sink', 'prop_bar_sink_01', 1500},
        {'Cleaver', 'prop_cleaver', 1500},
        {'Kitchen stool', 'v_ilev_fh_kitchenstool', 1500},
        {'Fridge 1', 'prop_trailr_fridge', 1500},
        {'Fridge 2', 'prop_fridge_01', 1500},
        {'Fridge 3', 'prop_fridge_03', 1500},
        {'Vend 1', 'prop_vend_fridge01', 1500},
        {'Vend 2', 'prop_vend_snak_01', 1500},
        {'Vend 3', 'prop_vend_coffe_01', 1500},
        {'Vend 4', 'prop_vend_soda_01', 1500},
        {'Vend 5', 'prop_vend_soda_02', 1500},
        {'Vend 6', 'prop_vend_water_01', 1500},
        {'Vend 7', 'prop_watercooler', 1500},
        {'Target Practice Mug', 'v_ret_gc_mug01', 500},
        {'Papertowel Roll', 'v_ret_ta_paproll', 50},
        {'Covered Pot', 'v_ret_fh_pot01', 150},
        {'Cake Dome', 'v_res_cakedome', 500},
        {'Gumball Machine', 'prop_gumball_03', 5000},
        {'Juicer', 'prop_kitch_juicer', 600},
        {'Pan', 'prop_kitch_pot_frye2', 100},
        {'Pot', 'prop_kitch_pot_lrg', 100},
        {'Soup Pot', 'prop_kitch_pot_lrg2', 100},
        {'Butter Knife', 'v_res_fa_butknife', 500},
        {'Tea Kettle', 'v_res_r_coffpot', 800},
        {'Sugar Bowl', 'v_res_r_sugarbowl', 800},
        {'Silver Tray', 'v_res_r_silvrtray', 800},
        {'Mixer', 'v_res_tre_mixer', 800},
        {'Toaster', 'prop_cs_toaster', 500},
        {'Sponge', 'prop_scourer_01', 500},
        {'Kitchen Decor 1', 'pata_kit2_1', 500},
        {'Kitchen Decor 2', 'pata_kit2_2', 500},
        {'Kitchen Decor 3', 'pata_kit2_3', 500},
        {'Kitchen Decor 4', 'pata_kit2_15', 500},
        {'Kitchen Decor 4', 'pata_kit2_44', 500},
        {'Spice Set', 'pata_kit2_4', 500},
        {'Kitchen Pot', 'pata_kit2_5', 800},
        {'Toaster 2', 'pata_kit2_6', 1500},
        {'Waffle Maker', 'pata_kit2_7', 1500},
        {'Waffle Maker (Open)', 'pata_kit2_9', 1501},
        {'Pressure Cooker', 'pata_kit2_8', 1500},
        {'Handheld Mixer', 'pata_kit2_10', 1500},
        {'Rice Cooker', 'pata_kit2_11', 1500},
        {'Coffee Maker 2', 'pata_kit2_12', 1500},
        {'Juicer', 'pata_kit2_13', 1500},
        {'Blender', 'pata_kit2_14', 1500},
        {'Mixer With Bowl', 'pata_kit2_16', 1500},
        {'Sugar Bag', 'pata_kit2_17', 1000},
        {'Sugar Cup', 'pata_kit2_18', 1000},
        {'Egg Bowl', 'pata_kit2_19', 1000},
        {'Cookie Jar', 'pata_kit2_20', 1000},
        {'Tabletop Stove', 'pata_kit2_21', 1500},
        {'Coffee Pot 2', 'pata_kit2_22', 1500},
        {'Toaster Oven', 'pata_kit2_23', 1500},
        {'Skillet Food', 'pata_kit2_24', 1500},
        {'Overhead Cabinet', 'pata_kit2_25', 1700},
        {'Overhead Cabinet 2', 'pata_kit2_33', 1700},
        {'Floor Cabinet', 'pata_kit2_26', 1700},
        {'Floor Cabinet Shelf', 'pata_kit2_27', 1700},
        {'Blue Pot', 'pata_kit2_28', 1000},
        {'Fridge 4', 'pata_kit2_29', 1700},
        {'Oven', 'pata_kit2_30', 200},
        {'Oven Coffee Combo Maker Critical Hit', 'pata_kit2_31', 1700},
        {'Kitchen Stool', 'pata_kit2_32', 1500},
        {'Cheese Plate', 'pata_kit2_34', 800},
        {'Floor Cabinet 2', 'pata_kit2_35', 1500},
        {'Floor Cabinet 3', 'pata_kit2_36', 1500},
        {'Wooden Boards', 'pata_kit2_37', 800},
        {'Tall Cabinet', 'pata_kit2_38', 1500},
        {'Smoke Sucker', 'pata_kit2_39', 1500},
        {'Sink Cabinet Combo', 'pata_kit2_40', 1500},
        {'Hand Soap', 'pata_kit2_41', 400},
        {'Plate Stack', 'pata_kit2_42', 400},
        {'Rolling Pin Set', 'pata_kit2_43', 400},
        {'Wine Fridge', 'pata_kit2_45', 1500},
        {'Stove Cabinet Combo', 'pata_kit2_46', 1500},
        {'Onion Platter', 'pata_kit2_47', 400},
        {'Cutting Board', 'pata_kit2_48', 400},
        {'Wine Rack', 'pata_kit2_49', 1500},
        {'Kitchen Shelf', 'pata_kit2_50', 1500},
        {'Kitchen Shelf Small', 'pata_kit2_51', 1000},
        {'Pizza Maker', 'pata_kit2_55', 1000},
        {'Generic Food 1', 'pata_kit2_56', 350},
        {'Generic Food 2', 'pata_kit2_57', 350},
        {'Generic Food 3', 'pata_kit2_58', 350},
        {'Generic Food 4', 'pata_kit2_59', 350},
        {'Coffee Pods', 'pata_kit2_60', 350},
        {'Bread Loaf', 'pata_kit2_61', 350},
        {'Cereal Box', 'pata_kit2_62', 350},
        {'Generic Food 5', 'pata_kit2_63', 350},
        {'Generic Food 6', 'pata_kit2_64', 350},
        {'Canned Foods', 'pata_kit2_65', 350},
        {'Wooden Utensil Set', 'pata_kit2_66', 350},
        {'Generic Food 7', 'pata_kit2_67', 400},
        {'Spice Rack', 'pata_kit2_68', 400},
        {'Plate Holder', 'pata_kit2_69', 400},
        {'Utensil Rack', 'pata_kit2_70', 400},
        {'Pottery Rack', 'pata_kit2_71', 400},
        {'kitchen cabinet', 'dnxprops_furniture_kitchencabinet01_a', 400},
        {'kitchen cabinet 2', 'dnxprops_furniture_kitchencabinet01_b', 400},
        {'kitchen cabinet 3', 'dnxprops_furniture_kitchencabinet02_a', 400},
        {'kitchen cabinet 4', 'dnxprops_furniture_kitchencabinet02_b', 400},
        {'kitchen cabinet 5', 'dnxprops_furniture_kitchencabinet03_a', 400},
        {'kitchen cabinet 6', 'dnxprops_furniture_kitchencabinet03_b', 400},
        {'kitchen cabinet 7', 'dnxprops_furniture_kitchencabinet04_a', 400},
        {'kitchen cabinet 8', 'dnxprops_furniture_kitchencabinet04_b', 400},
        {'kitchen cabinet 9', 'dnxprops_furniture_kitchencabinet05_a', 400},
        {'kitchen cabinet 10', 'dnxprops_furniture_kitchencabinet05_b', 400},
        {'kitchen cabinet 11', 'dnxprops_furniture_kitchencabinet06_a', 400},
        {'kitchen cabinet 12', 'dnxprops_furniture_kitchencabinet06_b', 400},
        {'kitchen cabinet 13', 'dnxprops_furniture_kitchencabinet07_a', 400},
        {'kitchen cabinet 14', 'dnxprops_furniture_kitchencabinet07_b', 400},
        {'kitchen cabinet 15', 'dnxprops_furniture_kitchencabinet08_a', 400},
        {'kitchen cabinet 16', 'dnxprops_furniture_kitchencabinet08_b', 400},
        {'kitchen cabinet 17', 'dnxprops_furniture_kitchencabinet09_a', 400},
        {'kitchen cabinet 18', 'dnxprops_furniture_kitchencabinet09_b', 400},
        {'kitchen cabinet 19', 'dnxprops_furniture_kitchencabinet10_a', 400},
        {'kitchen cabinet 20', 'dnxprops_furniture_kitchencabinet10_b', 400},
        {'kitchen cabinet 21', 'dnxprops_furniture_kitchencabinet11_a', 400},
        {'kitchen cabinet 22', 'dnxprops_furniture_kitchencabinet11_b', 400},
        {'kitchen cabinet 23', 'dnxprops_furniture_kitchencabinet12_a', 400},
        {'kitchen cabinet 24', 'dnxprops_furniture_kitchencabinet12_b', 400},
        {'kitchen cabinet 25', 'dnxprops_furniture_kitchencabinet13_a', 400},
        {'kitchen cabinet 26', 'dnxprops_furniture_kitchencabinet13_b', 400},
        {'Dish Washer', 'dnxprops_furniture_kitchendishwasher_a', 700},
        {'Dish Washer 2', 'dnxprops_furniture_kitchendishwasher_b', 400},
        {'Fridge Large', 'dnxprops_furniture_kitchenfridgelarge01_a', 400},
        {'Kitchen Oven', 'dnxprops_furniture_kitchenoven01_a', 400},
        {'Kitchen Oven 2', 'dnxprops_furniture_kitchenoven01_b', 400},
        {'kitchen Oven 3', 'dnxprops_furniture_kitchenoven02_a', 400},
        {'kitchen Oven 4', 'dnxprops_furniture_kitchenoven02_b', 400},
      }
    },

    {
      name = 'Bathroom',

      items = {
        {'Towel', 'prop_shower_towel', 1500},
        {'Towel 2', 'p_shower_towel_s', 1500},
        {'Towel 3', 'v_res_mbtowel', 1500},
        {'Towel 4', 'v_res_mbtowelfld', 1500},
        {'Shower rack', 'prop_shower_rack_01', 1500},
        {'Bath', 'v_res_mbath', 1500},
        {'Bathtub', 'apa_mp_h_bathtub_01', 1500},
        {'Lotion1', 'prop_beach_lotion_01', 1500},
        {'Lotion2', 'prop_beach_lotion_02', 1500},
        {'Lotion3', 'prop_beach_lotion_03', 1500},
        {'Clippers', 'prop_clippers_01', 1500},
        {'Toilet 1', 'prop_toilet_01', 1500},
        {'Toilet 2', 'prop_ld_toilet_01', 1500},
        {'Toilet 3', 'pata_2bathroom43', 100},
        {'Soap', 'prop_soap_disp_01', 1500},
        {'Sink 1', 'prop_sink_02', 1500},
        {'Sink 2', 'pata_2bathroom1', 1500},
        {'Sink 3', 'prop_sink_04', 1500},
        {'Sink 4', 'prop_sink_05', 1500},
        {'Sink 5', 'prop_sink_06', 1500},
        {'Sink 6', 'v_res_mbsink', 1500},
        {'Sink 7', 'pata_2bathroom5', 1500},
        {'Sink 8', 'pata_2bathroom7', 1500},
        {'Sink 9', 'pata_2bathroom34', 1500},
        {'Sink 10', 'pata_2bathroom42', 1500},
        {'Handdry', 'prop_handdry_01', 1500},
        {'Handdry 2', 'prop_handdry_02', 1500},
        {'Glass Lotion Bottle', 'v_res_r_lotion', 300},
        {'Glass Bubblebath Bottle', 'v_res_r_bublbath', 300},
        {'Glass Perfume Bottle', 'v_res_r_perfume', 300},
        {'Styling Gel', 'v_serv_bs_gelx3', 200},
        {'Toiletpaper Roll', 'v_serv_bs_looroll', 100},
        {'Laundry Hamper', 'pata_2bathroom2', 1500},
        {'Robe', 'pata_2bathroom3', 1500},
        {'Bathtub 2', 'pata_2bathroom4', 1500},
        {'Soap Bottle', 'pata_2bathroom6', 300},
        {'Barn Design Shelf', 'pata_2bathroom8', 1500},
        {'Toilet 3', 'pata_2bathroom9', 1500},
        {'Barn Design Shelf Butt Wipe Holder', 'pata_2bathroom10', 1500},
        {'Hand Bath', 'pata_2bathroom11', 1500},
        {'Standing Hand Bath', 'pata_2bathroom12', 1500},
        {'Bath Salts', 'pata_2bathroom13', 1500},
        {'Modern Wall Art', 'pata_2bathroom14', 1500},
        {'Standing Mirror', 'pata_2bathroom15', 50000}, -- Price for usability
        {'Curtain Bath', 'pata_2bathroom16', 1500},
        {'Mini Stool', 'pata_2bathroom17', 1500},
        {'Mini Stool (White)', 'pata_2bathroom31', 1500},
        {'Jewelry Plate', 'pata_2bathroom18', 1500},
        {'Bathroom Decor', 'pata_2bathroom19', 300},
        {'Bathroom Decor 2', 'pata_2bathroom37', 300},
        {'Bathroom Decor 3', 'pata_2bathroom39', 300},
        {'Bathroom Decor 4', 'pata_2bathroom48', 600},
        {'Wooden Rack', 'pata_2bathroom22', 700},
        {'Wooden Rack 2', 'pata_2bathroom24', 1399},
        {'Towel Rack', 'pata_2bathroom23', 1300},
        {'Bath Tray', 'pata_2bathroom26', 1300},
        {'Butt Wipe Holder (Wall Edition)', 'pata_2bathroom27', 300},
        {'Tooth Brush Set', 'pata_2bathroom28', 799},
        {'Wall Mirror', 'pata_2bathroom29', 50000}, -- Price for usability
        {'Scale Of Weighing', 'pata_2bathroom30', 800},
        {'Double Drawer', 'pata_2bathroom32', 1200},
        {'TP Tower Holder', 'pata_2bathroom44', 1110},
        {'TP Tower Stand', 'pata_2bathroom33', 1111},
        {'Flower Floor Mat', 'pata_2bathroom35', 300},
        {'Floor Mat', 'pata_2bathroom36', 400},
        {'Wall Shower', 'pata_2bathroom38', 1509},
        {'Wall Shower 2', 'pata_2bathroom41', 1510},
        {'Wall Shower 3', 'pata_2bathroom46', 1500},
        {'Wall Rag', 'pata_2bathroom40', 1400},
        {'Standing Shower', 'pata_2bathroom45', 1500},
        {'Clothes Hamper', 'pata_2bathroom47', 400},
        {'TP Holder', 'pata_2bathroom49', 400},
        {'Towel Holder', 'pata_2bathroom50', 500},
        {'Bathroom cabinet', 'dnxprops_furniture_bathroomcabinet01_a', 5000}, -- Make useable maybe 10kg
        {'Bathroom cabinet 2', 'dnxprops_furniture_bathroomcabinet01_b', 5000}, -- Make useable maybe 10kg
        {'Bathroom cabinet 3', 'dnxprops_furniture_bathroomcabinet01_c', 5000}, -- Make useable maybe 10kg
        {'Bathroom Tub', 'dnxprops_furniture_bathtub02_a', 2500},
      }
    },

    {
      name = 'Bins',

      items = {
        {'Bin 1', 'prop_cs_bin_02', 1000},
        {'Bin 2', 'prop_cs_bin_03', 1000},
        {'Bin 3', 'prop_fbibombbin', 1000},
        {'Bin 4', 'prop_rub_binbag_sd_01', 1000},
        {'Bin 5', 'prop_bin_11a', 1500},
        {'Bin 6', 'prop_bin_04a', 1500},
        {'Bin 7', 'prop_bin_07a', 1500},
        {'Bin 8', 'prop_bin_06a', 1500},
        {'Bin 9', 'prop_bin_10b', 1500},
        {'Bin 10', 'prop_bin_11b', 1500},
        {'Large bin', 'prop_bin_13a', 1500},
      }
    },

    {
      name = 'More Usable Storage',

      items = {
        {'Locker (75kg)', 'p_cs_locker_01_s', 21500},
        {'Locker 2 (35kg)', 'p_cs_locker_02', 8700},
        {'Locker 3 (75kg)', 'p_cs_locker_01', 21500},
        {'Lester Crate (15kg)', 'prop_cs_lester_crate', 4000},
        {'Champ Box (10kg)', 'prop_champ_box_01', 3200},
        {'Side Unit (100kg)', 'v_res_fh_sidebrdlngb', 31100},
        {'Side Unit 2 (100kg)', 'v_res_fh_sidebrddine', 31100},
        {'Side unit (50kg)', 'v_res_d_sideunit', 15600},
        {'Bed table (15kg)', 'v_res_mbbedtable', 4000},
        {'TV stand (5kg)', 'v_res_j_tvstand', 2100},
		    {'TV stand 2 (20kg)', 'v_res_tre_tvstand', 6300}, -- TODO: Make storage item
        {'Dresser (15kg)', 'v_res_mbdresser', 4000},
        {'Ottoman (20kg)', 'v_res_mbottoman', 6300},
        {'Console (30kg)', 'v_res_mconsolemod', 7500},
        {'Cupboard (40kg)', 'v_res_mcupboard', 8500},
        {'Chest (25kg)', 'v_res_mdchest', 6500},
        {'Cabinet 3 (55kg)', 'v_res_msoncabinet', 17000},
        {'Cabinet 4 (30kg)', 'prop_cabinet_02b', 7500},
        {'Cabinet 5 (35kg)', 'prop_cabinet_01b', 8700},
        {'Armoire (25kg)', 'v_res_m_armoire', 6500},
        {'Sidetable (5kg)', 'v_res_m_sidetable', 2100},
        {'Bedsidetable (10kg)', 'v_res_tre_bedsidetable', 3200},
        {'Bookshelf (15kg)', 'v_res_tre_smallbookshelf', 4000},
        {'Storage box (100kg)', 'v_res_tre_storagebox', 31100},
		    {'Storage Crate (10kg)', 'prop_drop_armscrate_01b', 3200}, -- TODO: Make storage item
        {'Storage unit (55kg)', 'v_res_tre_storageunit', 16000},
        {'Woodtable (35kg)', 'v_res_tre_wdunitscuz', 8700},
        {'Devin Box (40kg)', 'prop_devin_box_closed', 12500},
        {'Safe 1 (15kg)', 'prop_ld_int_safe_01', 4000},
        {'Safe 2 (25kg)', 'p_v_43_safe_s', 6500},
        {'Woodtable (250kg)', 'prop_mil_crate_02', 75000},
        {'Dressing table (5kg)', 'v_res_d_dressingtable', 2100},
        {'Cabinet (35kg)', 'prop_fbibombfile', 8700},
        {'Cabinet 2 (250kg)', 'v_res_cabinet', 75000},
        {'Weapon Box (400kg)', 'p_secret_weapon_02', 85500},
        {'Gun Case (5kg)', 'prop_gun_case_02', 3500},
        {'Coffin (60kg)', 'prop_coffin_02b', 17500},
        {'Spanish Box (5kg)', 'v_res_m_spanishbox', 1500},
        {'Bong Display Cabinet (35kg)', 'prop_disp_cabinet_01', 6000},
        {'Jewelry Box (5kg)', 'v_res_jewelbox', 1500}, -- TODO: Make storage item, 1-5kg, made for vangi jewelry
        {'Duffel Bag (5kg)', 'prop_cs_duffel_01', 2500}, -- TODO: Make storage item, 5kg?
      }
    },

    {
      name = 'Accessory',

      items = {
        {'Watch', 'p_watch_02_s', 750},
        {'Watch 2', 'p_watch_01_s', 750},
        {'Cigar pack', 'p_cigar_pack_02_s', 750},
        {'Cigar pack 2', 'p_fag_packet_01_s', 750},
        {'Wallet', 'prop_ld_wallet_01_s', 750},
        {'Handbag', 'prop_ld_handbag_s', 750},
        {'Bag', 'prop_m_pack_int_01', 750},
        {'Bag 2', 'prop_cs_heist_bag_02', 750},
        {'Nigel Bag', 'prop_nigel_bag_pickup', 750},
        {'Suitcase', 'prop_ld_suitcase_01', 75},
        {'Ironing Board', 'v_ret_fh_ironbrd', 500},
      }
    },


    {
      name = 'Activity',

      items = {
        {'Pool Cue Rack', 'prop_pool_rack_01', 2000},
        {'Pool Table', 'prop_pooltable_02', 50000},
        {'Dart Board', 'prop_dart_bd_cab_01', 10000},
        {'Arcade Game', 'prop_arcade_01', 5000},
      }
    },

    {
      name = 'Glass/Window',

      items = {
        {'Window with blinds', 'v_61_ktn_mesh_windows', 2000 },
        {'Window with White blinds', 'v_ilev_cor_windowsmash', 2000 },
        {'Highend Windows', 'prop_fncglass_01a', 2000 },
        {'Small Sized Glass', 'hei_prop_yah_glass_07', 2000 },
        {'Medium Sized Glass', 'hei_prop_yah_glass_10', 2000 },
        {'Large Sized Glass', 'hei_prop_yah_glass_05', 2000 },
        {'Booth Glass', 'xm_prop_lab_booth_glass03', 2000 },
        {'Booth Glass 2', 'xm_prop_lab_booth_glass05', 2000 },
        {'Old Shop Glass', 'prop_rcyl_win_03', 2000 },
      }
    },

    {
      name = 'Floors',

      items = {
        {'Floor Texture 01', 'floor01', 500, 90},
        {'Floor Texture 02', 'floor02', 500, 90},
        {'Floor Texture 03', 'floor03', 500, 90},
        {'Floor Texture 04', 'floor04', 500, 90},
        {'Floor Texture 05', 'floor05', 500, 90},
        {'Floor Texture 06', 'floor06', 500, 90},
        {'Floor Texture 07', 'floor07', 500, 90},
        {'Floor Texture 08', 'floor08', 500, 90},
        {'Floor Texture 09', 'floor09', 500, 90},
        {'Floor Texture 10', 'floor10', 500, 90},
        {'Floor Texture 11', 'floor11', 500, 90},
        {'Floor Texture 12', 'floor12', 500, 90},
        {'Floor Texture 13', 'floor13', 500, 90},
        {'Floor Texture 14', 'floor14', 500, 90},
        {'Floor Texture 15', 'floor15', 500, 90},
        {'Floor Texture 16', 'floor16', 500, 90},
        {'Floor Texture 17', 'floor17', 500, 90},
        {'Floor Texture 18', 'floor18', 500, 90},
        {'Floor Texture 19', 'floor19', 500, 90},
        {'Floor Texture 20', 'floor20', 500, 90},
        {'Floor Texture 21', 'floor21', 500, 90},
        {'Floor Texture 22', 'floor22', 500, 90},
        {'Floor Texture 23', 'floor23', 500, 90},
        {'Floor Texture 24', 'floor24', 500, 90},
        {'Floor Texture 25', 'floor25', 500, 90},
        {'Floor Texture 26', 'floor26', 500, 90},
        {'Floor Texture 27', 'floor27', 500, 90},
        {'Floor Texture 28', 'floor28', 500, 90},
        {'Floor Texture 29', 'floor29', 500, 90},
        {'Floor Texture 30', 'floor30', 500, 90},
      }
    },


    {
      name = 'Small Width Walls',

      items = {
        {'Small Wall Texture 01', 'bzzz_prop_build_wall_a1', 500, 90},
        {'Small Wall Texture 02', 'bzzz_prop_build_wall_a2', 500, 90},
        {'Small Wall Texture 03', 'bzzz_prop_build_wall_a3', 500, 90},
        {'Small Wall Texture 04', 'bzzz_prop_build_wall_a4', 500, 90},
        {'Small Wall Texture 05', 'bzzz_prop_build_wall_a5', 500, 90},
        {'Small Wall Texture 06', 'bzzz_prop_build_wall_a6', 500, 90},
        {'Small Wall Texture 07', 'halfwall01', 500, 90},
        {'Small Wall Texture 08', 'halfwall02', 500, 90},
        {'Small Wall Texture 09', 'halfwall03', 500, 90},
        {'Small Wall Texture 10', 'halfwall04', 500, 90},
        {'Small Wall Texture 11', 'halfwall05', 500, 90},
        {'Small Wall Texture 12', 'halfwall06', 500, 90},
        {'Small Wall Texture 13', 'halfwall07', 500, 90},
        {'Small Wall Texture 14', 'halfwall08', 500, 90},
        {'Small Wall Texture 15', 'halfwall09', 500, 90},
        {'Small Wall Texture 16', 'halfwall10', 500, 90},
        {'Small Wall Texture 17', 'halfwall11', 500, 90},
        {'Small Wall Texture 18', 'halfwall12', 500, 90},
        {'Small Wall Texture 19', 'halfwall13', 500, 90},
        {'Small Wall Texture 20', 'halfwall14', 500, 90},
        {'Small Wall Texture 21', 'halfwall15', 500, 90},
        {'Small Wall Texture 22', 'halfwall16', 500, 90},
        {'Small Wall Texture 23', 'halfwall17', 500, 90},
        {'Small Wall Texture 24', 'halfwall18', 500, 90},
        {'Small Wall Texture 25', 'halfwall19', 500, 90},
        {'Small Wall Texture 26', 'halfwall20', 500, 90},
        {'Small Wall Texture 27', 'halfwall21', 500, 90},
        {'Small Wall Texture 28', 'halfwall22', 500, 90},
        {'Small Wall Texture 29', 'halfwall23', 500, 90},
        {'Small Wall Texture 30', 'halfwall24', 500, 90},
        {'Small Wall Texture 31', 'halfwall25', 500, 90},
        {'Small Wall Texture 32', 'halfwall26', 500, 90},
        {'Small Wall Texture 33', 'halfwall27', 500, 90},
        {'Small Wall Texture 34', 'halfwall28', 500, 90},
        {'Small Wall Texture 35', 'halfwall29', 500, 90},
        {'Small Wall Texture 36', 'halfwall30', 500, 90},
        {'Small Wall Texture 37', 'halfwall31', 500, 90},
        {'Small Wall Texture 38', 'halfwall32', 500, 90},
        {'Small Wall Texture 39', 'halfwall33', 500, 90},
        {'Small Wall Texture 40', 'halfwall34', 500, 90},
        {'Small Wall Texture 41', 'halfwall35', 500, 90},
        {'Small Wall Texture 42', 'halfwall36', 500, 90},
        {'Small Wall Texture 43', 'halfwall37', 500, 90},
        {'Small Wall Texture 44', 'halfwall38', 500, 90},
        {'Small Wall Texture 45', 'halfwall39', 500, 90},
        {'Small Wall Texture 46', 'halfwall40', 500, 90},

      }
    },

    {
      name = 'Medium Width Walls',

      items = {
        {'Medium Wall Texture 01', 'bzzz_prop_build_wall_b1', 500, 90},
        {'Medium Wall Texture 02', 'bzzz_prop_build_wall_b2', 500, 90},
        {'Medium Wall Texture 03', 'bzzz_prop_build_wall_b3', 500, 90},
        {'Medium Wall Texture 04', 'bzzz_prop_build_wall_b4', 500, 90},
        {'Medium Wall Texture 05', 'bzzz_prop_build_wall_b5', 500, 90},
        {'Medium Wall Texture 06', 'bzzz_prop_build_wall_b6', 500, 90},
        {'Medium Wall Texture 07', 'wall01', 500, 90},
        {'Medium Wall Texture 08', 'wall02', 500, 90},
        {'Medium Wall Texture 09', 'wall03', 500, 90},
        {'Medium Wall Texture 10', 'wall04', 500, 90},
        {'Medium Wall Texture 11', 'wall05', 500, 90},
        {'Medium Wall Texture 12', 'wall06', 500, 90},
        {'Medium Wall Texture 13', 'wall07', 500, 90},
        {'Medium Wall Texture 14', 'wall08', 500, 90},
        {'Medium Wall Texture 15', 'wall09', 500, 90},
        {'Medium Wall Texture 16', 'wall10', 500, 90},
        {'Medium Wall Texture 17', 'wall11', 500, 90},
        {'Medium Wall Texture 18', 'wall12', 500, 90},
        {'Medium Wall Texture 19', 'wall13', 500, 90},
        {'Medium Wall Texture 20', 'wall14', 500, 90},
        {'Medium Wall Texture 21', 'wall15', 500, 90},
        {'Medium Wall Texture 22', 'wall16', 500, 90},
        {'Medium Wall Texture 23', 'wall17', 500, 90},
        {'Medium Wall Texture 24', 'wall18', 500, 90},
        {'Medium Wall Texture 25', 'wall19', 500, 90},
        {'Medium Wall Texture 26', 'wall20', 500, 90},
        {'Medium Wall Texture 27', 'wall21', 500, 90},
        {'Medium Wall Texture 28', 'wall22', 500, 90},
        {'Medium Wall Texture 29', 'wall23', 500, 90},
        {'Medium Wall Texture 30', 'wall24', 500, 90},
        {'Medium Wall Texture 31', 'wall25', 500, 90},
        {'Medium Wall Texture 32', 'wall26', 500, 90},
        {'Medium Wall Texture 33', 'wall27', 500, 90},
        {'Medium Wall Texture 34', 'wall28', 500, 90},
        {'Medium Wall Texture 35', 'wall29', 500, 90},
        {'Medium Wall Texture 36', 'wall30', 500, 90},
        {'Medium Wall Texture 37', 'wall31', 500, 90},
        {'Medium Wall Texture 38', 'wall32', 500, 90},
        {'Medium Wall Texture 39', 'wall33', 500, 90},
        {'Medium Wall Texture 40', 'wall34', 500, 90},
        {'Medium Wall Texture 41', 'wall35', 500, 90},
        {'Medium Wall Texture 42', 'wall36', 500, 90},
        {'Medium Wall Texture 43', 'wall37', 500, 90},
        {'Medium Wall Texture 44', 'wall38', 500, 90},
        {'Medium Wall Texture 45', 'wall39', 500, 90},
        {'Medium Wall Texture 46', 'wall40', 500, 90},
      }
    },

    {
      name = 'Large Width Walls',

      items = {
        {'Large Wall Texture 01', 'bzzz_prop_build_wall_c1', 500, 90},
        {'Large Wall Texture 02', 'bzzz_prop_build_wall_c2', 500, 90},
        {'Large Wall Texture 03', 'bzzz_prop_build_wall_c3', 500, 90},
        {'Large Wall Texture 04', 'bzzz_prop_build_wall_c4', 500, 90},
        {'Large Wall Texture 05', 'bzzz_prop_build_wall_c5', 500, 90},
        {'Large Wall Texture 06', 'bzzz_prop_build_wall_c6', 500, 90},
        {'Large Wall Texture 07', 'longwall01', 500, 90},
        {'Large Wall Texture 08', 'longwall02', 500, 90},
        {'Large Wall Texture 09', 'longwall03', 500, 90},
        {'Large Wall Texture 10', 'longwall04', 500, 90},
        {'Large Wall Texture 11', 'longwall05', 500, 90},
        {'Large Wall Texture 12', 'longwall06', 500, 90},
        {'Large Wall Texture 13', 'longwall07', 500, 90},
        {'Large Wall Texture 14', 'longwall08', 500, 90},
        {'Large Wall Texture 15', 'longwall09', 500, 90},
        {'Large Wall Texture 16', 'longwall10', 500, 90},
        {'Large Wall Texture 17', 'longwall11', 500, 90},
        {'Large Wall Texture 18', 'longwall12', 500, 90},
        {'Large Wall Texture 19', 'longwall13', 500, 90},
        {'Large Wall Texture 20', 'longwall14', 500, 90},
        {'Large Wall Texture 21', 'longwall15', 500, 90},
        {'Large Wall Texture 22', 'longwall16', 500, 90},
        {'Large Wall Texture 23', 'longwall17', 500, 90},
        {'Large Wall Texture 24', 'longwall18', 500, 90},
        {'Large Wall Texture 25', 'longwall19', 500, 90},
        {'Large Wall Texture 26', 'longwall20', 500, 90},
        {'Large Wall Texture 27', 'longwall21', 500, 90},
        {'Large Wall Texture 28', 'longwall22', 500, 90},
        {'Large Wall Texture 29', 'longwall23', 500, 90},
        {'Large Wall Texture 30', 'longwall24', 500, 90},
        {'Large Wall Texture 31', 'longwall25', 500, 90},
        {'Large Wall Texture 32', 'longwall26', 500, 90},
        {'Large Wall Texture 33', 'longwall27', 500, 90},
        {'Large Wall Texture 34', 'longwall28', 500, 90},
        {'Large Wall Texture 35', 'longwall29', 500, 90},
        {'Large Wall Texture 36', 'longwall30', 500, 90},
        {'Large Wall Texture 37', 'longwall31', 500, 90},
        {'Large Wall Texture 38', 'longwall32', 500, 90},
        {'Large Wall Texture 39', 'longwall33', 500, 90},
        {'Large Wall Texture 40', 'longwall34', 500, 90},
        {'Large Wall Texture 41', 'longwall35', 500, 90},
        {'Large Wall Texture 42', 'longwall36', 500, 90},
        {'Large Wall Texture 43', 'longwall37', 500, 90},
        {'Large Wall Texture 44', 'longwall38', 500, 90},
        {'Large Wall Texture 45', 'longwall39', 500, 90},
        {'Large Wall Texture 46', 'longwall40', 500, 90},
      }
    },


    {
      name = 'Doorframes',

      items = {
        {'Small Doorframe Texture 01', 'bzzz_prop_build_wall_d1', 500, 90},
        {'Small Doorframe Texture 02', 'bzzz_prop_build_wall_d2', 500, 90},
        {'Small Doorframe Texture 03', 'bzzz_prop_build_wall_d3', 500, 90},
        {'Small Doorframe Texture 04', 'bzzz_prop_build_wall_d4', 500, 90},
        {'Small Doorframe Texture 05', 'bzzz_prop_build_wall_d5', 500, 90},
        {'Small Doorframe Texture 06', 'bzzz_prop_build_wall_d6', 500, 90},
        {'Small Doorframe Texture 07', 'doorwall01', 500, 90},
        {'Small Doorframe Texture 08', 'doorwall02', 500, 90},
        {'Small Doorframe Texture 09', 'doorwall03', 500, 90},
        {'Small Doorframe Texture 10', 'doorwall04', 500, 90},
        {'Small Doorframe Texture 11', 'doorwall05', 500, 90},
        {'Small Doorframe Texture 12', 'doorwall06', 500, 90},
        {'Small Doorframe Texture 13', 'doorwall07', 500, 90},
        {'Small Doorframe Texture 14', 'doorwall08', 500, 90},
        {'Small Doorframe Texture 15', 'doorwall09', 500, 90},
        {'Small Doorframe Texture 16', 'doorwall10', 500, 90},
        {'Small Doorframe Texture 17', 'doorwall11', 500, 90},
        {'Small Doorframe Texture 18', 'doorwall12', 500, 90},
        {'Small Doorframe Texture 19', 'doorwall13', 500, 90},
        {'Small Doorframe Texture 20', 'doorwall14', 500, 90},
        {'Small Doorframe Texture 21', 'doorwall15', 500, 90},
        {'Small Doorframe Texture 22', 'doorwall16', 500, 90},
        {'Small Doorframe Texture 23', 'doorwall17', 500, 90},
        {'Small Doorframe Texture 24', 'doorwall18', 500, 90},
        {'Small Doorframe Texture 25', 'doorwall19', 500, 90},
        {'Small Doorframe Texture 26', 'doorwall20', 500, 90},
        {'Small Doorframe Texture 27', 'doorwall21', 500, 90},
        {'Small Doorframe Texture 28', 'doorwall22', 500, 90},
        {'Small Doorframe Texture 29', 'doorwall23', 500, 90},
        {'Small Doorframe Texture 30', 'doorwall24', 500, 90},
        {'Small Doorframe Texture 31', 'doorwall25', 500, 90},
        {'Small Doorframe Texture 32', 'doorwall26', 500, 90},
        {'Small Doorframe Texture 33', 'doorwall27', 500, 90},
        {'Small Doorframe Texture 34', 'doorwall28', 500, 90},
        {'Small Doorframe Texture 35', 'doorwall29', 500, 90},
        {'Small Doorframe Texture 36', 'doorwall30', 500, 90},
        {'Small Doorframe Texture 37', 'doorwall31', 500, 90},
        {'Small Doorframe Texture 38', 'doorwall32', 500, 90},
        {'Small Doorframe Texture 39', 'doorwall33', 500, 90},
        {'Small Doorframe Texture 40', 'doorwall34', 500, 90},
        {'Small Doorframe Texture 41', 'doorwall35', 500, 90},
        {'Small Doorframe Texture 42', 'doorwall36', 500, 90},
        {'Small Doorframe Texture 43', 'doorwall37', 500, 90},
        {'Small Doorframe Texture 44', 'doorwall38', 500, 90},
        {'Small Doorframe Texture 45', 'doorwall39', 500, 90},
        {'Small Doorframe Texture 46', 'doorwall40', 500, 90},

        {'Medium Doorframe Texture 01', 'bzzz_prop_build_wall_e1', 500, 90},
        {'Medium Doorframe Texture 02', 'bzzz_prop_build_wall_e2', 500, 90},
        {'Medium Doorframe Texture 03', 'bzzz_prop_build_wall_e3', 500, 90},
        {'Medium Doorframe Texture 04', 'bzzz_prop_build_wall_e4', 500, 90},
        {'Medium Doorframe Texture 05', 'bzzz_prop_build_wall_e5', 500, 90},
        {'Medium Doorframe Texture 06', 'bzzz_prop_build_wall_e6', 500, 90},

        {'Medium Offset Doorframe Texture 01', 'bzzz_prop_build_wall_f1', 500, 90},
        {'Medium Offset Doorframe Texture 02', 'bzzz_prop_build_wall_f2', 500, 90},
        {'Medium Offset Doorframe Texture 03', 'bzzz_prop_build_wall_f3', 500, 90},
        {'Medium Offset Doorframe Texture 04', 'bzzz_prop_build_wall_f4', 500, 90},
        {'Medium Offset Doorframe Texture 05', 'bzzz_prop_build_wall_f5', 500, 90},
        {'Medium Offset Doorframe Texture 06', 'bzzz_prop_build_wall_f6', 500, 90},

        {'Large Doorframe Texture 01', 'bzzz_prop_build_wall_g1', 500, 90},
        {'Large Doorframe Texture 02', 'bzzz_prop_build_wall_g2', 500, 90},
        {'Large Doorframe Texture 03', 'bzzz_prop_build_wall_g3', 500, 90},
        {'Large Doorframe Texture 04', 'bzzz_prop_build_wall_g4', 500, 90},
        {'Large Doorframe Texture 05', 'bzzz_prop_build_wall_g5', 500, 90},
        {'Large Doorframe Texture 06', 'bzzz_prop_build_wall_g6', 500, 90},

        {'Large Offset Doorframe Texture 01', 'bzzz_prop_build_wall_h1', 500, 90},
        {'Large Offset Doorframe Texture 02', 'bzzz_prop_build_wall_h2', 500, 90},
        {'Large Offset Doorframe Texture 03', 'bzzz_prop_build_wall_h3', 500, 90},
        {'Large Offset Doorframe Texture 04', 'bzzz_prop_build_wall_h4', 500, 90},
        {'Large Offset Doorframe Texture 05', 'bzzz_prop_build_wall_h5', 500, 90},
        {'Large Offset Doorframe Texture 06', 'bzzz_prop_build_wall_h6', 500, 90},



      }
    },

  },
}

Citizen.CreateThread(function()
  local base_items = {
    { 'Gunz', 'prop_bl_houseposter_20_st', 5000 },
    { 'Light and Lines', 'prop_bl_houseposter_21_st', 5000 },
    { 'Girlz and Gunz', 'prop_bl_houseposter_22_st', 5000 },
    { 'Gas and Gunz', 'prop_bl_houseposter_23_st', 5000 },
  }

  local colors = {
    'White',
    'Bubblegum',
    'Cotton Candy',
    'Grape Juice',
    'Lilac',
    'Blue',
    'Blue Green',
    'Toothpaste',
    'Forest Green',
    'Seashell',
    'Cherry Red',
    'Khaki',
    'Grey',
    'Beige',
    'Dark Red',
    'Acid',
    'Tangerine',
  }

  for k, category in pairs(config.categories) do
    if category.name == 'Wall Art (Tintable)' then
      for _, item in pairs(base_items) do
        for c_key, c_name in pairs(colors) do
          table.insert(config.categories[k].items, {
            item[1] .. ' - ' .. c_name, item[2], item[3], false, (c_key - 1)
          })
        end
      end
    end
  end
end)

