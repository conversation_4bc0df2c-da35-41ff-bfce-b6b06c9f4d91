<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/5.0.0/normalize.min.css">
    <link rel='stylesheet' href='https://fonts.googleapis.com/css?family=Heebo:100,300,400,500,700,800,900'>
    <link rel="stylesheet" href="./style.css">
  </head>
  <body id="app">
    <main v-if="file != null">
      <section class="cards">
        <div :class="{ card: !holographic, cardholo: holographic}" class="animated">
          <div class="text">{{ text }}</div>
        </div>
      </section>

      <style class="hover"></style>
    </main>

    <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js'></script>
    <script src="https://cfx-nui-blrp_ui/ui/vue.global.prod.js"></script>
    <script src="./app.js"></script>
    <script src="./script.js"></script>
  </body>
</html>
