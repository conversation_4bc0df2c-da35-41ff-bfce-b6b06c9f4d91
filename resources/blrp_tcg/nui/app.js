function initialState() {
  return {
    holographic: false,
    file: null,
    color1: null,
    color2: null,
    text: null,
  }
}

const app = Vue.createApp({
  data() {
    return initialState();
  },

  mounted() {
    window.addEventListener('message', (event) => {
      if(event.data && event.data.action == 'show') {
        this.initialize(event.data);
      }

      if(event.data && event.data.action == 'hide') {
        this.reset();
      }
    });

    document.addEventListener('keydown', (event) => {
      if(event.key == 'Escape') {
        fetch(`https://blrp_tcg/hide`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=UTF-8',
          },
          body: JSON.stringify({})
        });
      }
    })
  },

  methods: {
    reset() {
      Object.assign(this.$data, initialState());
    },

    initialize(data = false) {
      if(data) {
        this.file = data.image;
        this.color1 = data.color1;
        this.color2 = data.color2;
        this.holographic = data.holographic;
        this.text = data.text;
      }

      document.documentElement.style.setProperty('--image', `url('../images/${this.file}.webp')`);
      document.documentElement.style.setProperty('--color1', `#${this.color1}`);
      document.documentElement.style.setProperty('--color2', `#${this.color2}`);
    },
  }
});

const root = app.mount('#app');
