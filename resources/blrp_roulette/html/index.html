<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />

    <script src="DEP/howler.js"></script>
    <script src="DEP/vue.js"></script>
    <script src="DEP/jquery.js"></script>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@3/dark.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@9/dist/sweetalert2.min.js"></script>

    <script>
        Vue.config.productionTip = false;
        Vue.config.devtools = false;
    </script>
</head>

<body>
    <style>
        @font-face {
            font-family: "majestic";
            src: url("ProximaNova.woff") format('woff');
        }

        .eltunes-enter-active,
        .eltunes-leave-active {
            transition: all 0.7s ease;
        }

        .eltunes-enter,
        .eltunes-leave-to,
        .eltunes-leave-active {
            opacity: 0;
        }

        body {
            font-family: majestic;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            overflow: hidden;
        }

        :root {
            --background: linear-gradient(0deg, rgba(10, 10, 10, 0.80) 0%, rgba(10, 10, 10, 0.75) 100%);
            --entryBack: linear-gradient(0deg, rgba(35, 35, 35, 0.55) 0%, rgba(65, 65, 65, 0.45) 100%);
            --entryBackHover: linear-gradient(0deg, rgba(65, 65, 65, 0.55) 0%, rgba(90, 90, 90, 0.45) 100%);
            --blueColorFaded: rgba(0, 119, 255, 0.75);
            --blueColorHard: rgba(0, 119, 255, 0.95);
            --blue: rgb(40, 90, 226);
            --bluefade: rgba(40, 90, 226, 0.55);
            --lightgreen: rgb(0, 255, 106);
            --unselected: rgb(220, 220, 220);
        }

        ::-webkit-scrollbar {
            width: .5vw;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(14, 14, 14, 0.9);
        }

        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.4);
        }

        .menuHeader {
            display: flex;
            margin-bottom: 0;
            background-color: var(--blueColorHard);
            font-variant: small-caps;
            color: white;
            font-size: 1.5vw;
            align-items: center;
            border-bottom: solid 2px rgba(255, 255, 255, 0.5);
            padding: 1% 3%;
            position: relative;
        }

        * {
            text-shadow: 1px 1px 1px rgb(0, 0, 0);
        }

        .kozeprehuz {
            position: absolute;
            height: 100vh;
            width: 100vw;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .currentChips,
        .currentBet {
            position: absolute;
            left: 25%;
            bottom: 10%;
            font-size: 1.5vw;
            color: var(--unselected);
        }

        .currentChips .szoveg,
        .currentBet .szoveg {
            position: absolute;
            left: 40%;
            height: 2vw;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.9vw;
            font-weight: bold;
            color: var(--unselected);
        }

        .currentChips .szoveg img,
        .currentBet .szoveg img {
            position: absolute;
            left: -2.8vw;
            width: 2vw;
            height: 2vw;
        }

        .gameInfo {
            position: absolute;
            top: 12%;
            max-width: 50%;
            height: 6vw;
            border-radius: .5vw;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;

            color: var(--unselected);
            font-size: 1vw;
            transform: rotate(-3deg) skew(-3deg);
        }

        .gameInfo .header {
            position: relative;
            font-size: 1.7vw;
            color: var(--unselected);
            font-variant: small-caps;
            width: 100%;
            text-align: center;
        }

        .gameInfo .szoveg {
            font-size: 2.3vw;
            color: yellow;
            width: 100%;
            text-align: center;
        }

        .instructionGuide {
          position: absolute;
          bottom: 30;
          right: 20;
          text-align: right;
          font-weight: bold;
          color: var(--unselected);
        }
    </style>

    <div id="rulettPanel">
        <template>
            <transition name="eltunes">
                <div class="kozeprehuz" v-if="opened">
                    <!-- how many chips player have -->
                    <div class="currentChips">
                        Total Chips
                        <br>
                        <div class="szoveg">
                            <img src="./img/chips.png" alt="">
                            {{formatMoney(currentChips,0)}}
                        </div>
                    </div>


                    <!-- the current(selected) bet amount to place -->
                    <div class="currentBet" style="left: 62%;">
                        Current Bet
                        <br>
                        <div class="szoveg">
                            <img src="./img/chips.png" alt="">
                            {{formatMoney(betAmount,0)}}
                        </div>
                    </div>

                    <div class="gameInfo">
                        <div class="header">{{gameInfo.header}}</div>
                        <div class="szoveg">{{gameInfo.szoveg}}</div>
                    </div>

                    <div class="instructionGuide">
                        <h3>Instructions</h3>
                        <p>
                          Stand up: BACKSPACE</br>
                          Change Camera: E</br>
                          Place Bid: LEFT CLICK</br>
                          Raise Bet: UP ARROW</br>
                          Reduce Bet: DOWN ARROW</br>
                          Custom Bet: SPACE</br>
                        </p>
                    </div>
                </div>
            </transition>
        </template>
    </div>
    <script src="./js/rulett_html.js"></script>
</body>

</html>
