<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />

  <InitDatas>
    <Item>
      <modelName>rtxboat</modelName>
      <txdName>rtxboat</txdName>
      <handlingId>rtxboat</handlingId>
      <gameName>rtxboat</gameName>
      <vehicleMakeName />
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>PREDATOR</audioNameHash>
      <layout>LAYOUT_BOAT_PREDATOR</layout>
      <coverBoundOffsets>STANDARD_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_BOAT_MEDIUM</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_BOAT_CAMERA</cameraName>
      <aimCameraName>BOAT_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BOAT_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.033000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="-0.050000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="-0.050000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.063000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="-0.063000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="-0.030000" y="0.015000" z="-0.046000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.020000" z="-0.041000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.033000" y="0.020000" z="-0.070000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.643000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.113000" y="0.450000" z="0.503000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.113000" y="0.450000" z="0.596000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.113000" y="0.450000" z="0.596000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.050000" z="0.755000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.045000" />
      <PovPassengerCameraOffset x="0.125000" y="0.200000" z="-0.050000" />
      <PovRearPassengerCameraOffset x="0.125000" y="0.200000" z="-0.050000" />
      <vfxInfoName>VFXVEHICLEINFO_BOAT_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.500000" />
      <wheelScaleRear value="0.500000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.922" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999" />
      <flags>FLAG_NO_BOOT FLAG_PEDS_CAN_STAND_ON_TOP FLAG_GEN_NAVMESH FLAG_LAW_ENFORCEMENT FLAG_DONT_SPAWN_IN_CARGEN FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_DONT_SPAWN_AS_AMBIENT</flags>
      <type>VEHICLE_TYPE_BOAT</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_BOAT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_Y_USCG_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards>
        <Item>REWARD_WEAPON_CARBINERIFLE</Item>
        <Item>REWARD_AMMO_CARBINERIFLE</Item>
        <Item>REWARD_STAT_WEAPON</Item>
      </rewards>
      <cinematicPartCamera>
        <Item>VEH_WINDOW_FRONT_LEFT_CAMERA</Item>
        <Item>VEH_WINDOW_FRONT_RIGHT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>BOAT_PREDATOR_FRONT_LEFT</Item>
        <Item>BOAT_PREDATOR_FRONT_RIGHT</Item>
        <Item>BOAT_PREDATOR_REAR_LEFT</Item>
        <Item>BOAT_PREDATOR_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
 </InitDatas>

  <txdRelationships>
    <Item>
      <parent>vehicles_jet_interior</parent>
      <child>predator2</child>
    </Item>
  </txdRelationships>

</CVehicleModelInfo__InitDataList>
