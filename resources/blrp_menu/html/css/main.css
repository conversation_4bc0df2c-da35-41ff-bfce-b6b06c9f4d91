@import url('https://fonts.googleapis.com/css2?family=Maven+Pro:wght@400;500;600;700&display=swap');

* {
	font-family: 'Maven Pro', sans-serif;
	font-size: 12px;
	font-weight: 400;
}


h1 {
	font-weight: normal;
	color: white;
    border: deepskyblue 1px solid !important;
}

div.info {
	margin: 12px 0;
}

div.info * {
	color: white;
	text-shadow: 1px 1px 0 #000000D0;
}

.container {
	position: absolute;
	left: 1000px;
}

.fa-menustyling {
  color: white;
  font-size: 110px;
  height: 150px;
  cursor: pointer;
}

div.menuHolder {
    user-select: none;
    -moz-user-select: none;
    position: absolute;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    top: 30%;
}

svg.icons {
    display: none;
}

svg.menu {
    position: absolute;
    overflow: visible;
    transition: 0.1s;
    transition-timing-function: ease-out;
    filter: drop-shadow(0px 0px 2px rgba(0, 0, 0, .5)); /* Add drop shadow effect */
}

svg.menu.inner {
    transform: scale(0.66) rotate(-10deg);
    opacity: 0;
    visibility: hidden;
}

svg.menu.outer {
    opacity: 0;
    visibility: hidden;
}

svg.menu > g > path {
    fill-opacity: 0.7;
    fill: rgba(36, 38, 60, 0.8);
    stroke-width: .5px;
    stroke: #0e111a solid;
}

svg.menu > g.sector > path {
    cursor: pointer;

}

svg.menu > g.sector > text,
svg.menu > g.sector > use {
    margin-top: 5px;
    font-size: 4px;
    cursor: pointer;
    fill: white;
}

svg.menu > g.sector:hover > path {
    stroke: deepskyblue; /* Add stroke color */
    fill-opacity: 0.55;
    stroke-width: .5px;
    filter: drop-shadow(0px 0px 2px deepskyblue);
}

svg.menu > g.center:hover > circle {
    stroke: deepskyblue; /* Add stroke color */
    fill-opacity: 0.55;
    stroke-width: .5px;
    filter: drop-shadow(0px 0px 2px deepskyblue);
}

svg.menu > g.center > circle {
    cursor: pointer;
    fill: rgba(36, 38, 60, 0.8);
    fill-opacity: 0.85;
}

svg.menu > g.center > text,
svg.menu > g.center > use {
    margin-top: 5px;
    font-size: 4px;
    cursor: pointer;
    fill: white;
}
