--[[AddBoxZone('RugratsManagement', vector3(-955.4986, -1426.72, 0.9822381), 0.5, 0.5, {
  name = 'RugratsManagement',
  heading = 100.0,
  minZ = 0.97,
  maxZ = 1.35,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Rugrats',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('RugratsStorageMain', vector3(-962.5351, -1438.385, 2.160613), 1.0, 2.0, {
  name = 'RugratsStorageMain',
  heading = 112.0,
  minZ = 0.0,
  maxZ = 2.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Rugrats',
      component_id = 'storage',
    },
    {
      event_server = 'core:server:item-durability:useGunRepairTarget',
      icon = 'fa-regular fa-toolbox',
      label = 'Repair Firearms',

      business_name = 'Rugrats',
      component_id = 'firearm-repair',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:item-durability:useGunBreakdown',
      icon = 'fa-regular fa-toolbox',
      label = 'Break Down Firearms',

      business_name = 'Rugrats',
      component_id = 'firearm-repair',
      component_perm = 'crafting',
    },
  },
  distance = 2.5
})

AddBoxZone('RugratsCloset1', vector3(-965.9709, -1424.615, 2.272765), 1.0, 3.0, {
  name = 'RugratsCloset1',
  heading = 112.0,
  minZ = 0.0,
  maxZ = 2.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Rugrats',
      }
    },
  },
  distance = 3.0
})
]]---