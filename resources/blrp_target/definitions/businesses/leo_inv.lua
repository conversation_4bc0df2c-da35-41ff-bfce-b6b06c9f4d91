AddBoxZone('INVBusinessManagment2', vector3(369.2495, -1590.016, 25.64688), 0.7, 0.6, {
  name = 'INVBusinessManagment2',
  heading = 210.0,
  minZ = 25.5,
  maxZ = 26.1,
}, {
  options = {
    {
      event_server = 'core:server:police:openComputer',
      icon = 'fas fa-computer-classic',
      label = 'Police Computer',

      groups = {
        'LEO',
      }
    },

    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'INV Business Management',

      business_name = 'LEO INV',
      component_id = 'management',
    },
  },
  distance = 3.0
})

AddBoxZone('LEOINVStorage', vector3(373.1107, -1591.445, 25.4107), 1.0, 1.5, {
  name = 'LEOINVStorage',
  heading = 320.0,
  minZ = 24.4,
  maxZ = 25.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'LEO INV',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '2'
      },
    },
  },
  distance = 3.0
})
