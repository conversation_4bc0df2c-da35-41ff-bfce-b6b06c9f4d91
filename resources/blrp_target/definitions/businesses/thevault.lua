AddGangBenchOption('The Vault', {
  event_server = 'core:server:restaurants:transformFood',
  icon = 'fa-regular fa-fire-burner',
  label = 'Mix Kavakava Tea',
  auto_order = true,
  food_item_id = 'drink_kavakava_tea',

  groups = {
    'The Vault'
  },
})

AddGangBenchOption('The Vault', {
  event_server = 'core:server:restaurants:transformFood',
  icon = 'fa-regular fa-fire-burner',
  label = 'Make Jello Shot',
  auto_order = true,
  food_item_id = 'drink_jello_shot',

  groups = {
    'The Vault'
  },
})

AddGangBenchOption('The Vault',{
  event_server = 'core:server:restaurant-general:requestRestock',
  icon = 'fa-regular fa-cash-register',
  label = 'Order Stock',

  groups = {
    'The Vault',
  },
})

AddGangBenchOption('The Vault',{
  event_server = 'blrp_core:server:item-store:resolveTargetConfig',
  icon = 'fa-regular fa-shelves',
  label = 'The Vault Supply',

  uid = 'thevault-supply',

  groups = {
    'The Vault',
  },
})

AddBoxZoneAutoname(vector4(229.5669, -1094.915, 131.3328, 265.0), 1.0, 4.0, {
  minZ = 131.1,
  maxZ = 131.5,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(229.5669, -1094.915, 131.3328),
    },
  },
  distance = 2.5,
})
