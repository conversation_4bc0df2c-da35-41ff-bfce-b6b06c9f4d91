-- Management
AddBoxZoneAutoname(vector4(71.08959, 6501.341, 31.25393, 45.0), 0.6, 0.6, {
  minZ = 31.2,
  maxZ = 31.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management - Vice Motorsports',

      business_name = 'Vice Motorsports',
      component_id = 'management',
    },
  },
  distance = 2.5
})

-- Storages
AddBoxZoneAutoname(vector4(73.43328, 6502.047, 31.10054, 45.0), 0.7, 0.7, {
  minZ = 30.5,
  maxZ = 31.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Vice Motorsports',
      component_id = 'storage',

      args = {
        capacity = 100.0,
        uid = '_Safe',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supplies',

      uid = 'Vice-supply',
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(82.54807, 6504.615, 31.74293, 225.0), 0.9, 3.0, {
  minZ = 30.5,
  maxZ = 32.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Vice Motorsports',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '_RearStorage',
      },
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(68.29968, 6506.327, 31.34465, 225.0), 0.6, 1.2, {
  minZ = 30.5,
  maxZ = 31.4,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Vice Motorsports',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '_OfficeStorage',
      },
    },
  },
  distance = 2.5
})
