AddBoxZone('MMManagement', vector3(-963.82, -1434.55, 1.18), 1.6, 0.4, {
  name = 'MMManagement',
  heading = 20.0,
  minZ = 0.98,
  maxZ = 1.78,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Mutiny Mob',
      component_id = 'management',
    },
  },
  distance = 2.5
})
AddBoxZone('MMStorage2', vector3(-950.5, -1422.310, 1.184), 1.0, 1.0,{
  name = 'MMStorage2',
  heading = 305.0,
  minZ = 0.9,
  maxZ = 1.7,
},{ 
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Mutiny Mob',
      component_id = 'storage',

      args = {
        capacity = 2000.0,
        named = 'MMStorage2',
      },
    }
  },
  distance = 2.5
})


AddBoxZone('MMStorage1', vector3(-965.16, -1431.32, 1.18), 2.6, 1.0, {
  name = 'MMStorage1',
  heading = 305.0,
  minZ = 0.18,
  maxZ = 1.18,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Mutiny Mob',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        named = 'MMStorage1',
      },
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft WM-29',
    
      item_id = 'wbody|WEAPON_PISTOLXM3',
    
      business_name = 'Mutiny Mob',
      component_id = 'craft-WEAPON_PISTOLXM3',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft 9mm Ammo (20)',
    
      item_id = 'ammo_9mm',
    
      business_name = 'Mutiny Mob',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:item-durability:useGunBreakdown',
      icon = 'fa-regular fa-toolbox',
      label = 'Break Down Firearms',
    
      business_name = 'Mutiny Mob',
      component_id = 'firearm-repair',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Large Storage Crate',
    
      item_id = 'bl_prop_gunbox',
    
      business_name = 'Mutiny Mob',
      component_id = 'crafting',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-spray-can',
      label = 'Craft Stencil',
    
      item_id = 'stencil_mm',
    
      business_name = 'Mutiny Mob',
      component_id = 'craft-stencil_mm',
      component_perm = 'crafting',
    }
  },
  distance = 2.5
})

AddBoxZone('MMCloset', vector3(-965.71, -1424.57, 1.18), 1.0, 0.2, {
  name = 'MMCloset',
  heading = 20.0,
  minZ = 0.18,
  maxZ = 2.38,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Mutiny Mob',
      }
    },
  },
  distance = 3.0
})