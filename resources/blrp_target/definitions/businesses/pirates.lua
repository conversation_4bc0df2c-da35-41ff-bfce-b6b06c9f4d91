--Used to be called (Alamo Sea Pirates)
AddBoxZone('PiratesManagement', vector3(100.4238, 3606.839, 40.53378), 0.5, 0.5, {
  name = 'PiratesManagement',
  heading = 230.0,
  minZ = 40.5,
  maxZ = 41.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Stab City Outlaws',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('PiratesStorageMain', vector3(109.9677, 3618.681, 41.52153), 0.6, 4.0, {
  name = 'PiratesStorageMain',
  heading = 270.0,
  minZ = 39.5,
  maxZ = 41.6,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Stab City Outlaws',
      }
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Stab City Outlaws',
      component_id = 'storage',
    },
  },
  distance = 3.0
})

AddBoxZone('PiratesStorage2', vector3(104.8074, 3607.409, 39.96367), 0.75, 0.72, {
  name = 'PiratesStorage2',
  heading = 180.0,
  minZ = 39.485,
  maxZ = 40.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Stab City Outlaws',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 100.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('PiratesFridge', vector3(101.7639, 3604.665, 39.5237), 0.7, 1.3, {
  name = 'PiratesFridge',
  heading = 90.0,
  minZ = 39.5237,
  maxZ = 40.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Stab City Outlaws',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('PiratesCrafting', vector3(106.2124, 3607.409, 39.48685), 1.0, 2.075, {
  name = 'PiratesCrafting',
  heading = 180.0,
  minZ = 39.485,
  maxZ = 41.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-toolbox',
      label = 'Crafting',

      business_name = 'Stab City Outlaws',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})
