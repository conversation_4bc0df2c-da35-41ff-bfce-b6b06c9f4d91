disableContext(true)

AddBoxZone('RanchManagement', vector3(1392.79, 1146.89, 117.54), 0.4, 0.4, {
  name = 'RanchManagement',
  heading = 345.0,
  minZ = 117.14,
  maxZ = 117.74,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Petrovich Cartel',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('RanchStorage1', vector3(1372.63, 1133.87, 106.66), 1.6, 0.6, {
  name = 'RanchStorage1',
  heading = 0.0,
  minZ = 105.66,
  maxZ = 108.26,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',
      component_perm = 'extra_b',

      business_name = 'Petrovich Cartel',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('RanchStorage2', vector3(1367.88, 1149.07, 106.66), 2.0, 2.0, {
  name = 'RanchStorage2',
  heading = 0.0,
  minZ = 105.66,
  maxZ = 107.86,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Petrovich Cartel',
      component_id = 'storage',

      args = {
        capacity = 2500.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('RanchStorage3', vector3(1400.81, 1156.03, 114.35), 0.6, 1.2, {
  name = 'RanchStorage3',
  heading = 0.0,
  minZ = 113.35,
  maxZ = 115.15,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Petrovich Cartel',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '3'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('RanchStorage4', vector3(1394.529, 1056.758, 114.7574), 2.1, 1.3, {
  name = 'RanchStorage4',
  heading = 180.0,
  minZ = 113.0,
  maxZ = 114.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Petrovich Cartel',
      component_id = 'storage',
      component_perm = 'general',

      args = {
        capacity = 1000.0,
        named = 'b6extFreezer'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('RanchStorage5', vector3(1402.87, 1164.67, 114.57), 1.4, 0.4, {
  name = 'RanchStorage5',
  heading = 0.0,
  minZ = 113.57,
  maxZ = 115.97,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Petrovich Cartel',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('RanchGlobeStorage', vector3(1391.8, 1150.23, 117.52), 0.8, 0.8, {
  name = 'RanchGlobeStorage',
  heading = 0.0,
  minZ = 117.12,
  maxZ = 117.92,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-face-awesome',
      label = 'Stash',

      business_name = 'Petrovich Cartel',
      component_id = 'storage',
      component_perm = 'extra_c',

      args = {
        capacity = 35.0,
        uid = 'RanchGlobeStorage'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('RanchSafe', vector3(1407.53, 1135.38, 117.54), 0.6, 0.8, {
  name = 'RanchSafe',
  heading = 0.0,
  minZ = 116.54,
  maxZ = 117.54,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Petrovich Cartel',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 50.0,
        uid = 'RanchSafe'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('RanchCloset1', vector3(1393.42, 1159.66, 117.54), 1.2, 2.2, {
  name = 'RanchCloset1',
  heading = 0.0,
  minZ = 116.54,
  maxZ = 119.14,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Petrovich Cartel',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('RanchMirror1', vector3(1391.12, 1162.61, 117.54), 1.0, 0.2, {
  name = 'RanchMirror1',
  heading = 0.0,
  minZ = 117.14,
  maxZ = 119.14,
}, {
  options = {
    {
      event_client = 'blrp_character:openBarbershop',
      icon = 'fa-regular fa-face-awesome',
      label = 'Hair and Makeup',
      cloakroom_name = 'clothingshop',

      groups = {
        'Petrovich Cartel',
      }
    },
  },
  distance = 2.5
})

--[[ AddBoxZone('RanchGrinder1', vector3(1460.317, 1044.365, 114.7592), 0.8, 1.6, {
  name = 'RanchGrinder1',
  heading = 180.0,
  minZ = 113.0,
  maxZ = 115.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Grind Raw Meat into Patties (10)',

      item_id = 'food_raw_meat_patty',

      business_name = 'Petrovich Cartel',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})

AddBoxZone('RanchSupply', vector3(1486.346, 1132.766, 114.3711), 0.8, 2.0, {
  heading = 180.0,
  minZ = 113.0,
  maxZ = 115.3,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supplies',

      uid = 'cartel-supply',
    },
  },
  distance = 2.5
}) ]]

AddBoxZoneAutoname(vector4(1362.886, 1143.921, 106.4988, 0.0), 1.0, 0.4, {
  minZ = 106.26,
  maxZ = 106.86,
}, {
  options = {
    {
        event_server = 'core:server:target-backhaul:craft',
        icon = 'fa-regular fa-wrench',
        label = 'Craft Golden AK',

        weekly_craft_limit = 35,
        item_id = 'wbody|WEAPON_ASSAULTRIFLE',
        gun_components = { 'comp_sk_ar_lr' },

        business_name = 'Petrovich Cartel',
        component_id = 'craft-WEAPON_ASSAULTRIFLE',
        component_perm = 'crafting',
     },
     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft Firearm Body',

       item_id = 'firearm_body',

       business_name = 'Petrovich Cartel',
       component_id = 'crafting',
       component_perm = 'crafting',
     },
     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft 7.62x39 Ammo (20)',

       item_id = 'ammo_762x39',

       business_name = 'Petrovich Cartel',
       component_id = 'crafting',
       component_perm = 'crafting',
     },
     {
       event_server = 'core:server:item-durability:useGunBreakdown',
       icon = 'fa-regular fa-toolbox',
       label = 'Break Down Firearms',

       business_name = 'Petrovich Cartel',
       component_id = 'firearm-repair',
       component_perm = 'crafting',
     },
     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft Large Storage Crate',

       item_id = 'bl_prop_gunbox',

      business_name = 'Petrovich Cartel',
      component_id = 'crafting',
      component_perm = 'crafting',
    },
  },
  distance = 2.5
})
