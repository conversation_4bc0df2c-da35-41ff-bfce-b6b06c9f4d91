
AddBoxZone('SpellboundSupply', vector3(-74.80294, -201.7696, 147.7548), 1.0, 1.0, {
    name = 'SpellboundSupply',
    heading = 200.0,
    minZ = 147.0,
    maxZ = 148.0,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-regular fa-dollar-sign',
        label = 'Buy Supplies',

        uid = 'spellbound-supply',
      },
      {
        event_server = 'core:server:restaurants:transformFood',
        icon = 'fa-regular fa-bottle-water',
        label = 'Mix Love Potion',
        auto_order = true,
        food_item_id = 'sb_lovepotion',
  
        groups = {
          'Spellbound Occult',
        }
      },
      {
        event_server = 'core:server:restaurants:transformFood',
        icon = 'fa-regular fa-bottle-water',
        label = 'Mix Sanguine Shot',
        auto_order = true,
        food_item_id = 'sb_sanguine',
  
        groups = {
          'Spellbound Occult',
        }
      },
      {
        event_server = 'core:server:restaurants:transformFood',
        icon = 'fa-regular fa-mug-hot',
        label = 'Brew Spellbound Tea',
        auto_order = true,
        food_item_id = 'sb_tea_c',
  
        groups = {
          'Spellbound Occult',
        }
      },
    },
    distance = 2.5
})

for k, tray_coords in ipairs({
  vector3(-74.78516, -203.5684, 147.747),
}) do
  AddBoxZone('SpellboundTray' .. k, tray_coords, 0.6, 1.0, {
    name = 'SpellboundTray' .. k,
    heading=330,
    minZ=147.0,
    maxZ=148.0
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-utensils',
        label = 'Tray',
        table_coords = tray_coords,
      },
    },
    distance = 2.5
  })
end


AddBoxZone('SpellboundRegister', vector3(-74.03262, -203.5116, 147.8928), 1.0, 1.0, {
    name = 'SpellboundRegister',
    heading = 200.0,
    minZ = 147.0,
    maxZ = 148.0,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-general:cashRegister',
        icon = 'fa-regular fa-cash-register',
        label = 'Use Register',

        groups = {
            'Spellbound Occult'
        }
      },
      {
        event_server = 'core:server:restaurant-general:requestRestock',
        icon = 'fa-regular fa-cash-register',
        label = 'Order Stock',
  
        groups = {
          'Spellbound Occult'
        }
      },
    },
    distance = 2.5
})
