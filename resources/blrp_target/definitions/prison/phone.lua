AddBoxZone('PrisonPhone', vector3(1828.726, 2580.127, 46.014), 2.0, 2.0, {
  name = 'PrisonPhone',
  heading = 178.7,
  minZ = 45.5,
  maxZ = 47.0,
  notifyText = '<i class="fa-regular fa-eye"></i> Phone',
}, {
  options = {
    {
      event_server = 'core:server:prison:checkTime',
      icon = 'fa-solid fa-phone-flip',
      label = 'Check Time Remaining',
    },
    {
      event_server = 'core:server:prison:tryLeave',
      icon = 'fa-solid fa-phone-flip',
      label = 'Leave Prison',
    },
    --[[
    {
      event_server = 'core:server:prison:tryPhoneCall',
      icon = 'fa-solid fa-phone-flip',
      label = 'Place Phone Call',
    },
    ]]
  },
  context_coords = vector3(1828.775, 2579.863, 46.55659),
  distance = 10.0
})
