-- <PERSON><PERSON><PERSON> Door
--AddBoxZone('ZancudoAcademyClockin', vector3(-2173.1, 3256.047, 33.12953), 1.0, 0.5, {
--  name = 'ZancudoAcademyClockin',
--  heading = 145.0,
--  minZ = 31.8,
--  maxZ = 34.5,
--  notifyText = '<i class="fa-regular fa-eye"></i> Zancudo Reception',
--}, {
-- Garage Door
AddBoxZone('ZancudoAcademyClockin', vector3(-2112.0, 3238.32, 32.81), 4.8, 0.4, {
  name = 'ZancudoAcademyClockin',
  heading = 330.0,
  minZ = 31.81,
  maxZ = 35.61,
  notifyText = '<i class="fa-regular fa-eye"></i> Zancudo Storage Room',
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'DOC',

      icon = 'fas fa-clock',
      label = 'Clock in - DOC',

      groups = {
        'Civilian',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD',

      groups = {
        'Civilian',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO',

      groups = {
        'Civilian',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',

      groups = {
        'LEO',
        'DOC',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'BCSO Uniform Wardrobe',
      cloakroom_name = 'bcso'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSPD Uniform Wardrobe',
      cloakroom_name = 'lspd'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'SAHP Uniform Wardrobe',
      cloakroom_name = 'sahp'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Equipment',

      uid = 'leo-equipment',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:vehicle-target:takeEvidenceBin',
      icon = 'fa-regular fa-box',
      label = 'Take Evidence Bin',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'BCSO Supervisor Shop',

      uid = 'leo-bcso-supervisor',

      groups = {
        'sheriff_rank5',
      }
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'LSPD Supervisor Shop',

      uid = 'leo-lspd-supervisor',

      groups = {
        'police_rank5',
      }
    },
  },
  distance = 2.5
})