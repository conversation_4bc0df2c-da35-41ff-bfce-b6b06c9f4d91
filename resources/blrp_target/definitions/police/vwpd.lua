AddBoxZoneAutoname(vector4(611.4774, -19.23192, 88.98615, 70.0), 0.75, 0.9, {
  minZ = 86.8,
  maxZ = 88.8
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-refrigerator',
      label = 'Canteen',

      uid = 'canteen-lspd',
    },
  },
  distance = 2.5
})

-- Downstairs
AddBoxZoneAutoname(vector4(621.1288, 7.90476, 83.69278, 70.0), 0.8, 3.0, {
  minZ = 83.5,
  maxZ = 85.0
}, {
  options = {
    {
      event_server = 'core:server:prison:checkParoleTime',
      icon = 'fa-regular fa-clock-desk',
      label = 'Check Parole Time',
    },
    {
      event_server = 'core:server:takePoliceReport',
      icon = 'far fa-clipboard-list',
      label = 'Make Report',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'SAHP',

      icon = 'fas fa-clock',
      label = 'Clock in - SAHP',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 3.0
})

-- Upstairs
AddBoxZoneAutoname(vector4(vector3(617.5756, 12.47155, 87.74696), 160.0), 0.8, 3.0, {
  minZ = 87.7,
  maxZ = 89.2
}, {
  options = {
    {
      event_server = 'core:server:prison:checkParoleTime',
      icon = 'fa-regular fa-clock-desk',
      label = 'Check Parole Time',
    },
    {
      event_server = 'core:server:takePoliceReport',
      icon = 'far fa-clipboard-list',
      label = 'Make Report',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'SAHP',

      icon = 'fas fa-clock',
      label = 'Clock in - SAHP',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 3.0
})

-- Locker room
local locker_room_options = {
  {
    event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
    icon = 'far fa-tshirt',
    label = 'BCSO Uniform Wardrobe',
    cloakroom_name = 'bcso',

    groups = {
      'LEO',
    },
  },
  {
    event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
    icon = 'far fa-tshirt',
    label = 'LSPD Uniform Wardrobe',
    cloakroom_name = 'lspd',

    groups = {
      'LEO',
    },
  },
  {
    event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
    icon = 'far fa-tshirt',
    label = 'SAHP Uniform Wardrobe',
    cloakroom_name = 'sahp',

    groups = {
      'LEO',
    },
  },
  {
    event_server = 'core:server:target-backhaul:openWardrobePersonal',
    icon = 'far fa-tshirt',
    label = 'Personal Wardrobe',

    groups = {
      'LEO',
    },
  },
}

--questioning tables
AddBoxZone('table1', vector3(617.11, -10.49, 75.04), 0.8, 2.0, {
  name = 'table1',
  heading = 340.0,
  minZ = 74.84,
  maxZ = 75.04,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-solid fa-folders',
      label = 'Open Folder',
      table_coords = vector3(617.11, -10.49, 75.04),
    },
  },
  distance = 2.5
})
AddBoxZone('table2', vector3(628.44, -14.56, 75.04), 0.8, 2.2, {
  name = 'table2',
  heading = 340.0,
  minZ = 74.84,
  maxZ = 75.04,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-solid fa-folders',
      label = 'Open Folder',
      table_coords = vector3(628.44, -14.56, 75.04),
    },
  },
  distance = 2.5
})

-- North lockers
AddBoxZoneAutoname(vector4(609.8333, 14.4362, 88.89946, 160.0), 0.6, 4.4, {
  minZ = 86.8,
  maxZ = 88.9,
}, {
  options = locker_room_options,
  distance = 3.5
})

-- Middle lockers
AddBoxZoneAutoname(vector4(608.8361, 11.65831, 88.90195, 160.0), 1.0, 3.3, {
  minZ = 86.8,
  maxZ = 88.9,
}, {
  options = locker_room_options,
  distance = 3.5
})

-- South lockers
AddBoxZoneAutoname(vector4(607.8063, 8.946904, 88.88725, 160.0), 0.6, 4.4, {
  minZ = 86.8,
  maxZ = 88.9,
}, {
  options = locker_room_options,
  distance = 3.5
})

-- Barber shop
AddBoxZoneAutoname(vector4(607.0118, 15.70068, 87.99621, 160.0), 0.6, 1.1, {
  minZ = 87.2,
  maxZ = 88.8,
}, {
  options = {
    {
      event_client = 'blrp_character:openBarbershop',
      icon = 'fa-regular fa-face-awesome',
      label = 'Hair and Makeup',
      cloakroom_name = 'clothingshop',
    },
  },
  context_coords = vector3(471.4765, -983.343, 31.01078),
  distance = 3.0,
})

-- Equipment rooms
local equipment_room_options = {
  {
    event_server = 'blrp_core:server:item-store:resolveTargetConfig',
    icon = 'far fa-raygun',
    label = 'Police Equipment',

    uid = 'leo-equipment',

    groups = {
      'LEO',
    },
  },
  {
    event_server = 'core:server:vehicle-target:takeEvidenceBin',
    icon = 'fa-regular fa-box',
    label = 'Take Evidence Bin',

    groups = {
      'LEO',
    },
  },
  {
    event_server = 'blrp_core:server:item-store:resolveTargetConfig',
    icon = 'far fa-raygun',
    label = 'Police Tints',

    uid = 'leo-tints',

    groups = {
      'LEO',
    },
  },
  {
    event_server = 'blrp_core:server:item-store:resolveTargetConfig',
    icon = 'far fa-raygun',
    label = 'LSPD Supervisor Shop',

    uid = 'leo-lspd-supervisor',

    groups = {
      'police_rank5',
    }
  },
  {
    event_server = 'blrp_core:server:item-store:resolveTargetConfig',
    icon = 'far fa-raygun',
    label = 'BCSO Supervisor Shop',

    uid = 'leo-bcso-supervisor',

    groups = {
      'sheriff_rank5',
    }
  },
  {
    event_server = 'core:server:target-backhaul:openChest',
    icon = 'far fa-box',
    label = 'Police Locker',

    chest_name = 'locker_storage_char_id_1',
    chest_radius = 3.0,
    chest_weight = 100.0,
    chest_permission = 'police.store_weapons',

    groups = {
      'LEO',
    },
  },
  {
    event_server = 'core:server:id-cards:requestFactionCard',
    icon = 'far fa-id-card',
    label = 'Request LSPD ID Card',
    card_type = 'id_lspd',

    groups = {
      'LSPD'
    }
  },
  {
    event_server = 'core:server:id-cards:requestFactionBadge',
    icon = 'far fa-id-card',
    label = 'Request LSPD Detective Badge',
    card_type = 'prop_lspd_badge',

    groups = {
      'LEO INV'
    }
  },
  {
    event_server = 'core:server:id-cards:requestFactionBadge',
    icon = 'far fa-id-card',
    label = 'Request LSPD Badge',
    card_type = 'prop_lspd2_badge',

    groups = {
      'LSPD'
    }
  },
  {
    event_server = 'core:server:target-backhaul:openLeoTrashcan',
    icon = 'far fa-trash-alt',
    label = 'Secure Item Disposal',
    location = 'Mrpd',

    groups = {
      'LEO',
    },
  },
}

-- North floor 2
AddBoxZoneAutoname(vector4(605.596, 8.129957, 87.99158, 160.0), 1.0, 3.0, {
  minZ = 87.0,
  maxZ = 89.0,
}, {
  options = equipment_room_options,
  distance = 3.0
})

-- West floor 2
AddBoxZoneAutoname(vector4(603.2703, 6.453773, 88.02588, 70.0), 1.0, 3.0, {
  minZ = 87.0,
  maxZ = 89.0,
}, {
  options = equipment_room_options,
  distance = 3.0
})

-- Captain's Office
AddBoxZoneAutoname(vector4(629.291, -4.786152, 87.69091, 70.0), 0.9, 1.2, {
  minZ = 86.8,
  maxZ = 87.9,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'LSPD Command Storage',

      chest_name = 'locker_lspdcommand_mrpd',
      chest_radius = 3.0,
      chest_weight = 1000.0,
      chest_permission = 'police.commandstorage',
    },
  },
  distance = 2.0
})

-- Police computers
for _, v in pairs({
  { vector4(618.2745, 9.091871, 83.47003, 160.0), false },
  { vector4(621.7606, 10.22182, 83.64408, 265.0), false },
  { vector4(620.9062, 12.0914, 83.4604, 160.0), false },
  { vector4(617.0974, 7.861432, 83.47536, 160.0), false },
  { vector4(626.3157, -3.496399, 83.47003, 70.0), false },
  { vector4(622.9585, -11.09952, 83.47003, 70.0), false },
  { vector4(609.0385, 1.394451, 87.63978, 160.0), false },
  { vector4(617.8994, 2.974026, 87.62813, 160.0), false },
  { vector4(618.8577, 4.171815, 87.62228, 160.0), false },
  { vector4(618.1514, 11.66486, 87.61282, 160.0), false },
  { vector4(616.0615, 12.37447, 87.61282, 160.0), false },
  { vector4(633.5945, -8.92978, 87.72085, 70.0), false },
  { vector4(632.9751, -14.59596, 87.63005, 160.0), false },
  { vector4(617.0614, -9.693604, 87.62228, 160.0), false },
  { vector4(618.0134, -8.543806, 87.62813, 160.0), false },
  { vector4(616.6967, -15.54752, 87.63212, 160.0), false },
  { vector4(615.6651, -13.55348, 87.63212, 70.0), false },
  { vector4(617.615, -12.29162, 87.63212, 160.0), false },
  { vector4(626.6575, -30.37085, 87.63793, 160.0), false },
  { vector4(621.5952, -28.54062, 87.63132, 160.0), false },
  { vector4(627.3883, -27.12342, 87.63005, 70.0), false },
  { vector4(624.1106, -14.62934, 74.86623, 160.0), true },
  { vector4(621.5474, -13.71174, 74.86623, 160.0), true },
  { vector4(614.0058, 3.281046, 74.9998, 70.0), true },
  { vector4(614.0007, 7.674602, 74.85905, 160.0), true },
  { vector4(621.712, 12.01001, 74.8737, 70.0), true },
}) do
  local coords, lawyer_option = table.unpack(v)
  local _options = {
    {
      event_server = 'core:server:police:openComputer',
      icon = 'fas fa-computer-classic',
      label = 'Police Computer',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'vrp:server:openCourtComputer',
      icon = 'fas fa-computer-classic',
      label = 'Court Computer',

      groups = {
        'DOJ',
      },
    },
  }

  if lawyer_option then
    table.insert(_options, {
      event_server = 'core:server:lawyer-tickets:generate',
      icon = 'fas fa-fw fa-money-check-edit',
      label = 'Write Lawyer Payment',

      groups = {
        'LEO'
      }
    })
  end

  for _, management in pairs({
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'INV Business Management',

      business_name = 'LEO INV',
      component_id = 'management',
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'SWAT Business Management',

      business_name = 'LEO SWAT',
      component_id = 'management',
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'TMU Business Management',

      business_name = 'LEO TMU',
      component_id = 'management',
    },
  }) do
    table.insert(_options, management)
  end

  AddBoxZoneAutoname(coords, 0.5, 0.6, {
    minZ = coords.z - 0.1,
    maxZ = coords.z + 0.2,
  }, {
    options = _options,
    distance = 3.0
  })
end

local elevatorZones = {
  { name = 'VWPDRoof', coords = vector3(599.6057, -15.5971, 101.4619), minZ = 101.2, maxZ = 101.8, side = 'Left' },
  { name = 'VWPDRoof2', coords = vector3(603.9011, -17.16048, 101.4799), minZ = 101.2, maxZ = 101.8, side = 'Right' },
  { name = 'VWPDBasement', coords = vector3(609.9863, -8.010676, 75.1556), minZ = 74.9, maxZ = 75.5, side = 'Left' },
  { name = 'VWPDBasement2', coords = vector3(614.5069, -9.656052, 75.16592), minZ = 74.9, maxZ = 75.5, side = 'Right' },
  { name = 'VWPDLevelOne', coords = vector3(614.5009, -9.653943, 83.73415), minZ = 83.5, maxZ = 84.1, side = 'Right' },
  { name = 'VWPDLevelOne2', coords = vector3(609.9744, -8.00644, 83.76542), minZ = 83.5, maxZ = 84.1, side = 'Left' },
  { name = 'VWPDLevelTwo', coords = vector3(609.9768, -8.006911, 87.94556), minZ = 87.7, maxZ = 88.3, side = 'Left' },
  { name = 'VWPDLevelTwo2', coords = vector3(614.5429, -9.66883, 87.91659), minZ = 87.7, maxZ = 88.3, side = 'Right' },
}

for _, zone in ipairs(elevatorZones) do
  local zoneName = zone.name
  local zoneCoords = zone.coords
  local side = zone.side

  local options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'VWPDRoof'..side,
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Roof',
      groups = { 'LEO', 'LEO_OffDuty', 'TMU_Internal', 'LSFD', 'DOJ',  },
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'VWPDTwo'..side,
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',
      groups = { 'LEO', 'LEO_OffDuty', 'TMU_Internal', 'LSFD', 'DOJ',  },
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'VWPDOne'..side,
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',
      groups = { 'LEO', 'LEO_OffDuty', 'TMU_Internal', 'LSFD', 'DOJ',  },
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'VWPDBasement'..side,
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Basement',
      groups = { 'LEO', 'LEO_OffDuty', 'TMU_Internal', 'LSFD', 'DOJ',  },
    },
  }

  AddBoxZone(zoneName, zoneCoords, 0.5, 0.5, {
    name = zoneName,
    heading = 70.0,
    minZ = zone.minZ,
    maxZ = zone.maxZ,
    notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
  }, { options = options, distance = 2.5 })
end
