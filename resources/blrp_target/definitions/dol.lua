AddBoxZone('DOLSandyShores', vector3(1700.115, 3784.413, 34.60313), 4.0, 7.0, {
  name = 'DOLSandyShores',
  heading = 34.808,
  minZ = 33.5,
  maxZ = 35.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Sandy Shores City Hall',
}, {
  options = {
    {
      event_client = 'license_shop:client:openInterface',
      icon = 'fa-regular fa-id-card',
      label = 'Purchase Licenses',
    },
    {
      event_server = 'core:server:id-cards:requestCitizenCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request Replacement Citizen Card',
    },
    {
      event_server = 'core:server:id-cards:requestCDLCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request CDL Card',
    },
    {
      event_server = 'core:server:id-cards:requestHuntingCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request Hunting Card',
    },
    {
      event_server = 'core:server:id-cards:requestFishingCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request Fishing Card',
    },
    {
      event_server = 'core:server:plateRequest',
      icon = 'fa-regular fa-rectangle-wide',
      label = 'Apply For Personalized License Plate',
    },
    {
      event_server = 'core:server:target-backhaul:purchasePinkSlip',
      icon = 'fa-regular fa-file-alt',
      label = 'Purchase Vehicle Transfer Slips'
    },
    {
      event_client = 'core:client:target-backhaul:proxyShowPostOffice',
      icon = 'fa-regular fa-envelope',
      label = 'Send mail via GoPostal',
      location = 'sandy'
    },
    {
      event_server = 'core:server:compensation:openChest',
      icon = 'fa-regular fa-treasure-chest',
      label = 'Open Compensation Chest'
    },
    {
      event_server = 'vrp:server:openCourtComputer',
      icon = 'fas fa-computer-classic',
      label = 'Court Computer',

      groups = {
        'DOJ',
      },
    },
    {
      event_server = 'core:server:target-backhaul:changeDOB',
      icon = 'fa-regular fa-computer-classic',
      label = 'Update date of birth',

      filter = function()
        return string.match(exports.blrp_core:me().get('dateofbirth'), '%-01%-01')
      end
    },
    {
      event_server = 'core:server:target-backhaul:grantPilotLicense',
      icon = 'fa-regular fa-plane',
      label = 'Grant Pilot License',

      groups = {
        'sheriff_rank5',
        'police_rank5',
        'sahp_rank2',
        'Blaine County Hawks',
      }
    },
    {
      event_server = 'core:server:businesses:manageWildcard',
      icon = 'fa-regular fa-computer-classic',
      label = 'Business Management',

      filter = function(entity, ray_intersect_coords)
        for _, permissions in pairs(exports.blrp_core:me().get('business_perms')) do
          if permissions.management then
            return true
          end
        end

        return false
      end
    },
  },
  distance = 3.0
})

AddBoxZone('DOLPaletoBay', vector3(-153.075, 6296.399, 31.90631), 3.0, 5.0, {
  name = 'DOLPaletoBay',
  heading = 134.941,
  minZ = 30.5,
  maxZ = 33.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Department of Licensing'
}, {
  options = {
    {
      event_client = 'license_shop:client:openInterface',
      icon = 'fa-regular fa-id-card',
      label = 'Purchase Licenses',
    },
    {
      event_server = 'core:server:id-cards:requestCitizenCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request Replacement Citizen Card',
    },
    {
      event_server = 'core:server:id-cards:requestCDLCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request CDL Card',
    },
    {
      event_server = 'core:server:id-cards:requestHuntingCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request Hunting Card',
    },
    {
      event_server = 'core:server:id-cards:requestFishingCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request Fishing Card',
    },
    {
      event_server = 'core:server:plateRequest',
      icon = 'fa-regular fa-rectangle-wide',
      label = 'Apply For Personalized License Plate',
    },
    {
      event_server = 'core:server:target-backhaul:purchasePinkSlip',
      icon = 'fa-regular fa-file-alt',
      label = 'Purchase Vehicle Transfer Slips'
    },
    {
      event_server = 'core:server:compensation:openChest',
      icon = 'fa-regular fa-treasure-chest',
      label = 'Open Compensation Chest'
    },
    {
      event_server = 'blrp_tablet:server:payTickets',
      icon = 'fa-regular fa-money',
      label = 'Pay Outstanding Tickets'
    },
    {
      event_server = 'vrp:server:openCourtComputer',
      icon = 'fas fa-computer-classic',
      label = 'Court Computer',

      groups = {
        'DOJ',
      },
    },
    {
      event_server = 'core:server:target-backhaul:changeDOB',
      icon = 'fa-regular fa-computer-classic',
      label = 'Update date of birth',

      filter = function()
        return string.match(exports.blrp_core:me().get('dateofbirth'), '%-01%-01')
      end
    },
    {
      event_server = 'core:server:target-backhaul:grantPilotLicense',
      icon = 'fa-regular fa-plane',
      label = 'Grant Pilot License',

      groups = {
        'sheriff_rank5',
        'police_rank5',
        'sahp_rank2',
        'Blaine County Hawks',
      }
    },
    {
      event_server = 'core:server:businesses:manageWildcard',
      icon = 'fa-regular fa-computer-classic',
      label = 'Business Management',

      filter = function(entity, ray_intersect_coords)
        for _, permissions in pairs(exports.blrp_core:me().get('business_perms')) do
          if permissions.management then
            return true
          end
        end

        return false
      end
    },
  },
  distance = 3.0
})

AddBoxZone('CourthouseCanteen', vector3(-535.664, -183.4928, 43.42632), 0.8, 1.2, {
  name = 'CourthouseCanteen',
  heading = 300.0,
  minZ = 43.12,
  maxZ = 43.56,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-burger-soda',
      label = 'Snack Bowl',

      uid = 'doj-canteen-court',

      groups = {
        'DOJ',
      },
    },
  },
  distance = 2.0
})
