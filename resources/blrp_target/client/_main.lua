tTarget = {}
T.bindInstance('main', tTarget)

local debugPolyFiles = {}
local contextDisabledFiles = {}
local current_property = {}

function debugPolys(switch)
  if not GlobalState.is_dev then
    return
  end

  debugPolyFiles[debug.getinfo(2).source] = switch
end

function disableContext(switch)
  if not switch then
    switch = nil
  end

  contextDisabledFiles[debug.getinfo(2).source] = switch
end

-- Shut down zones

local zones_shutdown = {}

RegisterNetEvent('blrp_target:client:shutdownZone', function(zones)
  if type(zones) ~= 'table' then
    zones[zones] = true
  end

  for zone, _ in pairs(zones) do
    zones_shutdown[zone] = true
  end
end)

-- Deconflict for specific peds with actions

local used_ped_models = {}

function pedHasSpecialAssignment(entity)
  return used_ped_models[GetEntityModel(entity)]
end

exports('PedHasSpecialAssignment', pedHasSpecialAssignment)

local Models = {}
local Zones = {}
local targetEntity = nil
local context_distance = 5.0

Citizen.CreateThread(function()
  RegisterKeyMapping('+playerTarget', '(Target) Open target', 'keyboard', 'LMENU') --Removed Bind System and added standalone version
  RegisterCommand('+playerTarget', playerTargetEnable, false)
  RegisterCommand('-playerTarget', playerTargetDisable, false)
  TriggerEvent('chat:removeSuggestion', '/+playerTarget')
  TriggerEvent('chat:removeSuggestion', '/-playerTarget')

  while true do
    Citizen.Wait(100)

    if targetActive and IsPlayerFreeAiming(PlayerId()) then
      targetActive = false
      SendNUIMessage({response = 'closeTarget'})
    end

    exports.blrp_hud:SetWalking(targetActive)
  end
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    if targetActive then
      DisableControlAction(0, 21, true)
      DisableControlAction(1, 21, true)
      DisableControlAction(2, 21, true)
      DisableControlAction(0, 24, true) -- Attack
      DisablePlayerFiring(PlayerPedId(), true)

    end
  end
end)

local nearby_zones = {}

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    if not targetActive then
      nearby_zones = {}
    end

    local has_inside_zone = false

    for _, nearby_zone in pairs(nearby_zones) do
      if nearby_zone.inside then
        has_inside_zone = true
      end
    end

    for _, nearby_zone in pairs(nearby_zones) do
      local on_screen, screen_x, screen_y = GetScreenCoordFromWorldCoord(nearby_zone.coords.x, nearby_zone.coords.y, nearby_zone.coords.z)

      if on_screen and nearby_zone.z_distance < 3.0 and (not has_inside_zone or nearby_zone.inside) then
        SetTextFont(11)
        SetTextScale(0.0, 0.55)

        if nearby_zone.inside then
          SetTextColour(30, 144, 255, 255)
        else
          SetTextColour(128, 128, 128, 255)
        end

        SetTextDropShadow(5, 0, 78, 255, 255)
        SetTextEdge(0, 0, 0, 0, 0)

        BeginTextCommandDisplayText('STRING')
        SetTextCentre(true)
        AddTextComponentSubstringPlayerName('o')
        EndTextCommandDisplayText(screen_x, screen_y)
      end
    end
  end
end)

RegisterNetEvent('menu:forceCloseMenu', function()
  success = false

  SendNUIMessage({response = 'closeTarget'})

  Citizen.SetTimeout(250, function()
    targetActive = false
  end)

end)

RegisterNetEvent('blrp_target:forceClose', function()
  success = false

  SendNUIMessage({response = 'closeTarget'})

  Citizen.SetTimeout(250, function()
    targetActive = false
  end)
end)

local groups_cache = {}
local business_perms_cache = {}

function playerTargetEnable()
  if success then
    success = false
    return
  end

  if exports.blrp_inventory:IsInventoryOpen() then
    return
  end

  local me = exports.blrp_core:me()

  if me.isHandcuffed()
          or me.isZipTied()
          or me.hasInterfaceOpen()
          or me.isBeingCarried()
          or me.isInComa()
          or exports.vrp:IsKnockedOut() then
    return
  end

  if IsPedInAnyVehicle(PlayerPedId()) then
    return
  end

  targetActive = true
  groups_cache = exports.blrp_core:ComputeAllGroups()
  business_perms_cache = me.get('business_perms')
  current_property = exports.housing:GetCurrentHouse()

  SendNUIMessage({response = 'openTarget'})

  while targetActive do
    nearby_zone_idx = 0
    nearby_zones = {}

    local plyCoords = GetEntityCoords(GetPlayerPed(-1))
    local hit, coords, entity = RayCastGamePlayCamera(20.0)

    if hit == 1 then
      for _, zone in pairs(Zones) do
        local has_nearby_zone = false

        nearby_zone_idx = nearby_zone_idx + 1

        local allowed_options = {}

        local zone_distance = nil

        if not zone_distance and type(Zones[_].center) == 'vector3' and #(plyCoords - Zones[_].center) <= zone.targetoptions.distance then
          zone_distance = #(plyCoords - Zones[_].center)
        end

        if not zone_distance and type(Zones[_].center) == 'vector2' then
          zone_distance = #(plyCoords.xy - Zones[_].center)
        end

        if zone_distance and zone_distance <= context_distance and not zones_shutdown[zone.name] then
          for __, option_data in ipairs(Zones[_].targetoptions.options) do
            local allow = true

            if not checkGroupPermission(option_data) then
              allow = false
            end

            if allow and not checkZoneIntersects(option_data.zones, coords) then
              allow = false
            end

            if option_data.filter and not option_data.filter(coords) then
              allow = false
            end

            local _option_data = deepcopy(option_data)

            _option_data._position = Zones[_].center

            if allow then
              table.insert(allowed_options, _option_data)
            end
          end

          if #allowed_options > 0 then
            if zone_distance <= context_distance and zone.targetoptions.context_disable ~= true then
              local nearby_zone_coords = Zones[_].center

              if zone.centerTrue then
                nearby_zone_coords = zone.centerTrue
              end

              if zone.targetoptions.context_coords then
                nearby_zone_coords = zone.targetoptions.context_coords
              end

              nearby_zones[nearby_zone_idx] = {
                coords = nearby_zone_coords,
                center_distance = zone_distance,
                z_distance = math.abs(plyCoords.z - nearby_zone_coords.z),
                inside = false
              }

              has_nearby_zone = true
            end
          end

          if #allowed_options > 0 and zone_distance <= zone.targetoptions.distance then
            if Zones[_]:isPointInside(coords) then
              if has_nearby_zone then
                nearby_zones[nearby_zone_idx].inside = true
              end

              targetEntity = nil
              success = true

              SendNUIMessage({response = 'validTarget', data = allowed_options})

              while success and targetActive do
                local plyCoords = GetEntityCoords(GetPlayerPed(-1))
                local hit, coords, entity = RayCastGamePlayCamera(20.0)

                DisablePlayerFiring(PlayerPedId(), true)

                if exports.blrp_inventory:IsInventoryOpen() then
                  success = false
                end

                if (IsControlJustReleased(0, 24) or IsDisabledControlJustReleased(0, 24)) then
                  SetNuiFocus(true, true)
                  SetCursorLocation(0.5, 0.5)
                elseif not Zones[_]:isPointInside(coords) or #(plyCoords - Zones[_].center) > zone.targetoptions.distance then
                  success = false
                end

                Citizen.Wait(1)
              end

              SendNUIMessage({response = 'leftTarget'})
            end
          end
        end
      end

      if GetEntityType(entity) ~= 0 then
        for model_hash, model_options in pairs(Models) do
          local entity_type = GetEntityType(entity)

          if
            model_hash == GetEntityModel(entity) or
            (model_hash == 'ANY_PED' and entity_type == 1 and IsEntityAPed(entity) and IsPedAPlayer(entity)) or
            (model_hash == 'ANY_PED_LOCAL' and entity_type == 1 and IsEntityAPed(entity) and not IsPedAPlayer(entity)) or
            (model_hash == 'ANY_VEHICLE' and entity_type == 2) or
            (model_hash == 'ANY_OBJECT' and entity_type == 3)
          then
            if checkGroupPermission(model_options) then
              if #(plyCoords - coords) <= model_options.distance then
                if not model_options.required_upright_value or GetEntityUprightValue(entity) >= model_options.required_upright_value then
                  if not model_options.check or model_options.check() then
                    if not model_options.check_on_foot or not IsPedSittingInAnyVehicle(PlayerPedId()) then
                      local allowed_options = filterOptions(model_options.options, entity, coords, me)

                      local allowed_options_count = #allowed_options

                      if allowed_options_count > 0 then
                        success = true

                        SendNUIMessage({ response = 'validTarget', data = allowed_options })

                        local last_time = GetGameTimer()

                        while success and targetActive do
                          local plyCoords = GetEntityCoords(GetPlayerPed(-1))
                          local hit, newCoords, entity = RayCastGamePlayCamera(20.0)

                          DisablePlayerFiring(PlayerPedId(), true)

                          if exports.blrp_inventory:IsInventoryOpen() then
                            success = false
                          end

                          if model_hash == 'ANY_PED_LOCAL' then
                            if entity ~= exports.blrp_core:GetPedWantingDrugs() and not Entity(entity).state.quest_id then
                              TaskTurnPedToFaceEntity(entity, PlayerPedId(), 500)
                              TaskStandStill(entity, 2000)
                            end
                          end

                          if (IsControlJustReleased(0, 24) or IsDisabledControlJustReleased(0, 24)) then
                            SetNuiFocus(true, true)
                            SetCursorLocation(0.5, 0.5)
                          end

                          if GetEntityType(entity) == 0 or #(plyCoords - coords) > model_options.distance then
                            success = false
                            targetEntity = nil
                          else
                            targetEntity = entity
                          end

                          if GetGameTimer() - last_time >= 250 then
                            last_time = GetGameTimer()
                            local new_allowed_options = filterOptions(model_options.options, entity, coords, me)

                            if #new_allowed_options ~= allowed_options_count then
                              success = false
                              targetEntity = nil
                            end
                          end

                          Citizen.Wait(1)
                        end
                        SendNUIMessage({response = 'leftTarget'})
                      end
                    end
                  end
                end
              end
            end
          end
        end
      end

      success = false
    end

    Citizen.Wait(250)
  end
end

function playerTargetDisable()
  if success then
    disableTargetOnSuccessLoss()
    return
  end

  SendNUIMessage({response = 'closeTarget'})

  Citizen.SetTimeout(250, function()
    targetActive = false
  end)
end

function disableTargetOnSuccessLoss()
  Citizen.CreateThread(function()
    while success do
      Citizen.Wait(1)
    end

    playerTargetDisable()
  end)
end

function checkGroupPermission(option_data)
  local function checkBusinessPermission(business_name, permission_name)
    if
      groups_cache['LEO'] and
      (
        permission_name == 'storage' or
        permission_name == 'crafting'
      )
    then
      return true
    end

    if not business_perms_cache[business_name] then
      return false
    end

    -- Coerce fridge to general
    if permission_name == 'fridge' then
      permission_name = 'general'
    end

    -- Handle extra permissions from bitwise flags
    if string.match(permission_name, 'extra') then
      local extra_flags = business_perms_cache[business_name].extra_flags

      if not extra_flags or extra_flags == 0 then
        return false
      end

      return exports.blrp_core:HasAnyBusinessExtraFlags(extra_flags, permission_name)
    end

    return business_perms_cache[business_name][permission_name]
  end

  local tried_check = false

  if option_data.groups then
    tried_check = true

    for _, group in pairs(option_data.groups) do
      if group == 'all' or groups_cache[group] then
        return true
      end

      if string.find(group, '|') and checkBusinessPermission(table.unpack(string.split(group, '|'))) then
        return true
      end
    end
  end

  -- Business specific permissions
  if option_data.business_name and option_data.component_id then
    tried_check = true

    local target_perm = option_data.component_perm or option_data.component_id

    if checkBusinessPermission(option_data.business_name, target_perm) then
      return true
    end
  end

  if not tried_check then
    return true
  end

  return false
end

function checkZoneIntersects(zones, ray_coords)
  if not zones then
    return true
  end

  local intersects = false

  for _, zone in ipairs(zones) do
    if type(zone) == 'string' then
      if not intersects and exports.blrp_zones:IsInsideMatchedZone(zone, false, false, ray_coords) then
        intersects = true
      end
    elseif zone:isPointInside(ray_coords) then
      intersects = true
    end
  end

  return intersects
end

--NUI CALL BACKS

RegisterNUICallback('selectTarget', function(data, cb)
  data = json.decode(data)

  SetNuiFocus(false, false)

  success = false

  Citizen.SetTimeout(250, function()
    targetActive = false
  end)

  local entity_hash = GetEntityModel(targetEntity)

  if not entity_hash or entity_hash == 0 then
    entity_hash = nil
  end

  data.position = GetEntityCoords(targetEntity)
  data.heading = GetEntityHeading(targetEntity)
  data.rotation = GetEntityHeading(targetEntity)
  data.upright_value = GetEntityUprightValue(targetEntity)
  data.entity_hash = entity_hash or data.entity_hash or 0
  data.entity_name = prop_translation_table[data.entity_hash]

  if data.freeze then
    FreezeEntityPosition(targetEntity, true)
  end

  if data.event_client then
    TriggerEvent(data.event_client, targetEntity, data)
  end

  if data.event then -- default to client event (for other target script compatibility)
    TriggerEvent(data.event, targetEntity, data)
  end

  if data.event_server or data.proxied then
    local targetNetId = nil

    if data._position and type(data._position) == 'table' then
      data.position = vector3(data._position.x, data._position.y, data._position.z)
      data._position = nil
    end

    if data.force_networked and not NetworkGetEntityIsNetworked(targetEntity) then
      NetworkRegisterEntityAsNetworked(targetEntity)
    end

    if NetworkGetEntityIsNetworked(targetEntity) then
      targetNetId = NetworkGetNetworkIdFromEntity(targetEntity)
    end

    if GetEntityType(targetEntity) == 1 and IsPedAPlayer(targetEntity) then
      data.target_source = GetPlayerServerId(NetworkGetPlayerIndexFromPed(targetEntity))
    end

    if data.event_server then
      TriggerServerEvent(data.event_server, targetNetId or false, data)
    elseif data.proxied then
      local proxy = string.sub(data.proxied, 1, string.find(data.proxied, '%.') - 1)
      local event = string.sub(data.proxied, string.find(data.proxied, '%.') + 1, #data.proxied)

      _G[proxy][event]({ targetNetId or false, data })
    end
  end
end)

RegisterNUICallback('closeTarget', function(data, cb)
  SetNuiFocus(false, false)

  success = false

  Citizen.SetTimeout(250, function()
    targetActive = false
  end)
end)

--Functions from https://forum.cfx.re/t/get-camera-coordinates/183555/14

function RotationToDirection(rotation)
  local adjustedRotation =
  {
    x = (math.pi / 180) * rotation.x,
    y = (math.pi / 180) * rotation.y,
    z = (math.pi / 180) * rotation.z
  }
  local direction =
  {
    x = -math.sin(adjustedRotation.z) * math.abs(math.cos(adjustedRotation.x)),
    y = math.cos(adjustedRotation.z) * math.abs(math.cos(adjustedRotation.x)),
    z = math.sin(adjustedRotation.x)
  }
  return direction
end

function RayCastGamePlayCamera(distance)
  local cameraRotation = GetGameplayCamRot()
  local cameraCoord = GetGameplayCamCoord()
  local direction = RotationToDirection(cameraRotation)
  local destination =
  {
    x = cameraCoord.x + direction.x * distance,
    y = cameraCoord.y + direction.y * distance,
    z = cameraCoord.z + direction.z * distance
  }
  local a, b, c, d, e = GetShapeTestResult(StartShapeTestRay(cameraCoord.x, cameraCoord.y, cameraCoord.z, destination.x, destination.y, destination.z, -1, PlayerPedId(), 4))
  return b, c, e
end

--Exports

function AddCircleZone(name, center, radius, options, targetoptions)
  if not options.name then
    options.name = name
  end

  Zones[name] = CircleZone:Create(center, radius, options)
  Zones[name].targetoptions = targetoptions
end

function AddBoxZone(name, center, length, width, options, targetoptions, debug_source)
  if not debug_source then
    debug_source = debug.getinfo(2).source
  end

  if type(center) == 'vector4' then
    options.heading = center.w
    center = center.xyz
  end

  if not options.name then
    options.name = name
  end

  if debugPolyFiles[debug_source] then
    options.debugPoly = true
  end

  if contextDisabledFiles[debug_source] then
    targetoptions.context_disable = true
  end

  if not targetoptions.groups then
    targetoptions.groups = {
      'all'
    }
  end

  if options.notifyText then
    options.onPlayerInOut = function(is_inside)
      local action = 'START'

      if not is_inside then
        action = 'END'
      end

      TriggerEvent('mythic_notify:client:PersistentAlert', {
        action = action,
        id = name,
        type = 'inform',
        text = options.notifyText,
        style = {
          ['font-size'] = '18px'
        }
      })
    end
  end

  Zones[name] = BoxZone:Create(center, length, width, options)
  Zones[name].targetoptions = targetoptions
end

local counters = {}
local collisions = {}

function AddBoxZoneAutoname(center, length, width, options, targetoptions)
  local debug_source = debug.getinfo(2)
  local debug_source_ex = string.split(debug_source.source, '/')
  local file = string.gsub(debug_source_ex[#debug_source_ex], '.lua', '')

  if not counters[file] then
    counters[file] = 1
  end

  name = file .. ':' .. debug_source.currentline .. ':' .. counters[file]
  counters[file] = counters[file] + 1

  if collisions[name] then
    print('Target zone name collision', name)
  end

  collisions[name] = true

  AddBoxZone(name, center, length, width, options, targetoptions, debug_source.source)
end

function AddPolyzone(name, points, options, targetoptions)
  if not options.name then
    options.name = name
  end

  if not targetoptions.groups then
    targetoptions.groups = {
      'all'
    }
  end

  Zones[name] = PolyZone:Create(points, options)
  Zones[name].targetoptions = targetoptions
end

function AddTargetModel(models, settings)
  if not settings.groups then
    settings.groups = {
      'all'
    }
  end

  for _, model in pairs(models) do
    if IsModelAPed(model) then
      used_ped_models[model] = true
    end

    local found_existing = false

    -- This shouldn't cause lag, right?  ....
    if Models[model] then
        --table.insert(Models[_model].options, settings.options)
        found_existing = true
    end

    if not found_existing then
      Models[model] = settings
    end
  end
end

function HardSetTargetModel(model, settings)
  Models[model] = settings
end

function RemoveTargetModelOption(model, option_id)
  local removed = false

  for _model, _settings in pairs(Models) do
    for _, option in pairs(_settings.options) do
      if option.id and option.id == option_id then
        table.remove(Models[_model].options, _)
        removed = true
      end
    end
  end

  if not removed then
    print('[Error]', 'Failed to resolve target option ' .. option_id .. ' when removing')
  end
end

function RemoveZone(name)
  if not Zones[name] then return end
  if Zones[name].destroy then
    Zones[name]:destroy()
  end

  Zones[name] = nil
end

function deepcopy(orig)
    local orig_type = type(orig)
    local copy
    if orig_type == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            if orig_key ~= 'zones' and orig_key ~= 'filter' then
              copy[deepcopy(orig_key)] = deepcopy(orig_value)
            end
        end
        setmetatable(copy, deepcopy(getmetatable(orig)))
    else -- number, string, boolean, etc
        copy = orig
    end
    return copy
end

function filterOptions(options, entity, coords, me)
  local allowed_options = {}

  -- Cache vehicle state to reduce overhead
  local state = {}

  -- peds, vehicles
  if GetEntityType(entity) == 1 or GetEntityType(entity) == 2 then
    state = Entity(entity).state
  end

  for _, option_data in ipairs(options) do
    local allow = true

    if not checkGroupPermission(option_data) then
      allow = false
    end

    if allow and not checkZoneIntersects(option_data.zones, coords) then
      allow = false
    end

    local filter_value = nil

    if option_data.canInteract then
      option_data.filter = option_data.canInteract
    end

    if option_data.filter then
      local ray_intersect_distance = #(GetEntityCoords(PlayerPedId()) - coords)

--[[      if type(option_data.filter) == 'table' then
        -- This happens when the resource that has the reference to the function is restarted
        print('[Error]', 'Table passed as filter instead of function for option: Label',
          option_data.label, json.encode(option_data.filter))
      end]]

      filter_value = option_data.filter(entity, coords, ray_intersect_distance, state, me)

      if not filter_value then
        allow = false
      end
    end

    if allow then
      local _option_data = deepcopy(option_data)

      if option_data.filter_pass_value then
        _option_data.filter_value = filter_value
      end

      if option_data.filter_pass_value_as then
        _option_data[option_data.filter_pass_value_as] = filter_value
      end

      -- Label computation function
      if type(option_data.label) == 'function' then
        _option_data.label = option_data.label(entity)
      end

      -- Icon computation function
      if type(option_data.icon) == 'function' then
        _option_data.icon = option_data.icon(entity)
      end

      table.insert(allowed_options, _option_data)
    end
  end

  return allowed_options
end


function AddCookingZone(group_name, zone_name, coords, length, width, heading, minZ, maxZ, hands_washed_cb, base_options, auto_order)
  local cook_options = {
    {
      event_client = 'event:stub',
      icon = 'fa-regular fa-sink',
      label = 'Go wash your hands!',

      groups = {
        group_name
      },

      filter = function()
        return not hands_washed_cb()
      end
    }
  }

  local item_idx = 2

  for _, food_option in pairs(base_options) do
    cook_options[item_idx] = {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-fire-burner',
      label = food_option.label,

      food_item_id = food_option.id,
      auto_order = auto_order,

      groups = {
        group_name
      },

      filter = function()
        return hands_washed_cb()
      end
    }

    item_idx = item_idx + 1
  end
  AddBoxZone(zone_name, coords, length, width, {
    name = zone_name,
    heading = heading,
    minZ = minZ,
    maxZ = maxZ,
  }, {
    options = cook_options,
    distance = 2.5,
  })

end

function AddDrugLabOption(warehouse_business, craftable_items)
  -- Create filter wrapper with gang bench specific checks
  local option_real = {
    filter = function(entity, coords, ray_intersect_distance, state)
      -- Ensure correct entity model
      if GetEntityModel(entity) ~= `bkr_prop_coke_table01a` then
        return false
      end

      -- Check current property business against the required business
      if not current_property or current_property.business_name ~= warehouse_business then

        return false
      end

      local placed_furniture_id = Entity(entity).state.placed_furniture_id

      if not placed_furniture_id or placed_furniture_id <= 0 then
        return false
      end

      return placed_furniture_id
    end
  }

  local options = {
    {
      event_server = 'blrp_core:server:cooking-prepare',
      icon = 'fa-regular fa-box',
      label = 'Add/Remove Ingredients',
      target_items = craftable_items,

      business_name = warehouse_business,
      component_id = 'crafting',

      filter_pass_value = true,
      filter = option_real.filter
    },
    {
      event_server = 'blrp_core:server:cooking-checksolution',
      icon = 'fa-regular fa-box',
      label = 'View/Take Solution',

      business_name = warehouse_business,
      component_id = 'crafting',

      filter_pass_value = true,
      filter = option_real.filter
    },
    {
      event_server = 'blrp_core:server:cooking-changetemp',
      icon = 'fa-solid fa-temperature-arrow-up',
      label = 'Set heat: High',
      heat_setting = "high",

      business_name = warehouse_business,
      component_id = 'crafting',

      filter_pass_value = true,
      filter = option_real.filter
    },
    {
      event_server = 'blrp_core:server:cooking-changetemp',
      icon = 'fa-solid fa-temperature-arrow-up',
      label = 'Set heat: Low',
      heat_setting = "low",

      business_name = warehouse_business,
      component_id = 'crafting',

      filter_pass_value = true,
      filter = option_real.filter
    },
    {
      event_server = 'blrp_core:server:cooking-changetemp',
      icon = 'fa-solid fa-temperature-arrow-down',
      label = 'Set heat: Off',
      heat_setting = "off",

      business_name = warehouse_business,
      component_id = 'crafting',

      filter_pass_value = true,
      filter = option_real.filter
    },
    {
      event_server = 'blrp_core:server:cooking-emergency-stop',
      icon = 'fa-regular fa-octagon',
      label = 'Emergency Stop!',

      filter_pass_value = true,
      filter = option_real.filter
    }
  }
  for o, option in pairs(options) do
    table.insert(any_object_options, option)
  end

end


function AddGangBenchOption(warehouse_business, option)
  -- Create filter wrapper with gang bench specific checks
  local option_real = {
    filter = function(entity, coords, ray_intersect_distance, state)
      -- Ensure correct entity model
      if GetEntityModel(entity) ~= `prop_bl_gangbench` then
        return false
      end

      -- Check current property business against the required business
      if not current_property or current_property.business_name ~= warehouse_business then
        return false
      end

      -- If base option has no filter, return true
      if not option.filter then
        return true
      end

      -- Return base option filter value
      return option.filter(entity, coords, ray_intersect_distance, state)
    end
  }

  -- Copy KVPs from base option so there's no stack overflow caused by filter loop
  for k, v in pairs(option) do
    if k ~= 'filter' then
      option_real[k] = v
    end
  end

  table.insert(any_object_options, option_real)
end



exports('AddCircleZone', AddCircleZone)
exports('AddBoxZone', AddBoxZone)
exports('AddPolyzone', AddPolyzone)
exports('RemoveZone', RemoveZone)

exports('AddTargetModel', AddTargetModel)
exports('RemoveTargetModelOption', RemoveTargetModelOption) -- For removing specific options
exports('HardSetTargetModel', HardSetTargetModel) -- For syncing hot reloads
