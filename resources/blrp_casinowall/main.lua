local inCasino = false
local videoWallRenderTarget = nil
local showBigWin = false

--
-- Video shown on screen. Default is "CASINO_DIA_PL".
--
-- CASINO_DIA_PL    - Falling Diamonds
-- CASINO_HLW_PL    - Falling Skulls
-- CASINO_SNWFLK_PL - Falling Snowflakes
--

local videoType = 'CASINO_DIA_PL'

--
-- Threads
--

function startCasinoThreads()
  local interior = GetInteriorAtCoords(GetEntityCoords(GetPlayerPed(-1)))
  while not IsInteriorReady(interior) do Citizen.Wait(10) end
  RequestStreamedTextureDict('Prop_Screen_Vinewood')

  while not HasStreamedTextureDictLoaded('Prop_Screen_Vinewood') do
    Citizen.Wait(100)
  end

  RegisterNamedRendertarget('casinoscreen_01')

  LinkNamedRendertarget(`vw_vwint01_video_overlay`)

  videoWallRenderTarget = GetNamedRendertargetRenderId('casinoscreen_01')

  Citizen.CreateThread(function()
    local lastUpdatedTvChannel = 0

    while true do
      Citizen.Wait(0)

      if not inCasino then
        ReleaseNamedRendertarget('casinoscreen_01')

        videoWallRenderTarget = nil
        showBigWin = false

        break
      end

      if videoWallRenderTarget then
        local currentTime = GetGameTimer()

        if showBigWin then
          setVideoWallTvChannelWin()

          lastUpdatedTvChannel = GetGameTimer() - 33666
          showBigWin = false
        else
          if (currentTime - lastUpdatedTvChannel) >= 42666 then
            setVideoWallTvChannel()

            lastUpdatedTvChannel = currentTime
          end
        end

        SetTextRenderId(videoWallRenderTarget)
        SetScriptGfxDrawOrder(4)
        SetScriptGfxDrawBehindPausemenu(true)
        DrawInteractiveSprite('Prop_Screen_Vinewood', 'BG_Wall_Colour_4x4', 0.25, 0.5, 0.5, 1.0, 0.0, 255, 255, 255, 255)
        DrawTvChannel(0.5, 0.5, 1.0, 1.0, 0.0, 255, 255, 255, 255)
        SetTextRenderId(GetDefaultScriptRendertargetRenderId())
      end
    end
  end)
end

--
-- Functions
--

function setVideoWallTvChannel()
  SetTvChannelPlaylist(0, videoType, true)
  SetTvAudioFrontend(true)
  SetTvVolume(-100.0)
  SetTvChannel(0)
end

function setVideoWallTvChannelWin()
  SetTvChannelPlaylist(0, 'CASINO_WIN_PL', true)
  SetTvAudioFrontend(true)
  SetTvVolume(-100.0)
  SetTvChannel(-1)
  SetTvChannel(0)
end

--
-- Events
--

AddEventHandler('blrp_casinowall:enter', function()
  inCasino = true

  startCasinoThreads()
end)


AddEventHandler('blrp_casinowall:exit', function()
  inCasino = false
end)

AddEventHandler('blrp_casinowall:bigWin', function()
  print(117)
  if not inCasino then
    print(119)
    return
  end

  print(123)

  showBigWin = true
end)


TriggerEvent('blrp_casinowall:enter')


Citizen.Wait(5000)

TriggerEvent('blrp_casinowall:bigWin')
