local emoteMenuOpen = false

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    if emoteMenuOpen then
      DisableControlAction(0, 140, true)
      DisableControlAction(0, 141, true)
      DisableControlAction(0, 142, true)
      DisableControlAction(0, 24, true)
      DisableControlAction(0, 69, true) -- nice
      DisableControlAction(0, 92, true)
      DisableControlAction(0, 106, true)
      DisableControlAction(0, 257, true)
      DisableControlAction(0, 142, true)
      DisableControlAction(0, 322, true) -- Disable the Escape key
      DisableControlAction(0, 200, true) -- Disable the Escape key
      DisablePlayerFiring(PlayerId(), true)
    end
  end
end)

RegisterCommand('openemotemenu', function(source, args)
    emoteMenuOpen = true
    SetNuiFocus(true, true)
    SetNuiFocusKeepInput(true)

    local Table = {
        { label = 'Gestures', sub = GetEmoteTable(DP.Emotes, 'e') },
        { label = 'Walk Styles', sub = GetEmoteTable(DP.Walks, 'walk') },
        { label = 'Shared Emotes', sub = GetEmoteTable(DP.Shared, 'nearby') },
        { label = 'Dances', sub = GetEmoteTable(DP.Dances, 'e') },
        { label = 'Props', sub = GetEmoteTable(DP.PropEmotes, 'e') },
        { label = 'Moods', sub = GetEmoteTable(DP.Expressions, 'mood') },
    }

    SendNUIMessage({
        action = 'open', 
        list = Table 
    })


  TriggerEvent('core:client:enableInterface', 'dpemotes', true, function()
    -- Callback
  end, {
    is_custom = true,
    block_movement_keys = false,
    block_typing_keys = false,
    block_car_movement = false,
  })
end)

RegisterNUICallback("takeFocus", function()
  Citizen.Wait(500)
  SetNuiFocusKeepInput(false)
end)

RegisterNUICallback("releaseFocus", function()
  if emoteMenuOpen then
    SetNuiFocusKeepInput(true)
  end
end)

RegisterNUICallback("exit" , function(data, cb)
    SetNuiFocusKeepInput(true)
    Citizen.Wait(200)
    SetNuiFocus(false, false)
    TriggerEvent('core:client:enableInterface', 'dpemotes', false)
    emoteMenuOpen = false
end)

function GetEmoteTable(Table, Prefix)
    local Emotes = {}

    if Prefix ~= 'walk' and Prefix ~= 'mood' then
      for i,v in pairs(Table) do
          table.insert(Emotes, { label = v[3], value = i, prefix = Prefix })
      end
    else
      for i,v in pairs(Table) do
          table.insert(Emotes, { label = i, value = i, prefix = Prefix })
      end
    end

    return Emotes
end 

RegisterNUICallback('execute', function(data) 
    ExecuteCommand(data.anim.prefix..' '..data.anim.value)
end)