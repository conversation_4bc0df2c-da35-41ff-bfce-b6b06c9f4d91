local last_url = {
  ['mrpd'] = 'https://i.gyazo.com/ccfb13fa1ec4f9dd0bbc4827615a1680.png',
  ['sandy'] = 'https://i.gyazo.com/ac09366c3e1923013682c24ae0e0672b.jpg'
}

RegisterNetEvent('core:server:registerSelectedPlayer', function(source)
  local character = exports.blrp_core:character(source)

  for projector_id, projector_url in pairs(last_url) do
    character.client('blrp_projectors:client:setProjectorUrl', projector_id, projector_url)
  end
end)

RegisterNetEvent('blrp_projectors:server:setUrl', function(_, event_data)
  if not event_data.projector_id then
    return
  end

  local character = exports.blrp_core:character(source)

  if not character.hasGroup('LEO') then
    character.notify('You cannot change this')
    return
  end

  local value = character.prompt('Enter image URL (gyazo only)')

  if not value or value == '' then
    character.notify('Invalid URL')
    return
  end

  if not string.match(value, 'https?://i.gyazo.com/.*.[jpg|png|gif|jpeg]') then
    character.notify('Invalid URL')
    return
  end

  last_url[event_data.projector_id] = value

  character.log('PROJECTOR', 'Set image URL / projector_id = ' .. event_data.projector_id .. ' / url = ' .. value)
  TriggerClientEvent('blrp_projectors:client:setProjectorUrl', -1, event_data.projector_id, value)
end)

RegisterNetEvent('blrp_projectors:server:reset', function(_, event_data)
  if not event_data.projector_id then
    return
  end

  local character = exports.blrp_core:character(source)

  local url = 'https://i.gyazo.com/ccfb13fa1ec4f9dd0bbc4827615a1680.png'

  if event_data.projector_id == 'sandy' then
    url = 'https://i.gyazo.com/ac09366c3e1923013682c24ae0e0672b.jpg'
  end

  last_url[event_data.projector_id] = url

  TriggerClientEvent('blrp_projectors:client:setProjectorUrl', -1, event_data.projector_id, url)
end)
