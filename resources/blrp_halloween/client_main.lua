local inside_maze_wx = false
local inside_maze_fpv = false

local is_staff = false

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)

    is_staff = exports.blrp_core:me().hasGroup('staff')

    if is_staff then
      break
    end
  end
end)

local Halloween_Maze_Wx = BoxZone:Create(vector3(-2140.0, 2675.0, 6.0), 500.0, 500.0, {
  name = 'Halloween_Maze_Wx',
  heading = -10.0,
  minZ = 0.0,
  maxZ = 500.0,
  onPlayerInOut = function(inside)
    inside_maze_wx = inside

    exports.cd_easytime:PauseSync(inside)

    if inside then
      Citizen.CreateThread(function()
        while inside_maze_wx do
          Citizen.Wait(0)

          if inside_maze_wx then
            ClearPedWetness(PlayerPedId())
            NetworkOverrideClockTime(2, 0, 0)
            ClearOverrideWeather()
            ClearWeatherTypePersist()
            SetWeatherTypePersist('HALLOWEEN')
            SetWeatherTypeNow('HALLOWEEN')
            SetWeatherTypeNowPersist('HALLOWEEN')
          end
        end
      end)
    end
  end,
})

local Halloween_Maze_FPV = BoxZone:Create(vector3(-2140.0, 2675.0, 6.0), 55.0, 120.0, {
  name = 'Halloween_Maze_FPV',
  heading = -10.0,
  minZ = 2.0,
  maxZ = 7.0,
  onPlayerInOut = function(inside)
    inside_maze_fpv = inside

    if inside then
      Citizen.CreateThread(function()
        while inside_maze_fpv do
          Citizen.Wait(0)

          if not is_staff then
            if GetFollowPedCamViewMode() ~= 4 then
              SetFollowPedCamViewMode(4)
            end

            local selected_weapon = GetSelectedPedWeapon(PlayerPedId())

            if selected_weapon ~= `WEAPON_FLASHLIGHT` and selected_weapon ~= `WEAPON_UNARMED` and selected_weapon ~= `WEAPON_INVALID` then
              SetCurrentPedWeapon(PlayerPedId(), `WEAPON_UNARMED`, false)
            end
          end
        end
      end)
    end
  end,
})

AddEventHandler('core:client:playerDied', function()
  local player_coords = GetEntityCoords(PlayerPedId())

  if Halloween_Maze_FPV:isPointInside(player_coords) then
    DoScreenFadeOut(1000)
    SetTimeout(5000, function()
      TriggerEvent('vrp:survival:varyHealth', 100)
      TriggerEvent('mythic_hospital:client:RemoveBleed')
      TriggerEvent('mythic_hospital:client:ResetLimbs')
      TriggerEvent('mythic_hospital:client:ResetPlayer')
      TriggerEvent('vrp:client:isRevived')
      SetEntityCoords(PlayerPedId(), -2157.609, 2681.958, 2.879, true, true, false, false)
      DoScreenFadeIn(1000)
    end)
  end
end)
