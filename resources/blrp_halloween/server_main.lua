--local skull_cache = {}

--RegisterNetEvent('blrp_halloween:pickupSkull', function(_, event_data)
--  local character = exports.blrp_core:character(source)
--
--  local found_location = false
--
--  for _, location in pairs({
--    vector3(-2155.111, 2681.514, 2.736713),
--    vector3(-2164.395, 2657.938, 2.020111),
--    vector3(-2096.948, 2661.826, 1.942975),
--    vector3(-2094.734, 2687.532, 2.303839),
--    vector3(-2127.087, 2690.206, 2.096335),
--    vector3(-2166.461, 2697.528, 2.024307),
--  }) do
--    if #(location - event_data.position) < 0.5 then
--      found_location = location
--      break
--    end
--  end
--
--  if not found_location then
--    return
--  end
--
--  local cache_key = character.get('identifier') .. found_location.x .. found_location.y .. found_location.z
--
--  if skull_cache[cache_key] then
--    character.notify('You have already collected a skull from this location')
--    return
--  end
--
--  if not character.progressPromise('Taking Skull', 3, {
--    animation = {
--      task = 'CODE_HUMAN_MEDIC_TEND_TO_DEAD'
--    }
--  }) then
--    return
--  end
--
--  skull_cache[cache_key] = true
--  character.give('hween_skull_23', 1)
--  character.log('HALLOWEEN-2023', 'Rewarding 1 x hween_skull_23 / position = ' .. found_location)
--end)
--
--local prices = {
--  ['clth_hw23_mask_a1'] = 5,
--  ['clth_hw23_mask_a2'] = 5,
--  ['clth_hw23_mask_b1'] = 5,
--  ['clth_hw23_mask_b2'] = 5,
--  ['clth_hw23_mask_b3'] = 5,
--}
--
--RegisterNetEvent('blrp_halloween:redeemPrize', function(_, event_data)
--  local character = exports.blrp_core:character(source)
--
--  if not event_data.position or not event_data.item_id then
--    return
--  end
--
--  if #(event_data.position - GetEntityCoords(GetPlayerPed(character.source))) > 5.0 then
--    return
--  end
--
--  if not prices[event_data.item_id] then
--    return
--  end
--
--  local item_id = event_data.item_id
--
--  if not character.request('Are you sure you want to redeem ' .. prices[item_id] .. ' skulls for one ' .. exports.blrp_core:GetItemName(item_id) .. '?') then
--    return
--  end
--
--  if not character.take('hween_skull_23', prices[item_id], true) then
--    character.notify('You do not have enough skull heads to redeem')
--    return
--  end
--
--  character.give(item_id, 1)
--  character.log('HALLOWEEN-2023', 'Redeemed ' .. prices[item_id] .. ' skull heads for 1 ' .. item_id)
--end)
