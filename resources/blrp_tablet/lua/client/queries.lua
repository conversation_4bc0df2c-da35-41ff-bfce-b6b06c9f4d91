local exclude_vehicles = {
  ['cuban800'] = true,
  ['dodo'] = true,
  ['duster'] = true,
  ['luxor'] = true,
  ['mammatus'] = true,
  ['nimbus'] = true,
  ['shamal'] = true,
  ['velum2'] = true,
  ['buzzard2'] = true,
  ['frogger'] = true,
  ['maverick'] = true,
  ['supervolito'] = true,
  ['swift'] = true,
  ['volatus'] = true,
	['polmav'] = true,
	['predator'] = true,
	['predator2'] = true,
	['predator3'] = true,
	['seashark2'] = true,
	['dinghy'] = true,
	['dinghy2'] = true,
	['jetmax'] = true,
	['marquis'] = true,
	['seashark'] = true,
	['seasharkb'] = true,
	['speeder'] = true,
	['squalo'] = true,
	['submersible'] = true,
	['submersible2'] = true,
	['suntrap'] = true,
	['toro'] = true,
	['tropic'] = true,
	['tug'] = true,
}

function localiseVehicleModel(model_name)
  local model_hash = GetHashKey(model_name)

  -- Bypass for direct hashes
  if tonumber(model_name) == model_name then
    model_hash = model_name
  end

  local vehicleModelName = GetDisplayNameFromVehicleModel(model_hash)
  local vehicleModelNameLocalised = GetLabelText(vehicleModelName)

  if vehicleModelNameLocalised and vehicleModelNameLocalised ~= 'NULL' then
    return vehicleModelNameLocalised
  end

  return nil
end

RegisterNUICallback('getVehicleData', function(data, callback)
  callback('ok')

  local vehicle_data = pTablet.getVehicleInformation({ data.term })

  if vehicle_data then
    local name_localised = localiseVehicleModel(vehicle_data.vehicle)
    vehicle_data.character_id = vehicle_data.characterNumber

      if name_localised then
      vehicle_data.vehicle = name_localised
    end
  end

  SendNUIMessage({
    type = 'tablet:message',
    name = 'vehicle:data_callback',
    packet = {
      vehicle = vehicle_data
    }
  })
end)

RegisterNUICallback('getVehicleDataByDL', function(data, callback)
  callback('ok')

  local character_data, license_statuses, vehicle_rows = pTablet.getDLInformation({ data.term })

  local vehicles = {}

  if character_data then
    for v_key, v_data in pairs(vehicle_rows) do
      if not exclude_vehicles[v_data.vehicle] then
        local name_localised = localiseVehicleModel(v_data.vehicle)

        if name_localised then
          vehicle_rows[v_key].vehicle = name_localised
        end

        table.insert(vehicles, vehicle_rows[v_key])
      end
    end
  end

  SendNUIMessage({
    type = 'tablet:message',
    name = 'vehicle:data_callback_dl',
    packet = {
      character = character_data,
      vehicles = vehicles,
      licenses = license_statuses
    }
  })
end)

RegisterNUICallback('getWeaponInformation', function(data, callback)
  callback('ok')

  local weapon_data = pTablet.getWeaponInformation({ data.term })

  if weapon_data and weapon_data.business then
    weapon_data.lastname = weapon_data.lastname .. ' [Business: ' .. weapon_data.business ..  ']'
  end

  SendNUIMessage({
    type = 'tablet:message',
    name = 'vehicle:data_callback_weapon',
    packet = {
      weapon_data = weapon_data,
    }
  })
end)
