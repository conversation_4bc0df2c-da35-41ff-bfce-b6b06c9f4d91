local key = 'vsbtstge5sg4mfn26265mntynEGHsdgsg78sgro9a'
local keyString = '?key=' .. key
local isDev = GetConvarInt('dev', -1)
local syncedModels = { }

apiEndpoint = nil
socketsEndpoint = nil

if isDev == 1 then
  apiEndpoint = GetConvar('tablet_dev_url', 'http://dev-res.blrp.net:801')
else
  apiEndpoint = GetConvar('tablet_prod_url', 'http://prod-tablet.blrp.net:801')
end

if isDev == 1 then
  socketsEndpoint = GetConvar('tablet_sockets_dev_url', 'http://dev-res.blrp.net:63791')
else
  socketsEndpoint = GetConvar('tablet_sockets_prod_url', 'http://prod-tablet.blrp.net:60011')
end

function doGetRequest(uri, cb)
    local endpoint = apiEndpoint .. uri .. keyString
    local promise = promise:new()

    PerformHttpRequest(endpoint, function(errorCode, resultData, resultHeaders)
        if errorCode and errorCode ~= 200 and errorCode ~= 201 then
            return
        end

        if resultData then
            local decoded = json.decode(resultData)
            if cb then
                cb(decoded)
            end

            promise:resolve(decoded)
        else
            cb({})
        end
    end, 'GET')

    if cb then -- Return early if callback is provided
        return
    end

    return Citizen.Await(promise)
end

function doPostRequest(uri, data, cb, requestHeaders)
  if not data then data = {} end
  if not requestHeaders then requestHeaders = {} end
  local endpoint = apiEndpoint .. uri .. keyString
  local promise = promise:new()

  local startTime = GetGameTimer()

  PerformHttpRequest(endpoint, function(errorCode, resultData, resultHeaders)
    local elapsed = (GetGameTimer() - startTime)

    if errorCode and errorCode ~= 200 and errorCode ~= 201 then
      exports.blrp_core:print_r({
        message = 'API ERROR [POST]',
        code = errorCode,
        endpoint = endpoint,
        time = elapsed .. 's',
      })
      promise:resolve(false)
      return
    end

    if resultData then
      local decoded = json.decode(resultData)
      if cb then
        cb(decoded)
      end

      print(('[doPostRequest] %s took %.2fms'):format(uri, elapsed))
      promise:resolve(decoded)
    end
  end, 'POST', json.encode(data), requestHeaders)

  return Citizen.Await(promise)
end

exports('DoPostRequest', doPostRequest)
exports('DoGetRequest', doGetRequest)

------------------------------------------------------
--- SYNC Fucntionality
------------------------------------------------------
--- Why am I doing this? Well mainly for meta data or data I need to access on the backend of PHP
-- This is so that tablet can have a database version of these vehicles, so that it can be returned
-- as a relationship. This is no way will ever effect lua code, and is only for
-- meta data about vehicles names.


Citizen.CreateThread(function()
    Citizen.Wait(2000)
    print('[Tablet API]', 'Sending vehicles to tablet DB')
    __syncVehicles()
    print('[Tablet API]', 'Sending items to tablet DB')
    __syncItems()
end)

function __syncVehicles()
    local vehicles = { }

    for raw_category_name, category_data in pairs(config_vehicles.categories) do
        local category_name = raw_category_name
        local permission = nil

        for vehicle_name, vehicle_data in pairs(category_data) do

            if vehicle_name == '_config' and vehicle_data.display then
                category_name = vehicle_data.display
            end

            if vehicle_name == '_config' and vehicle_data.permission then
                permission = vehicle_data.permission
            end

            if vehicle_name ~= '_config' then
                local name, price, seats = table.unpack(vehicle_data)
                table.insert(vehicles, {
                    name = name,
                    identifier = vehicle_name,
                    category_identifier = raw_category_name,
                    category_name = category_name,
                    price = price,
                    permission = permission,
                })
            end
        end
    end
    doSync('UserCarType', 'identifier', vehicles)
end

function __syncItems()
    local items = {}
    local existingItems = exports.blrp_core:GetGItemsInternal()

    for idname, properties in pairs(existingItems) do
        local itemName = properties.displayName or properties.name

        -- If it's a function, try to extract base_name (from drawable_name)
        if type(itemName) == "function" then
            local baseName = debug.getupvalue(itemName, 1)
            if type(baseName) == "string" then
                itemName = baseName
            else
                itemName = nil -- fallback to idname later
            end
        end

        -- Final fallback: use item ID as name if still invalid
        if type(itemName) ~= "string" then
            itemName = idname
        end

        -- Make sure category is a string
        local category = properties.category
        if type(category) ~= "string" then
            category = tostring(category or "uncategorized")
        end

        table.insert(items, {
            idname = idname,
            name = itemName,
            category = category
        })
    end

    doSync('Item', 'idname', items)
end


function doSync(modelName, identifierField, data)
    local totalItems = 0
    for _, __ in pairs(data) do
        totalItems = totalItems + 1
    end

    local chunkSize = 250 -- tweak this size as needed
    local chunks = math.ceil(totalItems / chunkSize)

    if not syncedModels[modelName] then
        print('[Tablet API]', 'Syncing model data', modelName, identifierField, '| Total Results:', totalItems, '| Chunks:', chunks)

        for i = 1, totalItems, chunkSize do
            local chunk = {}

            for j = i, math.min(i + chunkSize - 1, totalItems) do
                table.insert(chunk, data[j])
            end

            doPostRequest('/secure/sync-data/' .. modelName .. '/' .. identifierField, {
                data = chunk,
            }, nil, {
                ["Content-Type"] = "application/json"
            })

            Citizen.Wait(100) -- brief pause to reduce server load; adjust if needed
        end
    end
end
