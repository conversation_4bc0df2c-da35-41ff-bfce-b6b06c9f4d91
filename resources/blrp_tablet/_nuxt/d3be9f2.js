(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[75],{

/***/ 2131:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/community-settings/_thread.vue?vue&type=template&id=518e0435
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('app-page', [_c('business-thread', {
    attrs: {
      "thread-id": _vm.$route.params.thread
    }
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/community-settings/_thread.vue?vue&type=template&id=518e0435

// EXTERNAL MODULE: ./components/Pages/Business/business-thread.vue + 9 modules
var business_thread = __webpack_require__(1641);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/community-settings/_thread.vue?vue&type=script&lang=js


/* harmony default export */ var _threadvue_type_script_lang_js = ({
  components: {
    AppPage: AppPage["a" /* default */],
    BusinessThread: business_thread["a" /* default */]
  }
});
// CONCATENATED MODULE: ./pages/cad/community-settings/_thread.vue?vue&type=script&lang=js
 /* harmony default export */ var community_settings_threadvue_type_script_lang_js = (_threadvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/community-settings/_thread.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  community_settings_threadvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var _thread = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);