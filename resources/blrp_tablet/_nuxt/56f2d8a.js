(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[139],{

/***/ 1581:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1628);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("6a7ff30a", content, true, {"sourceMap":false});

/***/ }),

/***/ 1582:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1630);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("a2c3be40", content, true, {"sourceMap":false});

/***/ }),

/***/ 1627:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_WantedCard_vue_vue_type_style_index_0_id_6a6a0646_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1581);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_WantedCard_vue_vue_type_style_index_0_id_6a6a0646_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_WantedCard_vue_vue_type_style_index_0_id_6a6a0646_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1628:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".wanted-card-container[data-v-6a6a0646]{cursor:pointer;height:500px;padding:8px 4px;perspective:1000px;transition:all .3s ease}.wanted-card-container[data-v-6a6a0646]:hover{transform:translateY(-2px)}.wanted-card-flip[data-v-6a6a0646]{height:100%;position:relative;transform-style:preserve-3d;transition:transform .6s;width:100%}.wanted-card-flip.flipped[data-v-6a6a0646]{transform:rotateY(180deg)}.wanted-card[data-v-6a6a0646]{backface-visibility:hidden;background:linear-gradient(135deg,#1a1a1a,#2a2a2a);border:1px solid #444!important;border-radius:12px!important;height:100%;margin:0!important;overflow:hidden;position:absolute;width:100%}.wanted-card[data-v-6a6a0646]:before{background:linear-gradient(90deg,#444,#666,#444);border-radius:12px 12px 0 0;content:\"\";height:3px;left:0;position:absolute;right:0;top:0;z-index:1}.wanted-card-back[data-v-6a6a0646]{transform:rotateY(180deg)}.wanted-badge[data-v-6a6a0646]{border-radius:12px;color:#fff;font-size:10px;font-weight:700;letter-spacing:.5px;padding:6px 12px;position:absolute;right:12px;text-transform:uppercase;top:12px;transition:opacity .3s ease;z-index:3}.wanted-badge i[data-v-6a6a0646]{margin-right:4px}.recent-badge[data-v-6a6a0646]{background:linear-gradient(135deg,#ff9800,#f57c00);box-shadow:0 4px 12px rgba(255,152,0,.4)}.most-wanted-badge[data-v-6a6a0646]{background:linear-gradient(135deg,#f44336,#d32f2f);box-shadow:0 4px 12px rgba(244,67,54,.4)}.cold-case-badge[data-v-6a6a0646]{background:linear-gradient(135deg,#2196f3,#1976d2);box-shadow:0 4px 12px rgba(33,150,243,.4)}.case-age-badge[data-v-6a6a0646]{background:rgba(0,0,0,.8);border:1px solid hsla(0,0%,100%,.2);border-radius:12px;color:#ccc;font-size:10px;font-weight:700;left:12px;padding:6px 10px;position:absolute;top:12px;transition:opacity .3s ease;z-index:3}.case-date[data-v-6a6a0646]{margin-top:12px;text-align:center}.warrant-incident-title[data-v-6a6a0646]{color:#ccc;font-size:14px;line-height:1.4}.charges-content[data-v-6a6a0646]{display:flex;flex-direction:column;height:100%;padding:20px}.charges-header[data-v-6a6a0646]{margin-bottom:20px;text-align:center}.charges-header .person-name-small[data-v-6a6a0646]{color:#fff;font-size:16px;font-weight:700;margin-bottom:8px}.charges-header .charges-title[data-v-6a6a0646]{color:#aaa;font-size:14px;letter-spacing:1px;text-transform:uppercase}.charges-list[data-v-6a6a0646]{flex:1;overflow-y:auto;padding-right:8px}.charges-list[data-v-6a6a0646]::-webkit-scrollbar{width:4px}.charges-list[data-v-6a6a0646]::-webkit-scrollbar-track{background:hsla(0,0%,100%,.1)}.charges-list[data-v-6a6a0646]::-webkit-scrollbar-thumb{background:hsla(0,0%,100%,.3);border-radius:2px}.charge-item[data-v-6a6a0646]{background:hsla(0,0%,100%,.05);border-left:4px solid #666;border-radius:4px;margin-bottom:12px;padding:12px;transition:all .2s ease}.charge-item[data-v-6a6a0646]:hover{background:hsla(0,0%,100%,.08);transform:translateX(4px)}.charge-item .charge-name[data-v-6a6a0646]{color:#fff;font-size:13px;font-weight:700;line-height:1.3;margin-bottom:6px}.charge-item .charge-details[data-v-6a6a0646]{display:flex;flex-wrap:wrap;font-size:11px;gap:8px}.charge-item .charge-details .charge-type[data-v-6a6a0646]{background:hsla(0,0%,100%,.1);border-radius:3px;color:#ccc;font-weight:700;padding:2px 6px;text-transform:uppercase}.charge-item .charge-details .charge-count[data-v-6a6a0646]{background:rgba(255,193,7,.2);border-radius:3px;color:#ffc107;font-weight:700;padding:2px 6px}.charge-item .charge-details .charge-modifier[data-v-6a6a0646]{background:rgba(156,39,176,.2);border-radius:3px;color:#ab47bc;font-style:italic;padding:2px 6px}.charge-felony[data-v-6a6a0646]{border-left-color:#f44336}.charge-felony .charge-type[data-v-6a6a0646]{background:rgba(244,67,54,.2);color:#f44336}.charge-misdemeanor[data-v-6a6a0646]{border-left-color:#ff9800}.charge-misdemeanor .charge-type[data-v-6a6a0646]{background:rgba(255,152,0,.2);color:#ff9800}.charge-infraction[data-v-6a6a0646]{border-left-color:#4caf50}.charge-infraction .charge-type[data-v-6a6a0646]{background:rgba(76,175,80,.2);color:#4caf50}.charge-unknown[data-v-6a6a0646]{border-left-color:#9e9e9e}.charge-unknown .charge-type[data-v-6a6a0646]{background:hsla(0,0%,62%,.2);color:#9e9e9e}.no-charges[data-v-6a6a0646]{align-items:center;color:#666;display:flex;flex-direction:column;height:100%;justify-content:center;text-align:center}.no-charges i[data-v-6a6a0646]{font-size:48px;margin-bottom:16px;opacity:.5}.no-charges div[data-v-6a6a0646]{font-size:14px;margin-bottom:8px}.no-charges .no-charges-subtitle[data-v-6a6a0646]{color:#555;font-size:12px;font-style:italic}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1629:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_WantedGrid_vue_vue_type_style_index_0_id_a03a6ad0_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1582);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_WantedGrid_vue_vue_type_style_index_0_id_a03a6ad0_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_WantedGrid_vue_vue_type_style_index_0_id_a03a6ad0_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1630:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".wanted-col-5[data-v-a03a6ad0]{flex:2 2 20%!important;margin-bottom:4px;max-width:20%!important;padding:3px!important}.wanted-card-container[data-v-a03a6ad0]{margin:2!important;padding:2!important}.wanted-card[data-v-a03a6ad0]{border:none!important;border-radius:0!important}.wanted-card[data-v-a03a6ad0]:before{display:none!important}.empty-state[data-v-a03a6ad0]{flex-direction:column;min-height:400px;padding:60px 40px;text-align:center}.empty-state[data-v-a03a6ad0],.empty-state .empty-icon[data-v-a03a6ad0]{align-items:center;display:flex;justify-content:center}.empty-state .empty-icon[data-v-a03a6ad0]{background:linear-gradient(135deg,rgba(79,195,247,.2),rgba(2,136,209,.1));border:2px solid rgba(79,195,247,.3);border-radius:50%;height:120px;margin-bottom:24px;width:120px}.empty-state .empty-icon i[data-v-a03a6ad0]{color:#4fc3f7;font-size:48px;opacity:.7}.empty-state h3[data-v-a03a6ad0]{color:#4fc3f7;font-size:24px;font-weight:600;margin:0 0 12px}.empty-state p[data-v-a03a6ad0]{color:#aaa;font-size:16px;margin:0 0 32px;max-width:400px}.empty-decoration[data-v-a03a6ad0]{align-items:center;display:flex;gap:16px}.empty-decoration i[data-v-a03a6ad0]{color:#4caf50;font-size:20px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1634:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Wanted/WantedGrid.vue?vue&type=template&id=a03a6ad0&scoped=true



var WantedGridvue_type_template_id_a03a6ad0_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VRow["a" /* default */], {
    staticClass: "wanted-row",
    attrs: {
      "no-gutters": ""
    }
  }, _vm._l(_vm.records, function (record) {
    return _c(VCol["a" /* default */], {
      key: record.id,
      staticClass: "wanted-col wanted-col-5"
    }, [_c('WantedCard', {
      attrs: {
        "record": record,
        "badge-type": _vm.getBadgeType(record),
        "show-age-badge": _vm.showAgeBadge,
        "show-date": _vm.showDate
      }
    })], 1);
  }), 1), _vm._v(" "), _vm.records.length === 0 ? _c('div', {
    staticClass: "empty-state"
  }, [_c('div', {
    staticClass: "empty-icon"
  }, [_c('i', {
    class: _vm.emptyIcon
  })]), _vm._v(" "), _c('h3', [_vm._v(_vm._s(_vm.emptyTitle))]), _vm._v(" "), _c('p', [_vm._v(_vm._s(_vm.emptyMessage))]), _vm._v(" "), _vm._m(0)]) : _vm._e()], 1);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "empty-decoration"
  }, [_c('div', {
    staticClass: "decoration-line"
  }), _vm._v(" "), _c('i', {
    staticClass: "fa-solid fa-check-circle"
  }), _vm._v(" "), _c('div', {
    staticClass: "decoration-line"
  })]);
}];

// CONCATENATED MODULE: ./components/Pages/Wanted/WantedGrid.vue?vue&type=template&id=a03a6ad0&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VChip/VChip.js
var VChip = __webpack_require__(467);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(890);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Wanted/WantedCard.vue?vue&type=template&id=6a6a0646&scoped=true






var WantedCardvue_type_template_id_6a6a0646_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "wanted-card-container",
    on: {
      "mouseenter": function mouseenter($event) {
        _vm.isHovered = true;
      },
      "mouseleave": function mouseleave($event) {
        _vm.isHovered = false;
      },
      "click": _vm.goToIncident
    }
  }, [_c('div', {
    staticClass: "wanted-card-flip",
    class: {
      'flipped': _vm.isHovered
    }
  }, [_c(VCard["a" /* default */], {
    staticClass: "wanted-card wanted-card-front",
    attrs: {
      "flat": "",
      "height": "500",
      "max-height": "500"
    }
  }, [_c('div', {
    directives: [{
      name: "show",
      rawName: "v-show",
      value: !_vm.isHovered && _vm.badgeType !== 'recent',
      expression: "!isHovered && badgeType !== 'recent'"
    }],
    staticClass: "wanted-badge",
    class: _vm.badgeClass
  }, [_c('i', {
    class: _vm.badgeIcon
  }), _vm._v("\n        " + _vm._s(_vm.badgeText) + "\n      ")]), _vm._v(" "), _vm.showAgeBadge && _vm.record.days_old && !_vm.isHovered ? _c('div', {
    staticClass: "case-age-badge"
  }, [_vm._v("\n        " + _vm._s(_vm.record.days_old) + " days old\n      ")]) : _vm._e(), _vm._v(" "), _c(VImg["a" /* default */], {
    attrs: {
      "min-height": "400",
      "max-height": "400",
      "src": _vm.record.person.latest_mugshot
    }
  }), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c('div', {
    staticClass: "font-weight-bold",
    class: _vm.nameClass,
    staticStyle: {
      "font-size": "20px !important"
    }
  }, [_vm._v("\n          " + _vm._s(_vm.record.person.name) + "\n        ")]), _vm._v(" "), _vm.record.incident ? _c('div', {
    staticClass: "warrant-incident-title",
    staticStyle: {
      "font-size": "14px !important"
    }
  }, [_vm._v("\n          " + _vm._s(_vm.record.incident.title) + "\n        ")]) : _vm._e(), _vm._v(" "), _vm.showDate && _vm.record.incident && _vm.record.incident.created_at ? _c('div', {
    staticClass: "case-date mt-2"
  }, [_c(VChip["a" /* default */], {
    attrs: {
      "small": "",
      "color": "grey",
      "dark": ""
    }
  }, [_vm._v("\n            " + _vm._s(_vm.formatDate(_vm.record.incident.created_at)) + "\n          ")])], 1) : _vm._e(), _vm._v(" "), _c('br')])], 1), _vm._v(" "), _c(VCard["a" /* default */], {
    staticClass: "wanted-card wanted-card-back",
    attrs: {
      "flat": "",
      "height": "500",
      "max-height": "500"
    }
  }, [_c('div', {
    directives: [{
      name: "show",
      rawName: "v-show",
      value: !_vm.isHovered && _vm.badgeType !== 'recent',
      expression: "!isHovered && badgeType !== 'recent'"
    }],
    staticClass: "wanted-badge",
    class: _vm.badgeClass
  }, [_c('i', {
    staticClass: "fa-solid fa-gavel"
  }), _vm._v("\n        CHARGES\n      ")]), _vm._v(" "), _c('div', {
    staticClass: "charges-content"
  }, [_c('div', {
    staticClass: "charges-header"
  }, [_c('div', {
    staticClass: "person-name-small"
  }, [_vm._v(_vm._s(_vm.record.person.name))]), _vm._v(" "), _c('div', {
    staticClass: "charges-title"
  }, [_vm._v("Criminal Charges")])]), _vm._v(" "), _vm.record.crimes && _vm.record.crimes.length > 0 ? _c('div', {
    staticClass: "charges-list"
  }, _vm._l(_vm.record.crimes, function (crime) {
    var _crime$crime, _crime$crime2, _crime$crime3;
    return _c('div', {
      key: crime.id,
      staticClass: "charge-item",
      class: _vm.getChargeTypeClass((_crime$crime = crime.crime) === null || _crime$crime === void 0 ? void 0 : _crime$crime.type)
    }, [_c('div', {
      staticClass: "charge-name"
    }, [_vm._v(_vm._s(((_crime$crime2 = crime.crime) === null || _crime$crime2 === void 0 ? void 0 : _crime$crime2.name) || 'Unknown Charge'))]), _vm._v(" "), _c('div', {
      staticClass: "charge-details"
    }, [_c('span', {
      staticClass: "charge-type"
    }, [_vm._v(_vm._s(((_crime$crime3 = crime.crime) === null || _crime$crime3 === void 0 ? void 0 : _crime$crime3.type) || 'Unknown'))]), _vm._v(" "), crime.counts > 1 ? _c('span', {
      staticClass: "charge-count"
    }, [_vm._v("x" + _vm._s(crime.counts))]) : _vm._e(), _vm._v(" "), crime.is_attempted ? _c('span', {
      staticClass: "charge-modifier"
    }, [_vm._v("Attempted")]) : _vm._e(), _vm._v(" "), crime.is_accessory ? _c('span', {
      staticClass: "charge-modifier"
    }, [_vm._v("Accessory")]) : _vm._e()])]);
  }), 0) : _c('div', {
    staticClass: "no-charges"
  }, [_c('i', {
    staticClass: "fa-solid fa-exclamation-triangle"
  }), _vm._v(" "), _c('div', [_vm._v("No specific charges listed")]), _vm._v(" "), _c('div', {
    staticClass: "no-charges-subtitle"
  }, [_vm._v("See incident for details")])])])])], 1)]);
};
var WantedCardvue_type_template_id_6a6a0646_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Wanted/WantedCard.vue?vue&type=template&id=6a6a0646&scoped=true

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Wanted/WantedCard.vue?vue&type=script&lang=js

/* harmony default export */ var WantedCardvue_type_script_lang_js = ({
  name: 'WantedCard',
  data: function data() {
    return {
      isHovered: false
    };
  },
  props: {
    record: {
      type: Object,
      required: true
    },
    badgeType: {
      type: String,
      required: true,
      validator: function validator(value) {
        return ['recent', 'most-wanted', 'cold-case'].includes(value);
      }
    },
    showAgeBadge: {
      type: Boolean,
      default: false
    },
    showDate: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    badgeClass: function badgeClass() {
      return {
        'recent-badge': this.badgeType === 'recent',
        'most-wanted-badge': this.badgeType === 'most-wanted',
        'cold-case-badge': this.badgeType === 'cold-case'
      };
    },
    badgeIcon: function badgeIcon() {
      switch (this.badgeType) {
        case 'recent':
          return 'fa-solid fa-clock';
        case 'most-wanted':
          return 'fa-solid fa-crosshairs-simple';
        case 'cold-case':
          return 'fa-solid fa-snowflake';
        default:
          return 'fa-solid fa-clock';
      }
    },
    badgeText: function badgeText() {
      switch (this.badgeType) {
        case 'recent':
          return 'RECENT';
        case 'most-wanted':
          return 'MOST WANTED';
        case 'cold-case':
          return 'COLD CASE';
        default:
          return 'RECENT';
      }
    },
    nameClass: function nameClass() {
      return this.badgeType === 'cold-case' ? 'text-light-blue' : '';
    }
  },
  methods: {
    formatDate: function formatDate(dateString) {
      return new Date(dateString).toLocaleDateString();
    },
    getChargeTypeClass: function getChargeTypeClass(type) {
      switch (type === null || type === void 0 ? void 0 : type.toLowerCase()) {
        case 'felony':
          return 'charge-felony';
        case 'misdemeanor':
          return 'charge-misdemeanor';
        case 'infraction':
          return 'charge-infraction';
        default:
          return 'charge-unknown';
      }
    },
    goToIncident: function goToIncident() {
      this.$router.push("/cad/incidents/view/".concat(this.record.incident.id));
    }
  }
});
// CONCATENATED MODULE: ./components/Pages/Wanted/WantedCard.vue?vue&type=script&lang=js
 /* harmony default export */ var Wanted_WantedCardvue_type_script_lang_js = (WantedCardvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Wanted/WantedCard.vue?vue&type=style&index=0&id=6a6a0646&prod&lang=scss&scoped=true
var WantedCardvue_type_style_index_0_id_6a6a0646_prod_lang_scss_scoped_true = __webpack_require__(1627);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Wanted/WantedCard.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Wanted_WantedCardvue_type_script_lang_js,
  WantedCardvue_type_template_id_6a6a0646_scoped_true_render,
  WantedCardvue_type_template_id_6a6a0646_scoped_true_staticRenderFns,
  false,
  null,
  "6a6a0646",
  null
  
)

/* harmony default export */ var WantedCard = (component.exports);
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Wanted/WantedGrid.vue?vue&type=script&lang=js


/* harmony default export */ var WantedGridvue_type_script_lang_js = ({
  name: 'WantedGrid',
  components: {
    WantedCard: WantedCard
  },
  props: {
    records: {
      type: Array,
      required: true,
      default: function _default() {
        return [];
      }
    },
    badgeType: {
      type: String,
      required: true,
      validator: function validator(value) {
        return ['recent', 'most-wanted', 'cold-case', 'dynamic'].includes(value);
      }
    },
    emptyTitle: {
      type: String,
      default: 'No Records Found'
    },
    emptyMessage: {
      type: String,
      default: 'No records available at this time.'
    },
    emptyIcon: {
      type: String,
      default: 'fa-solid fa-clock'
    },
    showAgeBadge: {
      type: Boolean,
      default: false
    },
    showDate: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getBadgeType: function getBadgeType(record) {
      // If badgeType is dynamic, determine based on record data
      if (this.badgeType === 'dynamic') {
        // Check if warrant is more than 7 days old
        var createdAt = new Date(record.created_at);
        var sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        if (createdAt < sevenDaysAgo) {
          return 'cold-case';
        }
        return record.is_most_wanted ? 'most-wanted' : 'recent';
      }
      // Otherwise use the static badgeType
      return this.badgeType;
    }
  }
});
// CONCATENATED MODULE: ./components/Pages/Wanted/WantedGrid.vue?vue&type=script&lang=js
 /* harmony default export */ var Wanted_WantedGridvue_type_script_lang_js = (WantedGridvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Wanted/WantedGrid.vue?vue&type=style&index=0&id=a03a6ad0&prod&lang=scss&scoped=true
var WantedGridvue_type_style_index_0_id_a03a6ad0_prod_lang_scss_scoped_true = __webpack_require__(1629);

// CONCATENATED MODULE: ./components/Pages/Wanted/WantedGrid.vue






/* normalize component */

var WantedGrid_component = Object(componentNormalizer["a" /* default */])(
  Wanted_WantedGridvue_type_script_lang_js,
  WantedGridvue_type_template_id_a03a6ad0_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "a03a6ad0",
  null
  
)

/* harmony default export */ var WantedGrid = __webpack_exports__["a"] = (WantedGrid_component.exports);

/***/ }),

/***/ 1713:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1833);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("9e713d02", content, true, {"sourceMap":false});

/***/ }),

/***/ 1832:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_most_wanted_vue_vue_type_style_index_0_id_1b5e3043_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1713);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_most_wanted_vue_vue_type_style_index_0_id_1b5e3043_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_most_wanted_vue_vue_type_style_index_0_id_1b5e3043_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1833:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".loading-container[data-v-1b5e3043]{align-items:center;background:radial-gradient(circle at center,rgba(244,67,54,.05) 0,transparent 70%);display:flex;flex-direction:column;justify-content:center;min-height:400px;padding:60px 40px;text-align:center}.targeting-system[data-v-1b5e3043]{height:200px;margin-bottom:30px;position:relative;width:200px}.crosshairs-container[data-v-1b5e3043]{height:100%;position:relative;width:100%}.crosshair-ring[data-v-1b5e3043]{border:2px solid #f44336;border-radius:50%;left:50%;opacity:.6;position:absolute;top:50%;transform:translate(-50%,-50%)}.ring-1[data-v-1b5e3043]{animation:target-pulse-1b5e3043 2s ease-in-out infinite;height:60px;width:60px}.ring-2[data-v-1b5e3043]{animation:target-pulse-1b5e3043 2s ease-in-out .3s infinite;height:100px;opacity:.4;width:100px}.ring-3[data-v-1b5e3043]{animation:target-pulse-1b5e3043 2s ease-in-out .6s infinite;height:140px;opacity:.2;width:140px}.crosshair-center[data-v-1b5e3043]{animation:crosshair-rotate-1b5e3043 3s linear infinite;color:#f44336;font-size:32px;left:50%;position:absolute;text-shadow:0 0 10px rgba(244,67,54,.5);top:50%;transform:translate(-50%,-50%)}.scan-line[data-v-1b5e3043]{animation:scan-1b5e3043 2.5s ease-in-out infinite;background:linear-gradient(90deg,transparent,#f44336,transparent);opacity:.8;position:absolute}.scan-line.horizontal[data-v-1b5e3043]{height:2px;left:0;top:50%;transform:translateY(-50%);width:100%}.scan-line.vertical[data-v-1b5e3043]{animation-delay:.5s;height:100%;left:50%;top:0;transform:translateX(-50%) rotate(90deg);width:2px}.target-dots[data-v-1b5e3043]{height:100%;position:absolute;width:100%}.dot[data-v-1b5e3043]{animation:dot-blink-1b5e3043 1.5s ease-in-out infinite;background:#f44336;border-radius:50%;box-shadow:0 0 8px rgba(244,67,54,.6);height:8px;position:absolute;width:8px}.dot-1[data-v-1b5e3043]{animation-delay:0s;left:20%;top:20%}.dot-2[data-v-1b5e3043]{animation-delay:.4s;right:20%;top:20%}.dot-3[data-v-1b5e3043]{animation-delay:.8s;bottom:20%;left:20%}.dot-4[data-v-1b5e3043]{animation-delay:1.2s;bottom:20%;right:20%}.loading-text[data-v-1b5e3043]{animation:text-flicker-1b5e3043 2s ease-in-out infinite;color:#f44336;font-family:\"Courier New\",monospace;font-size:16px;font-weight:500;letter-spacing:1px;text-shadow:0 0 10px rgba(244,67,54,.3)}@keyframes target-pulse-1b5e3043{0%,to{opacity:.6;transform:translate(-50%,-50%) scale(1)}50%{opacity:.3;transform:translate(-50%,-50%) scale(1.1)}}@keyframes crosshair-rotate-1b5e3043{0%{transform:translate(-50%,-50%) rotate(0deg)}to{transform:translate(-50%,-50%) rotate(1turn)}}@keyframes scan-1b5e3043{0%,to{opacity:0;transform:translateY(-50%) scaleX(0)}50%{opacity:.8;transform:translateY(-50%) scaleX(1)}}@keyframes dot-blink-1b5e3043{0%,to{opacity:1;transform:scale(1)}50%{opacity:.3;transform:scale(1.2)}}@keyframes text-flicker-1b5e3043{0%,to{opacity:1;text-shadow:0 0 10px rgba(244,67,54,.3)}50%{opacity:.8;text-shadow:0 0 20px rgba(244,67,54,.6),0 0 30px rgba(244,67,54,.4)}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2128:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/wanted/most-wanted.vue?vue&type=template&id=1b5e3043&scoped=true
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('app-page', {}, [_vm.loading ? _c('div', {
    staticClass: "loading-container"
  }, [_c('div', {
    staticClass: "targeting-system"
  }, [_c('div', {
    staticClass: "crosshairs-container"
  }, [_c('div', {
    staticClass: "crosshair-ring ring-1"
  }), _vm._v(" "), _c('div', {
    staticClass: "crosshair-ring ring-2"
  }), _vm._v(" "), _c('div', {
    staticClass: "crosshair-ring ring-3"
  }), _vm._v(" "), _c('div', {
    staticClass: "crosshair-center"
  }, [_c('i', {
    staticClass: "fa-solid fa-crosshairs-simple"
  })]), _vm._v(" "), _c('div', {
    staticClass: "scan-line horizontal"
  }), _vm._v(" "), _c('div', {
    staticClass: "scan-line vertical"
  })]), _vm._v(" "), _c('div', {
    staticClass: "target-dots"
  }, [_c('div', {
    staticClass: "dot dot-1"
  }), _vm._v(" "), _c('div', {
    staticClass: "dot dot-2"
  }), _vm._v(" "), _c('div', {
    staticClass: "dot dot-3"
  }), _vm._v(" "), _c('div', {
    staticClass: "dot dot-4"
  })])]), _vm._v(" "), _c('div', {
    staticClass: "loading-text"
  }, [_vm._v("Acquiring targets...")])]) : _c('WantedGrid', {
    attrs: {
      "records": _vm.mostWantedWarrants,
      "badge-type": "dynamic",
      "empty-title": "No Most Wanted Suspects",
      "empty-message": "No suspects meet the most wanted criteria (2+ warrants or 4+ felonies) at this time.",
      "empty-icon": "fa-solid fa-crosshairs-simple"
    }
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/wanted/most-wanted.vue?vue&type=template&id=1b5e3043&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// EXTERNAL MODULE: ./components/Pages/Wanted/WantedGrid.vue + 9 modules
var WantedGrid = __webpack_require__(1634);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/wanted/most-wanted.vue?vue&type=script&lang=js





/* harmony default export */ var most_wantedvue_type_script_lang_js = ({
  name: 'MostWanted',
  components: {
    AppPage: AppPage["a" /* default */],
    WantedGrid: WantedGrid["a" /* default */]
  },
  data: function data() {
    return {
      mostWantedWarrants: [],
      loading: false
    };
  },
  mounted: function mounted() {
    this.index();
  },
  methods: {
    index: function index() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _this.loading = true;
              _context.prev = 1;
              _context.next = 4;
              return _this.$axios.$get("/police/incidents/wanted/most-wanted");
            case 4:
              _this.mostWantedWarrants = _context.sent;
              _context.next = 11;
              break;
            case 7:
              _context.prev = 7;
              _context.t0 = _context["catch"](1);
              console.error('Error loading most wanted suspects:', _context.t0);
              _this.mostWantedWarrants = [];
            case 11:
              _context.prev = 11;
              _this.loading = false;
              return _context.finish(11);
            case 14:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[1, 7, 11, 14]]);
      }))();
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./pages/cad/wanted/most-wanted.vue?vue&type=script&lang=js
 /* harmony default export */ var wanted_most_wantedvue_type_script_lang_js = (most_wantedvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/cad/wanted/most-wanted.vue?vue&type=style&index=0&id=1b5e3043&prod&lang=scss&scoped=true
var most_wantedvue_type_style_index_0_id_1b5e3043_prod_lang_scss_scoped_true = __webpack_require__(1832);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/wanted/most-wanted.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  wanted_most_wantedvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  "1b5e3043",
  null
  
)

/* harmony default export */ var most_wanted = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);