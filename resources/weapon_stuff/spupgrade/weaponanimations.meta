<CWeaponAnimationsSets>
  <WeaponAnimationsSets>
    <Item key="Ballistic">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash />
          <MotionClipSetHash>move_ballistic_2h</MotionClipSetHash>
          <MotionFilterHash />
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash />
          <WeaponClipSetStreamedHash />
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash />
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash />
          <JumpUpperbodyClipSetHash />
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="NULL" />
		  <WeaponSwapClipSetHash>weapons@holster_fat_2h</WeaponSwapClipSetHash>
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="Default">
      <WeaponAnimations>
        <Item key="WEAPON_HATCHET">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash>Cover_Wpn_Melee1h</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@melee_1h</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash>move_strafe_melee_unarmed</MotionStrafingClipSetHash>
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash>move_strafe@melee_small_weapon</MotionStrafingUpperBodyClipSetHash>
          <WeaponClipSetHash>melee@holster</WeaponClipSetHash>
          <WeaponClipSetStreamedHash />
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@hatchet@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@1H_MELEE@HAMMER</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="0.000000" />
          <AnimBlindFireRateModifier value="0.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="false" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
        </Item>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash>cover@move@ai@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_Wpn_RailGun</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@rifle@lo@rail_gun</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@rifle@lo@rail_gun</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@rifle@lo@rail_gun_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@rifle@lo@rail_gun@stealth</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
		  <AimGrenadeThrowNormalClipsetHash />
		  <AimGrenadeThrowAlternateClipsetHash />
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="MP_F_Freemode">
      <Fallback>Gang</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash />
          <MotionClipSetHash>weapons@rifle@f</MotionClipSetHash>
          <MotionFilterHash />
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash />
          <WeaponClipSetStreamedHash />
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash />
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash />
          <JumpUpperbodyClipSetHash />
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="NULL" />
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="FirstPerson">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_HATCHET">
          <MovementOverrideClipSetHash>move_m@generic</MovementOverrideClipSetHash>
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash>Cover_Wpn_Melee1h</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_idle@generic@melee@small_wpn@hammer@</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash>move_strafe_melee_unarmed_fps</MotionStrafingClipSetHash>
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash>move_strafe@melee_small_weapon_fps</MotionStrafingUpperBodyClipSetHash>
          <WeaponClipSetHash>weapons@first_person@aim_idle@generic@melee@small_wpn@hammer@</WeaponClipSetHash>
          <WeaponClipSetStreamedHash />
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_stealth@generic@melee@hammer@</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>anim@melee@machete@streamed_core_fps</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@1H_MELEE@HAMMER</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="0.000000" />
          <AnimBlindFireRateModifier value="0.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="false" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash />
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@melee@small_wpn@hammer@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
          <WeaponClipSetHashForClone>weapons@first_person@aim_idle@remote_clone@melee@one_handed@shared@core</WeaponClipSetHashForClone>
		  <FPSFidgetClipsetHashes>
           <Item>weapons@first_person@aim_idle@p_m_zero@melee@small_wpn@hammer@fidgets@a</Item>
	       <Item>weapons@first_person@aim_idle@p_m_zero@melee@small_wpn@hammer@fidgets@b</Item>
	       <Item>weapons@first_person@aim_idle@p_m_zero@melee@small_wpn@hammer@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RailGun</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_idle@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_idle@generic@heavy@rail_gun@w_idle</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@heavy@rail_gun@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_stealth@generic@assault_rifle@shared@core</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash />
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_idle</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_idle</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_idle</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_idle</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_stealth</FPSTransitionToStealthHash>
          <FPSTransitionToStealthFromUnholsterHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_stealth</FPSTransitionToStealthFromUnholsterHash>
		  <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_idle@p_m_zero@heavy@rail_gun@fidgets@a</Item>
	        <Item>weapons@first_person@aim_idle@p_m_zero@heavy@rail_gun@fidgets@b</Item>
	        <Item>weapons@first_person@aim_idle@p_m_zero@heavy@rail_gun@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="FirstPersonAiming">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RailGun</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_lt@generic@heavy@rail_gun@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@heavy@rail_gun@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_lt@generic@heavy@rail_gun@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash />
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_lt</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_lt</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash></FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_lt</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_lt</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_lt</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_stealth</FPSTransitionToStealthHash>
		  <FPSFidgetClipsetHashes>
        <Item>weapons@first_person@aim_lt@p_m_zero@heavy@rail_gun@fidgets@a</Item>
        <Item>weapons@first_person@aim_lt@p_m_zero@heavy@rail_gun@fidgets@b</Item>
        <Item>weapons@first_person@aim_lt@p_m_zero@heavy@rail_gun@fidgets@c</Item>
		  </FPSFidgetClipsetHashes>
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="FirstPersonRNG">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RailGun</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_rng@generic@heavy@rail_gun@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@heavy@rail_gun@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_rng@generic@heavy@rail_gun@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash />
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_rng</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_rng</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_rng</FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_rng</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_rng</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_stealth</FPSTransitionToStealthHash>
		  <FPSFidgetClipsetHashes>
        <Item>weapons@first_person@aim_rng@p_m_zero@heavy@rail_gun@fidgets@a</Item>
        <Item>weapons@first_person@aim_rng@p_m_zero@heavy@rail_gun@fidgets@b</Item>
        <Item>weapons@first_person@aim_rng@p_m_zero@heavy@rail_gun@fidgets@c</Item>
          </FPSFidgetClipsetHashes>
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="FirstPersonScope">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash>cover@first_person@move@base@2h</CoverMovementClipSetHash>
          <CoverMovementExtraClipSetHash>cover@first_person@move@extra@2h</CoverMovementExtraClipSetHash>
          <CoverAlternateMovementClipSetHash>cover@move@ai@base@2h</CoverAlternateMovementClipSetHash>
          <CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_RailGun</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@first_person@aim_rng@generic@assault_rifle@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_scope@generic@heavy@rail_gun@w_fire</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@heavy@rail_gun@str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_scope@generic@heavy@rail_gun@w_fire</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@rifle@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@2h</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@RIFLE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@RIFLE</FallUpperbodyClipSetHash>
          <FromStrafeTransitionUpperBodyClipSetHash>weapons@rifle@</FromStrafeTransitionUpperBodyClipSetHash>
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash />
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@assault_rifle@shared@aim_trans@idle_to_scope</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@assault_rifle@shared@aim_trans@rng_to_scope</FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@assault_rifle@shared@aim_trans@lt_to_scope</FPSTransitionFromLTHash>
          <FPSTransitionFromScopeHash></FPSTransitionFromScopeHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@unholster_to_scope</FPSTransitionFromUnholsterHash>
          <FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@assault_rifle@shared@aim_trans@stealth_to_scope</FPSTransitionFromStealthHash>
          <FPSTransitionToStealthHash>weapons@first_person@aim_scope@p_m_zero@assault_rifle@shared@aim_trans@scope_to_stealth</FPSTransitionToStealthHash>
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="Fat">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash />
          <MotionClipSetHash>move_fat_2h</MotionClipSetHash>
          <MotionFilterHash />
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash />
          <WeaponClipSetStreamedHash />
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash />
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash />
          <JumpUpperbodyClipSetHash />
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="NULL" />
		  <WeaponSwapClipSetHash>weapons@holster_fat_2h</WeaponSwapClipSetHash>
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="SuperFat">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash />
          <MotionClipSetHash>move_superfat_2h</MotionClipSetHash>
          <MotionFilterHash />
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash />
          <WeaponClipSetStreamedHash />
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash />
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash />
          <JumpUpperbodyClipSetHash />
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="NULL" />
		  <WeaponSwapClipSetHash>weapons@holster_superfat_2h</WeaponSwapClipSetHash>
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="Female">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash />
          <MotionClipSetHash>move_female_2h_heavy_sniper</MotionClipSetHash>
          <MotionFilterHash />
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash />
          <WeaponClipSetStreamedHash />
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash />
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash />
          <JumpUpperbodyClipSetHash />
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="NULL" />
        </Item>
	  </WeaponAnimations>
	</Item>	  
	<Item key="GangFemale">
      <Fallback>Gang</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_RAILGUN">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash />
          <MotionClipSetHash>move_female_2h</MotionClipSetHash>
          <MotionFilterHash />
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash />
          <WeaponClipSetStreamedHash />
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash />
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash />
          <JumpUpperbodyClipSetHash />
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash />
          <SwapWeaponInLowCoverFilterHash />
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="true" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="NULL" />
        </Item>
	  </WeaponAnimations>
	</Item>
  </WeaponAnimationsSets>
</CWeaponAnimationsSets>
