<?xml version="1.0" encoding="UTF-8"?>

<CWeaponComponentInfoBlob>
  <Data>
  </Data>
  <Infos>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SNSPISTOL_CLIP_01</Name>
      <Model>w_pi_sns_pistol_mag1</Model>
      <LocName>WCT_CLIP1</LocName>
      <LocDesc>WCD_SNSP_CLIP1</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="6" />
      <ReloadData ref="RELOAD_DEFAULT_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SNSPISTOL_CLIP_02</Name>
      <Model>w_pi_sns_pistol_mag2</Model>
      <LocName>WCT_CLIP2</LocName>
      <LocDesc>WCD_SNSP_CLIP2</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="33" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="12" />
      <ReloadData ref="RELOAD_LARGE_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_SNSPISTOL_VARMOD_LOWRIDER</Name>
      <Model>w_pi_sns_pistol_luxe</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
	  <TintIndexOverride value="6"/>
	  <ExtraComponents>
		<Item>
		  <ComponentName>COMPONENT_SNSPISTOL_CLIP_01</ComponentName>
		  <ComponentModel>w_pi_sns_pistol_luxe_mag1</ComponentModel>
		</Item>
		<Item>
		  <ComponentName>COMPONENT_SNSPISTOL_CLIP_02</ComponentName>
		  <ComponentModel>w_pi_sns_pistol_luxe_mag2</ComponentModel>
		</Item>
	  </ExtraComponents>
    </Item>
  </Infos>
  <InfoBlobName>DLC - Beach</InfoBlobName>
</CWeaponComponentInfoBlob>
