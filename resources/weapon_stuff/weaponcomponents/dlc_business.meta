<?xml version="1.0" encoding="UTF-8"?>

<CWeaponComponentInfoBlob>
  <Data>
  </Data>
  <Infos>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_HEAVYPISTOL_CLIP_01</Name>
      <Model>w_pi_heavypistol_mag1</Model>
      <LocName>WCT_CLIP1</LocName>
      <LocDesc>WCD_HPST_CLIP1</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="18" />
      <ReloadData ref="RELOAD_DEFAULT_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_HEAVYPISTOL_CLIP_02</Name>
      <Model>w_pi_heavypistol_mag2</Model>
      <LocName>WCT_CLIP2</LocName>
      <LocDesc>WCD_HPST_CLIP2</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="33" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="36" />
      <ReloadData ref="RELOAD_LARGE_WITH_EMPTIES" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SPECIALCARBINE_CLIP_01</Name>
      <Model>w_ar_specialcarbine_mag1</Model>
      <LocName>WCT_CLIP1</LocName>
      <LocDesc>WCD_SCRB_CLIP1</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="30" />
      <ReloadData ref="RELOAD_DEFAULT" />
    </Item>
    <Item type="CWeaponComponentClipInfo">
      <Name>COMPONENT_SPECIALCARBINE_CLIP_02</Name>
      <Model>w_ar_specialcarbine_mag2</Model>
      <LocName>WCT_CLIP2</LocName>
      <LocDesc>WCD_SCRB_CLIP2</LocDesc>
      <AttachBone>AAPClip</AttachBone>
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="true" />
      <CreateObject value="true" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="100" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <ClipSize value="60" />
      <ReloadData ref="RELOAD_DEFAULT" />
	</Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_HEAVYPISTOL_VARMOD_LUXE</Name>
      <Model>W_PI_HeavyPistol_Luxe</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
	  <TintIndexOverride value="7"/>
	  <ExtraComponents>
		<Item>
		  <ComponentName>COMPONENT_HEAVYPISTOL_CLIP_01</ComponentName>
		  <ComponentModel>W_PI_HeavyPistol_LUXE_Mag1</ComponentModel>
		</Item>
		<Item>
		  <ComponentName>COMPONENT_HEAVYPISTOL_CLIP_02</ComponentName>
		  <ComponentModel>W_PI_HeavyPistol_LUXE_Mag2</ComponentModel>
		</Item>
		<Item>
		  <ComponentName>COMPONENT_AT_PI_FLSH</ComponentName>
		  <ComponentModel>w_at_pi_flsh_pdluxe</ComponentModel>
		</Item>
		<Item>
		  <ComponentName>COMPONENT_AT_PI_SUPP</ComponentName>
		  <ComponentModel>W_AT_PI_Supp_LUXE</ComponentModel>
		</Item>
	  </ExtraComponents>
    </Item>
	<Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_SPECIALCARBINE_VARMOD_LOWRIDER</Name>
      <Model>w_ar_specialcarbine_luxe</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
	  <TintIndexOverride value="0" />
	  <ExtraComponents>
		<Item>
		  <ComponentName>COMPONENT_SPECIALCARBINE_CLIP_01</ComponentName>
		  <ComponentModel>w_ar_specialcarbine_luxe_mag1</ComponentModel>
		</Item>
		<Item>
		  <ComponentName>COMPONENT_SPECIALCARBINE_CLIP_02</ComponentName>
		  <ComponentModel>w_ar_specialcarbine_luxe_mag2</ComponentModel>
		</Item>
		<Item>
		  <ComponentName>COMPONENT_SPECIALCARBINE_CLIP_03</ComponentName>
		  <ComponentModel>w_ar_specialcarbine_boxmag_luxe</ComponentModel>
		</Item>
		<Item>
		  <ComponentName>COMPONENT_AT_AR_FLSH</ComponentName>
		  <ComponentModel>w_at_ar_flsh_pdluxe</ComponentModel>
		</Item>
		<Item>
		  <ComponentName>COMPONENT_AT_AR_AFGRIP</ComponentName>
		  <ComponentModel>w_at_ar_afgrip_luxe</ComponentModel>
		</Item>
		<Item>
		  <ComponentName>COMPONENT_AT_SCOPE_MEDIUM</ComponentName>
		  <ComponentModel>w_at_scope_medium_luxe</ComponentModel>
		</Item>
		<Item>
		  <ComponentName>COMPONENT_AT_AR_SUPP_02</ComponentName>
		  <ComponentModel>w_at_ar_supp_luxe_02</ComponentModel>
		</Item>
	  </ExtraComponents>
    </Item>
  </Infos>
  <InfoBlobName>DLC - Business</InfoBlobName>
</CWeaponComponentInfoBlob>
